{"uid": "instruct-1", "prompt": ["You are assisting the Hong Kong Police in extracting key case-related information from emails received from other police forces around the world. These emails request assistance from the Hong Kong Police in handling various cases. Your task is to identify and organize critical details.", "Use the following context to answer the question at the end.", "If the information for a specific field is not known or not available, please respond with 'Unfound' for that specific field.", "There are explanations for the individual's property. Age: calculate based on the current year, 2024, and the Birth Date provided. Gender: The sex of an individual (e.g., male, female, non-binary, etc.). Type: The role or status of the individual in a given situation (e.g., suspect, victim, beneficial). Other ID Type: Any form of identification other than the primary one listed (e.g., driver's license, passport). Other ID Number: The number pertaining to the Other ID Type. Occupation: The job or profession of an individual.", "There are explanations for the case's property. Internal Case ID: This refers to the Your Ref in the email. External Case ID: This refers to the Our Ref in the email. Request Data: The date at the start of the email. Request Subject: The subject of the email", "\n{context}", "\nQuestion: {question}", "\nPrecise Answers Format:", "Case List:", "Internal Case ID: /* Your Ref or Unfound */;", "External Case ID: /* Our Ref or Unfound */;", "Priority: /* The priority at the start of the email or Unfound */;", "Request Date: /* The date at the start of the email or Unfound */;", "Request Party: /* Party Requesting or Unfound */;", "Request PIC: /* Police Officer In Charge of Request or Unfound */;", "Request Subject: /* Subject of Request or Unfound */;", "Case Type: /* Type of Case or Unfound */;", "Police Officer in charge: /* Name of Police Officer in Charge or Unfound */;", "Police Officer Number: /* Police Officer's Badge Number or Unfound */;", "Confidentiality: /* Confidentiality Level or Unfound */;", "Suspect Names: /* Names of Suspects or Unfound */;", "Victim Names: /* Names of Victims or Unfound */;", "Individual Lists:", "Full Name: /* Individual's Full Name or Unfound */;", "Age: /* Calculate based on the current year, 2024, and the Birth Date provided or Unfound */;", "Address: /* Individual's Address or Unfound */;", "Gender: /* Individual's Gender or Unfound */;", "Type: /* Suspect, victim, or beneficial or Unfound */;", "Chinese Commercial Code: /* Chinese Commercial Code or Unfound*/;", "Alias: /* <PERSON><PERSON> or Unfound */;", "Alias Chinese Commercial Code: /* Alias's Chinese Commercial Code or Unfound */;", "Immigration Status: /* Immigration Status or Unfound */;", "Birth Date: /* Date of Birth or Unfound */;", "HKID: /* Hong Kong ID Number or Unfound*/;", "Date of Issue: /* Date of Issue for HKID or Unfound */;", "Other ID Type: /* Any form of identification other than the primary one listed, e.g., passport, driver's license) or Unfound */;", "Other ID Number: /* Number of the other ID type provided or Unfound */;", "Height: /* Height or Unfound */;", "Weight: /* Weight or Unfound */;", "Native: /* Native Place or Unfound */;", "Nationality: /* Nationality or Unfound */;", "Race: /* Race or Unfound */;", "Dialect: /* <PERSON><PERSON><PERSON> Spoken or Unfound */;", "Birth Place: /* Place of Birth or Unfound */;", "Birth Country: /* Country of Birth or Unfound */;", "Birth Province: /* Province of Birth or Unfound */;", "Occupation: /* Company and position or Unfound */;", "Occupation Code: /* job's Code or Unfound */;", "Occupation Remark: /* Remark on job or Unfound*/;", "School: /* School Attended or Unfound*/;", "Crime Record Bureau Record: /* Crime Record Bureau Record or Unfound*/;", "Crime Record Bureau Record Ref Number: /* Crime Record Bureau Record Reference Number or Unfound*/;"]}