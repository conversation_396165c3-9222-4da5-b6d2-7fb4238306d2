{"uid": "instruct-1", "prompt": ["Use the following context to answer the question at the end.", "For each cases mentioned in the context, provide their details in the following format. Do not add any extra text.", "For each person involved in these cases, provide their details in the following format. Do not add any extra text.", "If you don't know the answer or if the information is not available, please respond with 'Unfound' for that specific field.", "\n{context}", "\nQuestion: {question}", "\nPrecise Answers Format:", "Case List:", "Internal Case ID: /* Internal Case ID or 'Unfound' */;", "External Case ID: /* External Case ID or 'Unfound' */;", "Priority: /* Priority Level or 'Unfound' */;", "Request Date: /* Date of Request or 'Unfound' */;", "Request Party: /* Party Requesting or 'Unfound' */;", "Request PIC: /* Person In Charge of Request or 'Unfound' */;", "Request Subject: /* Subject of Request or 'Unfound' */;", "Case Type: /* Type of Case or 'Unfound' */;", "Police Officer in charge: /* Name of Officer in Charge or 'Unfound' */;", "Police Officer Number: /* Officer's Badge Number or 'Unfound' */;", "Confidentiality: /* Confidentiality Level or 'Unfound' */;", "Suspect Names: /* Names of Suspects or 'Unfound' */;", "Victim Names: /* Names of Victims or 'Unfound' */;", "Individual Lists:", "Full Name: /* Individual's Full Name or 'Unfound' */;", "Age: /* Individual's Age or 'Unfound' */;", "Address: /* Individual's Address or 'Unfound' */;", "Gender: /* Individual's Gender or 'Unfound' */;", "Type: /* Individual Type (Suspect / Victim) or 'Unfound' */;", "Chinese Commercial Code: /* Chinese Commercial Code or 'Unfound' */;", "Alias: /* <PERSON><PERSON> or 'Unfound' */;", "Alias Chinese Commercial Code: /* Alias's Chinese Commercial Code or 'Unfound' */;", "Immigration Status: /* Immigration Status or 'Unfound' */;", "Birth Date: /* Date of Birth or 'Unfound' */;", "HKID: /* Hong Kong ID Number or 'Unfound' */;", "Date of Issue: /* Date of Issue for ID or 'Unfound' */;", "Other ID Type: /* Other Identification Type or 'Unfound' */;", "Other ID Number: /* Other Identification Number or 'Unfound' */;", "Height: /* Height or 'Unfound' */;", "Weight: /* Weight or 'Unfound' */;", "Native: /* Native Place or 'Unfound' */;", "Nationality: /* Nationality or 'Unfound' */;", "Race: /* Race or 'Unfound' */;", "Dialect: /* <PERSON><PERSON><PERSON> Spoken or 'Unfound' */;", "Birth Place: /* Place of Birth or 'Unfound' */;", "Birth Country: /* Country of Birth or 'Unfound' */;", "Birth Province: /* Province of Birth or 'Unfound' */;", "Occupation: /* Occupation or 'Unfound' */;", "Occupation Code: /* Occupation Code or 'Unfound' */;", "Occupation Remark: /* Remark on Occupation or 'Unfound' */;", "School: /* School Attended or 'Unfound' */;", "Crime Record Bureau Record: /* Crime Record Bureau Record or 'Unfound' */;", "Crime Record Bureau Record Ref Number: /* Crime Record Bureau Record Reference Number or 'Unfound' */;"]}