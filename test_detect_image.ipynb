{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Severe Disease Extended Coverage,(For individuals aged 86 or younger at the time of application) 1. 严重疾病保障,   - 56种严重病情中的任何1种 2. 疾病组别1：癌症,   (包括新发病、复发、转移及持续癌症) 3. 疾病组别2：心臟病发作／中风 4. 疾病组别3：阿茲海默症 / 帕金森症 / 不可还原之器官性功能障碍 / 脑退化疾病 (脑退化症) / 柏金逊症 5. 疾病组别4：与主要器官及功能相关的疾病 6. 疾病组别5：其他 7. 索偿1次高达100%,+ 额外索偿2次200%,+ 额外索偿2次200%,+ 额外索偿1次100%,+ 额外索偿2次200%\n"]}], "source": ["import re\n", "\n", "def replace_newlines(text):\n", "    # First, replace double newlines with a space\n", "    text = re.sub(r'\\n\\n', ' ', text)\n", "    \n", "    # Then, replace single newlines with a comma\n", "    text = re.sub(r'\\n', ',', text)\n", "    \n", "    return text\n", "\n", "# Your input text\n", "input_text = \"\"\"Severe Disease Extended Coverage\\n(For individuals aged 86 or younger at the time of application)\\n\\n1. 严重疾病保障\\n   - 56种严重病情中的任何1种\\n\\n2. 疾病组别1：癌症\\n   (包括新发病、复发、转移及持续癌症)\\n\\n3. 疾病组别2：心臟病发作／中风\\n\\n4. 疾病组别3：阿茲海默症 / 帕金森症 / 不可还原之器官性功能障碍 / 脑退化疾病 (脑退化症) / 柏金逊症\\n\\n5. 疾病组别4：与主要器官及功能相关的疾病\\n\\n6. 疾病组别5：其他\\n\\n7. 索偿1次高达100%\\n+ 额外索偿2次200%\\n+ 额外索偿2次200%\\n+ 额外索偿1次100%\\n+ 额外索偿2次200%\"\"\"\n", "\n", "# Apply the replacement\n", "output_text = replace_newlines(input_text)\n", "\n", "print(output_text)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No valid text detected in the image.\n", "No valid text detected in the image.\n", "Valid text detected: A\n", "Valid text detected: Actual text in the image\n", "Valid text detected: Some other valid text output\n"]}], "source": ["import re\n", "\n", "def validate_ocr_output(ocr_text):\n", "    # Remove any \"All words in the image:\" prefix\n", "    cleaned_text = re.sub(r'^All words in the image:\\s*', '', ocr_text.strip())\n", "    \n", "    # Define patterns for unwanted outputs\n", "    number_sequence_pattern = r'^[\\d\\s]+$'\n", "    single_digit_pattern = r'^\\d$'\n", "    \n", "    # Check if the cleaned text matches any of the unwanted patterns\n", "    if (re.match(number_sequence_pattern, cleaned_text) or \n", "        re.match(single_digit_pattern, cleaned_text)):\n", "        return None  # No valid text detected\n", "    \n", "    # If we get here, it means the text doesn't match our \"no text\" patterns\n", "    return cleaned_text if cleaned_text else None\n", "\n", "# Example usage:\n", "ocr_outputs = [\n", "    \"All words in the image:\\n1\\n2\\n3\\n4\\n5\\n6\\n7\\n8\\n9\\n0\",\n", "    \"All words in the image:\\n0\",\n", "    'All words in the image:\\nA',\n", "    \"All words in the image:\\nActual text in the image\",\n", "    \"Some other valid text output\"\n", "]\n", "\n", "for output in ocr_outputs:\n", "    validated_text = validate_ocr_output(output)\n", "    if validated_text:\n", "        print(f\"Valid text detected: {validated_text}\")\n", "    else:\n", "        print(\"No valid text detected in the image.\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["'Hello' is a valid word.\n", "'a' is not a valid word.\n", "'123' is not a valid word.\n", "'Cat!' is not a valid word.\n", "'Dog123' is not a valid word.\n", "'img' is not a valid word.\n", "'Valid' is a valid word.\n", "'Not-Valid' is not a valid word.\n", "'' is not a valid word.\n"]}], "source": ["import re\n", "import string\n", "\n", "def is_valid_word(word):\n", "    # Remove leading/trailing whitespace\n", "    word = word.strip()\n", "    \n", "    # Check if the word is empty after stripping\n", "    if not word:\n", "        return False\n", "    \n", "    # Check if the word is too short (e.g., less than 2 characters)\n", "    if len(word) < 2:\n", "        return False\n", "    \n", "    # Check if the word contains only letters\n", "    if not word.isalpha():\n", "        return False\n", "    \n", "    # Check if the word contains any digits\n", "    if any(char.isdigit() for char in word):\n", "        return False\n", "    \n", "    # Check if the word contains any punctuation\n", "    if any(char in string.punctuation for char in word):\n", "        return False\n", "    \n", "    # Check if the word is a common OCR error pattern (e.g., sequence of numbers)\n", "    if re.match(r'^\\d+$', word):\n", "        return False\n", "    \n", "    # Check if the word is in a list of known invalid words or common OCR errors\n", "    invalid_words = ['the', 'a', 'an', 'and', 'or', 'but', 'img', 'image', 'photo']\n", "    if word.lower() in invalid_words:\n", "        return False\n", "    \n", "    # If all checks pass, consider the word valid\n", "    return True\n", "\n", "# Example usage\n", "test_words = ['Hello', 'a', '123', 'Cat!', 'Dog123', 'img', 'Valid', 'Not-Valid', '']\n", "\n", "for word in test_words:\n", "    if is_valid_word(word):\n", "        print(f\"'{word}' is a valid word.\")\n", "    else:\n", "        print(f\"'{word}' is not a valid word.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dtt-llm-rag/venv/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "Ultralytics YOLOv8.0.43 🚀 Python-3.10.12 torch-2.2.0+cu121 CUDA:0 (NVIDIA L40S, 45596MiB)\n", "Model summary (fused): 168 layers, 11126358 parameters, 0 gradients, 28.4 GFLOPs\n", "\n", "image 1/1 /home/<USER>/dtt-llm-rag/image/CIM3_BCIM3 Product Manual (2023-05-17) PIL/image-new-44-0.png: 416x640 1 borderless, 99.1ms\n", "Speed: 94.4ms preprocess, 99.1ms inference, 368.5ms postprocess per image at shape (1, 3, 640, 640)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<class 'numpy.ndarray'>\n", "<class 'PIL.Image.Image'>\n"]}], "source": ["from ultralyticsplus import YOLO, render_result\n", "from matplotlib import pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "pd.set_option('display.max_rows', 500)\n", "pd.set_option('display.max_columns', 500)\n", "pd.set_option('display.width', 1000)\n", "\n", "import pytesseract\n", "from pytesseract import Output\n", "from PIL import Image\n", "import os\n", "\n", "os.environ['TESSDATA_PREFIX'] = '/home/<USER>/dtt-llm-rag/tessdata_dir'\n", "special_config = '--psm 12 --oem 1'\n", "languages_ = \"eng\"\n", "\n", "# load model\n", "model = YOLO('foduucom/table-detection-and-extraction')\n", "\n", "# set model parameters\n", "model.overrides['conf'] = 0.25  # NMS confidence threshold\n", "model.overrides['iou'] = 0.45  # NMS IoU threshold\n", "model.overrides['agnostic_nms'] = False  # NMS class-agnostic\n", "model.overrides['max_det'] = 1000  # maximum number of detections per image\n", "\n", "# set image\n", "image = '/home/<USER>/dtt-llm-rag/image/CIM3_BCIM3 Product Manual (2023-05-17) PIL/image-new-44-0.png'\n", "# perform inference\n", "results = model.predict(image)\n", "\n", "# observe results\n", "# print(\"results:\",results)\n", "# print(\"bbox:\",results[0].boxes.xyxy.tolist())\n", "render = render_result(model=model, image=image, result=results[0])\n", "render.save(fp=f\"detect_table.png\")\n", "\n", "x1, y1, x2, y2, _, _ = tuple(int(item) for item in results[0].boxes.data.cpu().numpy()[0])\n", "img = np.array(Image.open(image))\n", "reverted_image=Image.fromarray(img)\n", "reverted_image.save(fp=f\"reverted_table.png\")\n", "#cropping\n", "cropped_image = img[y1:y2, x1:x2]\n", "print(type(cropped_image))\n", "cropped_image = Image.fromarray(cropped_image)\n", "print(type(cropped_image))\n", "cropped_image.save(fp=f\"crop_table.png\")\n", "table_img=Image.open(\"crop_table.png\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# use img2table library to extract tables\n", "from IPython.display import display_html\n", "from PIL import Image as PILImage\n", "import io\n", "from img2table.document import Image"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************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", "text/plain": ["<PIL.PngImagePlugin.PngImageFile image mode=P size=2338x1655>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["image_path=\"yolo/CIM3_BCIM3 Product Manual (2023-05-17) PIL/original/yolo-45.png\"\n", "PILImage.open(image_path)\n", "\n", "# bw_img = image.convert(\"L\")\n", "# bw_img.save(\"grescale.png\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(self, lang: str = 'en', kw: Dict = None)\n"]}], "source": ["import inspect\n", "from img2table.ocr import PaddleOCR\n", "\n", "# Print the signature of the constructor\n", "print(inspect.signature(PaddleOCR.__init__))"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["download https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_det_infer.tar to /home/<USER>/.paddleocr/whl/det/ch/ch_PP-OCRv4_det_infer/ch_PP-OCRv4_det_infer.tar\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 4.89M/4.89M [00:00<00:00, 12.2MiB/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["download https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_rec_infer.tar to /home/<USER>/.paddleocr/whl/rec/ch/ch_PP-OCRv4_rec_infer/ch_PP-OCRv4_rec_infer.tar\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 11.0M/11.0M [00:00<00:00, 18.6MiB/s]\n"]}], "source": ["from img2table.ocr import PaddleOCR\n", "\n", "ocr = PaddleOCR(lang=\"ch\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# Use AzureOCR\n", "from img2table.ocr import AzureOCR\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "load_dotenv()\n", "# Get the endpoint and subscription key from .env file\n", "endpoint = os.getenv('AZURE_OCR_ENDPOINT')\n", "subscription_key = os.getenv('AZURE_OCR_SUBSCRIPTION_KEY')\n", "\n", "# Use the variables in your OCR setup\n", "ocr = AzureOCR(endpoint=endpoint, subscription_key=subscription_key)\n", "# help(AzureOCR)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["image = PILImage.open(image_path).convert('L')\n", "# Define the threshold value\n", "threshold_value = 190  # Adjust this value as needed\n", "grayscale_image = image.point(lambda p: 255 if p > threshold_value else 0)\n", "# grayscale_image.save('test.png')"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["# Define image\n", "# grayscale_image = PILImage.open(\"test.png\")\n", "img_byte_arr = io.BytesIO()\n", "grayscale_image.save(img_byte_arr, format='PNG') #save as io.BytesIO object\n", "img = Image(src=img_byte_arr.getvalue())"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dtt-llm-rag/venv/lib/python3.10/site-packages/img2table/tables/processing/bordered_tables/cells/identification.py:17: PerformanceWarning: Determining the column names of a LazyFrame requires resolving its schema, which is a potentially expensive operation. Use `LazyFrame.collect_schema().names()` to get the column names without this warning.\n", "  .rename({col: f\"{col}_\" for col in df_h_lines.columns})\n", "/home/<USER>/dtt-llm-rag/venv/lib/python3.10/site-packages/img2table/tables/processing/bordered_tables/cells/deduplication.py:21: PerformanceWarning: Determining the column names of a LazyFrame requires resolving its schema, which is a potentially expensive operation. Use `LazyFrame.collect_schema().names()` to get the column names without this warning.\n", "  .rename({col: f\"{col}_\" for col in df_cells.columns})\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[ExtractedTable(title=None, bbox=(251, 231, 2078, 1195),shape=(21, 5))]\n", "Number of tables: 1\n"]}], "source": ["import inspect\n", "# Extract tables with implicit rows\n", "extracted_tables = img.extract_tables(\n", "    ocr=ocr,\n", "    implicit_rows=False,\n", "    borderless_tables=False,\n", "    min_confidence=50,\n", ")\n", "# print(inspect.signature(img.extract_tables))\n", "print(extracted_tables)\n", "print(f\"Number of tables: {len(extracted_tables)}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(extracted_tables)\n", "print(extracted_tables[0].content)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/html": ["<h3 style=\"text-align: center\">Table detected</h3>\n", "                   <p style=\"text-align: center\">\n", "                       <b>Title:</b> No title detected<br>\n", "                       <b>Bounding box:</b> x1=251, y1=231, x2=2078, y2=1195\n", "                   </p>\n", "                   <div align=\"center\"><table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>公司名</td>\n", "      <td>友公司\\n宏公司\\n保誠\\n保誠\\n「誠保一生」危疾保系列\\n危疾加装保II\\n「爱X航」系列\\n守X无周危疾保系列</td>\n", "      <td>友公司\\n宏公司\\n保誠\\n保誠\\n「誠保一生」危疾保系列\\n危疾加装保II\\n「爱X航」系列\\n守X无周危疾保系列</td>\n", "      <td>友公司\\n宏公司\\n保誠\\n保誠\\n「誠保一生」危疾保系列\\n危疾加装保II\\n「爱X航」系列\\n守X无周危疾保系列</td>\n", "      <td>友公司\\n宏公司\\n保誠\\n保誠\\n「誠保一生」危疾保系列\\n危疾加装保II\\n「爱X航」系列\\n守X无周危疾保系列</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>計劃名</td>\n", "      <td>友公司\\n宏公司\\n保誠\\n保誠\\n「誠保一生」危疾保系列\\n危疾加装保II\\n「爱X航」系列\\n守X无周危疾保系列</td>\n", "      <td>友公司\\n宏公司\\n保誠\\n保誠\\n「誠保一生」危疾保系列\\n危疾加装保II\\n「爱X航」系列\\n守X无周危疾保系列</td>\n", "      <td>友公司\\n宏公司\\n保誠\\n保誠\\n「誠保一生」危疾保系列\\n危疾加装保II\\n「爱X航」系列\\n守X无周危疾保系列</td>\n", "      <td>友公司\\n宏公司\\n保誠\\n保誠\\n「誠保一生」危疾保系列\\n危疾加装保II\\n「爱X航」系列\\n守X无周危疾保系列</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>保善豁免</td>\n", "      <td>免1年保费（重兒鱼病况）</td>\n", "      <td>免1年保费（器重兒鱼病况）</td>\n", "      <td>X</td>\n", "      <td>X</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>■親子保营豁免保障</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>保单持有人身故時豁免日後\\n所有保费至年届受保人25</td>\n", "      <td>X</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>早期最重疾病保障</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>受保病况数目</td>\n", "      <td>49頂</td>\n", "      <td>49顶</td>\n", "      <td>44真</td>\n", "      <td>44厦</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>·原位癌保障金额</td>\n", "      <td>每次预支25%保额</td>\n", "      <td>每次预支25%保额</td>\n", "      <td>每次预支20%保额</td>\n", "      <td>每次预支20%保额</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>指定器官敷目</td>\n", "      <td>所有器官【皮虏原位癌除外]</td>\n", "      <td>所有器官（皮虏原位癌除外]</td>\n", "      <td>所有器官（皮虏原位癌除外]</td>\n", "      <td>12個器官</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>可赔次敷</td>\n", "      <td>2次（不同器官）</td>\n", "      <td>2 次(不同器官)</td>\n", "      <td>2次（不同器官</td>\n", "      <td>2次(不同器官)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>「通波仔」保障金额</td>\n", "      <td>每次预支25%保额</td>\n", "      <td>每次预支25%保额</td>\n", "      <td>每次预支20%保额</td>\n", "      <td>每次预支20%保题</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>o可赔值次敷</td>\n", "      <td>2次</td>\n", "      <td>2次</td>\n", "      <td>1次</td>\n", "      <td>1次</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>■原位癌及「通波仔」\\n病况保障限</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>50,000美元/400,000港元\\n[計算所有危疾保单]</td>\n", "      <td>50,000美元/400,000港元\\n[計算所有危疾保单]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>早期甲状腺或前列腺\\n癌\\n次级侵害性恶性履瘤</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>每次预支20%保额\\n共1次</td>\n", "      <td>每次预支20%保额\\n早期甲状腺及早期恶性瘤各1次</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>保曹豁免保障</td>\n", "      <td>√豁免1年保费</td>\n", "      <td>√免1年保费</td>\n", "      <td>X</td>\n", "      <td>X</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>深切治瘾保障</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>保障金额</td>\n", "      <td>香港/澳門\\n：预支保额20%\\n其他地區：预支保额10%</td>\n", "      <td>香港：预支保20%\\n其他地區：预支保额10%</td>\n", "      <td>香港/澳門：预支保额20%\\n其他地區：预支保额10%</td>\n", "      <td>预支20%保额</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>暗要求</td>\n", "      <td>無需使用侵人性生支持</td>\n", "      <td>無需使用侵人性生支持</td>\n", "      <td>需速3日使用侵人性維生支持</td>\n", "      <td>無需使用侵人性維生支持</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>重疾病保障</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>受保病况数目</td>\n", "      <td>56頂</td>\n", "      <td>56頂</td>\n", "      <td>59 12</td>\n", "      <td>61夏</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>保障金额</td>\n", "      <td>100%保额－预支保障+特别红利</td>\n", "      <td>100%保额－预支保障+特别红利</td>\n", "      <td>100%保额－预支保障+终期分红</td>\n", "      <td>100%保额－预支保障+格期红利</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>保营豁免保障</td>\n", "      <td>豁免所有保费</td>\n", "      <td>豁免所有保费</td>\n", "      <td>豁免所有保费</td>\n", "      <td>豁免所有保普</td>\n", "    </tr>\n", "  </tbody>\n", "</table></div>\n", "                   <hr>\n", "                "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# table_implicit_rows = extracted_tables_implicit.pop()\n", "# print(table_implicit_rows)\n", "display_html(extracted_tables[0].html_repr(title=\"Table detected\"), raw=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3.10.12 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "1386024b0daf3ee9b9bb08d1296ae51d55bef7092d20bccba14b0811047acfe1"}}}, "nbformat": 4, "nbformat_minor": 2}