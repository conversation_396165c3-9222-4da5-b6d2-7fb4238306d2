#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试虚拟环境修复
"""

import os
import tempfile

def test_venv_processor():
    """测试虚拟环境处理器"""
    print("🧪 测试虚拟环境处理器")
    print("=" * 40)
    
    try:
        from mineru_venv_integration import get_venv_processor
        
        processor = get_venv_processor()
        print(f"✅ 处理器获取成功: {type(processor)}")
        print(f"🔍 处理器可用: {processor.is_available()}")
        
        if processor.is_available():
            # 创建一个假的PDF文件来测试
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f:
                f.write(b'%PDF-1.4\n%fake pdf content\n')
                fake_pdf_path = f.name
            
            try:
                print(f"🔍 测试处理假PDF: {fake_pdf_path}")
                layout_data, error = processor.process_pdf(fake_pdf_path)
                
                print(f"🔍 处理结果:")
                print(f"  - layout_data: {layout_data is not None}")
                print(f"  - error: {error}")
                
                if layout_data:
                    print(f"  - layout_data类型: {type(layout_data)}")
                    print(f"  - layout_data内容: {layout_data}")
                    
                    # 测试转换为chunks
                    chunks = processor.convert_to_chunks(layout_data, "test.pdf")
                    print(f"  - 生成chunks数量: {len(chunks)}")
                    
                    if chunks:
                        print(f"  - 第一个chunk: {chunks[0][0][:100]}...")
                        print(f"  - 第一个chunk元数据: {chunks[0][1]}")
                    
                    return True
                else:
                    print(f"❌ 处理失败: {error}")
                    return False
                    
            finally:
                # 清理临时文件
                try:
                    os.unlink(fake_pdf_path)
                except:
                    pass
        else:
            print("❌ 处理器不可用")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 虚拟环境修复测试")
    print("=" * 50)
    
    success = test_venv_processor()
    
    print(f"\n📊 测试结果")
    print("=" * 50)
    
    if success:
        print("🎉 虚拟环境处理器测试成功!")
        print("💡 现在可以重新测试v7版本:")
        print("   python chatbot_newui_new_version_v7.py")
        print("📋 上传PDF时应该会看到:")
        print("   ✅ [PROCESSOR_DEBUG] 使用虚拟环境处理器")
        print("   🔍 [DEBUG] 处理结果: layout_data=True")
    else:
        print("❌ 虚拟环境处理器测试失败")
        print("💡 可能需要进一步调试")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 测试通过!")
        else:
            print("\n❌ 测试失败!")
    except Exception as e:
        print(f"\n❌ 测试过程出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
