# 改进的PDF标注功能使用指南

## 概述

本指南介绍如何使用改进的PDF标注功能，该功能现在**只显示包含边界框(bbox)标注的页面**，生成更精简的PDF文件，**默认不包含图例页面**。

## 🆕 重要更新

### 主要特性
- ✅ **只保留有标注的页面**：生成的PDF只包含有bbox的页面，跳过空白或无关页面
- ✅ **默认不添加图例**：生成更简洁的PDF，可选择性添加图例页面
- ✅ **支持text_with_title类型**：完全支持标题文本的标注
- ✅ **增强的视觉效果**：彩色边框、半透明填充、内容类型标签
- ✅ **详细统计信息**：返回页面数量、标注统计等信息

## API端点

### 1. `/annotate_pdf_with_chunks` (推荐)

专门处理chunks_info格式数据的新API。

#### 请求格式
```json
{
    "filename": "your_file.pdf",
    "add_legend": false,
    "chunks_info": [
        {
            "bbox": [x1, y1, x2, y2],
            "page_num": 1,
            "content_type": "text_with_title",
            "source": "document_name"
        }
    ]
}
```

#### 响应格式
```json
{
    "status": "success",
    "message": "PDF annotation completed - 只显示包含标注的 5 页，无图例页面",
    "output_file": "filename_annotated.pdf",
    "output_path": "processed/filename_annotated.pdf",
    "annotation_statistics": {
        "text_with_title": 3,
        "text": 2,
        "table": 1
    },
    "total_annotations": 6,
    "legend_added": false,
    "pages_with_bbox": 5,
    "original_total_pages": 50
}
```

### 2. `/draw_file` (改进版)

原始API，现在也只保留有bbox的页面，不添加图例。

#### 请求格式
```json
{
    "filename": "your_file.pdf",
    "pages_data": {
        "3": [
            {
                "content_type": "text_with_title",
                "bbox": [64.0, 486.0, 408.0, 630.0]
            }
        ],
        "5": [
            {
                "content_type": "table",
                "bbox": [100.0, 200.0, 500.0, 400.0]
            }
        ]
    }
}
```

## 支持的内容类型

| 类型 | 颜色 | 说明 |
|------|------|------|
| `text_with_title` | 绿色 | 包含标题的文本区域 |
| `text` | 绿色 | 普通文本区域 |
| `table` | 蓝色 | 表格区域 |
| `image` | 红色 | 图片区域 |
| `chart` | 橙色 | 图表区域 |
| `flowchart` | 紫色 | 流程图区域 |
| `confusion_matrix` | 品红色 | 混淆矩阵区域 |

## Python使用示例

### 基本使用（不添加图例）
```python
import requests

def annotate_pdf_simple():
    url = "http://localhost:8037/annotate_pdf_with_chunks"
    
    data = {
        "filename": "document.pdf",
        # add_legend 默认为 false，可以不设置
        "chunks_info": [
            {
                "bbox": [64.0, 486.0, 408.0, 630.0],
                "page_num": 3,
                "content_type": "text_with_title",
                "source": "document"
            },
            {
                "bbox": [450.0, 358.0, 791.0, 430.0],
                "page_num": 3,
                "content_type": "text",
                "source": "document"
            }
        ]
    }
    
    response = requests.post(url, json=data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 成功! 只保留了 {result['pages_with_bbox']} 页")
        print(f"输出文件: {result['output_file']}")
        print(f"原始PDF: {result['original_total_pages']} 页 → 标注PDF: {result['pages_with_bbox']} 页")
        print(f"图例添加: {result['legend_added']}")
    else:
        print(f"❌ 失败: {response.text}")

annotate_pdf_simple()
```

### 可选择性添加图例
```python
def annotate_with_legend():
    url = "http://localhost:8037/annotate_pdf_with_chunks"
    
    data = {
        "filename": "document.pdf",
        "add_legend": True,  # 明确要求添加图例
        "chunks_info": [
            {
                "bbox": [64.0, 486.0, 408.0, 630.0],
                "page_num": 3,
                "content_type": "text_with_title",
                "source": "document"
            }
        ]
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    print(f"处理结果: {result['message']}")
    print(f"图例添加状态: {result['legend_added']}")

annotate_with_legend()
```

### 使用原始API
```python
def annotate_with_draw_file():
    url = "http://localhost:8037/draw_file"
    
    data = {
        "filename": "document.pdf",
        "pages_data": {
            "3": [
                {
                    "content_type": "text_with_title",
                    "bbox": [64.0, 486.0, 408.0, 630.0]
                }
            ]
        }
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    print(f"处理结果: {result['message']}")
    print(f"只保留了 {result['pages_with_bbox']} 页")

annotate_with_draw_file()
```

## JavaScript使用示例

### 基本使用
```javascript
async function annotateSelectedPages() {
    const url = 'http://localhost:8037/annotate_pdf_with_chunks';
    
    const data = {
        filename: 'document.pdf',
        // add_legend 默认为 false，可以不设置
        chunks_info: [
            {
                bbox: [64.0, 486.0, 408.0, 630.0],
                page_num: 3,
                content_type: 'text_with_title',
                source: 'document'
            }
        ]
    };
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log(`✅ 成功! ${result.message}`);
            console.log(`页面缩减: ${result.original_total_pages} → ${result.pages_with_bbox}`);
            console.log(`图例状态: ${result.legend_added}`);
        } else {
            console.error(`❌ 失败:`, result.error);
        }
    } catch (error) {
        console.error('❌ 网络错误:', error);
    }
}

annotateSelectedPages();
```

### 添加图例的版本
```javascript
async function annotateWithLegend() {
    const data = {
        filename: 'document.pdf',
        add_legend: true,  // 明确要求添加图例
        chunks_info: [
            // ... chunks_info 数据
        ]
    };
    
    const response = await fetch('/annotate_pdf_with_chunks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });
    
    const result = await response.json();
    console.log(`图例添加状态: ${result.legend_added}`);
}
```

## 功能优势

### 🎯 精简输出
- **原始PDF**: 可能有50-100页
- **标注PDF**: 只包含有标注的5-10页
- **文件大小**: 显著减小
- **查看效率**: 只关注重要内容
- **无冗余**: 默认不添加图例页面

### 🎨 视觉增强
- 彩色边框和填充
- 内容类型标签
- 专业的视觉效果
- 可选的图例页面

### 📊 详细统计
- 标注数量统计
- 页面数量对比
- 内容类型分布
- 图例添加状态

## 配置选项

### 图例页面控制
```json
{
    "add_legend": false  // 默认false，设为true添加图例页面
}
```

#### 图例选项说明
- `false` (默认): 不添加图例页面，PDF更简洁
- `true`: 在PDF开头添加颜色图例页面，便于理解标注含义

### 内容类型映射
系统自动识别并映射内容类型到相应颜色，无需手动配置。

## 故障排除

### 常见问题

1. **没有生成PDF**
   - 检查chunks_info是否为空
   - 确认页面编号是否存在

2. **页面顺序错误**
   - 系统按原始页码自动排序
   - 检查page_num字段值

3. **需要图例但没有显示**
   - 确认设置了 `"add_legend": true`
   - 检查API响应中的 `legend_added` 字段

### 调试信息
服务器控制台会显示详细处理信息：
```
原始PDF共有 50 页
将处理 10 个区域
处理第 3 页，共 5 个区域
  - 添加 text_with_title 标注: [64.0, 486.0, 408.0, 630.0]
标注完成！只保留有bbox的 3 页，新PDF已保存为: processed/document_annotated.pdf
```

## 性能考量

- **处理速度**: 页面越少处理越快
- **内存使用**: 按需加载页面，内存效率高
- **文件大小**: 只包含必要页面，默认无图例，文件更小
- **并发处理**: 支持多个请求同时处理

## 最佳实践

1. **页面选择**: 只标注重要页面，提高效率
2. **图例使用**: 只在需要理解颜色含义时添加图例
3. **内容类型**: 使用准确的content_type便于识别
4. **坐标精度**: 确保bbox坐标准确
5. **批量处理**: 一次性处理多个区域而非分批

## 使用场景建议

### 不添加图例的场景
- 内部文档审阅
- 快速标注预览
- 自动化批量处理
- 文件大小敏感的场合

### 添加图例的场景
- 展示给他人查看
- 需要解释标注含义
- 正式文档输出
- 培训和教学用途

---

**注意**: 此功能将原始PDF中的所有页面过滤，只保留包含标注的页面。默认不添加图例页面以保持PDF简洁。如需图例或完整PDF，请相应设置参数或备份原始文件。 