#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本 - chatbot_newui_new_version_v7
带有MinerU集成的增强版本
"""

import os
import sys
import subprocess
import time
import webbrowser
from threading import Timer

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    required_files = [
        'chatbot_newui_new_version_v7.py',
        'mineru_integration.py',
        'mineru_config.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 核心文件检查通过")
    return True

def check_mineru_status():
    """检查MinerU状态"""
    print("🔍 检查MinerU状态...")
    
    try:
        from mineru_integration import MINERU_AVAILABLE
        from mineru_config import get_mineru_config
        
        if MINERU_AVAILABLE:
            print("✅ MinerU可用")
            
            config = get_mineru_config()
            if config.is_enabled():
                print("✅ MinerU已启用")
                return True
            else:
                print("⚠️  MinerU已禁用")
                return True
        else:
            print("⚠️  MinerU不可用")
            return False
            
    except ImportError as e:
        print(f"❌ MinerU导入失败: {str(e)}")
        return False

def install_mineru_if_needed():
    """如果需要，安装MinerU"""
    if not check_mineru_status():
        print("\n🤔 是否要安装MinerU? (推荐)")
        print("MinerU可以提供更好的PDF处理能力")
        choice = input("输入 y/yes 安装，或按回车跳过: ").strip().lower()
        
        if choice in ['y', 'yes']:
            print("🚀 开始安装MinerU...")
            try:
                if os.path.exists('install_mineru.py'):
                    subprocess.run([sys.executable, 'install_mineru.py'], check=True)
                    print("✅ MinerU安装完成")
                else:
                    print("❌ 找不到install_mineru.py，请手动安装")
                    print("运行: pip install magic-pdf[full]")
            except subprocess.CalledProcessError as e:
                print(f"❌ MinerU安装失败: {str(e)}")
                print("可以稍后手动安装")

def open_browser_delayed(url, delay=3):
    """延迟打开浏览器"""
    def open_browser():
        try:
            webbrowser.open(url)
            print(f"🌐 浏览器已打开: {url}")
        except Exception as e:
            print(f"⚠️  无法自动打开浏览器: {str(e)}")
            print(f"请手动访问: {url}")
    
    Timer(delay, open_browser).start()

def start_application():
    """启动应用程序"""
    print("\n🚀 启动 chatbot_newui_new_version_v7...")
    print("=" * 50)
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = os.getcwd()
        
        # 启动Flask应用
        cmd = [sys.executable, 'chatbot_newui_new_version_v7.py']
        
        print("📋 启动信息:")
        print(f"   - Python: {sys.executable}")
        print(f"   - 工作目录: {os.getcwd()}")
        print(f"   - 主程序: chatbot_newui_new_version_v7.py")
        print()
        
        # 延迟打开浏览器
        open_browser_delayed('http://localhost:5000/chat', delay=5)
        open_browser_delayed('http://localhost:5000/mineru', delay=8)
        
        print("🌐 应用将在以下地址启动:")
        print("   - 主界面: http://localhost:5000/chat")
        print("   - MinerU状态: http://localhost:5000/mineru")
        print()
        print("⏳ 正在启动应用...")
        print("=" * 50)
        
        # 启动应用
        process = subprocess.run(cmd, env=env)
        
        return process.returncode == 0
        
    except KeyboardInterrupt:
        print("\n\n⚠️  应用被用户中断")
        return False
    except Exception as e:
        print(f"\n\n❌ 启动失败: {str(e)}")
        return False

def show_help():
    """显示帮助信息"""
    print("""
🚀 chatbot_newui_new_version_v7 启动脚本

版本特性:
✅ MinerU集成 - 实时PDF布局分析
✅ 智能回退机制 - 多层处理方式
✅ 配置管理 - 灵活的参数配置
✅ 处理统计 - 详细的处理记录
✅ API端点 - 状态查询和配置管理

使用方法:
  python start_v7.py          # 启动应用
  python start_v7.py --help   # 显示帮助
  python start_v7.py --check  # 仅检查状态

访问地址:
  http://localhost:5000/chat   # 主聊天界面
  http://localhost:5000/mineru # MinerU状态页面

故障排除:
  1. 如果MinerU不可用，运行: python install_mineru.py
  2. 如果端口被占用，请检查其他Flask应用
  3. 如果依赖缺失，请检查requirements.txt

更多信息请查看: MINERU_INTEGRATION_README.md
""")

def main():
    """主函数"""
    print("🚀 chatbot_newui_new_version_v7 启动器")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h']:
            show_help()
            return
        elif sys.argv[1] in ['--check', '-c']:
            print("🔍 仅检查状态模式")
            check_dependencies()
            check_mineru_status()
            return
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请确保所有必要文件存在")
        return
    
    # 检查MinerU并提供安装选项
    install_mineru_if_needed()
    
    # 启动应用
    success = start_application()
    
    if success:
        print("\n✅ 应用启动成功")
    else:
        print("\n❌ 应用启动失败")
        print("\n🔧 故障排除建议:")
        print("1. 检查端口5000是否被占用")
        print("2. 检查Python依赖是否完整")
        print("3. 查看错误日志获取详细信息")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  启动被用户中断")
    except Exception as e:
        print(f"\n\n❌ 启动过程出现错误: {str(e)}")
        print("请检查错误信息并重试")
