"""

    Module Name :           init_interface
    Last Modified Date :    29 Jan 2024

"""

# Import Open-source Libraries
import os
import pandas as pd

# Import Self-defined Libraries
import env
import preprocessing
from langchain.embeddings import HuggingFaceEmbeddings


# def load_model_uid():
#     file_list = os.listdir(env.)


def load_prompt_template():
    file_list = os.listdir(env.prompt_tempte_dir)
    prompt_template_uid = [t.split(env.prompt_tempte_fmt)[0] for t in file_list if env.prompt_tempte_fmt in t]
    return prompt_template_uid

def load_model_template():
    model_dict  = dict()
    basemodel_list = os.listdir(env.model_dir_root)

    for basemodel in basemodel_list:
        model_uid = os.listdir(os.path.join(env.model_dir_root, basemodel))
        model_dict["basemodel"] = basemodel
        model_dict["list"] = model_uid

    return model_dict, basemodel_list

def load_config():
    # Retrieval of Default Configuration
    config_dir = os.path.join(env.default_dir, env.default_config_dir)
    config = pd.read_json(config_dir, typ="series")
    return config

def load_preprocessing_config(chunk_mode, embedding_mode):
    chunk_mode = 'chunk-1'
    chunk_method = pd.read_json(env.chunk_dir + \
                                chunk_mode + \
                                env.chunk_config_suffix, typ="series")


    embedding_config = pd.read_json(env.embedding_dir + \
                                embedding_mode + \
                                env.embedding_config_suffix, typ="series")
    embedding_method = preprocessing.load_embedding(embedding_config)

    return chunk_method, embedding_method

def load_preprocessing_config_chi(chunk_mode, embedding_mode):
    chunk_mode = 'chunk-1'
    chunk_method = pd.read_json(env.chunk_dir + chunk_mode + env.chunk_config_suffix, typ="series")

    embedding_config = pd.read_json(env.embedding_dir + embedding_mode + env.embedding_config_suffix, typ="series")

    if embedding_config['method'] == 'HUGGINGFACE':
        model_name = embedding_config.get('model',
                                          'sentence-transformers/all-MiniLM-L6-v2')  # Default model if not specified
        print("model_name: ", model_name)
        embedding_method = HuggingFaceEmbeddings(model_name=model_name)
    else:
        # Handle other embedding methods if needed
        raise ValueError(f"Unsupported embedding method: {embedding_config['method']}")

    return chunk_method, embedding_method