"""

    Module Name :           llama_module
    Last Modified Date :    3 Jan 2024

"""
from datetime import datetime

# Import Open-source Libraries
from langchain import HuggingFacePipeline
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema import StrOutputParser
from langchain_community.llms.ollama import Ollama
from langchain_core.runnables import RunnablePassthrough
from langchain_core.messages import HumanMessage
import transformers
from transformers import AutoTokenizer
import torch
from huggingface_hub import hf_hub_download
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain.llms import LlamaCpp
from langchain import PromptTemplate, LLMChain



# Import Self-defined Modules
import preprocessing

# init llama pipeline
def init_llama_piepeline(model_para):
    tokenizer = AutoTokenizer.from_pretrained(model_para["model"])
    pipeline = transformers.pipeline(
        model_para["task"],
        model=model_para["model"],
        tokenizer=tokenizer,
        torch_dtype=eval(model_para["dtype"]),
        trust_remote_code=eval(model_para["trust_remote_code"]),
        device_map=model_para["device_map"],
        max_new_tokens=model_para["max_new_tokens"],
        eos_token_id=tokenizer.eos_token_id,
        repetition_penalty=model_para["repetition_penalty"]
    )
    return pipeline

# llama Module
def llama_response(question,
                   system_prompt,
                   condense_system_prompt,
                   prompt_template,
                   model_para,
                   retriever,
                   chat_history=None):

    print("... Generating AI Response")
    ai_msg_content = ''
    llm = Ollama(model="llama2")

    # Without Vector Store
    if not retriever:

        # Download LLM Model
        # if model_para["path"]:
        #     model_path = hf_hub_download(repo_id=model_para["path"], filename=model_para["model"])
        # callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
        #
        # llm = LlamaCpp(
        #     model_path=model_path,
        #     device_map=model_para["device_map"],
        #     n_ctx=model_para["n_ctx"],
        #     n_gpu_layers=model_para["n_gpu_layers"],
        #     n_batch=model_para["n_batch"],
        #     callback_manager=callback_manager,
        #     max_tokens=model_para["max_tokens"],
        #     n_parts=model_para["n_parts"],
        # )

        # Generate response
        # ai_msg_content = llm(question)
        ai_msg_content = llm.invoke(question)

    # With Vector Store
    else:
        # llm = HuggingFacePipeline(pipeline=pipeline)
        custom_rag_prompt = PromptTemplate.from_template(prompt_template)

        rag_chain = (
                {"context": retriever | preprocessing.format_docs, "question": RunnablePassthrough()}
                | custom_rag_prompt
                | llm
                | StrOutputParser()
        )
        airespone_starttime = datetime.now()
        ai_msg_content = rag_chain.invoke(question)
        airesponse_endtime = datetime.now()
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")
    print('>>> Generated AI Response')

    return ai_msg_content # Return only the content of the AI's response
