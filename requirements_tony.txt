accelerate==0.27.2
aiofiles==24.1.0
aiohttp==3.9.3
aiosignal==1.3.1
annotated-types==0.6.0
antlr4-python3-runtime==4.9.3
anyio==4.3.0
appdirs==1.4.4
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
asgiref==3.8.1
asttokens==2.4.1
async-timeout==4.0.3
attrs==23.2.0
backcall==0.2.0
backoff==2.2.1
beautifulsoup4==4.12.3
bentoml==1.2.20
bleach==6.1.0
blinker==1.7.0
build==1.2.1
cattrs==23.1.2
certifi==2024.2.2
cffi==1.16.0
chardet==5.2.0
charset-normalizer==3.3.2
ci-info==0.3.0
circus==0.18.0
click==8.1.7
click-option-group==0.5.6
cloudpickle==3.0.0
cmake==3.30.0
coloredlogs==15.0.1
configobj==5.0.8
configparser==7.0.0
contourpy==1.2.0
cryptography==42.0.2
cuda-python==12.5.0
cycler==0.12.1
dataclasses-json==0.6.4
dataclasses-json-speakeasy==0.5.11
datasets==2.20.0
decorator==5.1.1
deepmerge==1.1.1
defusedxml==0.7.1
Deprecated==1.2.14
dill==0.3.8
dirtyjson==1.0.8
diskcache==5.6.3
distro==1.9.0
dnspython==2.6.1
docopt==0.6.2
effdet==0.4.1
einops==0.8.0
email_validator==2.2.0
emoji==2.10.1
environs==9.5.0
etelemetry==0.3.1
exceptiongroup==1.2.0
executing==2.0.1
fastapi==0.111.1
fastapi-cli==0.0.4
fastcore==1.5.54
fastjsonschema==2.19.1
filelock==3.13.1
filetype==1.2.0
fitz==0.0.1.dev2
Flask==3.0.2
flatbuffers==23.5.26
fonttools==4.49.0
frontend==0.0.3
frozenlist==1.4.1
fs==2.4.16
fsspec==2024.2.0
future==1.0.0
ghapi==1.0.5
greenlet==3.0.3
grpcio==1.60.0
h11==0.14.0
h2==4.1.0
hpack==4.0.0
httpcore==1.0.4
httplib2==0.22.0
httptools==0.6.1
httpx==0.25.2
httpx-ws==0.6.0
huggingface==0.0.1
huggingface-hub==0.20.3
humanfriendly==10.0
hyperframe==6.0.1
idna==3.6
importlib-metadata==6.11.0
importlib-resources==6.1.1
inflection==0.5.1
InstructorEmbedding==1.0.1
interegular==0.3.3
iopath==0.1.10
ipython==8.12.3
isodate==0.6.1
itsdangerous==2.1.2
jedi==0.19.1
Jinja2==3.1.3
joblib==1.3.2
jsonpatch==1.33
jsonpath-python==1.0.6
jsonpointer==2.4
jsonschema==4.21.1
jsonschema-specifications==2023.12.1
jupyter_client==8.6.1
jupyter_core==5.7.2
jupyterlab_pygments==0.3.0
kiwisolver==1.4.5
langchain==0.1.11
langchain-community==0.0.27
langchain-core==0.1.30
langchain-experimental==0.0.62
langchain-openai==0.0.8
langchain-text-splitters==0.0.1
langdetect==1.0.9
langsmith==0.1.23
lark==1.1.9
layoutparser==0.3.4
llama-cloud==0.0.9
llama-index==0.10.55
llama-index-agent-openai==0.2.8
llama-index-cli==0.1.12
llama-index-core==0.10.55
llama-index-embeddings-huggingface==0.2.2
llama-index-embeddings-openai==0.1.10
llama-index-graph-stores-nebula==0.2.0
llama-index-graph-stores-neo4j==0.2.7
llama-index-indices-managed-llama-cloud==0.2.5
llama-index-legacy==0.9.48
llama-index-llms-llama-cpp==0.1.4
llama-index-llms-ollama==0.1.5
llama-index-llms-openai==0.1.25
llama-index-llms-openllm==0.1.5
llama-index-multi-modal-llms-openai==0.1.7
llama-index-program-openai==0.1.6
llama-index-question-gen-openai==0.1.3
llama-index-readers-file==0.1.30
llama-index-readers-llama-parse==0.1.6
llama-index-vector-stores-milvus==0.1.20
llama-parse==0.4.7
llama_cpp_python==0.2.82
llvmlite==0.43.0
lm-format-enforcer==0.10.3
looseversion==1.3.0
lxml==5.1.0
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.20.2
matplotlib==3.7.2
matplotlib-inline==0.1.6
mdurl==0.1.2
milvus-lite==2.4.8
minijinja==2.0.1
minio==7.2.5
mistune==3.0.2
mpmath==1.3.0
msgpack==1.0.8
multidict==6.0.5
multiprocess==0.70.16
mypy-extensions==1.0.0
nbclient==0.10.0
nbconvert==7.16.2
nbformat==5.10.3
nebula3-python==3.8.0
neo4j==5.22.0
nest-asyncio==1.6.0
networkx==3.2.1
nibabel==5.2.1
ninja==********
nipype==1.8.6
nltk==3.8.1
numba==0.60.0
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-ml-py==11.525.150
nvidia-nccl-cu12==2.19.3
nvidia-nvjitlink-cu12==12.4.99
nvidia-nvtx-cu12==12.1.105
ollama==0.1.7
omegaconf==2.3.0
onnx==1.15.0
onnxruntime==1.15.1
openai==1.13.3
opencv-python==********
openllm==0.5.7
openllm-client==0.5.7
openllm-core==0.5.7
opentelemetry-api==1.20.0
opentelemetry-instrumentation==0.41b0
opentelemetry-instrumentation-aiohttp-client==0.41b0
opentelemetry-instrumentation-asgi==0.41b0
opentelemetry-sdk==1.20.0
opentelemetry-semantic-conventions==0.41b0
opentelemetry-util-http==0.41b0
orjson==3.9.15
outlines==0.0.46
packaging==23.2
pandas==2.2.0
pandocfilters==1.5.1
parso==0.8.3
pathlib==1.0.1
pathspec==0.12.1
pdf2image==1.17.0
pdfminer.six==20221105
pdfplumber==0.10.4
pexpect==4.9.0
pickleshare==0.7.5
pikepdf==8.11.0
pillow==10.2.0
pillow_heif==0.15.0
pip-requirements-parser==32.0.1
pip-tools==7.4.1
pipreqs==0.5.0
platformdirs==4.2.0
portalocker==2.8.2
prometheus-fastapi-instrumentator==7.0.0
prometheus_client==0.20.0
prompt-toolkit==3.0.43
protobuf==4.23.4
prov==2.0.1
psutil==5.9.8
psycopg2-binary==2.9.9
ptyprocess==0.7.0
pure-eval==0.2.2
py-cpuinfo==9.0.0
pyairports==2.1.1
pyarrow==15.0.1
pyarrow-hotfix==0.6
pycocotools==2.0.7
pycountry==24.6.1
pycparser==2.21
pycryptodome==3.20.0
pydantic==2.6.3
pydantic_core==2.16.3
pydot==3.0.1
Pygments==2.17.2
pymilvus==2.3.6
PyMuPDF==1.24.8
PyMuPDFb==1.24.8
pyparsing==3.0.9
pypdf==4.0.1
pypdfium2==4.27.0
pyproject_hooks==1.1.0
pytesseract==0.3.10
python-dateutil==2.8.2
python-dotenv==1.0.1
python-iso639==2024.2.7
python-json-logger==2.0.7
python-magic==0.4.27
python-multipart==0.0.9
pytils==0.4.1
pytz==2024.1
pyxnat==1.6.2
PyYAML==6.0.1
pyzmq==25.1.2
rapidfuzz==3.6.1
ray==2.32.0
rdflib==6.3.2
referencing==0.34.0
regex==2023.12.25
requests==2.31.0
rich==13.7.1
rpds-py==0.18.0
safetensors==0.4.2
schema==0.7.7
scikit-learn==1.4.1.post1
scipy==1.10.1
sentence-transformers==2.2.2
sentencepiece==0.2.0
shellingham==1.5.4
simple-di==0.1.5
simplejson==3.19.2
six==1.16.0
sniffio==1.3.1
soupsieve==2.5
SQLAlchemy==2.0.28
stack-data==0.6.3
starlette==0.37.2
striprtf==0.0.26
sympy==1.12
tabulate==0.9.0
tenacity==8.2.3
threadpoolctl==3.3.0
tiktoken==0.6.0
timm==0.9.12
tinycss2==1.2.1
tokenizers==0.15.2
tomli==2.0.1
tomli_w==1.0.0
tools==0.1.9
torch==2.2.0
torchvision==0.17.0
tornado==6.4
tqdm==4.66.2
traitlets==5.14.2
traits==6.3.2
transformers==4.38.1
triton==2.2.0
typer==0.12.3
typing-inspect==0.9.0
typing_extensions==4.9.0
tzdata==2024.1
ujson==5.9.0
unstructured==0.12.6
unstructured-client==0.18.0
unstructured-inference==0.7.23
unstructured.pytesseract==0.3.12
urllib3==1.26.18
uvicorn==0.30.1
uvloop==0.19.0
vllm==0.5.2
vllm-flash-attn==2.5.9.post1
watchfiles==0.22.0
wcwidth==0.2.13
webencodings==0.5.1
websockets==12.0
Werkzeug==3.0.1
wrapt==1.16.0
wsproto==1.2.0
xformers==0.0.27
xxhash==3.4.1
yarg==0.1.9
yarl==1.9.4
zipp==3.17.0
