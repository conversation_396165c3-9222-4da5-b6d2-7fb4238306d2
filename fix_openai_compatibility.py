#!/usr/bin/env python3
"""
修复 OpenAI 兼容性问题的脚本
这个脚本会安装正确版本的依赖包以解决 OpenAI 客户端初始化错误
"""

import subprocess
import sys

def run_command(command):
    """运行系统命令"""
    print(f"运行命令: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"错误: {result.stderr}")
    else:
        print(f"成功: {result.stdout}")
    return result.returncode == 0

def fix_openai_compatibility():
    """修复 OpenAI 兼容性问题"""
    print("开始修复 OpenAI 兼容性问题...")
    
    # 安装兼容的 OpenAI 版本
    if not run_command("pip install 'openai>=1.0.0,<1.3.0'"):
        print("安装 OpenAI 失败")
        return False
    
    # 安装兼容的 httpx 版本
    if not run_command("pip install 'httpx<0.25.0'"):
        print("安装 httpx 失败")
        return False
    
    print("OpenAI 兼容性问题修复完成！")
    return True

def test_openai_client():
    """测试 OpenAI 客户端是否可以正常初始化"""
    try:
        from openai import OpenAI
        client = OpenAI(api_key='test', base_url='http://localhost:8000/v1')
        print("✅ OpenAI 客户端初始化成功！")
        return True
    except Exception as e:
        print(f"❌ OpenAI 客户端初始化失败: {e}")
        return False

if __name__ == "__main__":
    print("=== OpenAI 兼容性修复工具 ===")
    
    # 首先测试是否已经修复
    if test_openai_client():
        print("OpenAI 客户端已经可以正常工作，无需修复。")
        sys.exit(0)
    
    # 尝试修复
    if fix_openai_compatibility():
        # 再次测试
        if test_openai_client():
            print("🎉 修复成功！您的应用程序现在应该可以正常运行了。")
        else:
            print("❌ 修复失败，请检查错误信息。")
            sys.exit(1)
    else:
        print("❌ 修复过程中出现错误。")
        sys.exit(1) 