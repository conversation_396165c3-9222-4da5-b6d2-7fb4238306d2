#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU集成测试脚本
用于验证MinerU集成是否正常工作
"""

import os
import sys
import json
import time
from typing import List, Tuple, Dict

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from mineru_integration import MinerUProcessor, MINERU_AVAILABLE
        print("✅ mineru_integration 导入成功")
        
        if MINERU_AVAILABLE:
            print("✅ MinerU 可用")
        else:
            print("❌ MinerU 不可用，请先安装")
            return False
            
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    
    try:
        from mineru_config import get_mineru_config
        print("✅ mineru_config 导入成功")
    except ImportError as e:
        print(f"⚠️  mineru_config 导入失败: {str(e)}")
    
    return True

def test_config():
    """测试配置"""
    print("\n🔍 测试配置...")
    
    try:
        from mineru_config import get_mineru_config
        config = get_mineru_config()
        
        print(f"✅ 配置加载成功")
        print(f"   - 启用状态: {config.is_enabled()}")
        print(f"   - 错误回退: {config.should_fallback_on_error()}")
        print(f"   - 保存结果: {config.should_save_results()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {str(e)}")
        return False

def test_processor_init():
    """测试处理器初始化"""
    print("\n🔍 测试处理器初始化...")
    
    try:
        from mineru_integration import MinerUProcessor
        processor = MinerUProcessor()
        print("✅ MinerU处理器初始化成功")
        return processor
        
    except Exception as e:
        print(f"❌ 处理器初始化失败: {str(e)}")
        return None

def create_test_pdf():
    """创建测试PDF文件"""
    print("\n📄 创建测试PDF文件...")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        test_pdf_path = "test_mineru.pdf"
        
        # 创建简单的PDF文件
        c = canvas.Canvas(test_pdf_path, pagesize=letter)
        c.drawString(100, 750, "MinerU Integration Test Document")
        c.drawString(100, 700, "This is a test PDF file for MinerU integration.")
        c.drawString(100, 650, "It contains some sample text for processing.")
        
        # 添加表格样式的文本
        c.drawString(100, 600, "Sample Table:")
        c.drawString(120, 580, "Name    | Age | City")
        c.drawString(120, 560, "--------|-----|--------")
        c.drawString(120, 540, "Alice   | 25  | Beijing")
        c.drawString(120, 520, "Bob     | 30  | Shanghai")
        
        c.save()
        
        print(f"✅ 测试PDF文件创建成功: {test_pdf_path}")
        return test_pdf_path
        
    except ImportError:
        print("⚠️  reportlab未安装，尝试查找现有PDF文件...")
        
        # 查找uploads文件夹中的PDF文件
        uploads_dir = "uploads"
        if os.path.exists(uploads_dir):
            for file in os.listdir(uploads_dir):
                if file.lower().endswith('.pdf'):
                    test_pdf_path = os.path.join(uploads_dir, file)
                    print(f"✅ 找到现有PDF文件: {test_pdf_path}")
                    return test_pdf_path
        
        print("❌ 无法创建或找到测试PDF文件")
        return None
        
    except Exception as e:
        print(f"❌ 创建测试PDF失败: {str(e)}")
        return None

def test_pdf_processing(processor, pdf_path: str):
    """测试PDF处理"""
    print(f"\n🔄 测试PDF处理: {pdf_path}")
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF文件不存在: {pdf_path}")
        return False
    
    try:
        start_time = time.time()
        
        # 处理PDF
        layout_data, markdown_content = processor.process_pdf(pdf_path)
        
        processing_time = time.time() - start_time
        
        if layout_data:
            print(f"✅ PDF处理成功")
            print(f"   - 处理时间: {processing_time:.2f}秒")
            print(f"   - 布局数据类型: {type(layout_data)}")
            
            # 检查布局数据结构
            if isinstance(layout_data, dict):
                print(f"   - 布局数据键: {list(layout_data.keys())}")
                if "pdf_info" in layout_data:
                    print(f"   - 页面数量: {len(layout_data['pdf_info'])}")
            
            return layout_data
        else:
            print("❌ PDF处理失败，返回空结果")
            return None
            
    except Exception as e:
        print(f"❌ PDF处理出错: {str(e)}")
        return None

def test_chunks_conversion(processor, layout_data: Dict, filename: str):
    """测试chunks转换"""
    print(f"\n🔄 测试chunks转换...")
    
    try:
        start_time = time.time()
        
        # 转换为chunks
        chunks_with_metadata = processor.convert_to_chunks(layout_data, filename)
        
        conversion_time = time.time() - start_time
        
        if chunks_with_metadata:
            print(f"✅ chunks转换成功")
            print(f"   - 转换时间: {conversion_time:.2f}秒")
            print(f"   - 生成chunks数量: {len(chunks_with_metadata)}")
            
            # 显示前几个chunks的信息
            for i, (text, metadata) in enumerate(chunks_with_metadata[:3]):
                print(f"   - Chunk {i+1}:")
                print(f"     文本长度: {len(text)}")
                print(f"     内容类型: {metadata.get('content_type', 'unknown')}")
                print(f"     页码: {metadata.get('start_page_num', 'unknown')}")
                print(f"     文本预览: {text[:50]}...")
            
            return chunks_with_metadata
        else:
            print("❌ chunks转换失败，返回空结果")
            return None
            
    except Exception as e:
        print(f"❌ chunks转换出错: {str(e)}")
        return None

def test_integration_in_upload_context():
    """测试在上传上下文中的集成"""
    print(f"\n🔄 测试上传上下文集成...")
    
    try:
        # 模拟上传文件处理流程
        from mineru_integration import MinerUProcessor, MINERU_AVAILABLE
        
        if not MINERU_AVAILABLE:
            print("❌ MinerU不可用，无法测试集成")
            return False
        
        # 查找测试文件
        test_files = []
        uploads_dir = "uploads"
        if os.path.exists(uploads_dir):
            for file in os.listdir(uploads_dir):
                if file.lower().endswith('.pdf'):
                    test_files.append(os.path.join(uploads_dir, file))
        
        if not test_files:
            print("⚠️  uploads文件夹中没有找到PDF文件")
            return False
        
        test_file = test_files[0]
        print(f"使用测试文件: {test_file}")
        
        # 模拟上传处理逻辑
        filename = test_file
        base_name = os.path.splitext(os.path.basename(filename))[0]
        json_path = os.path.join(uploads_dir, base_name + '.json')
        
        chunks_with_metadata = None
        
        # 检查是否存在预计算文件
        if os.path.exists(json_path):
            print(f"⚠️  发现预计算文件: {json_path}")
            print("跳过MinerU处理（模拟真实逻辑）")
        else:
            print("✅ 没有预计算文件，使用MinerU处理")
            
            try:
                mineru_processor = MinerUProcessor()
                layout_data, markdown_content = mineru_processor.process_pdf(filename)
                
                if layout_data:
                    chunks_with_metadata = mineru_processor.convert_to_chunks(layout_data, os.path.basename(filename))
                    
                    if chunks_with_metadata and len(chunks_with_metadata) > 0:
                        print(f"✅ MinerU成功生成 {len(chunks_with_metadata)} 个文本块")
                        
                        # 模拟保存结果
                        try:
                            mineru_json_path = os.path.join(uploads_dir, base_name + '_mineru.json')
                            with open(mineru_json_path, 'w', encoding='utf-8') as f:
                                json.dump(layout_data, f, ensure_ascii=False, indent=2)
                            print(f"💾 MinerU结果已保存到: {mineru_json_path}")
                        except Exception as save_e:
                            print(f"⚠️  保存MinerU结果失败: {str(save_e)}")
                    else:
                        print("⚠️  MinerU处理结果为空")
                else:
                    print("⚠️  MinerU处理失败")
                    
            except Exception as mineru_e:
                print(f"❌ MinerU处理出错: {str(mineru_e)}")
        
        return chunks_with_metadata is not None
        
    except Exception as e:
        print(f"❌ 集成测试出错: {str(e)}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print(f"\n🧹 清理测试文件...")
    
    test_files = [
        "test_mineru.pdf",
        "uploads/test_mineru_mineru.json"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"✅ 删除测试文件: {file_path}")
            except Exception as e:
                print(f"⚠️  删除文件失败: {file_path} - {str(e)}")

def main():
    """主测试流程"""
    print("🚀 MinerU集成测试")
    print("=" * 50)
    
    # 测试结果统计
    tests_passed = 0
    total_tests = 0
    
    # 1. 测试导入
    total_tests += 1
    if test_imports():
        tests_passed += 1
    
    # 2. 测试配置
    total_tests += 1
    if test_config():
        tests_passed += 1
    
    # 3. 测试处理器初始化
    total_tests += 1
    processor = test_processor_init()
    if processor:
        tests_passed += 1
    
    if processor:
        # 4. 创建/查找测试PDF
        pdf_path = create_test_pdf()
        
        if pdf_path:
            # 5. 测试PDF处理
            total_tests += 1
            layout_data = test_pdf_processing(processor, pdf_path)
            if layout_data:
                tests_passed += 1
                
                # 6. 测试chunks转换
                total_tests += 1
                chunks = test_chunks_conversion(processor, layout_data, os.path.basename(pdf_path))
                if chunks:
                    tests_passed += 1
        
        # 7. 测试集成上下文
        total_tests += 1
        if test_integration_in_upload_context():
            tests_passed += 1
    
    # 清理测试文件
    cleanup_test_files()
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print(f"通过测试: {tests_passed}/{total_tests}")
    print(f"成功率: {(tests_passed/total_tests)*100:.1f}%")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！MinerU集成正常工作")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和安装")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程出现未预期的错误: {str(e)}")
        sys.exit(1)
