"""

    Module Name :           mixtral_module
    Last Modified Date :    3 Jan 2024

"""

# Import Open-source Libraries
from langchain import HuggingFacePipeline
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain import PromptTemplate, LLMChain
import transformers

# Import Self-defined Modules
import preprocessing

# init mixtral pipeline
def init_mixtral_piepeline(model_para):
    pipeline = transformers.pipeline(
        model_para["task"],
        model=model_para["model"],
        device_map=model_para["device_map"],
        model_kwargs={"torch_dtype": eval(model_para["torch_dtype"])},
        max_length=model_para["max_length"],
        max_new_tokens=model_para["max_new_tokens"],
        repetition_penalty=model_para["repetition_penalty"],
        return_full_text=eval(model_para["return_full_text"])
    )
    return pipeline


# mixtral Module
def mixtral_response(question,
                     system_prompt,
                     condense_system_prompt,
                     prompt_template,
                     model_para,
                     pipeline,
                     retriever,
                     chat_history=None):

    print("... Generating AI Response")
    ai_msg_content = ''

    # Without Vector Store
    if not retriever:
        messages = [{"role": "user", "content": question}]
        prompt = pipeline.tokenizer.apply_chat_template(
            messages, 
            tokenize=False, 
            add_generation_prompt=True
        )

        outputs = pipeline(
            prompt, 
            max_new_tokens=10000,
            do_sample=True, 
            temperature=0.7, 
            top_k=50, 
            top_p=0.95)
        
        ai_msg_content = outputs[0]["generated_text"].replace('<s>[INST] who is Alice? [/INST]', '')

    # With Vector Store
    else:
        llm = HuggingFacePipeline(pipeline=pipeline)
        custom_rag_prompt = PromptTemplate.from_template(prompt_template)

        rag_chain = (
                {"context": retriever | preprocessing.format_docs, "question": RunnablePassthrough()}
                | custom_rag_prompt
                | llm
                | StrOutputParser()
        )
        ai_msg_content = rag_chain.invoke(question)

    print('>>> Generated AI Response')

    return ai_msg_content # Return only the content of the AI's response
