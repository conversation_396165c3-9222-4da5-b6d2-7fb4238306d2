#FROM ubuntu:latest
#LABEL authors="oscar"
#
#RUN apt-get update && apt-get install -y python3 python3-pip
#
#WORKDIR /dtt-llm-rag
#
#COPY . /dtt-llm-rag
#
#RUN pip3 install -r requirements.txt
#
#EXPOSE 8000
#
#ENTRYPOINT ["top", "-b"]
#
#CMD ["python3", "chatbot.py"]

FROM python:3.10
RUN python3 -m pip install --upgrade pip

RUN apt-get update && apt-get install ffmpeg libsm6 libxext6  -y

WORKDIR /
COPY requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
EXPOSE 8009
CMD ["python", "chatbot.py"]
