#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU安装脚本
用于自动安装和配置MinerU依赖
"""

import subprocess
import sys
import os
import j<PERSON>
from typing import List, <PERSON><PERSON>

def run_command(command: str) -> Tuple[bool, str]:
    """
    执行命令并返回结果
    
    Args:
        command: 要执行的命令
        
    Returns:
        Tuple[bool, str]: (是否成功, 输出信息)
    """
    try:
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, f"Error: {e.stderr}"
    except Exception as e:
        return False, f"Exception: {str(e)}"

def check_python_version() -> bool:
    """检查Python版本是否满足要求"""
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("MinerU需要Python 3.8或更高版本")
        return False

def check_pip() -> bool:
    """检查pip是否可用"""
    success, output = run_command("pip --version")
    if success:
        print(f"✅ pip可用: {output.strip()}")
        return True
    else:
        print(f"❌ pip不可用: {output}")
        return False

def install_package(package: str) -> bool:
    """
    安装Python包
    
    Args:
        package: 包名
        
    Returns:
        bool: 是否安装成功
    """
    print(f"🔄 正在安装 {package}...")
    success, output = run_command(f"pip install {package}")
    
    if success:
        print(f"✅ {package} 安装成功")
        return True
    else:
        print(f"❌ {package} 安装失败: {output}")
        return False

def install_mineru_basic() -> bool:
    """安装MinerU基础版本"""
    packages = [
        "magic-pdf[full]",
        "torch",
        "torchvision",
        "transformers",
        "pillow",
        "opencv-python",
        "numpy"
    ]
    
    print("📦 开始安装MinerU基础依赖...")
    
    for package in packages:
        if not install_package(package):
            return False
    
    return True

def install_mineru_gpu() -> bool:
    """安装MinerU GPU版本"""
    print("🚀 安装GPU支持...")
    
    # 检查CUDA是否可用
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ 检测到CUDA: {torch.version.cuda}")
            gpu_packages = [
                "torch[cuda]",
                "torchvision[cuda]"
            ]
            
            for package in gpu_packages:
                if not install_package(package):
                    print("⚠️  GPU版本安装失败，将使用CPU版本")
                    return False
            
            print("✅ GPU支持安装成功")
            return True
        else:
            print("⚠️  未检测到CUDA，将使用CPU版本")
            return False
    except ImportError:
        print("⚠️  无法检测CUDA，将使用CPU版本")
        return False

def verify_installation() -> bool:
    """验证MinerU安装是否成功"""
    print("🔍 验证MinerU安装...")
    
    try:
        # 尝试导入MinerU
        from magic_pdf.api.magic_pdf_parse import magic_pdf_parse
        print("✅ MinerU核心模块导入成功")
        
        # 检查模型文件
        print("📋 检查模型配置...")
        
        return True
        
    except ImportError as e:
        print(f"❌ MinerU导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 验证过程出错: {str(e)}")
        return False

def create_config_files():
    """创建配置文件"""
    print("📝 创建配置文件...")
    
    # 创建基础配置
    config = {
        "enabled": True,
        "parse_mode": "auto",
        "lang": "auto",
        "layout_config": {
            "model": "doclayout_yolo",
            "confidence_threshold": 0.5
        },
        "formula_config": {
            "enable": True,
            "mfd_model": "yolo_v8_mfd",
            "mfr_model": "unimernet_small"
        },
        "table_config": {
            "enable": True,
            "model": "rapid_table"
        }
    }
    
    try:
        with open("mineru_user_config.json", 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print("✅ 配置文件创建成功: mineru_user_config.json")
    except Exception as e:
        print(f"⚠️  配置文件创建失败: {str(e)}")

def main():
    """主安装流程"""
    print("🚀 MinerU安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查pip
    if not check_pip():
        return False
    
    # 询问安装类型
    print("\n请选择安装类型:")
    print("1. 基础版本 (CPU)")
    print("2. 完整版本 (GPU + CPU)")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        # 安装基础版本
        if not install_mineru_basic():
            print("❌ 基础版本安装失败")
            return False
    elif choice == "2":
        # 安装完整版本
        if not install_mineru_basic():
            print("❌ 基础版本安装失败")
            return False
        
        install_mineru_gpu()  # GPU安装失败不影响整体
    else:
        print("❌ 无效选择")
        return False
    
    # 验证安装
    if not verify_installation():
        print("❌ 安装验证失败")
        return False
    
    # 创建配置文件
    create_config_files()
    
    print("\n🎉 MinerU安装完成!")
    print("=" * 50)
    print("📋 安装总结:")
    print("✅ MinerU核心模块已安装")
    print("✅ 配置文件已创建")
    print("\n📖 使用说明:")
    print("1. 重启你的应用程序")
    print("2. 上传PDF文件时将自动使用MinerU处理")
    print("3. 可以编辑 mineru_user_config.json 来调整设置")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️  安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 安装过程出现未预期的错误: {str(e)}")
        sys.exit(1)
