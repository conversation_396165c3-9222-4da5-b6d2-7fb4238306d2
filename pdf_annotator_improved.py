import fitz  # PyMuPDF
import os
from typing import List, Dict, Any

def annotate_pdf_with_colored_boxes(
    input_pdf_path: str,
    output_pdf_path: str,
    chunks_info: List[Dict[str, Any]]
) -> None:
    """
    在PDF上用彩色矩形框标注不同类型的内容区域
    
    Args:
        input_pdf_path: 输入PDF文件路径
        output_pdf_path: 输出PDF文件路径
        chunks_info: 包含bbox、page_num、content_type等信息的列表
    """
    
    # 定义颜色映射 (RGB)
    color_mapping = {
        'text_with_title': (0, 0.8, 0),     # 绿色
        'text': (0, 0.8, 0),                # 绿色
        'table': (0, 0, 1),                 # 蓝色
        'image': (1, 0, 0)                  # 红色
    }
    
    # 打开PDF文件
    pdf_document = fitz.open(input_pdf_path)
    
    print(f"PDF共有 {pdf_document.page_count} 页")
    print(f"将处理 {len(chunks_info)} 个区域")
    
    # 按页面分组chunks
    chunks_by_page = {}
    for chunk in chunks_info:
        page_num = chunk['page_num']
        if page_num not in chunks_by_page:
            chunks_by_page[page_num] = []
        chunks_by_page[page_num].append(chunk)
    
    # 处理每一页
    for page_num, page_chunks in chunks_by_page.items():
        # 注意：fitz使用0基索引，而我们的数据使用1基索引
        page_index = page_num - 1
        
        if page_index >= pdf_document.page_count:
            print(f"警告：页面 {page_num} 超出PDF范围，跳过")
            continue
            
        page = pdf_document[page_index]
        print(f"处理第 {page_num} 页，共 {len(page_chunks)} 个区域")
        
        # 在该页面上添加所有标注
        for i, chunk in enumerate(page_chunks):
            bbox = chunk['bbox']
            content_type = chunk['content_type']
            
            # 获取对应的颜色
            color = color_mapping.get(content_type, (0.5, 0.5, 0.5))  # 默认灰色
            
            # 创建矩形区域
            rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
            
            # 添加彩色矩形边框
            shape = page.new_shape()
            shape.draw_rect(rect)
            shape.finish(color=color, width=3)  # 3像素宽的边框
            shape.commit()
            
            # 添加半透明填充矩形
            shape2 = page.new_shape()
            shape2.draw_rect(rect)
            shape2.finish(fill=color, fill_opacity=0.1)  # 10%透明度填充
            shape2.commit()
            
            # 添加标签文本
            label_text = content_type.replace('_', ' ').title()
            label_color = color
            
            # 计算标签位置
            label_x = bbox[0]
            label_y = bbox[1] - 5
            
            # 确保标签不会超出页面边界
            if label_y < 15:
                label_y = bbox[1] + 15
            
            # 添加标签背景
            text_width = len(label_text) * 6  # 估算文本宽度
            label_bg_rect = fitz.Rect(label_x, label_y - 12, label_x + text_width, label_y + 2)
            shape3 = page.new_shape()
            shape3.draw_rect(label_bg_rect)
            shape3.finish(fill=(1, 1, 1), fill_opacity=0.8)  # 白色背景
            shape3.commit()
            
            # 添加标签文本
            text_point = fitz.Point(label_x + 2, label_y - 2)
            page.insert_text(text_point, label_text, fontsize=10, color=label_color)
            
            print(f"  - 添加 {content_type} 标注: {bbox}")
    
    # 保存标注后的PDF
    pdf_document.save(output_pdf_path)
    pdf_document.close()
    
    print(f"标注完成！新PDF已保存为: {output_pdf_path}")

def create_color_legend_page(pdf_document):
    """在PDF第一页添加颜色图例"""
    
    # 插入新页面作为图例页
    legend_page = pdf_document.new_page(width=595, height=842)  # A4尺寸
    
    # 标题
    title_point = fitz.Point(50, 50)
    legend_page.insert_text(title_point, "内容类型颜色图例", fontsize=16, color=(0, 0, 0))
    
    # 图例项目
    legend_items = [
        ("text_with_title", "标题文本", (0, 0.8, 0)),
        ("text", "普通文本", (0, 0.8, 0)),
        ("table", "表格", (0, 0, 1)),
        ("image", "图片", (1, 0, 0))
    ]
    
    y_start = 80
    for i, (type_name, chinese_name, color) in enumerate(legend_items):
        y_pos = y_start + i * 30
        
        # 绘制颜色方块
        color_rect = fitz.Rect(50, y_pos, 80, y_pos + 20)
        shape = legend_page.new_shape()
        shape.draw_rect(color_rect)
        shape.finish(color=color, width=2, fill=color, fill_opacity=0.3)
        shape.commit()
        
        # 添加说明文字
        text_point = fitz.Point(90, y_pos + 15)
        legend_page.insert_text(text_point, f"{chinese_name} ({type_name})", fontsize=12, color=(0, 0, 0))

def main():
    """主函数"""
    
    # 输入和输出文件路径
    input_pdf = "uploads/amber_place.pdf"
    output_pdf = "uploads/amber_place_boxed.pdf"
    
    # 您提供的chunks_info数据
    chunks_info = [
        {'bbox': [64.0, 486.0, 408.0, 630.0], 'page_num': 3, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
        {'bbox': [64.0, 666.0, 408.0, 842.0], 'page_num': 3, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
        {'bbox': [450.0, 358.0, 791.0, 430.0], 'page_num': 3, 'content_type': 'text', 'source': 'amberplacepdf'},
        {'bbox': [449.0, 571.0, 791.0, 618.0], 'page_num': 3, 'content_type': 'text', 'source': 'amberplacepdf'},
        {'bbox': [455.0, 651.0, 791.0, 723.0], 'page_num': 3, 'content_type': 'text', 'source': 'amberplacepdf'},
        {'bbox': [45.0, 129.0, 689.0, 172.0], 'page_num': 4, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
        {'bbox': [449.0, 298.0, 791.0, 334.0], 'page_num': 5, 'content_type': 'text', 'source': 'amberplacepdf'},
        {'bbox': [44.0, 129.0, 689.0, 172.0], 'page_num': 6, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
        {'bbox': [63.0, 556.0, 408.0, 673.0], 'page_num': 6, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
        {'bbox': [64.0, 491.0, 407.0, 568.0], 'page_num': 7, 'content_type': 'text_with_title', 'source': 'amberplacepdf'}
    ]
    
    # 检查输入文件是否存在
    if not os.path.exists(input_pdf):
        print(f"错误：找不到输入文件 {input_pdf}")
        return
    
    print("开始处理PDF矩形框标注...")
    print(f"输入文件: {input_pdf}")
    print(f"输出文件: {output_pdf}")
    print("\n颜色说明:")
    print("- text_with_title 和 text: 绿色边框")
    print("- table: 蓝色边框")
    print("- image: 红色边框")
    print()
    
    try:
        # 执行标注
        annotate_pdf_with_colored_boxes(input_pdf, output_pdf, chunks_info)
        
        # 添加图例页面
        print("添加颜色图例页面...")
        pdf_with_legend = fitz.open(output_pdf)
        create_color_legend_page(pdf_with_legend)
        
        # 重新保存包含图例的PDF
        final_output = "uploads/amber_place_final.pdf"
        pdf_with_legend.save(final_output)
        pdf_with_legend.close()
        
        print(f"\n✅ 标注完成！")
        print(f"原PDF: {input_pdf}")
        print(f"矩形框标注PDF: {output_pdf}")
        print(f"包含图例的最终PDF: {final_output}")
        
        # 显示统计信息
        print(f"\n📊 标注统计:")
        type_count = {}
        for chunk in chunks_info:
            content_type = chunk['content_type']
            type_count[content_type] = type_count.get(content_type, 0) + 1
        
        for content_type, count in type_count.items():
            print(f"- {content_type}: {count} 个区域")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 