#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复结果
验证MINERU_AVAILABLE是否正确设置
"""

def test_mineru_available():
    """测试MINERU_AVAILABLE设置"""
    print("🔍 测试MINERU_AVAILABLE设置")
    print("=" * 40)
    
    # 模拟v7的导入逻辑
    print("🔍 [TEST] 开始加载MinerU集成模块...")

    try:
        from mineru_integration import MinerUProcessor, MINERU_AVAILABLE
        from mineru_config import get_mineru_config
        print("✅ [TEST] MinerU直接集成模块加载成功")
        print(f"🔍 [TEST] MINERU_AVAILABLE = {MINERU_AVAILABLE}")
        
        # 初始化MinerU配置
        mineru_config = get_mineru_config()
        print(f"🔍 [TEST] MinerU配置加载: {mineru_config is not None}")
        if mineru_config and mineru_config.is_enabled():
            print("✅ [TEST] MinerU已启用")
        else:
            print("⚠️  [TEST] MinerU已禁用或配置未加载")
            
    except ImportError as e:
        # 初始设置为False，但可能会被虚拟环境版本覆盖
        MINERU_AVAILABLE = False
        mineru_config = None
        print(f"⚠️  [TEST] MinerU直接集成不可用: {str(e)}")
        print("🔍 [TEST] 尝试加载虚拟环境版本...")
        
        # 尝试加载虚拟环境版本
        try:
            from mineru_venv_integration import MinerUVenvProcessor, MINERU_VENV_AVAILABLE
            print(f"✅ [TEST] 虚拟环境集成模块加载成功")
            print(f"🔍 [TEST] MINERU_VENV_AVAILABLE = {MINERU_VENV_AVAILABLE}")
            
            if MINERU_VENV_AVAILABLE:
                print("✅ [TEST] 虚拟环境MinerU可用，设置为主要处理器")
                MINERU_AVAILABLE = True  # 🔧 关键修复：设置为True
                mineru_config = None  # 虚拟环境版本使用默认配置
                print(f"🔧 [TEST] 已设置 MINERU_AVAILABLE = {MINERU_AVAILABLE}")
            else:
                print("❌ [TEST] 虚拟环境MinerU不可用")
        except ImportError as venv_e:
            print(f"❌ [TEST] 虚拟环境MinerU模块导入失败: {str(venv_e)}")
        
        if not MINERU_AVAILABLE:
            print("💡 [TEST] 所有MinerU选项都不可用")

    print(f"🎯 [TEST] 最终MinerU状态: MINERU_AVAILABLE = {MINERU_AVAILABLE}")
    print(f"🎯 [TEST] 配置状态: mineru_config = {mineru_config is not None}")
    
    return MINERU_AVAILABLE

def test_processing_conditions():
    """测试处理条件"""
    print("\n🔍 测试处理条件")
    print("=" * 40)
    
    # 获取MINERU_AVAILABLE状态
    MINERU_AVAILABLE = test_mineru_available()
    
    # 模拟处理条件检查
    filename = "test.pdf"
    json_path = "test.json"  # 不存在的文件
    
    print(f"\n🔍 [TEST] 模拟处理条件检查...")
    print(f"🔍 [TEST] 文件名: {filename}")
    print(f"🔍 [TEST] 是PDF文件: {filename.lower().endswith('.pdf')}")
    print(f"🔍 [TEST] MINERU_AVAILABLE: {MINERU_AVAILABLE}")
    print(f"🔍 [TEST] 预计算文件存在: False")  # 模拟不存在
    
    # 修改后的条件检查
    should_use_mineru = (
        filename.lower().endswith(".pdf") and 
        MINERU_AVAILABLE and 
        not False and  # 模拟预计算文件不存在
        True  # 模拟配置启用或无配置
    )
    
    print(f"🔍 [TEST] 是否使用MinerU: {should_use_mineru}")
    
    if should_use_mineru:
        print("✅ [TEST] 将使用MinerU处理PDF")
        return True
    else:
        print("❌ [TEST] 将使用原始处理方式")
        return False

def main():
    """主测试函数"""
    print("🧪 修复验证测试")
    print("=" * 50)
    
    # 测试处理条件
    will_use_mineru = test_processing_conditions()
    
    print(f"\n📊 测试结果")
    print("=" * 50)
    
    if will_use_mineru:
        print("🎉 修复成功! MinerU将被正确使用")
        print("💡 现在可以运行: python chatbot_newui_new_version_v7.py")
        print("📋 上传PDF文件时应该会看到:")
        print("   🔄 [DEBUG] 开始使用MinerU实时处理PDF")
    else:
        print("❌ 修复失败，仍然会使用原始处理方式")
        print("💡 需要进一步调试")
    
    return will_use_mineru

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 测试通过!")
        else:
            print("\n❌ 测试失败!")
    except Exception as e:
        print(f"\n❌ 测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
