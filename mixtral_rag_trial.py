import os
from datetime import datetime

import torch
import transformers
from transformers import (
  AutoTokenizer,
  AutoModelForCausalLM,
  BitsAndBytesConfig,
  pipeline
)

model_name='mistralai/Mixtral-8x7B-Instruct-v0.1'

# model_config = transformers.AutoConfig.from_pretrained(
#    model_name,
# )

tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
tokenizer.pad_token = tokenizer.eos_token
tokenizer.padding_side = "right"

#################################################################
# bitsandbytes parameters
#################################################################

# Activate 4-bit precision base model loading
use_4bit = True

# Compute dtype for 4-bit base models
bnb_4bit_compute_dtype = "float16"

# Quantization type (fp4 or nf4)
bnb_4bit_quant_type = "nf4"

# Activate nested quantization for 4-bit base models (double quantization)
use_nested_quant = False

#################################################################
# Set up quantization config
#################################################################
compute_dtype = getattr(torch, bnb_4bit_compute_dtype)

bnb_config = BitsAndBytesConfig(
   load_in_4bit=use_4bit,
   bnb_4bit_quant_type=bnb_4bit_quant_type,
   bnb_4bit_compute_dtype=compute_dtype,
   bnb_4bit_use_double_quant=use_nested_quant,
)

# Check GPU compatibility with bfloat16
if compute_dtype == torch.float16 and use_4bit:
   major, _ = torch.cuda.get_device_capability()
   if major >= 8:
       print("=" * 80)
       print("Your GPU supports bfloat16: accelerate training with bf16=True")
       print("=" * 80)


#################################################################
# Load pre-trained config
#################################################################
model = AutoModelForCausalLM.from_pretrained(
   model_name,
   quantization_config=bnb_config,
   device_map="auto"
)

from langchain.text_splitter import CharacterTextSplitter
from langchain.document_loaders import AsyncChromiumLoader
from langchain.document_transformers import Html2TextTransformer
from langchain.vectorstores import FAISS
from langchain.embeddings.huggingface import HuggingFaceEmbeddings
import nest_asyncio

nest_asyncio.apply()

articles = ["https://indianexpress.com/article/sports/cricket/india-vs-south-africa-playing-xi-tip-off-rajat-patidar-shreyas-iyer-sai-sudharsan-9073113/",
           "https://indianexpress.com/article/sports/cricket/ipl-auction-2024-live-streaming-when-and-where-to-watch-in-india-9073091/",
           "https://indianexpress.com/article/sports/cricket/india-vs-south-africa-odi-live-streaming-2nd-odi-9072839/",
           "https://indianexpress.com/article/sports/cricket/why-australian-media-outraged-ricky-ponting-trevor-baylisss-visit-ipl-auction-9072573/",
           "https://indianexpress.com/article/sports/cricket/arshdeep-singh-on-his-player-of-the-match-vs-south-africa-9072530/",

"https://indianexpress.com/article/sports/cricket/australia-vs-pakistan-pat-cummins-wants-nathan-lyon-to-beat-shane-warnes-record-9072426/",
           "https://indianexpress.com/article/sports/cricket/australia-vs-pakistan-ian-healy-backs-david-warner-to-play-one-more-year-of-test-cricket-9072374/",
           "https://indianexpress.com/article/sports/cricket/ind-vs-sa-first-odi-shaun-pollock-unhappy-south-africa-bowlers-vs-india-9071857/",
           "https://indianexpress.com/article/sports/cricket/nathan-lyon-500-test-wickets-journey-self-doubts-emerging-from-shane-warnes-shadow-9072047/",
           "https://indianexpress.com/article/sports/cricket/india-versus-south-africa-1st-odi-arshdeep-singh-shows-signs-of-cracking-code-9072008/",
]

# # Scrapes the blogs above
# loader = AsyncChromiumLoader(articles)
# docs = loader.load()
#
# # Converts HTML to plain text
# html2text = Html2TextTransformer()
# docs_transformed = html2text.transform_documents(docs)
#
# # Chunk text
# text_splitter = CharacterTextSplitter(chunk_size=100,
#                                      chunk_overlap=0)
# chunked_documents = text_splitter.split_documents(docs_transformed)
#
# # Load chunked documents into the FAISS index
# db = FAISS.from_documents(chunked_documents,
#                          HuggingFaceEmbeddings(model_name='sentence-transformers/all-mpnet-base-v2'))

import vectorstore_module_lite
import env
import pandas as pd
import init_interface
search_method = 'search-1'
search_config = pd.read_json(env.search_config_dir + \
                             search_method + \
                             env.search_config_suffix, typ="series")
chunk_mode = 'chunk-1'
embedding_mode = 'embedding-1'
chunk_method, embedding_method = init_interface.load_preprocessing_config(chunk_mode, embedding_mode)


vs_config = {"host": "localhost",
             "port": 19530}

retriever = vectorstore_module.get_retriever(vs_config,
                                             embedding_method,
                                             "dataset_hkstp",
                                             search_config)


                                             # Connect query to FAISS index using a retriever
# retriever = db.as_retriever(
#    search_type="similarity",
#    search_kwargs={'k': 4}
# )

query = "Talk about IPL?"
# docs = db.similarity_search(query)
# print(docs[0].page_content)

from langchain.llms import HuggingFacePipeline
from langchain.prompts import PromptTemplate
from langchain.embeddings.huggingface import HuggingFaceEmbeddings
from langchain.chains import LLMChain

text_generation_pipeline = transformers.pipeline(
    model=model,
    tokenizer=tokenizer,
    task="text-generation",
    repetition_penalty=1.1,
    return_full_text=True,
    max_new_tokens=500,
    pad_token_id=2,
    eos_token_id=2

)

prompt_template = """
### [INST]
Instruction: Answer the question based on your
cricket knowledge. Here is context to help:

{context}

### QUESTION:
{question}

[/INST]
"""

mixtral_llm = HuggingFacePipeline(pipeline=text_generation_pipeline)

# Create prompt from prompt template
prompt = PromptTemplate(
   input_variables=["context", "question"],
   template=prompt_template,
)

# Create llm chain
llm_chain = LLMChain(llm=mixtral_llm, prompt=prompt)
# airespone_starttime = datetime.now()
# response = llm_chain.invoke({"context":"",
#                  "question": "Who is Shreyas Iyer and what is his role in the IPL?"})
# print(response)
# airesponse_endtime = datetime.now()
# print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")

from langchain_core.runnables import RunnablePassthrough
query= "Please help generate an English Summary with the following sections The summary shall contain the sections of Company Background, Management Team Members, Key Customers, Key products or services or solutions, Annual Financial Summary, Fundraising History, and Recent News and Activities. Present the summary section-by-section."
# retriever = db.as_retriever(
#    search_type="similarity",
#    search_kwargs={'k': 20}
# )


rag_chain = (
{"context": retriever, "question": RunnablePassthrough()}
   | llm_chain
)
airespone_starttime = datetime.now()
response = rag_chain.invoke(query)
print(response)
airesponse_endtime = datetime.now()
print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")
