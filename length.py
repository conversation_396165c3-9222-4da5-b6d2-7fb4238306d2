import os
from PIL import Image

def get_image_dimensions(folder_path):
    """
    读取文件夹内所有图像的长宽,并计算平均值。
    """
    total_width = 0
    total_height = 0
    num_images = 0

    for filename in os.listdir(folder_path):
        if filename.endswith(".jpg") or filename.endswith(".png") or filename.endswith(".gif"):
            image_path = os.path.join(folder_path, filename)
            image = Image.open(image_path)
            width, height = image.size
            total_width += width
            total_height += height
            num_images += 1

    if num_images > 0:
        average_width = total_width / num_images
        average_height = total_height / num_images
        return average_width, average_height
    else:
        return None, None

# 使用示例
folder_path = "image/CIM3_BCIM3 Product Manual (2023-05-17) PIL"
average_width, average_height = get_image_dimensions(folder_path)

if average_width is not None and average_height is not None:
    print(f"Average image width: {average_width:.2f} pixels")
    print(f"Average image height: {average_height:.2f} pixels")
else:
    print("No images found in the specified folder.")