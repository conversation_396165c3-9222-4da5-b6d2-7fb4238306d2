"""

    Module Name :           mixtral_module
    Last Modified Date :    3 Jan 2024

"""
import subprocess
from typing import Any

import chinese_converter
# Import Open-source Libraries
from langchain import HuggingFacePipeline, hub
from langchain.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain.schema import StrOutputParser
from langchain_community.chat_models import ChatOpenAI
from langchain_community.llms.llamafile import Llamafile
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableParallel
from langchain_core.messages import HumanMessage
import transformers
from transformers import AutoTokenizer
import torch
from huggingface_hub import hf_hub_download
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain.llms import LlamaCpp
from langchain import PromptTemplate, LLMChain
from datetime import datetime

# Import Self-defined Modules
import preprocessing
from langchain_community.llms import Ollama
from langchain_community.chat_models import ChatOllama
from ollama import Client
# import tools.gpu_detect
import langchain
# langchain.debug = True
# init mixtral pipeline
def init_mixtral_piepeline(model_para):
    pipeline = transformers.pipeline(
        model_para["task"],
        model=model_para["model"],
        device_map=model_para["device_map"],
        model_kwargs={"torch_dtype": eval(model_para["torch_dtype"])},
        max_new_tokens=model_para["max_new_tokens"],
        repetition_penalty=model_para["repetition_penalty"],
        return_full_text=eval(model_para["return_full_text"]),
        pad_token_id=2,
        eos_token_id=2
    )
    return pipeline


# mixtral Module
def mixtral_response(
                    question,
                    system_prompt,
                    condense_system_prompt,
                    prompt_template,
                    model_para,
                    retriever,
                    chat_history=None):
    
    print("... Generating AI Response")

    ai_msg_content = ''

    aimodel_starttime = datetime.now()
    # llm = Ollama(model="mistral")
    # llm = Ollama(model="codellama:70b-code-q5_K_M")
    from langchain.callbacks.base import BaseCallbackHandler

    class StreamingCallbackHandler(BaseCallbackHandler):
        def __init__(self):
            self.partial_output = ""

        def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
            self.partial_output += token
            print(token, end="", flush=True)

    # llm = Ollama(base_url="http://*************:11434", model="mistral")
    # llm = Ollama(base_url="http://*************:11434", model="qwen2:72b-instruct")
    # llm = Ollama(base_url="http://*************:11434", model="mistral")
    # llm = Ollama(base_url="http://*************:30211", model="mistral-nemo", num_ctx=10000, keep_alive = '-1s')
    llm = Ollama(base_url="http://*************:30299", model="qwen2:72b", num_ctx=10000, keep_alive = '-1s')
    # llm = Ollama(base_url="http://*************:11434", model="qwen2:72b")
    # llm = Ollama(base_url="http://*************:30222", model="mistral")
    # llm = Ollama(base_url="http://*************:30222", model="mistral", num_ctx=100000)
    # llm = Ollama(base_url="http://*************:30222", model="llama3.1:8b-instruct-fp16", num_ctx=100000)


    # from langchain_community.llms import VLLM
    #
    # llm = VLLM(
    #     model="mistral",
    #     tensor_parallel_size=8,
    #     trust_remote_code=True,  # mandatory for hf models
    #     max_new_tokens=512,
    #     top_k=10,
    #     top_p=0.95,
    #     temperature=0.8,
    # )
    # llm = Ollama(model="mixtral")
    # llm = Ollama(base_url="http://*************:11434", model="mistral", callbacks=[StreamingCallbackHandler()])
    # llm = Ollama(base_url="http://*************:11434", model="mixtral:8x7b-instruct-v0.1-q6_K")
    # llm = Ollama(base_url="http://*************:11434", model="mistral:7b-instruct-v0.2-fp16")
    # llm = Ollama(base_url="http://*************:11434", model="mixtral:8x7b-instruct-v0.1-fp16")
    # llm = Ollama(base_url="http://*************:11434", model="codellama:34b-instruct-q5_K_M")


    # llm = Ollama(model="mistral")
    # llm = Ollama(model="llama2:latest")
    # llm = Ollama(model="mixtral:8x7b-instruct-v0.1-fp16")
    from langchain_community.llms import llamafile
    # llm =Llamafile()
    aimodel_endtime = datetime.now()
    load_time = aimodel_endtime - aimodel_starttime
    print(f"AI Model Loading Time = {load_time}")

    # llm = ChatOllama(model="mixtral:8x7b")
    # Without Vector Store
    if not retriever:
        # Initialize chat_history as an empty list if it's None
        if chat_history is None:
            chat_history = []
        # airespone_starttime = datetime.now()
        #
        # ai_msg_content = llm.invoke(question)
        # airesponse_endtime = datetime.now()
        # inference_time = airesponse_endtime - airespone_starttime
        # # print("ai_msg_content: ", ai_msg_content)
        # # num_tokens = len(outputs[0].detach().cpu().numpy().flatten())
        # # num_tokens = len(ai_msg_content.numpy().flatten())
        # # token_per_sec = num_tokens / inference_time
        #
        # # print(f"Token per sec: {token_per_sec}")
        # print(f"AI Response Time = {inference_time}")
    #     prompt = [
    # "Answer the Question at the end.",
    # "\nQuestion: {question}"
  # ]
  #
        # for local call
  #       ai_msg_content = llm.generate(prompts=prompt, num_predict=-1)
  #       print(ai_msg_content)
        # for ollama api call
        # client = Client(host='http://*************:11434')
        # ai_msg_content = client.chat(model='mistral', messages=[
        #     {
        #         'role': 'user',
        #         'content': question,
        #     },
        # ])
        # print(ai_msg_content["message"]["content"])
        # ai_msg_content = ai_msg_content["message"]["content"]
        # for triton api call
        # airespone_starttime = datetime.now()
        #
        # command = f"python3 end_to_end_grpc_client.py -u *************:38001 -p '{question}' -S -o 128"
        #
        # process = subprocess.run(command, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        #
        # output = process.stdout.decode('utf-8')
        # error = process.stderr.decode('utf-8')
        #
        # if error:
        #     print("Error:", error)
        # else:
        #     print("Output:", output)
        #     ai_msg_content = output
        #     airesponse_endtime = datetime.now()
        #     print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")

        # messages = [{"role": "user", "content": question}]
        # prompt = pipeline.tokenizer.apply_chat_template(
        #     messages,
        #     tokenize=False,
        #     add_generation_prompt=True
        # )
        #
        # outputs = pipeline(
        #     prompt,
        #     max_new_tokens=10000,
        #     do_sample=True,
        #     top_k=50,
        #     top_p=0.95)
        #
        # ai_msg_content = outputs[0]["generated_text"].replace('<s>[INST] who is Alice? [/INST]', '')


        # aimodel_starttime = datetime.now()
        # # llm = HuggingFacePipeline(pipeline=pipeline)
        #
        # aimodel_endtime = datetime.now()
        # print(f"AI Model Loading Time = {aimodel_endtime - aimodel_starttime}")
        print("before_condense_q_chain: ", chat_history)
        condense_q_system_prompt = """Given a chat history and the user question \
        which might reference the chat history, formulate a standalone question \
        which can be understood without the chat history. Do NOT answer the question, \
        just reformulate it if needed and otherwise return it as is."""

        condense_q_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", condense_q_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        condense_q_chain = condense_q_prompt | llm | StrOutputParser()

        qa_system_prompt = """You are an assistant for question-answering tasks. \
        Use the following pieces of retrieved context to answer the question. \
        If you don't know the answer, just say that you don't know. \
        Use three sentences maximum and keep the answer concise.\

         {context}"""

        qa_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", qa_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        def condense_question(input: dict):
            print("Input content:", input)
            if input.get("chat_history"):
                return condense_q_chain
            else:
                return input["question"]

        rag_chain = (
                RunnablePassthrough.assign(context=condense_question)
                | qa_prompt
                | llm
                | StrOutputParser()
        )
        # Invoke the RAG chain and get the AI message
        airespone_starttime = datetime.now()
        # client = Client(host='http://*************:11434')
        #
        # ai_msg_content = client.chat(model='mistral', messages=[
        #     {
        #         'role': 'user',
        #         'content': question,
        #     },
        # ])
        ai_msg_content = rag_chain.invoke({"question": question, "chat_history": chat_history})
        # print(ai_msg_content)
        airesponse_endtime = datetime.now()
        # for chunks in rag_chain.stream(question):
        #     print(chunks)
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")
        # tools.gpu_detect.get_gpu_info()

    # With Vector Store
    else:
        # chat_history = []
        # aimodel_starttime = datetime.now()
        # # llm = HuggingFacePipeline(pipeline=pipeline)
        #
        # aimodel_endtime = datetime.now()
        # print(f"AI Model Loading Time = {aimodel_endtime - aimodel_starttime}")
        # print("before_condense_q_chain: ", chat_history)
        condense_q_system_prompt = """Given a chat history and the user question \
        which might reference the chat history, formulate a standalone question \
        which can be understood without the chat history. Do NOT answer the question, \
        just reformulate it if needed and otherwise return it as is."""

        condense_q_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", condense_q_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        condense_q_chain = condense_q_prompt | llm | StrOutputParser()

        qa_system_prompt = """You are an assistant for question-answering tasks. \
        Use the following pieces of retrieved context to answer the question. \
        If you don't know the answer, just say that you don't know. \
        Use three sentences maximum and keep the answer concise.\

          {context}"""
        
        # qa_system_prompt = """You are an assistant for question-answering tasks. \
        # Use the following pieces of retrieved context to answer the question. \
        # If you don't know the answer, just say No. \
        # If you know the answer, start the answer with Yes. \
        # Use three sentences maximum and keep the answer concise.\

        #  {context}"""


        qa_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", qa_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        def condense_question(input: dict):
            # print("Input content:", input)
            if input.get("chat_history"):

                return condense_q_chain
            else:
                return input["question"]

        rag_chain = (
                RunnablePassthrough.assign(context=condense_question | retriever)
                | qa_prompt
                | llm
                | StrOutputParser()
        )
        # print(rag_chain)

        # Invoke the RAG chain and get the AI message
        airespone_starttime = datetime.now()
        # Initialize chat_history as an empty list if it's None
        if chat_history is None:
            chat_history = []
        print("invoke_history", chat_history)
        # Now you can safely call rag_chain.invoke
        ai_msg_content = rag_chain.invoke({"question": question, "chat_history": chat_history})
        _start = datetime.now()
        ai_msg_content = chinese_converter.to_traditional(ai_msg_content)
        print("Translation Time: ", datetime.now() - _start)
        airesponse_endtime = datetime.now()

        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")

    print('>>> Generated AI Response')

    return ai_msg_content
    # Return only the content of the AI's response