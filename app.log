2024-06-12 14:55:23,935 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: hkunlp/instructor-large
2024-06-12 14:55:26,034 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2024-06-12 14:55:26,272 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8008
2024-06-12 14:55:26,272 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2024-06-12 14:55:29,421 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 14:55:29] "[32mGET / HTTP/1.1[0m" 302 -
2024-06-12 14:55:29,432 - chatbot_newui_20240612 - ERROR - Exception on /login_page [GET]
Traceback (most recent call last):
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 1463, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 872, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 870, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 855, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/home/<USER>/Downloads/dtt-llm-rag/chatbot_newui_20240612.py", line 224, in login_page
    return render_template('login_azure_2.html')
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/jinja2/environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/jinja2/environment.py", line 1010, in get_template
    return self._load_template(name, globals)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/jinja2/environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/jinja2/loaders.py", line 125, in load
    source, filename, uptodate = self.get_source(environment, name)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: login_azure_2.html
2024-06-12 14:55:29,434 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 14:55:29] "[35m[1mGET /login_page HTTP/1.1[0m" 500 -
2024-06-12 14:55:30,106 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 14:55:30] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2024-06-12 14:58:55,007 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: hkunlp/instructor-large
2024-06-12 14:58:57,023 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2024-06-12 14:58:57,226 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8008
2024-06-12 14:58:57,226 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2024-06-12 14:58:59,756 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 14:58:59] "[32mGET / HTTP/1.1[0m" 302 -
2024-06-12 14:58:59,761 - chatbot_newui_20240612 - ERROR - Exception on /login_page [GET]
Traceback (most recent call last):
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 1463, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 872, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 870, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 855, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/home/<USER>/Downloads/dtt-llm-rag/chatbot_newui_20240612.py", line 224, in login_page
    return render_template('login_azure_2.html.html')
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/jinja2/environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/jinja2/environment.py", line 1010, in get_template
    return self._load_template(name, globals)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/jinja2/environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/jinja2/loaders.py", line 125, in load
    source, filename, uptodate = self.get_source(environment, name)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: login_azure_2.html.html
2024-06-12 14:58:59,762 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 14:58:59] "[35m[1mGET /login_page HTTP/1.1[0m" 500 -
2024-06-12 14:59:00,440 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 14:59:00] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2024-06-12 15:00:58,904 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: hkunlp/instructor-large
2024-06-12 15:01:01,002 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2024-06-12 15:01:01,284 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8008
2024-06-12 15:01:01,284 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2024-06-12 15:01:03,590 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:01:03] "[32mGET / HTTP/1.1[0m" 302 -
2024-06-12 15:01:03,666 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:01:03] "GET /login_page HTTP/1.1" 200 -
2024-06-12 15:01:03,731 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:01:03] "GET /static/images/Deloitte.png HTTP/1.1" 200 -
2024-06-12 15:01:08,675 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2024-06-12 15:01:08,675 - msal.authority - INFO - Initializing with Entra authority: https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732
2024-06-12 15:01:08,677 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2024-06-12 15:01:09,150 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2024-06-12 15:01:09,152 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/kerberos', 'tenant_region_scope': 'AS', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2024-06-12 15:01:09,153 - msal.application - DEBUG - Broker enabled? None
2024-06-12 15:01:09,154 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:01:09] "[32mGET /login HTTP/1.1[0m" 302 -
2024-06-12 15:03:00,094 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2024-06-12 15:03:00,094 - msal.authority - INFO - Initializing with Entra authority: https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732
2024-06-12 15:03:00,096 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2024-06-12 15:03:00,440 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2024-06-12 15:03:00,442 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/kerberos', 'tenant_region_scope': 'AS', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2024-06-12 15:03:00,442 - msal.application - DEBUG - Broker enabled? None
2024-06-12 15:03:00,443 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 307d6428-e659-4a9d-9de2-913b4d3931bf
2024-06-12 15:03:01,225 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token HTTP/1.1" 200 4069
2024-06-12 15:03:01,227 - msal.token_cache - DEBUG - event={
    "client_id": "802fe47f-0185-4956-8278-91f1c9e2a60b",
    "data": {
        "claims": null,
        "client_id": "802fe47f-0185-4956-8278-91f1c9e2a60b",
        "code": "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "redirect_uri": "http://localhost:8008/chat",
        "scope": [
            "openid",
            "offline_access",
            "User.Read",
            "profile"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "authorization_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "eyJ1aWQiOiJjODkzYjY1OC02NmVhLTRhM2UtOWViYy1mMzQyOWU5ZjE2YWQiLCJ1dGlkIjoiMjc2NjAyMDQtNjZkMC00MDNjLTg2ZjQtMjQ1NWRjMDRkNzMyIn0",
        "expires_in": 3997,
        "ext_expires_in": 3997,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "openid profile User.Read email",
        "token_type": "Bearer"
    },
    "scope": [
        "openid",
        "profile",
        "User.Read",
        "email"
    ],
    "token_endpoint": "https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token"
}
2024-06-12 15:03:01,229 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:01] "[32mGET /chat?code=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&session_state=d8c18178-e442-48b1-8d68-dc68b888f220 HTTP/1.1[0m" 302 -
2024-06-12 15:03:01,236 - chatbot_newui_20240612 - ERROR - Exception on / [GET]
Traceback (most recent call last):
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 1463, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 872, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 870, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 855, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/home/<USER>/Downloads/dtt-llm-rag/chatbot_newui_20240612.py", line 220, in index
    return render_template('index_json_database_new_ui_24.html')
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/jinja2/environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/jinja2/environment.py", line 1010, in get_template
    return self._load_template(name, globals)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/jinja2/environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/jinja2/loaders.py", line 125, in load
    source, filename, uptodate = self.get_source(environment, name)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: index_json_database_new_ui_24.html
2024-06-12 15:03:01,276 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:01] "[35m[1mGET / HTTP/1.1[0m" 500 -
2024-06-12 15:03:27,629 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: hkunlp/instructor-large
2024-06-12 15:03:29,741 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2024-06-12 15:03:29,955 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8008
2024-06-12 15:03:29,955 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2024-06-12 15:03:30,425 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:30] "[32mGET / HTTP/1.1[0m" 302 -
2024-06-12 15:03:30,433 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:30] "GET /login_page HTTP/1.1" 200 -
2024-06-12 15:03:30,456 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:30] "[36mGET /static/images/Deloitte.png HTTP/1.1[0m" 304 -
2024-06-12 15:03:31,916 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2024-06-12 15:03:31,917 - msal.authority - INFO - Initializing with Entra authority: https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732
2024-06-12 15:03:31,919 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2024-06-12 15:03:32,242 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2024-06-12 15:03:32,244 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/kerberos', 'tenant_region_scope': 'AS', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2024-06-12 15:03:32,244 - msal.application - DEBUG - Broker enabled? None
2024-06-12 15:03:32,245 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:32] "[32mGET /login HTTP/1.1[0m" 302 -
2024-06-12 15:03:32,501 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2024-06-12 15:03:32,502 - msal.authority - INFO - Initializing with Entra authority: https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732
2024-06-12 15:03:32,502 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2024-06-12 15:03:32,791 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2024-06-12 15:03:32,793 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/kerberos', 'tenant_region_scope': 'AS', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2024-06-12 15:03:32,794 - msal.application - DEBUG - Broker enabled? None
2024-06-12 15:03:32,794 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 8c9338ef-da8a-4827-99f9-6a5dbd720431
2024-06-12 15:03:32,971 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token HTTP/1.1" 200 4065
2024-06-12 15:03:32,972 - msal.token_cache - DEBUG - event={
    "client_id": "802fe47f-0185-4956-8278-91f1c9e2a60b",
    "data": {
        "claims": null,
        "client_id": "802fe47f-0185-4956-8278-91f1c9e2a60b",
        "code": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "redirect_uri": "http://localhost:8008/chat",
        "scope": [
            "profile",
            "User.Read",
            "openid",
            "offline_access"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "authorization_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "eyJ1aWQiOiJjODkzYjY1OC02NmVhLTRhM2UtOWViYy1mMzQyOWU5ZjE2YWQiLCJ1dGlkIjoiMjc2NjAyMDQtNjZkMC00MDNjLTg2ZjQtMjQ1NWRjMDRkNzMyIn0",
        "expires_in": 5359,
        "ext_expires_in": 5359,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "openid profile User.Read email",
        "token_type": "Bearer"
    },
    "scope": [
        "openid",
        "profile",
        "User.Read",
        "email"
    ],
    "token_endpoint": "https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token"
}
2024-06-12 15:03:32,974 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:32] "[32mGET /chat?code=**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&session_state=d8c18178-e442-48b1-8d68-dc68b888f220 HTTP/1.1[0m" 302 -
2024-06-12 15:03:32,996 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:32] "GET / HTTP/1.1" 200 -
2024-06-12 15:03:33,040 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:33] "[33mGET /style.css HTTP/1.1[0m" 404 -
2024-06-12 15:03:33,041 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:33] "GET /static/images/deloitte-1%201.png HTTP/1.1" 200 -
2024-06-12 15:03:34,113 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:34] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:03:34,124 - chatbot_newui_20240612 - ERROR - Exception on /get-datasets [GET]
Traceback (most recent call last):
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 1463, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 872, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 870, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 855, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/home/<USER>/Downloads/dtt-llm-rag/chatbot_newui_20240612.py", line 410, in get_datasets
    dataset_list = vectorstore_module.init_vsdb_2(vs_config)
AttributeError: module 'vectorstore_module' has no attribute 'init_vsdb_2'. Did you mean: 'init_vsdb'?
2024-06-12 15:03:34,125 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:34] "GET /get-prompt-templates HTTP/1.1" 200 -
2024-06-12 15:03:34,127 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:34] "[35m[1mGET /get-datasets HTTP/1.1[0m" 500 -
2024-06-12 15:03:34,136 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:34] "GET /static/images/Cross%20button%20Frame.svg HTTP/1.1" 200 -
2024-06-12 15:03:35,121 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:35] "POST /delete-chat-history HTTP/1.1" 200 -
2024-06-12 15:03:35,122 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:35] "POST /create-new-chat-history HTTP/1.1" 200 -
2024-06-12 15:03:35,131 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:35] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:03:35,139 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:35] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:03:36,150 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:36] "GET /get-user-data/user-20240612-15-03-35 HTTP/1.1" 200 -
2024-06-12 15:03:36,155 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:03:36] "POST /update-user-session HTTP/1.1" 200 -
2024-06-12 15:04:27,287 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: hkunlp/instructor-large
2024-06-12 15:04:29,361 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2024-06-12 15:04:29,573 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8008
2024-06-12 15:04:29,574 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2024-06-12 15:04:36,457 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:36] "[32mGET / HTTP/1.1[0m" 302 -
2024-06-12 15:04:36,465 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:36] "GET /login_page HTTP/1.1" 200 -
2024-06-12 15:04:36,490 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:36] "[36mGET /static/images/Deloitte.png HTTP/1.1[0m" 304 -
2024-06-12 15:04:38,335 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:38] "GET /login_page HTTP/1.1" 200 -
2024-06-12 15:04:38,364 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:38] "[36mGET /static/images/Deloitte.png HTTP/1.1[0m" 304 -
2024-06-12 15:04:39,248 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2024-06-12 15:04:39,249 - msal.authority - INFO - Initializing with Entra authority: https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732
2024-06-12 15:04:39,250 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2024-06-12 15:04:39,479 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2024-06-12 15:04:39,481 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/kerberos', 'tenant_region_scope': 'AS', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2024-06-12 15:04:39,481 - msal.application - DEBUG - Broker enabled? None
2024-06-12 15:04:39,482 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:39] "[32mGET /login HTTP/1.1[0m" 302 -
2024-06-12 15:04:39,702 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2024-06-12 15:04:39,703 - msal.authority - INFO - Initializing with Entra authority: https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732
2024-06-12 15:04:39,704 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2024-06-12 15:04:40,005 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2024-06-12 15:04:40,007 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/kerberos', 'tenant_region_scope': 'AS', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2024-06-12 15:04:40,007 - msal.application - DEBUG - Broker enabled? None
2024-06-12 15:04:40,008 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 778e2fca-c7e7-4d5d-8270-82839dc6e414
2024-06-12 15:04:40,265 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token HTTP/1.1" 200 4065
2024-06-12 15:04:40,267 - msal.token_cache - DEBUG - event={
    "client_id": "802fe47f-0185-4956-8278-91f1c9e2a60b",
    "data": {
        "claims": null,
        "client_id": "802fe47f-0185-4956-8278-91f1c9e2a60b",
        "code": "0.ASsABAJmJ9BmPECG9CRV3ATXMn_kL4CFAVZJgniR8cnipgvkAL8.AgABBAIAAADnfolhJpSnRYB1SVj-Hgd8AgDs_wUA9P-nm0an-8Ou48K5up_vcBrIZlDyqP1x0p1-sxP3-oQYn-SE6imN0f49-4wqgiii1A8s7l7WUn_3-qaWQDXU-zSOoLpBYfuVyiqU0Z_2SvH94RVTWfjKqx1V93B0rWAYTGFFrtyppx5PuFhFNO8OdCiA-tR5U_YT0rtAUXOsBkvP-t9QrfxzNyaC4xkxYr1Jjhr0vIo3u8hgnlNc80bgjOvJgScOMRJ6W1QPHe_Iga1BIsN1fjIGpaYPlood0Par8WlM26hdivdnT9wkih8zXWW-sSmTWz0hJnvMjPNvFUDXICj1tStSNqNev0uEzzvEH4lQUFwkST3-pXr93u7SHPtCWa_j5olrHyYjsxK8nYWOpT39bLhJRkrs7rFOmwNHwLOK_ENlf2fwn7bSvwtm4oYiIpf6cDiIUSvSCsolXeQfDqtdMwM-hTR6VFdmAtH6FnwMMosi8wbleg-hjnqsE7VwHRxjSDtgFwDYnPxcfBkhsM7G-UeDQBi1do6UYxDs6-Gj9GXH2PoAbjt39YHTVnKhmi--Wnw9hGoQyt0mLsC4Ersotcqly7XT6E0deUXSYkCQoyEcn5Mdg6FBpLJ2G1rIdENUqHd20t3fsC4WlK70vlRintHb4viYQAY_jzJaauW5_WCFtTHnUeNw3OqwkjinWU9VKl2pZa_6Va8fU0iB7ixh8TTgEw",
        "redirect_uri": "http://localhost:8008/chat",
        "scope": [
            "User.Read",
            "openid",
            "offline_access",
            "profile"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "authorization_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "eyJ1aWQiOiJjODkzYjY1OC02NmVhLTRhM2UtOWViYy1mMzQyOWU5ZjE2YWQiLCJ1dGlkIjoiMjc2NjAyMDQtNjZkMC00MDNjLTg2ZjQtMjQ1NWRjMDRkNzMyIn0",
        "expires_in": 3881,
        "ext_expires_in": 3881,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "openid profile User.Read email",
        "token_type": "Bearer"
    },
    "scope": [
        "openid",
        "profile",
        "User.Read",
        "email"
    ],
    "token_endpoint": "https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token"
}
2024-06-12 15:04:40,268 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:40] "[32mGET /chat?code=0.ASsABAJmJ9BmPECG9CRV3ATXMn_kL4CFAVZJgniR8cnipgvkAL8.AgABBAIAAADnfolhJpSnRYB1SVj-Hgd8AgDs_wUA9P-nm0an-8Ou48K5up_vcBrIZlDyqP1x0p1-sxP3-oQYn-SE6imN0f49-4wqgiii1A8s7l7WUn_3-qaWQDXU-zSOoLpBYfuVyiqU0Z_2SvH94RVTWfjKqx1V93B0rWAYTGFFrtyppx5PuFhFNO8OdCiA-tR5U_YT0rtAUXOsBkvP-t9QrfxzNyaC4xkxYr1Jjhr0vIo3u8hgnlNc80bgjOvJgScOMRJ6W1QPHe_Iga1BIsN1fjIGpaYPlood0Par8WlM26hdivdnT9wkih8zXWW-sSmTWz0hJnvMjPNvFUDXICj1tStSNqNev0uEzzvEH4lQUFwkST3-pXr93u7SHPtCWa_j5olrHyYjsxK8nYWOpT39bLhJRkrs7rFOmwNHwLOK_ENlf2fwn7bSvwtm4oYiIpf6cDiIUSvSCsolXeQfDqtdMwM-hTR6VFdmAtH6FnwMMosi8wbleg-hjnqsE7VwHRxjSDtgFwDYnPxcfBkhsM7G-UeDQBi1do6UYxDs6-Gj9GXH2PoAbjt39YHTVnKhmi--Wnw9hGoQyt0mLsC4Ersotcqly7XT6E0deUXSYkCQoyEcn5Mdg6FBpLJ2G1rIdENUqHd20t3fsC4WlK70vlRintHb4viYQAY_jzJaauW5_WCFtTHnUeNw3OqwkjinWU9VKl2pZa_6Va8fU0iB7ixh8TTgEw&session_state=d8c18178-e442-48b1-8d68-dc68b888f220 HTTP/1.1[0m" 302 -
2024-06-12 15:04:40,290 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:40] "GET / HTTP/1.1" 200 -
2024-06-12 15:04:40,315 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:40] "[33mGET /style.css HTTP/1.1[0m" 404 -
2024-06-12 15:04:40,321 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:40] "[36mGET /static/images/deloitte-1%201.png HTTP/1.1[0m" 304 -
2024-06-12 15:04:40,456 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:40] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:04:40,465 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:40] "GET /get-prompt-templates HTTP/1.1" 200 -
2024-06-12 15:04:40,468 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:40] "[36mGET /static/images/Cross%20button%20Frame.svg HTTP/1.1[0m" 304 -
2024-06-12 15:04:40,565 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:40] "GET /get-datasets HTTP/1.1" 200 -
2024-06-12 15:04:41,482 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:41] "POST /delete-chat-history HTTP/1.1" 200 -
2024-06-12 15:04:41,484 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:41] "POST /create-new-chat-history HTTP/1.1" 200 -
2024-06-12 15:04:41,494 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:41] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:04:41,500 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:41] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:04:42,502 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:42] "GET /get-user-data/user-20240612-15-04-41 HTTP/1.1" 200 -
2024-06-12 15:04:42,505 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:42] "POST /update-user-session HTTP/1.1" 200 -
2024-06-12 15:04:43,257 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:43] "GET / HTTP/1.1" 200 -
2024-06-12 15:04:43,278 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:43] "[33mGET /style.css HTTP/1.1[0m" 404 -
2024-06-12 15:04:43,281 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:43] "[36mGET /static/images/deloitte-1%201.png HTTP/1.1[0m" 304 -
2024-06-12 15:04:43,364 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:43] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:04:43,379 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:43] "GET /get-prompt-templates HTTP/1.1" 200 -
2024-06-12 15:04:43,392 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:43] "[36mGET /static/images/Cross%20button%20Frame.svg HTTP/1.1[0m" 304 -
2024-06-12 15:04:43,466 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:43] "GET /get-datasets HTTP/1.1" 200 -
2024-06-12 15:04:44,368 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:44] "POST /delete-chat-history HTTP/1.1" 200 -
2024-06-12 15:04:44,370 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:44] "POST /create-new-chat-history HTTP/1.1" 200 -
2024-06-12 15:04:44,380 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:44] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:04:44,384 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:44] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:04:45,387 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:45] "GET /get-user-data/user-20240612-15-04-44 HTTP/1.1" 200 -
2024-06-12 15:04:45,389 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:45] "POST /update-user-session HTTP/1.1" 200 -
2024-06-12 15:04:50,054 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:50] "GET /get-datasets HTTP/1.1" 200 -
2024-06-12 15:04:51,109 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:04:51] "POST /selected-files HTTP/1.1" 200 -
2024-06-12 15:05:03,337 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:05:03] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:05:03,349 - langchain_community.vectorstores.milvus - DEBUG - Using previous connection: default
2024-06-12 15:05:03,351 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:05:03] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:05:03,432 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:05:10,223 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:05:12,382 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:05:14,510 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:05:16,083 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:05:16] "POST /chat HTTP/1.1" 200 -
2024-06-12 15:05:16,095 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:05:16] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:05:17,965 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:05:17] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:05:17,987 - langchain_community.vectorstores.milvus - DEBUG - Using previous connection: default
2024-06-12 15:05:17,989 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:05:17] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:05:18,034 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:05:18,701 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:05:19,930 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:05:23,025 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:05:24,486 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:05:24] "POST /chat HTTP/1.1" 200 -
2024-06-12 15:05:24,497 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:05:24] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:06:04,569 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:04] "GET /get-user-data/user-20240612-15-04-44 HTTP/1.1" 200 -
2024-06-12 15:06:04,580 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:04] "POST /update-user-session HTTP/1.1" 200 -
2024-06-12 15:06:06,010 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:06] "POST /create-new-chat-history HTTP/1.1" 200 -
2024-06-12 15:06:06,018 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:06] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:06:07,023 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:07] "GET /get-user-data/user-20240612-15-06-06 HTTP/1.1" 200 -
2024-06-12 15:06:07,026 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:07] "POST /update-user-session HTTP/1.1" 200 -
2024-06-12 15:06:10,437 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:10] "POST /selected-files HTTP/1.1" 200 -
2024-06-12 15:06:10,748 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:10] "POST /selected-files HTTP/1.1" 200 -
2024-06-12 15:06:27,683 - langchain_community.vectorstores.milvus - DEBUG - Using previous connection: default
2024-06-12 15:06:27,684 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:27] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:06:27,692 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:27] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:06:27,729 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:06:28,159 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:06:28,453 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:06:31,153 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:06:31,986 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:31] "POST /chat HTTP/1.1" 200 -
2024-06-12 15:06:31,997 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:31] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:06:37,064 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:37] "POST /create-new-chat-history HTTP/1.1" 200 -
2024-06-12 15:06:37,073 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:37] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:06:38,078 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:38] "GET /get-user-data/user-20240612-15-06-37 HTTP/1.1" 200 -
2024-06-12 15:06:38,080 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:38] "POST /update-user-session HTTP/1.1" 200 -
2024-06-12 15:06:43,230 - langchain_community.vectorstores.milvus - DEBUG - Using previous connection: default
2024-06-12 15:06:43,231 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:43] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:06:43,238 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:43] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:06:43,272 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:06:43,680 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:06:44,535 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:06:46,628 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:06:47,839 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:47] "POST /chat HTTP/1.1" 200 -
2024-06-12 15:06:47,859 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:47] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:06:55,783 - langchain_community.vectorstores.milvus - DEBUG - Using previous connection: default
2024-06-12 15:06:55,785 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:55] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:06:55,790 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:06:55] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:06:55,827 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:06:56,465 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:06:57,446 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:07:00,216 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:07:01,303 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:07:01] "POST /chat HTTP/1.1" 200 -
2024-06-12 15:07:01,316 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:07:01] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:07:07,485 - langchain_community.vectorstores.milvus - DEBUG - Using previous connection: default
2024-06-12 15:07:07,486 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:07:07] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:07:07,492 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:07:07] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:07:07,602 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:07:08,319 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:07:09,922 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:07:12,696 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:07:13,940 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:07:13] "POST /chat HTTP/1.1" 200 -
2024-06-12 15:07:13,950 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:07:13] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:07:59,197 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:07:59] "POST /create-new-chat-history HTTP/1.1" 200 -
2024-06-12 15:07:59,206 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:07:59] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:08:00,211 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:00] "GET /get-user-data/user-20240612-15-07-59 HTTP/1.1" 200 -
2024-06-12 15:08:00,213 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:00] "POST /update-user-session HTTP/1.1" 200 -
2024-06-12 15:08:08,682 - langchain_community.vectorstores.milvus - DEBUG - Using previous connection: default
2024-06-12 15:08:08,683 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:08] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:08:08,690 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:08] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:08:08,731 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:08:09,137 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:08:09,812 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:08:12,543 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:08:14,599 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:14] "POST /chat HTTP/1.1" 200 -
2024-06-12 15:08:14,608 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:14] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:08:19,220 - langchain_community.vectorstores.milvus - DEBUG - Using previous connection: default
2024-06-12 15:08:19,221 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:19] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:08:19,225 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:19] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:08:19,273 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:08:19,956 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:08:20,625 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 192.168.0.205:11434
2024-06-12 15:08:23,317 - urllib3.connectionpool - DEBUG - http://192.168.0.205:11434 "POST /api/generate HTTP/1.1" 200 None
2024-06-12 15:08:23,575 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:23] "POST /chat HTTP/1.1" 200 -
2024-06-12 15:08:23,585 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:23] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:08:27,491 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:27] "POST /logout HTTP/1.1" 200 -
2024-06-12 15:08:30,671 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:30] "[32mGET / HTTP/1.1[0m" 302 -
2024-06-12 15:08:30,675 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:30] "GET /login_page HTTP/1.1" 200 -
2024-06-12 15:08:30,690 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:30] "[36mGET /static/images/Deloitte.png HTTP/1.1[0m" 304 -
2024-06-12 15:08:30,725 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:30] "GET /login_page HTTP/1.1" 200 -
2024-06-12 15:08:30,753 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:08:30] "[36mGET /static/images/Deloitte.png HTTP/1.1[0m" 304 -
2024-06-12 15:10:05,208 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:10:05] "GET /login_page HTTP/1.1" 200 -
2024-06-12 15:10:05,229 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:10:05] "[36mGET /static/images/Deloitte.png HTTP/1.1[0m" 304 -
2024-06-12 15:10:06,216 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2024-06-12 15:10:06,217 - msal.authority - INFO - Initializing with Entra authority: https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732
2024-06-12 15:10:06,218 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2024-06-12 15:10:06,492 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2024-06-12 15:10:06,494 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/kerberos', 'tenant_region_scope': 'AS', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2024-06-12 15:10:06,494 - msal.application - DEBUG - Broker enabled? None
2024-06-12 15:10:06,495 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:10:06] "[32mGET /login HTTP/1.1[0m" 302 -
2024-06-12 15:11:00,446 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2024-06-12 15:11:00,446 - msal.authority - INFO - Initializing with Entra authority: https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732
2024-06-12 15:11:00,447 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2024-06-12 15:11:00,757 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2024-06-12 15:11:00,759 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/kerberos', 'tenant_region_scope': 'AS', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2024-06-12 15:11:00,759 - msal.application - DEBUG - Broker enabled? None
2024-06-12 15:11:00,759 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 0d47a6dd-8120-44c8-bfb4-e8b28f5603da
2024-06-12 15:11:00,928 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token HTTP/1.1" 200 4069
2024-06-12 15:11:00,930 - msal.token_cache - DEBUG - event={
    "client_id": "802fe47f-0185-4956-8278-91f1c9e2a60b",
    "data": {
        "claims": null,
        "client_id": "802fe47f-0185-4956-8278-91f1c9e2a60b",
        "code": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "redirect_uri": "http://localhost:8008/chat",
        "scope": [
            "User.Read",
            "openid",
            "offline_access",
            "profile"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "authorization_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "eyJ1aWQiOiI0YjAwY2YxOC0zNWYwLTQ1ODYtOWZlNS01MDI0ZGViMjkzNzkiLCJ1dGlkIjoiMjc2NjAyMDQtNjZkMC00MDNjLTg2ZjQtMjQ1NWRjMDRkNzMyIn0",
        "expires_in": 4427,
        "ext_expires_in": 4427,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "openid profile User.Read email",
        "token_type": "Bearer"
    },
    "scope": [
        "openid",
        "profile",
        "User.Read",
        "email"
    ],
    "token_endpoint": "https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token"
}
2024-06-12 15:11:00,931 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:00] "[32mGET /chat?code=******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&session_state=ad6fe954-0c3a-4932-80a1-6d462b8b6649 HTTP/1.1[0m" 302 -
2024-06-12 15:11:00,937 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:00] "GET / HTTP/1.1" 200 -
2024-06-12 15:11:00,971 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:00] "[33mGET /style.css HTTP/1.1[0m" 404 -
2024-06-12 15:11:00,978 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:00] "GET /static/images/deloitte-1%201.png HTTP/1.1" 200 -
2024-06-12 15:11:01,982 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:01] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:11:01,989 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:01] "GET /get-prompt-templates HTTP/1.1" 200 -
2024-06-12 15:11:01,997 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:01] "GET /static/images/Cross%20button%20Frame.svg HTTP/1.1" 200 -
2024-06-12 15:11:02,096 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:02] "GET /get-datasets HTTP/1.1" 200 -
2024-06-12 15:11:02,988 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:02] "POST /create-new-chat-history HTTP/1.1" 200 -
2024-06-12 15:11:02,996 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:02] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:11:04,000 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:04] "GET /get-user-data/user-20240612-15-11-02 HTTP/1.1" 200 -
2024-06-12 15:11:04,002 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:04] "POST /update-user-session HTTP/1.1" 200 -
2024-06-12 15:11:25,403 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:11:25] "GET /get-datasets HTTP/1.1" 200 -
2024-06-12 15:12:22,710 - langchain_community.document_loaders.directory - DEBUG - Processing file: uploads/requirements.txt
2024-06-12 15:12:24,239 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): packages.unstructured.io:443
2024-06-12 15:12:24,480 - urllib3.connectionpool - DEBUG - https://packages.unstructured.io:443 "GET /python-telemetry?version=0.12.6&platform=Linux&python3.10&arch=x86_64&gpu=True&dev=false HTTP/1.1" 302 None
2024-06-12 15:12:24,482 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): unstructured.io:443
2024-06-12 15:12:24,634 - urllib3.connectionpool - DEBUG - https://unstructured.io:443 "GET /?version=0.12.6&platform=Linux&python3.10&arch=x86_64&gpu=True&dev=false HTTP/1.1" 200 8357
2024-06-12 15:12:25,273 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 requirements
2024-06-12 15:12:25,273 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 requirements
2024-06-12 15:12:25,274 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

# requirements.
2024-06-12 15:12:25,274 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
 requirements
2024-06-12 15:12:25,274 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
vllm==026
2024-06-12 15:12:25,274 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

vllm==0.2.6
2024-06-12 15:12:25,274 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
tqdm
2024-06-12 15:12:25,274 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
tqdm
2024-06-12 15:12:25,275 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

tqdm
2024-06-12 15:12:25,275 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
tqdm
2024-06-12 15:12:25,275 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
spacy
2024-06-12 15:12:25,275 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
spacy
2024-06-12 15:12:25,275 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

spacy
2024-06-12 15:12:25,275 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
spacy
2024-06-12 15:12:25,276 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
scikitlearn
2024-06-12 15:12:25,276 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

scikit-learn
2024-06-12 15:12:25,276 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
scikitlearn
2024-06-12 15:12:25,276 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
jsonlines
2024-06-12 15:12:25,276 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
jsonlines
2024-06-12 15:12:25,277 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

jsonlines
2024-06-12 15:12:25,277 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
jsonlines
2024-06-12 15:12:25,277 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
transformers==4362
2024-06-12 15:12:25,277 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

transformers==4.36.2
2024-06-12 15:12:25,277 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
transformers==4362
2024-06-12 15:12:25,277 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
filelock
2024-06-12 15:12:25,277 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
filelock
2024-06-12 15:12:25,278 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

filelock
2024-06-12 15:12:25,278 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
filelock
2024-06-12 15:12:25,278 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
datasets==2150
2024-06-12 15:12:25,278 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

datasets==2.15.0
2024-06-12 15:12:25,278 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
datasets==2150
2024-06-12 15:12:25,278 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
nltk
2024-06-12 15:12:25,278 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
nltk
2024-06-12 15:12:25,279 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

nltk
2024-06-12 15:12:25,279 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
nltk
2024-06-12 15:12:25,279 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
dataclasses
2024-06-12 15:12:25,279 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
dataclasses
2024-06-12 15:12:25,279 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

dataclasses
2024-06-12 15:12:25,280 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
dataclasses
2024-06-12 15:12:25,280 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
sacrebleu==240
2024-06-12 15:12:25,280 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

sacrebleu==2.4.0
2024-06-12 15:12:25,280 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
sacrebleu==240
2024-06-12 15:12:25,280 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
rougescore==012
2024-06-12 15:12:25,280 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

rouge_score==0.1.2
2024-06-12 15:12:25,280 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
rougescore==012
2024-06-12 15:12:25,281 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
flashattn==236
2024-06-12 15:12:25,281 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

flash-attn==2.3.6
2024-06-12 15:12:25,281 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
flashattn==236
2024-06-12 15:12:25,281 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
einops==070
2024-06-12 15:12:25,281 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

einops==0.7.0
2024-06-12 15:12:25,281 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
sentencepiece
2024-06-12 15:12:25,282 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
sentencepiece
2024-06-12 15:12:25,282 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

sentencepiece
2024-06-12 15:12:25,282 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
sentencepiece
2024-06-12 15:12:25,282 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
deepspeed==0126
2024-06-12 15:12:25,282 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

deepspeed==0.12.6
2024-06-12 15:12:25,282 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
deepspeed==0126
2024-06-12 15:12:25,283 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
accelerate==0250
2024-06-12 15:12:25,283 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

accelerate==0.25.0
2024-06-12 15:12:25,283 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
accelerate==0250
2024-06-12 15:12:25,283 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
peft==071
2024-06-12 15:12:25,283 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

peft==0.7.1
2024-06-12 15:12:25,284 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
evaluate==041
2024-06-12 15:12:25,284 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

evaluate==0.4.1
2024-06-12 15:12:25,284 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
evaluate==041
2024-06-12 15:12:25,284 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
tokenizers==0150
2024-06-12 15:12:25,285 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

tokenizers==0.15.0
2024-06-12 15:12:25,285 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
tokenizers==0150
2024-06-12 15:12:25,285 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
tensorboard==2140
2024-06-12 15:12:25,285 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

tensorboard==2.14.0
2024-06-12 15:12:25,285 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
tensorboard==2140
2024-06-12 15:12:25,285 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
openai==0281
2024-06-12 15:12:25,285 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

openai==0.28.1
2024-06-12 15:12:25,286 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
tiktoken==052
2024-06-12 15:12:25,286 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

tiktoken==0.5.2
2024-06-12 15:12:25,286 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
tiktoken==052
2024-06-12 15:12:25,287 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 env
2024-06-12 15:12:25,287 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 env
2024-06-12 15:12:25,288 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 packages
2024-06-12 15:12:25,288 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 packages
2024-06-12 15:12:25,288 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

# packages.
2024-06-12 15:12:25,289 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
 packages
2024-06-12 15:12:25,289 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 factscore==015
2024-06-12 15:12:25,289 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

# factscore==0.1.5
2024-06-12 15:12:25,289 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
 factscore==015
2024-06-12 15:12:25,975 - pymilvus.decorators - ERROR - RPC error: [batch_insert], <DataNotMatchException: (code=1, message=The Input data type is inconsistent with defined schema, please check it.)>, <Time:{'RPC start': '2024-06-12 15:12:25.974407', 'RPC error': '2024-06-12 15:12:25.974923'}>
2024-06-12 15:12:25,975 - chatbot_newui_20240612 - ERROR - Exception on /upload [POST]
Traceback (most recent call last):
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 1463, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 872, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 870, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 855, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/home/<USER>/Downloads/dtt-llm-rag/chatbot_newui_20240612.py", line 344, in upload_file
    vectorstore_module.insert_vectors(
  File "/home/<USER>/Downloads/dtt-llm-rag/vectorstore_module.py", line 239, in insert_vectors
    collection.insert(entities, partition_name=partition_name)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/orm/collection.py", line 500, in insert
    return conn.batch_insert(
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/decorators.py", line 147, in handler
    raise e from e
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/decorators.py", line 143, in handler
    return func(*args, **kwargs)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/decorators.py", line 182, in handler
    return func(self, *args, **kwargs)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/decorators.py", line 122, in handler
    raise e from e
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/decorators.py", line 87, in handler
    return func(*args, **kwargs)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/client/grpc_handler.py", line 575, in batch_insert
    raise err from err
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/client/grpc_handler.py", line 558, in batch_insert
    request = self._prepare_batch_insert_request(
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/client/grpc_handler.py", line 542, in _prepare_batch_insert_request
    else Prepare.batch_insert_param(collection_name, entities, partition_name, fields_info)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/client/prepare.py", line 531, in batch_insert_param
    return cls._parse_batch_request(request, entities, fields_info, location)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/client/prepare.py", line 512, in _parse_batch_request
    raise DataNotMatchException(message=ExceptionsMessage.DataTypeInconsistent) from e
pymilvus.exceptions.DataNotMatchException: <DataNotMatchException: (code=1, message=The Input data type is inconsistent with defined schema, please check it.)>
2024-06-12 15:12:25,978 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:12:25] "[35m[1mPOST /upload HTTP/1.1[0m" 500 -
2024-06-12 15:14:26,980 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: hkunlp/instructor-large
2024-06-12 15:14:27,036 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: hkunlp/instructor-large
2024-06-12 15:14:29,072 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2024-06-12 15:14:29,185 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2024-06-12 15:14:29,308 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8008
2024-06-12 15:14:29,309 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2024-06-12 15:14:56,463 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: hkunlp/instructor-large
2024-06-12 15:14:58,549 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2024-06-12 15:14:58,782 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8008
2024-06-12 15:14:58,782 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2024-06-12 15:15:04,116 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:04] "[32mGET / HTTP/1.1[0m" 302 -
2024-06-12 15:15:04,155 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:04] "GET /login_page HTTP/1.1" 200 -
2024-06-12 15:15:04,704 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:04] "GET /static/images/Deloitte.png HTTP/1.1" 200 -
2024-06-12 15:15:04,738 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:04] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2024-06-12 15:15:06,160 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2024-06-12 15:15:06,160 - msal.authority - INFO - Initializing with Entra authority: https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732
2024-06-12 15:15:06,162 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2024-06-12 15:15:06,431 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2024-06-12 15:15:06,433 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/kerberos', 'tenant_region_scope': 'AS', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2024-06-12 15:15:06,433 - msal.application - DEBUG - Broker enabled? None
2024-06-12 15:15:06,434 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:06] "[32mGET /login HTTP/1.1[0m" 302 -
2024-06-12 15:15:14,868 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2024-06-12 15:15:14,868 - msal.authority - INFO - Initializing with Entra authority: https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732
2024-06-12 15:15:14,869 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2024-06-12 15:15:15,080 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2024-06-12 15:15:15,082 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/kerberos', 'tenant_region_scope': 'AS', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2024-06-12 15:15:15,082 - msal.application - DEBUG - Broker enabled? None
2024-06-12 15:15:15,083 - msal.telemetry - DEBUG - Generate or reuse correlation_id: f99636f7-925f-464d-836a-c1dd416187d3
2024-06-12 15:15:15,212 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token HTTP/1.1" 200 4069
2024-06-12 15:15:15,214 - msal.token_cache - DEBUG - event={
    "client_id": "802fe47f-0185-4956-8278-91f1c9e2a60b",
    "data": {
        "claims": null,
        "client_id": "802fe47f-0185-4956-8278-91f1c9e2a60b",
        "code": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "redirect_uri": "http://localhost:8008/chat",
        "scope": [
            "User.Read",
            "profile",
            "openid",
            "offline_access"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "authorization_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "eyJ1aWQiOiI0YjAwY2YxOC0zNWYwLTQ1ODYtOWZlNS01MDI0ZGViMjkzNzkiLCJ1dGlkIjoiMjc2NjAyMDQtNjZkMC00MDNjLTg2ZjQtMjQ1NWRjMDRkNzMyIn0",
        "expires_in": 4057,
        "ext_expires_in": 4057,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "openid profile User.Read email",
        "token_type": "Bearer"
    },
    "scope": [
        "openid",
        "profile",
        "User.Read",
        "email"
    ],
    "token_endpoint": "https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token"
}
2024-06-12 15:15:15,216 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:15] "[32mGET /chat?code=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&session_state=a9cbc875-8c11-482c-b108-f4a31662c69b HTTP/1.1[0m" 302 -
2024-06-12 15:15:15,237 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:15] "GET / HTTP/1.1" 200 -
2024-06-12 15:15:15,280 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:15] "[33mGET /style.css HTTP/1.1[0m" 404 -
2024-06-12 15:15:15,294 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:15] "[36mGET /static/images/deloitte-1%201.png HTTP/1.1[0m" 304 -
2024-06-12 15:15:16,369 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:16] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:15:16,414 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:16] "GET /get-prompt-templates HTTP/1.1" 200 -
2024-06-12 15:15:16,417 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:16] "[36mGET /static/images/Cross%20button%20Frame.svg HTTP/1.1[0m" 304 -
2024-06-12 15:15:16,515 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:16] "GET /get-datasets HTTP/1.1" 200 -
2024-06-12 15:15:17,406 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:17] "POST /create-new-chat-history HTTP/1.1" 200 -
2024-06-12 15:15:17,407 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:17] "POST /delete-chat-history HTTP/1.1" 200 -
2024-06-12 15:15:17,419 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:17] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:15:17,425 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:17] "GET /get-all-users-data HTTP/1.1" 200 -
2024-06-12 15:15:18,422 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:18] "GET /get-user-data/user-20240612-15-15-17 HTTP/1.1" 200 -
2024-06-12 15:15:18,424 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:18] "POST /update-user-session HTTP/1.1" 200 -
2024-06-12 15:15:23,275 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:23] "GET /get-datasets HTTP/1.1" 200 -
2024-06-12 15:15:26,992 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:26] "GET /get-datasets HTTP/1.1" 200 -
2024-06-12 15:15:30,692 - langchain_community.document_loaders.directory - DEBUG - Processing file: uploads/requirements.txt
2024-06-12 15:15:32,218 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): packages.unstructured.io:443
2024-06-12 15:15:32,455 - urllib3.connectionpool - DEBUG - https://packages.unstructured.io:443 "GET /python-telemetry?version=0.12.6&platform=Linux&python3.10&arch=x86_64&gpu=True&dev=false HTTP/1.1" 302 None
2024-06-12 15:15:32,457 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): unstructured.io:443
2024-06-12 15:15:32,607 - urllib3.connectionpool - DEBUG - https://unstructured.io:443 "GET /?version=0.12.6&platform=Linux&python3.10&arch=x86_64&gpu=True&dev=false HTTP/1.1" 200 8357
2024-06-12 15:15:33,223 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 requirements
2024-06-12 15:15:33,224 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 requirements
2024-06-12 15:15:33,224 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

# requirements.
2024-06-12 15:15:33,224 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
 requirements
2024-06-12 15:15:33,224 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
vllm==026
2024-06-12 15:15:33,224 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

vllm==0.2.6
2024-06-12 15:15:33,224 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
tqdm
2024-06-12 15:15:33,225 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
tqdm
2024-06-12 15:15:33,225 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

tqdm
2024-06-12 15:15:33,225 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
tqdm
2024-06-12 15:15:33,225 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
spacy
2024-06-12 15:15:33,225 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
spacy
2024-06-12 15:15:33,226 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

spacy
2024-06-12 15:15:33,226 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
spacy
2024-06-12 15:15:33,226 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
scikitlearn
2024-06-12 15:15:33,226 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

scikit-learn
2024-06-12 15:15:33,226 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
scikitlearn
2024-06-12 15:15:33,226 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
jsonlines
2024-06-12 15:15:33,226 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
jsonlines
2024-06-12 15:15:33,227 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

jsonlines
2024-06-12 15:15:33,227 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
jsonlines
2024-06-12 15:15:33,227 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
transformers==4362
2024-06-12 15:15:33,227 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

transformers==4.36.2
2024-06-12 15:15:33,227 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
transformers==4362
2024-06-12 15:15:33,227 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
filelock
2024-06-12 15:15:33,227 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
filelock
2024-06-12 15:15:33,228 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

filelock
2024-06-12 15:15:33,228 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
filelock
2024-06-12 15:15:33,228 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
datasets==2150
2024-06-12 15:15:33,228 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

datasets==2.15.0
2024-06-12 15:15:33,228 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
datasets==2150
2024-06-12 15:15:33,228 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
nltk
2024-06-12 15:15:33,229 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
nltk
2024-06-12 15:15:33,229 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

nltk
2024-06-12 15:15:33,229 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
nltk
2024-06-12 15:15:33,229 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
dataclasses
2024-06-12 15:15:33,229 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
dataclasses
2024-06-12 15:15:33,230 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

dataclasses
2024-06-12 15:15:33,230 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
dataclasses
2024-06-12 15:15:33,230 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
sacrebleu==240
2024-06-12 15:15:33,230 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

sacrebleu==2.4.0
2024-06-12 15:15:33,230 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
sacrebleu==240
2024-06-12 15:15:33,230 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
rougescore==012
2024-06-12 15:15:33,230 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

rouge_score==0.1.2
2024-06-12 15:15:33,231 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
rougescore==012
2024-06-12 15:15:33,231 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
flashattn==236
2024-06-12 15:15:33,231 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

flash-attn==2.3.6
2024-06-12 15:15:33,231 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
flashattn==236
2024-06-12 15:15:33,231 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
einops==070
2024-06-12 15:15:33,231 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

einops==0.7.0
2024-06-12 15:15:33,232 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
sentencepiece
2024-06-12 15:15:33,232 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
sentencepiece
2024-06-12 15:15:33,232 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

sentencepiece
2024-06-12 15:15:33,232 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
sentencepiece
2024-06-12 15:15:33,232 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
deepspeed==0126
2024-06-12 15:15:33,232 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

deepspeed==0.12.6
2024-06-12 15:15:33,232 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
deepspeed==0126
2024-06-12 15:15:33,233 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
accelerate==0250
2024-06-12 15:15:33,233 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

accelerate==0.25.0
2024-06-12 15:15:33,233 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
accelerate==0250
2024-06-12 15:15:33,233 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
peft==071
2024-06-12 15:15:33,233 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

peft==0.7.1
2024-06-12 15:15:33,234 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
evaluate==041
2024-06-12 15:15:33,234 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

evaluate==0.4.1
2024-06-12 15:15:33,234 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
evaluate==041
2024-06-12 15:15:33,235 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
tokenizers==0150
2024-06-12 15:15:33,235 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

tokenizers==0.15.0
2024-06-12 15:15:33,235 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
tokenizers==0150
2024-06-12 15:15:33,235 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
tensorboard==2140
2024-06-12 15:15:33,235 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

tensorboard==2.14.0
2024-06-12 15:15:33,235 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
tensorboard==2140
2024-06-12 15:15:33,235 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
openai==0281
2024-06-12 15:15:33,236 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

openai==0.28.1
2024-06-12 15:15:33,236 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
tiktoken==052
2024-06-12 15:15:33,236 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

tiktoken==0.5.2
2024-06-12 15:15:33,236 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
tiktoken==052
2024-06-12 15:15:33,237 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 env
2024-06-12 15:15:33,237 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 env
2024-06-12 15:15:33,238 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 packages
2024-06-12 15:15:33,238 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 packages
2024-06-12 15:15:33,238 - unstructured.trace - DETAIL - Not narrative. Text does not contain a verb:

# packages.
2024-06-12 15:15:33,239 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
 packages
2024-06-12 15:15:33,239 - unstructured.trace - DETAIL - Sentence does not exceed 3 word tokens, it will not count toward sentence count.
 factscore==015
2024-06-12 15:15:33,239 - unstructured.trace - DETAIL - Not narrative. Text exceeds cap ratio 0.5:

# factscore==0.1.5
2024-06-12 15:15:33,239 - unstructured.trace - DETAIL - Sentence does not exceed 5 word tokens, it will not count toward sentence count.
 factscore==015
2024-06-12 15:15:35,265 - chatbot_newui_20240612 - ERROR - Exception on /upload [POST]
Traceback (most recent call last):
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 1463, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 872, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 870, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/flask/app.py", line 855, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/home/<USER>/Downloads/dtt-llm-rag/chatbot_newui_20240612.py", line 344, in upload_file
    vectorstore_module.insert_vectors(
  File "/home/<USER>/Downloads/dtt-llm-rag/vectorstore_module.py", line 239, in insert_vectors
    collection.insert(entities, partition_name=partition_name)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/orm/collection.py", line 498, in insert
    check_insert_schema(self._schema, data)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/orm/schema.py", line 515, in check_insert_schema
    _check_data_schema_cnt(schema, data)
  File "/home/<USER>/Downloads/dtt-llm-rag/venv/lib/python3.10/site-packages/pymilvus/orm/schema.py", line 494, in _check_data_schema_cnt
    raise DataNotMatchException(message=message)
pymilvus.exceptions.DataNotMatchException: <DataNotMatchException: (code=1, message=The data don't match with schema fields, expect 3 list, got 8)>
2024-06-12 15:15:35,267 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2024 15:15:35] "[35m[1mPOST /upload HTTP/1.1[0m" 500 -
2024-06-12 15:16:45,825 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: hkunlp/instructor-large
2024-06-12 15:16:48,002 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2024-06-12 15:16:48,239 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8008
2024-06-12 15:16:48,239 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2024-06-12 15:24:03,524 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: hkunlp/instructor-large
2024-06-12 15:24:05,866 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2024-06-12 15:24:06,104 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://*************:8008
2024-06-12 15:24:06,105 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2024-06-12 15:24:17,390 - werkzeug - INFO - ************ - - [12/Jun/2024 15:24:17] "[32mGET / HTTP/1.1[0m" 302 -
2024-06-12 15:24:17,403 - werkzeug - INFO - ************ - - [12/Jun/2024 15:24:17] "GET /login_page HTTP/1.1" 200 -
2024-06-12 15:24:17,440 - werkzeug - INFO - ************ - - [12/Jun/2024 15:24:17] "GET /static/images/Deloitte.png HTTP/1.1" 200 -
2024-06-12 15:24:17,452 - werkzeug - INFO - ************ - - [12/Jun/2024 15:24:17] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2024-06-12 15:24:19,324 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2024-06-12 15:24:19,324 - msal.authority - INFO - Initializing with Entra authority: https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732
2024-06-12 15:24:19,326 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2024-06-12 15:24:19,620 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2024-06-12 15:24:19,622 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732/kerberos', 'tenant_region_scope': 'AS', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2024-06-12 15:24:19,622 - msal.application - DEBUG - Broker enabled? None
2024-06-12 15:24:19,624 - werkzeug - INFO - ************ - - [12/Jun/2024 15:24:19] "[32mGET /login HTTP/1.1[0m" 302 -
2024-09-16 09:35:30,233 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: hkunlp/instructor-large
2024-09-16 09:35:32,132 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2024-09-16 09:35:32,152 - pymilvus.decorators - ERROR - RPC error: [__internal_register], <MilvusException: (code=800, message=database not found[database=gloria])>, <Time:{'RPC start': '2024-09-16 09:35:32.149320', 'RPC error': '2024-09-16 09:35:32.152059'}>
