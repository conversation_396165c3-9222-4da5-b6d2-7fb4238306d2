#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU集成模块
用于将MinerU的PDF解析功能直接集成到文件上传处理流程中
"""

import os
import json
import tempfile
import logging
from typing import List, Dict, Tuple, Optional
from pathlib import Path

try:
    from magic_pdf.api.magic_pdf_parse import magic_pdf_parse
    from magic_pdf.config.make_content_config import DropMode, MakeContentConfig
    MINERU_AVAILABLE = True
except ImportError:
    MINERU_AVAILABLE = False
    logging.warning("MinerU not available. Please install with: pip install magic-pdf[full]")

try:
    from mineru_config import get_mineru_config
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    logging.warning("MinerU config not available. Using default settings.")

class MinerUProcessor:
    """MinerU处理器，用于PDF文档的布局分析和内容提取"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化MinerU处理器

        Args:
            config_path: MinerU配置文件路径，如果为None则使用默认配置
        """
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)

        if not MINERU_AVAILABLE:
            raise ImportError("MinerU not available. Please install with: pip install magic-pdf[full]")

        # 加载配置
        if CONFIG_AVAILABLE:
            self.config_manager = get_mineru_config()
            if config_path:
                self.config_manager.load_from_file(config_path)
        else:
            self.config_manager = None
    
    def process_pdf(self, pdf_path: str, output_dir: str = None) -> Tuple[Optional[Dict], Optional[str]]:
        """
        使用MinerU处理PDF文件
        
        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录，如果为None则使用临时目录
            
        Returns:
            Tuple[layout_data, markdown_content]: 布局数据和Markdown内容
        """
        if not os.path.exists(pdf_path):
            self.logger.error(f"PDF文件不存在: {pdf_path}")
            return None, None
            
        try:
            # 创建输出目录
            if output_dir is None:
                output_dir = tempfile.mkdtemp()
            else:
                os.makedirs(output_dir, exist_ok=True)
            
            # 配置MinerU参数
            if self.config_manager:
                parse_config = self.config_manager.get_parse_config()
                parse_config["output_dir"] = output_dir

                # 检查文件是否支持
                if not self.config_manager.is_file_supported(pdf_path):
                    self.logger.warning(f"文件格式不支持: {pdf_path}")
                    return None, None

                # 检查文件大小
                if not self.config_manager.check_file_size(pdf_path):
                    self.logger.warning(f"文件大小超出限制: {pdf_path}")
                    return None, None
            else:
                # 默认配置
                parse_config = {
                    "parse_mode": "auto",  # 自动模式
                    "output_format": ["json"],  # 输出格式
                    "output_dir": output_dir,
                    "lang": "auto",  # 自动语言检测
                    "layout_config": {
                        "model": "doclayout_yolo"
                    },
                    "formula_config": {
                        "enable": True,
                        "mfd_model": "yolo_v8_mfd",
                        "mfr_model": "unimernet_small"
                    },
                    "table_config": {
                        "enable": True,
                        "model": "rapid_table"
                    }
                }
            
            self.logger.info(f"开始使用MinerU处理PDF: {pdf_path}")
            
            # 调用MinerU API
            result = magic_pdf_parse(
                pdf_path=pdf_path,
                output_dir=output_dir,
                **parse_config
            )
            
            # 解析结果
            layout_data = None
            markdown_content = None
            
            if result and "layout" in result:
                layout_data = result["layout"]
            
            if result and "markdown" in result:
                markdown_content = result["markdown"]
            
            self.logger.info(f"MinerU处理完成: {pdf_path}")
            return layout_data, markdown_content
            
        except Exception as e:
            self.logger.error(f"MinerU处理失败: {str(e)}")
            return None, None
    
    def convert_to_chunks(self, layout_data: Dict, filename: str) -> List[Tuple[str, Dict]]:
        """
        将MinerU的布局数据转换为与现有系统兼容的chunks格式
        
        Args:
            layout_data: MinerU输出的布局数据
            filename: 原始文件名
            
        Returns:
            List[Tuple[str, Dict]]: chunks列表，格式与chunk_pdf_advanced相同
        """
        chunks_with_metadata = []
        
        try:
            if not layout_data or "pdf_info" not in layout_data:
                self.logger.warning("布局数据格式不正确")
                return chunks_with_metadata
            
            for page_idx, page_info in enumerate(layout_data["pdf_info"]):
                page_num = page_idx + 1
                
                if "preproc_blocks" not in page_info:
                    continue
                
                for block_idx, block in enumerate(page_info["preproc_blocks"]):
                    chunk_text = self._extract_text_from_block(block)
                    
                    if not chunk_text or chunk_text.strip() == "":
                        continue
                    
                    # 创建元数据
                    metadata = {
                        'start_page_num': page_num,
                        'end_page_num': page_num,
                        'start_line_num': block_idx + 1,
                        'end_line_num': block_idx + 1,
                        'content_type': block.get('type', 'text'),
                        'bbox': block.get('bbox', [0, 0, 0, 0]),
                        'source': filename,
                        'block_index': block_idx,
                        'confidence': block.get('score', 1.0)
                    }
                    
                    chunks_with_metadata.append((chunk_text, metadata))
            
            self.logger.info(f"转换完成，生成 {len(chunks_with_metadata)} 个文本块")
            return chunks_with_metadata
            
        except Exception as e:
            self.logger.error(f"转换chunks失败: {str(e)}")
            return chunks_with_metadata
    
    def _extract_text_from_block(self, block: Dict) -> str:
        """从block中提取文本内容"""
        text_content = ""
        
        try:
            if block.get('type') == 'text':
                # 处理文本块
                if 'lines' in block:
                    for line in block['lines']:
                        if 'spans' in line:
                            for span in line['spans']:
                                if span.get('type') == 'text' and 'content' in span:
                                    text_content += span['content'] + " "
                
            elif block.get('type') == 'title':
                # 处理标题块
                if 'lines' in block:
                    for line in block['lines']:
                        if 'spans' in line:
                            for span in line['spans']:
                                if span.get('type') == 'text' and 'content' in span:
                                    text_content += span['content'] + " "
                                    
            elif block.get('type') == 'table':
                # 处理表格块
                if 'blocks' in block:
                    for sub_block in block['blocks']:
                        if 'lines' in sub_block:
                            for line in sub_block['lines']:
                                if 'spans' in line:
                                    for span in line['spans']:
                                        if span.get('type') == 'table' and 'html' in span:
                                            text_content += f"[TABLE: {span['html']}]"
                                        elif span.get('type') == 'text' and 'content' in span:
                                            text_content += span['content'] + " "
            
            elif block.get('type') == 'image':
                # 处理图像块
                text_content = f"[IMAGE: {block.get('image_path', 'unknown')}]"
                
            elif block.get('type') == 'formula':
                # 处理公式块
                if 'latex' in block:
                    text_content = f"[FORMULA: {block['latex']}]"
                    
        except Exception as e:
            self.logger.warning(f"提取文本失败: {str(e)}")
            
        return text_content.strip()

def test_mineru_integration():
    """测试MinerU集成功能"""
    if not MINERU_AVAILABLE:
        print("MinerU不可用，请先安装: pip install magic-pdf[full]")
        return
    
    processor = MinerUProcessor()
    
    # 测试PDF文件路径
    test_pdf = "uploads/test.pdf"  # 替换为实际的测试文件
    
    if os.path.exists(test_pdf):
        layout_data, markdown_content = processor.process_pdf(test_pdf)
        
        if layout_data:
            chunks = processor.convert_to_chunks(layout_data, "test.pdf")
            print(f"成功处理PDF，生成 {len(chunks)} 个文本块")
            
            # 显示前几个chunks
            for i, (text, metadata) in enumerate(chunks[:3]):
                print(f"\nChunk {i+1}:")
                print(f"Text: {text[:100]}...")
                print(f"Metadata: {metadata}")
        else:
            print("PDF处理失败")
    else:
        print(f"测试文件不存在: {test_pdf}")

if __name__ == "__main__":
    test_mineru_integration()
