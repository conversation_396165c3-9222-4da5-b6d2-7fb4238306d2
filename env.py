"""

    Module Name :           env
    Last Modified Date :    7 Jan 2024

"""

# Dataset Upload
default_upload_folder = 'uploads/'
valid_file_extension =set([
    'txt',
    'json',
    'csv',
    'pdf',
    'docx'
])

# config Directory
config_dir = 'config.json'

# User Directories
user_dir_root = 'user/'
user_prefix = 'user-'
user_prof_suffix = '_profile.json'
user_chat_suffix = '_chat.json'

# Preprocessing Directories
preprocessing_root = 'preprocessing/'
chunk_dir = preprocessing_root + 'chunk/'
chunk_config_suffix = '_config.json'
embedding_dir = preprocessing_root + 'embedding/'
embedding_config_suffix = '_config.json'

# Vector Store Directories
vs_dir_root = 'vectorstore/'
vs_config_suffix = '_config.json'
vsdb_log_dir = vs_dir_root + 'vsdb_log.json'
vsschema_config_fmt = '.json'
default_dataset_name = 'Dataset_Woody_Test'

# Prompt Directories
prompt_dir_root = 'prompt/'
prompt_config_dir = prompt_dir_root + 'config/'
# default_prompt_dir = prompt_config_dir + 'default_prompt_config.json'
prompt_tempte_dir = prompt_dir_root + 'template/'
prompt_tempte_fmt = '.json'

# RAG Directories
rag_dir_root = 'rag/'
search_config_dir = rag_dir_root + 'search_config/'
search_config_suffix = '_config.json'

# Model Directories
model_dir_root = 'model/'
model_config_suffix = '_config.json'

# Default Directory
default_dir = 'default/'
default_config_dir = 'default_config.json'
default_vs_dir = 'default_vs_cofig.json' # Default Vector Store Configuration
default_prompt_dir = 'default_prompt_config.json'