import json
import re
import shutil
import subprocess
import base64
from flask import Flask, render_template,send_from_directory, redirect, url_for, flash
import pandas as pd
from werkzeug.security import generate_password_hash, check_password_hash
import warnings
import mixtral_module
from login import save_user_credentials, load_user_credentials
warnings.filterwarnings('ignore')
import env
import prompt_module
from graphstore_module_newui import graphstore_module_newui
import openai_module
import llama_module
import init_interface
from langchain.memory import ChatMessageHistory
import datetime as dt
from uuid import UUID
from flask import jsonify, request
import numpy as np
from langchain_community.document_loaders import DirectoryLoader
import preprocessing
import vectorstore_module_newui
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from openai import OpenAI
import fitz
import os
from typing import List, Dict, Tuple
import pdfplumber
datetime_userid_format= "%Y%m%d-%H-%M-%S"
app = Flask(__name__)
os.makedirs(env.default_upload_folder, exist_ok=True)
app.config['UPLOAD_FOLDER'] = env.default_upload_folder     
app.config['ALLOWED_EXTENSIONS'] = env.valid_file_extension 
app.config['PROCESSED_FOLDER'] = 'processed/'
prompt_template_list = init_interface.load_prompt_template()
model_dict, basemodel_list = init_interface.load_model_template()
config_dir = os.path.join(env.default_dir, env.default_config_dir)
config = pd.read_json(config_dir, typ="series")
chunk_mode = 'chunk-1'
embedding_mode = 'embedding-1'
chunk_method, embedding_method = init_interface.load_preprocessing_config(chunk_mode, embedding_mode)
prompt_dir = os.path.join(env.default_dir, env.default_prompt_dir)
system_prompt_para = pd.read_json(prompt_dir, typ="series")
vs_dir = os.path.join(env.vs_dir_root, "milvus" + env.vs_config_suffix)
vs_config = pd.read_json(vs_dir, typ="series")
dataset_list = vectorstore_module_newui.init_vsdb(vs_config)
print("load dataset")
print(*dataset_list, sep='\n')
vsschema_uid = 'vsschema-6'
schema_config_dir = env.vs_dir_root + vsschema_uid + env.vsschema_config_fmt
schema_config = pd.read_json(schema_config_dir, typ='series')
search_method = 'search-1'
search_config = pd.read_json(env.search_config_dir + \
                             search_method + \
                             env.search_config_suffix, typ="series")
user_id = dt.datetime.now().strftime(datetime_userid_format)
user_fullform = env.user_prefix + user_id
user_dir = os.path.join(env.user_dir_root, user_fullform)
user_history = os.path.join(user_dir, user_fullform + env.user_chat_suffix)
print("user_history: ", user_history)
chat_history = {"dir": None,
                "df": None,
                "langchain": None}
print("chat_history: ", chat_history)
chat_history["langchain"] = ChatMessageHistory()
print("chat_history(langchain)", chat_history["langchain"])
chat_history["dir"] = user_history
if not os.path.exists(chat_history["dir"]):
    chat_history["df"] = pd.DataFrame()
    chat_history["langchain"].add_user_message("")
    chat_history["langchain"].add_ai_message("")
else:
    df = pd.read_json(chat_history["dir"], orient="index")
    chat_history["df"] = df
    for q_msg, a_msg in zip(df["q_msg"].to_list(),
                            df["a_msg"].to_list()):
        chat_history["langchain"].add_user_message(q_msg)
        chat_history["langchain"].add_ai_message(a_msg)
    chat_history["langchain"] = chat_history["langchain"].messages
print("Chat History at Init: " , chat_history)
loaded_dataset = None
loaded_dataset_name = None
loaded_files = []
dataset_list = vectorstore_module_newui.init_vsdb(vs_config)
mixtral_config = r"model/mixtral/mixtral-1_config.json"
llama_config = r"model/llama/llama+rag-1_config.json"
pipeline_endtime = datetime.now()
@app.route('/chat', methods=['GET'])
def index():
    return render_template('index_json_database_new_ui_20.html')
selected_dataset = None 
@app.route('/selected-dataset', methods=['POST'])
def point_selected_dataset():
    global selected_dataset
    dataset = request.get_json()
    selected_dataset = dataset['selectedDataset']
    print(f'Selected Dataset: <{selected_dataset}>')
    return 'Received Dataset Selection'
selected_instrcut = 'role-default'
@app.route('/select-instruct', methods=['POST'])
def select_instruct():
    global selected_instrcut
    data = request.json
    selected_instrcut = data['selectedInstruct']
    print(f'Selected instruct option <{selected_instrcut}>')
    return jsonify({'message': 'Received instruct option: ' + selected_instrcut})

class ImageDescriptionPromptResultResponse(BaseModel):
    entry_id: UUID
    image_name: Optional[str] = None
    prompt_result: Optional[str] = None
    number_of_input_tokens: Optional[int] = None
    number_of_output_tokens: Optional[int] = None
    class Config:
        from_attributes = True
class ImageFileBase64(BaseModel):
    image_file_name:Optional[str] =None
    image_file_bytes_base64str:Optional[str] = None
    image_file_type: Optional[str] = None
    class Config:
        from_attributes = True
class OCRResultResponse(BaseModel):
    ocr_requestid:UUID
    ocr_name: Optional[str]
    ocr_image_results: Optional[list[ImageFileBase64]]
    ocr_text_results:Optional[list[str]]
    class Config:
        from_attributes= True
client = OpenAI(api_key='ollama',base_url="http://*************:30290/v1")
def png_image_to_base64_data_uri(file_path):
    with open(file_path,"rb") as img_file:
        base64_data=base64.b64encode(img_file.read()).decode('utf-8')
        return f"data:image/png;base64,{base64_data}"
def filter_OCR_Result(text):
    text = re.sub(r'\n\n', ' ', text)
    text = re.sub(r'\n', ',', text)  
    return text  
def validate_ocr_output(ocr_text):
    cleaned_text = re.sub(r'^All words in the image:\s*', '', ocr_text.strip())
    number_sequence_pattern = r'^[\d\s]+$'
    single_character_pattern = r'^[\da-zA-Z]$'
    if (re.match(number_sequence_pattern, cleaned_text) or 
        re.match(single_character_pattern, cleaned_text)):
        return None  
    return cleaned_text if cleaned_text else None
def adjust_bbox_to_page(bbox, page_bbox):
    x0, y0, x1, y1 = bbox
    page_x0, page_y0, page_x1, page_y1 = page_bbox
    return (
        max(x0, page_x0),
        max(y0, page_y0),
        min(x1, page_x1),
        min(y1, page_y1)
    )
def detect_images(page, file_name, folder_path="image"):
    OCR_model_name="internvl-2"
    images_extraction = []
    image_descriptions = []
    prev_page_num=-1
    number_of_tokens=0
    number_of_input_tokens = 0
    number_of_output_tokens = 0
    page_image=page.to_image(resolution=200)
    os.makedirs(f"{folder_path}/{file_name}", exist_ok=True)
    for i, image in enumerate(page.images):
        text=None
        prev_page_num=page.page_number
        x0=image['x0']
        invertedy0 = page.height-image['y1']
        y0=invertedy0 if invertedy0 >= 0 and invertedy0 <= page.height else (0 if invertedy0 < 0 else page.height)
        x1=image['x1']
        invertedy1 = page.height-image['y0']
        y1=invertedy1 if invertedy1 >= 0 and invertedy1 <= page.height else (0 if invertedy1 < 0 else page.height)
        bbox = (x0, y0, x1, y1)
        adjusted_bbox = adjust_bbox_to_page(bbox, (0, 0, page.width, page.height))
        cropped_image = page.crop(adjusted_bbox)
        image_object=cropped_image.to_image(resolution=200)
        image_name = f"image-new-{page.page_number}-{i}"
        image_file=f"{folder_path}/{file_name}/{image_name}.png"
        image_object.save(image_file)
        data_uri=png_image_to_base64_data_uri(image_file)
        messages=[
            {"role":"system","content":"you are an assistant who perfectly describes images."},
            {"role":"user","content":[
                {"type":"image_url","image_url":{"url":data_uri}},
                {"type":"text","text":"Extrct the text in this image. The output should be the text content only."}]}
        ]
        response=client.chat.completions.create(model=OCR_model_name,messages=messages)
        num_of_tokens=response.usage.completion_tokens
        if num_of_tokens > 20:
            text=filter_OCR_Result(response.choices[0].message.content)
        print("page number:",page.page_number)
        print("image name:",image_name)
        print("OCRresponse:",response)
        print("OCRnum_of_tokens:",num_of_tokens)
        print("OCRresult:",text)
        number_of_tokens=number_of_input_tokens+number_of_output_tokens
        images_extraction.append({"type": "image", "bbox": bbox ,"id":(page.page_number,i)})
    return images_extraction,image_descriptions,number_of_input_tokens,number_of_output_tokens,number_of_tokens
def clean_nested_table(table):
    clean_table_text = ""
    for row in table:
        for element in row:
            if element is not None and element != "":
                element = element.replace("\n", "")
                clean_table_text += element + " "
    return clean_table_text.strip()
def detect_tables(page,file_name,folder_path="table"):
    table_extraction=[]
    tables = page.find_tables()
    for i, t in enumerate(tables):
        width, height = t.bbox[2] - t.bbox[0], t.bbox[3] - t.bbox[1]
        new_bbox = (
            t.bbox[0] - 0.1 * width,
            t.bbox[1] - 0.1 * height,
            t.bbox[2] + 0.1 * width,
            t.bbox[3] + 0.1 * height
        )
        tables_text = t.extract(x_tolerance=2, y_tolerance=0) 
        tables_text =clean_nested_table(tables_text)
        table_extraction.append({"text":tables_text,"bbox":new_bbox ,"id":(page.page_number,i),"type":"table"})
    return table_extraction  
def detect_drawings_pdf(page,images_bbox_list,table_bbox_list,words_bbox_list):
    flag=False
    if images_bbox_list or table_bbox_list:
        for i_bbox in images_bbox_list: 
            page.draw_rect(fitz.Rect(i_bbox), color=(1,0,0), fill=None)
        for t_bbox in table_bbox_list: 
            page.draw_rect(fitz.Rect(t_bbox), color=(0,0,1), fill=None)
        flag=True
    if words_bbox_list:
        for w_bbox in words_bbox_list: 
            page.draw_rect(fitz.Rect(w_bbox), color=(0,1,0), fill=None)
        flag=True
    return flag
def bbox_intersects(bbox1, bbox2):
    x1, y1, x2, y2 = bbox1
    a1, b1, a2, b2 = bbox2
    return not (x2 < a1 or x1 > a2 or y2 < b1 or y1 > b2)
def get_element_description(element_type: str, element: Dict, image_description: str = None) -> str:
    if element_type == 'image':
        return f"{image_description}"
    elif element_type == 'table':
        return f"{element['text']}"
    else:
        return f"Unknown element type: {element_type}"


def visualize_pdf_chunks(input_pdf_path: str, output_pdf_path: str, detected_elements: List[Dict]):
    doc = fitz.open(input_pdf_path)
    new_doc = fitz.open()
    elements_by_page = {}

    for element in detected_elements:
        page_num = element['page_num']
        if page_num not in elements_by_page:
            elements_by_page[page_num] = []
        elements_by_page[page_num].append(element)

    for page_index in range(len(doc)):
        page = doc[page_index]
        new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
        new_page.show_pdf_page(new_page.rect, doc, page_index)

        if page_index in elements_by_page:
            for element in elements_by_page[page_index]:
                bbox = fitz.Rect(element['bbox'])
                if element['type'] == 'text':
                    color = (0, 0.7, 0)  # 绿色
                elif element['type'] == 'image':
                    color = (1, 0, 0)  # 红色
                elif element['type'] == 'table':
                    color = (0, 0, 1)  # 蓝色
                else:
                    color = (0.5, 0.5, 0.5)  # 灰色（用于未知类型）

                new_page.draw_rect(bbox, color=color, width=1.5)

    new_doc.save(output_pdf_path)
    new_doc.close()
    doc.close()
    print(f"已生成可视化的PDF文件：{output_pdf_path}")
def chunk_pdf_advanced(pdf_path: str, file_dataset: str, chunk_size: int = 800, chunk_overlap: int = 20) -> List[Tuple[str, Dict]]:
    chunks = []
    doc = fitz.open(pdf_path)
    DEFAULT_BBOX=(10000,10000,-1,-1)
    doc_pdfplumber = pdfplumber.open(pdf_path)
    element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}
    file_name = os.path.basename(pdf_path).split('.')[0]
    all_chunk_elements = []

    num_of_input_tokens=0
    num_of_output_tokens=0
    total_num_of_tokens=0
    current_line_number = 1
    current_page_number = 1
    start_time=datetime.now()
    all_detected_elements = []
    for page_index in range(len(doc)):
        page = doc[page_index]
        page_padplumber = doc_pdfplumber.pages[page_index]
        images, image_descriptions,input_tokens,output_tokens,total_tokens = detect_images(page_padplumber, file_name)
        image_bbox_list = [element["bbox"] for element in images]
        for img in images:
            all_detected_elements.append({
                'type': 'image',
                'bbox': img['bbox'],
                'page_num': page_index
            })
        total_num_of_tokens+=total_tokens
        num_of_input_tokens+=input_tokens
        num_of_output_tokens+=output_tokens
        tables = detect_tables(page_padplumber , file_name)
        for table in tables:
            all_detected_elements.append({
                'type': 'table',
                'bbox': table['bbox'],
                'page_num': page_index
            })
        tables_bbox_list = [element["bbox"] for element in tables]
        page_element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}
        words = page.get_text("words")
        words_dict_list = [{"bbox": word[:4], "text": word[4], "type":"word"} for word in words]
        for word in words:
            all_detected_elements.append({
                'type': 'text',
                'bbox': word[:4],
                'page_num': page_index
            })
        components= sorted(images+tables+words_dict_list,key=lambda c: (c["bbox"][3], c["bbox"][0]))
        words_bbox_list=[]
        current_chunk = ""
        current_metadata = {
            "start_page_num": current_page_number,
            "end_page_num": current_page_number,
            "start_line_num": current_line_number,
            "end_line_num": current_line_number,
            "contains_image": False,
            "contains_table": False,
            "contains_chart": False,
            "contains_flowchart": False,
            "contains_confusion_matrix": False,
            "elements": [],
            "bbox": DEFAULT_BBOX,
            "content_type": "text"
        }
        x_left,y_left,x_right,y_right=DEFAULT_BBOX
        last_y = words[0][3] if words else 0
        for index, c in enumerate(components):
            c_type=c["type"]
            if c_type=="word":
                ignore = False
                for image_bbox in image_bbox_list:
                    if bbox_intersects(c["bbox"], image_bbox):
                        ignore = True
                        break
                if not ignore:
                    for table_bbox in tables_bbox_list:
                        if bbox_intersects(c["bbox"], table_bbox):
                            ignore = True
                            break
                if ignore:
                    continue
                word_text = c["text"] + " "
                if abs(c["bbox"][3] - last_y) > 5:
                    current_line_number += 1
                    last_y = c["bbox"][3]
                current_chunk +=word_text
                current_metadata["end_line_num"] =  current_line_number
                x_left,y_left,x_right,y_right = min(x_left, c["bbox"][0]),min(y_left, c["bbox"][1]),max(x_right,c["bbox"][2]),max(y_right,c["bbox"][3])
                current_metadata["bbox"]=(x_left,y_left,x_right,y_right)
                if len(current_chunk)>= chunk_size:
                    all_chunk_elements.append({
                        'type': 'text',
                        'bbox': current_metadata["bbox"],
                        'page_num': page_index
                    })
                    current_metadata["end_page_num"] = current_page_number
                    words_bbox_list.append(current_metadata["bbox"])
                    chunks.append((current_chunk.strip(), current_metadata))
                    x_left,y_left,x_right,y_right=DEFAULT_BBOX
                    current_chunk = " ".join([word for word in current_chunk.strip().rsplit(' ', chunk_overlap+1)[-1-chunk_overlap:-1]])
                    current_metadata = {
                        "start_page_num": current_page_number,
                        "end_page_num": current_page_number,
                        "start_line_num": current_line_number,
                        "end_line_num": current_line_number,
                        "content_type": "text",
                        "bbox": DEFAULT_BBOX,
                        "elements": []
                    }
            else:
                if current_chunk:
                    current_metadata["end_page_num"] = current_page_number
                    words_bbox_list.append(current_metadata["bbox"])
                    x_left,y_left,x_right,y_right=DEFAULT_BBOX
                element_counters[c_type] += 1
                page_element_counters[c_type] +=1
                if c_type == 'image':
                    description = None
                elif c_type == 'table':
                    description = '\n'.join([' | '.join(row) for row in c['text']])
                else:
                    description = None
                element_description = get_element_description(c_type, c, description)
                element_metadata = {
                    "start_page_num": current_page_number,
                    "end_page_num": current_page_number,
                    "start_line_num": current_line_number,
                    "end_line_num": current_line_number,
                    "content_type": c_type,
                    "elements": [c],
                    "bbox": (min(current_metadata["bbox"][0], c["bbox"][0]),min(current_metadata["bbox"][1], c["bbox"][1]), max(current_metadata["bbox"][2], c["bbox"][2]), max(current_metadata["bbox"][3], c["bbox"][3]))
                }
                all_chunk_elements.append({
                    'type': 'text',
                    'bbox': current_metadata["bbox"],
                    'page_num': page_index
                })
                chunks.append((current_chunk.strip() + element_description, element_metadata))
                x_left,y_left,x_right,y_right=DEFAULT_BBOX
                current_chunk = ""
                current_metadata = {
                    "start_page_num": current_page_number,
                    "end_page_num": current_page_number,
                    "start_line_num": current_line_number,
                    "end_line_num": current_line_number,
                    "content_type": "text",
                    "elements": [],
                    "bbox": DEFAULT_BBOX
                }
        if current_chunk:
            all_chunk_elements.append({
                'type': 'text',
                'bbox': current_metadata["bbox"],
                'page_num': page_index
            })
            current_metadata["end_page_num"] = current_page_number
            words_bbox_list.append(current_metadata["bbox"])
            chunks.append((current_chunk.strip(), current_metadata))
            x_left,y_left,x_right,y_right=DEFAULT_BBOX
        current_page_number += 1
        current_line_number = 1
    os.makedirs(f"saved_pdf/{file_dataset}", exist_ok=True)
    output_file_name=f"{os.path.splitext(os.path.basename(pdf_path))[0]}.pdf"
    doc.save(f"saved_pdf/{file_dataset}/{output_file_name}")

    print("total number of tokens (image):",total_num_of_tokens)
    print("number of input tokens (image):",num_of_input_tokens)
    print("number of output tokens (image):",num_of_output_tokens)
    end_time=datetime.now()
    print("Time used: ", end_time-start_time)
    return chunks, element_counters, all_chunk_elements


def compare_bboxes(database_bboxes, visualization_bboxes):
    if len(database_bboxes) != len(visualization_bboxes):
        print(
            f"Warning: Number of bboxes doesn't match. Database: {len(database_bboxes)}, Visualization: {len(visualization_bboxes)}")

    mismatches = []
    for i, (db_bbox, vis_bbox) in enumerate(zip(database_bboxes, visualization_bboxes)):
        # Convert database bbox from string to tuple of floats
        db_bbox = tuple(map(float, db_bbox.strip('()').split(',')))

        # Convert visualization bbox to tuple if it's not already
        if not isinstance(vis_bbox, tuple):
            vis_bbox = tuple(vis_bbox)

        # Compare with a small tolerance for floating point differences
        if not all(abs(db - vis) < 0.001 for db, vis in zip(db_bbox, vis_bbox)):
            mismatches.append((i, db_bbox, vis_bbox))

    if mismatches:
        print(f"Found {len(mismatches)} mismatches:")
        for index, db, vis in mismatches:
            print(f"  Index {index}:")
            print(f"    Database bbox: {db}")
            print(f"    Visualization bbox: {vis}")
    else:
        print("All bboxes match between database and visualization.")

    return len(mismatches) == 0


@app.route('/upload', methods=['POST'])
def upload_file():
    global vs_config
    global selected_dataset
    global schema_config
    global vs_df 
    global user_id
    selected_dataset = request.form.get('selectedDataset', None)
    if not selected_dataset:
        selected_dataset = env.default_dataset_name
        print(f'* No Selected Dataset Name : Default Name <{selected_dataset}> is Used')
    created = vectorstore_module_newui.create_vscollection(
        vs_config,
        selected_dataset,
        schema_config
    )
    graph_store = graphstore_module_newui(selected_dataset)
    print(f'... Uploading the File into the Selected Dataset <{selected_dataset}>')
    uploaded_files = request.files.getlist('file')
    print("uploaded_files: ", uploaded_files)
    if not uploaded_files:
        print('Error: No Uploading Files Found!')
        return jsonify({'status': 'error', 'message': 'No files found for upload'})
    from transformers import AutoTokenizer
    tokenizer = AutoTokenizer.from_pretrained("hkunlp/instructor-large")
    total_tokens = 0
    for file in uploaded_files:
        if file:
            filename = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
            file.save(filename)
            print("saved file")
            if filename.lower().endswith(".pdf"):
                chunks_with_metadata, element_counters, all_chunk_elements = chunk_pdf_advanced(filename, selected_dataset)
                chunked_rawtexts = [chunk for chunk, _ in chunks_with_metadata]
                chunked_metadata = [metadata for _, metadata in chunks_with_metadata]
                # 使用 all_chunk_elements 进行可视化
                output_pdf_path = f"saved_pdf/{selected_dataset}/{os.path.splitext(file.filename)[0]}_visualized.pdf"
                visualize_pdf_chunks(filename, output_pdf_path, all_chunk_elements)
            else:
                loader = DirectoryLoader(app.config['UPLOAD_FOLDER'], show_progress=True, use_multithreading=True)
                document = loader.load()
                print("loaded other type document")
                if not document:
                    print('Warning: Cannot Find Document in Upload Folder')
                chunked_content = preprocessing.chunk_document(document, chunk_method)
                chunked_rawtexts = [chunk.page_content for chunk in chunked_content]
                chunked_metadata = [{}] * len(chunked_rawtexts)  
            for chunk in chunked_rawtexts:
                tokens = tokenizer.encode(chunk)
                total_tokens += len(tokens)
            embedded_vectors = embedding_method.embed_documents(chunked_rawtexts)
            partition_name = ''.join(e for e in file.filename if e.isalnum())
            created = vectorstore_module_newui.create_vspartition(
                vs_config,
                selected_dataset,
                partition_name=partition_name
            )
            id_list = [partition_name + "_" + str(np.int64(i)) for i in range(1, len(chunked_rawtexts) + 1)]
            field_dicts = []
            for i, (chunk_text, metadata, node_id) in enumerate(zip(chunked_rawtexts, chunked_metadata, id_list)):
                chunk_lines = chunk_text.split('\n')
                start_page = metadata['start_page_num']
                end_page = metadata['end_page_num']
                start_line = metadata['start_line_num']
                end_line = metadata['end_line_num']
                bbox = str(metadata['bbox'])
                content_type = metadata['content_type']
                field_dicts.append({
                    "source": partition_name,
                    "extension": file.filename.split('.')[-1],
                    "language": "US",
                    "permission": "3",
                    "date": datetime.now().strftime("%Y-%m-%d, %H:%M:%S"),
                    "uploader": user_id,
                    "start_page_num": start_page,
                    "end_page_num": end_page,
                    "start_line_num": start_line,
                    "end_line_num": end_line,
                    "node_id": node_id,
                    "content_type":content_type,
                    "bbox":bbox,
                })
            print(f'Total embedding input tokens: {total_tokens}')
            # graph_store.insert_document(chunked_rawtexts,field_dicts,id_list, partition_name)
            vectorstore_module_newui.insert_vectors_2(
                vs_config,
                selected_dataset,
                partition_name,
                schema_config,
                chunked_rawtexts,
                embedded_vectors,
                field_dicts
            )
            print("inserted document")
            print(f'Successfully Added the Uploaded File <{file.filename}> into Vector Store')
            new_file = {"dataset": selected_dataset,
                        "filename": file.filename,
                        "chunk_size": len(chunked_rawtexts),
                        "permission_level": 3,
                        "uploader": user_id,
                        "upload_time": datetime.now().strftime("%Y-%m-%d, %H:%M:%S")}

    return jsonify({'status': 'success', 'message': 'File uploaded successfully'})
selected_files = []
@app.route('/selected-files', methods=['POST'])
def point_selected_files():
    global selected_files
    selected_files.clear()  
    data = request.get_json()
    print(data)
    selected_files = data['selectedFiles']  
    selected_files = [f for f in selected_files]
    print('Selected Files:')
    print(*selected_files, sep='\n')
    return jsonify({'status': 'success', 'message': 'Selected files received'})
@app.route('/get-datasets', methods=['GET'])
def get_datasets():
    dataset_list = vectorstore_module_newui.init_vsdb(vs_config)
    print(dataset_list)
    return jsonify(dataset_list)
@app.route('/vectorstore/<filename>', methods=['GET'])
def log_file(filename):
    return send_from_directory('../vectorstore', filename)
@app.route('/get-files', methods=['GET'])
def get_files(vsdb_log_dir=env.vsdb_log_dir):
    global vs_df
    vs_df = pd.read_json(vsdb_log_dir)
    unique_files = vs_df['filename'].unique().tolist()
    return jsonify(unique_files)
@app.route('/delete-file', methods=['POST'])
def delete_file():
    global vs_df
    data = request.get_json()
    del_file = data['file_name']
    selected_dataset = data['dataset']
    print(f"Deleting File: {del_file}")
    del_file = ''.join(e for e in del_file if e.isalnum())
    graph_store = graphstore_module_newui(selected_dataset)
    graph_store.delete_document(del_file)
    vectorstore_module_newui.delete_entities(vs_config,
                                       del_file,
                                       selected_dataset)
    print('Deleted File Entities from Vector Store')
    return jsonify({'status': 'success', 'message': 'File deleted successfully'})
@app.route('/delete-dataset', methods=['POST'])
def delete_dataset():
    global vs_df
    data = request.get_json()
    selected_dataset = data['dataset']
    graph_store = graphstore_module_newui()
    graph_store.delete_dataset(selected_dataset)
    vectorstore_module_newui.delete_collection(vs_config, selected_dataset)
    return jsonify({'message': 'Dataset deleted successfully'})
@app.route('/get-prompt-templates', methods=['GET'])
def get_prompt_templates():
    return jsonify({'prompt_template_list': prompt_template_list})
USER_DATA_DIR = '../user/'
@app.route('/get-all-users-data')
def get_all_users_data():
    all_users_data = {}
    for user_dir in os.listdir(USER_DATA_DIR):
        user_dir_path = os.path.join(USER_DATA_DIR, user_dir)
        if os.path.isdir(user_dir_path):
            user_id = user_dir
            chat_file_path = os.path.join(user_dir_path, f'{user_id}_chat.json')
            try:
                if os.path.exists(chat_file_path):
                    with open(chat_file_path, 'r', encoding='utf-8') as file:
                        user_data = json.load(file)
                        first_entry = next(iter(user_data.values()), None)
                        if first_entry:
                            first_question = first_entry.get('q_msg', 'No question available')
                            question_time = first_entry.get('q_time', 'No time available')
                            all_users_data[user_id] = {
                                'q_msg': first_question,
                                'q_time': question_time,
                            }
                        else:
                            all_users_data[user_id] = {'q_msg': 'No question available', 'q_time': 'No time available'}
            except (IOError, json.JSONDecodeError) as e:
                print(f"Error reading from {chat_file_path}: {e}")
                all_users_data[user_id] = {'q_msg': 'Error loading data', 'q_time': 'Error loading data'}
    return jsonify(all_users_data)
@app.route('/get-user-data/<user_id>')
def get_user_data(user_id):
    user_data_path = os.path.join(USER_DATA_DIR, user_id, f'{user_id}_chat.json')
    if os.path.exists(user_data_path):
        with open(user_data_path, 'r', encoding='utf-8') as file:
            user_data = json.load(file)
            print(user_data)
            return jsonify(user_data)
    else:
        return jsonify({'error': 'User data not found'}), 404
@app.route('/get-user-count', methods=['GET'])
def get_user_count():
    user_dir_path = os.path.join(USER_DATA_DIR)  
    try:
        user_dirs = [name for name in os.listdir(user_dir_path) if os.path.isdir(os.path.join(user_dir_path, name))]
        user_count = len(user_dirs)
        print("user_count: ", user_count)
        return jsonify({'status': 'success', 'user_count': user_count})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})
@app.route('/delete-chat-history', methods=['POST'])
def delete_chat_history():
    data = request.get_json()  
    user_id = data['userId']  
    user_folder_path = os.path.join('../user', str(user_id))
    print("user_folder_path: ", user_folder_path)
    if os.path.exists(user_folder_path):
        try:
            shutil.rmtree(user_folder_path)  
            return jsonify({'message': 'Chat history successfully deleted'}), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500  
    else:
        return jsonify({'message': 'User folder not found'}), 404  
@app.route('/create-new-chat-history', methods=['POST'])
def create_new_chat_history():
    global user_id
    global chat_history
    user_dir_path = os.path.join(os.getcwd(), 'user')  
    if not os.path.exists(user_dir_path):
        os.makedirs(user_dir_path)
    user_dirs = [d for d in os.listdir(user_dir_path) if os.path.isdir(os.path.join(user_dir_path, d))]
    user_id = dt.datetime.now().strftime(datetime_userid_format)
    new_user_dir_name = "user-" + user_id
    new_user_dir_path = os.path.join(user_dir_path, new_user_dir_name)
    os.makedirs(new_user_dir_path)
    new_chat_history_path = os.path.join(new_user_dir_path, f"{new_user_dir_name}_chat.json")
    chat_history["dir"] = new_chat_history_path
    chat_history["df"] = pd.DataFrame()
    chat_history["langchain"] = ChatMessageHistory()
    chat_history["langchain"].add_user_message("")
    chat_history["langchain"].add_ai_message("")
    chat_history["langchain"] = chat_history["langchain"].messages
    print("Chat History at Create: ", chat_history)
    with open(new_chat_history_path, 'w') as file:
        json.dump({}, file)  
    return jsonify({'status': 'success', 'message': f'New chat history for {new_user_dir_name} created successfully.'})
@app.route('/process-selected-chat-history', methods=['POST'])
def process_selected_chat_history():
    data = request.get_json()  
    user_id = data.get('userId')
    return jsonify({'status': 'success', 'message': f'Processed chat history for user ID: {user_id}'})
@app.route('/update-user-session', methods=['POST'])
def update_user_session():
    global user_id
    global user_profile
    global chat_history
    data = request.get_json()
    user_id_str = data.get('userId')
    match = re.search(r'\d+$', user_id_str)
    try:
        user_fullform = user_id_str
        print("user_fullform: ", user_fullform)
        user_dir = os.path.join(env.user_dir_root, user_fullform)
        user_profile = os.path.join(user_dir, user_fullform + env.user_prof_suffix)
        user_history = os.path.join(user_dir, user_fullform + env.user_chat_suffix)
        chat_history = {"dir": None,
                        "df": None,
                        "langchain": None}
        chat_history["langchain"] = ChatMessageHistory()
        chat_history["dir"] = user_history
        if not os.path.exists(chat_history["dir"]):
            chat_history["df"] = pd.DataFrame()
            chat_history["langchain"].add_user_message("")
            chat_history["langchain"].add_ai_message("")
        else:
            df = pd.read_json(chat_history["dir"], orient="index")
            chat_history["df"] = df
            print("chat_history1:", chat_history)
            try:
                for q_msg, a_msg in zip(df["q_msg"].to_list(),
                                        df["a_msg"].to_list()):
                    chat_history["langchain"].add_user_message(q_msg)
                    chat_history["langchain"].add_ai_message(a_msg)
            except KeyError:
                chat_history["langchain"].add_user_message("")
                chat_history["langchain"].add_ai_message("")
        chat_history["langchain"] = chat_history["langchain"].messages
        return jsonify({'status': 'success', 'message': 'User session updated successfully.'})
    except ValueError:
        return jsonify({'status': 'error', 'message': 'Invalid userId format'}), 400
app.secret_key = 'your_secret_key'
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user_credentials = load_user_credentials()
        if username in user_credentials and check_password_hash(user_credentials[username], password):
            flash('Login successful.', 'success')
            return redirect(url_for('index'))  
        elif username not in user_credentials:
            flash('Username does not exist.', 'danger')
        else:
            flash('Incorrect password.', 'danger')
    return render_template('login.html')
@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user_credentials = load_user_credentials()
        if username in user_credentials:
            flash('Username already exists.', 'warning')
        else:
            user_credentials[username] = generate_password_hash(password)
            save_user_credentials(user_credentials)
            flash('Registration successful. Please login.', 'success')
            return redirect(url_for('login'))
    return render_template('register.html')
@app.route('/chat', methods=['POST'])
def chat():
    global selected_dataset
    global user_id
    global chat_history
    print("Chat History at Chat: " , chat_history)
    def allowed_file(filename):
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']
    user_input = request.form.get('user_input', '')
    filename_list=request.form.get('filename_list','')
    print(filename_list)
    mode = request.form.get('mode', 'online')
    model = request.form.get('model', 'mixtral')
    rag = request.form.get('rag', 'off')
    selected_dataset = request.form.get('selectedDataset', '')
    include_history = request.form.get('include_history', 'false') == 'true'
    max_history_no = request.form.get('max_history_no', '3')  
    print(model)
    try:
        max_history_no = int(max_history_no)
    except ValueError:
        max_history_no = 0  
    if model.lower() == "gpt-3.5":
        model = "openai"
    elif model.lower() == "llama":
        if rag == "on":
            model = "llama+rag-1"
        else:
            model = "llama-1"
    elif model.lower() == "mixtral":
        if rag == "on":
            model = "mixtral+rag-1"
        else:
            model = "mixtral-1"
    q_time = datetime.now()
    bot_response = ""
    system_prompt = prompt_module.gen_system_prompt(system_prompt_para) 
    condense_system_prompt = prompt_module.gen_condense_system_prompt(system_prompt_para)
    prompt_df = pd.read_json(env.prompt_tempte_dir + selected_instrcut + '.json')
    prompt_template = '\n'.join(prompt_df["prompt"])
    model_name = model.split('-')[0] 
    prime_model_name = model_name.split('+')[0] 
    prime_model_root = os.path.join(env.model_dir_root, prime_model_name)
    model_config_dir = os.path.join(prime_model_root, model + env.model_config_suffix)
    chunks_list = None
    if not os.path.exists(model_config_dir):
        err_msg = f" Input Error : The Model ID <{model}> could not be Found in {prime_model_root}"
        print(err_msg)
        bot_response = err_msg
    else:
        print("Successfully Retrieved Model Config File")
        model_para = pd.read_json(model_config_dir, typ='series')
    chunk_list = None
    retriever = None
    files_pages_data = None
    if rag == "on":
        if selected_dataset:
            print(f'RAG is ON. Loaded Dataset {selected_dataset}')
            retriever, chunks_list = vectorstore_module_newui.get_retriever3(vs_config, embedding_method, selected_dataset, selected_files, search_config, user_input)
            files_pages_data={}
            for chunk in chunks_list:
                filename = chunk['source']
                filename = filename.replace('pdf', '.pdf')
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                page_number = chunk['page_num']
                bbox = chunk['bbox']
                content_type = chunk['content_type']
                if filename not in files_pages_data:
                    files_pages_data[filename]={}
                if page_number not in files_pages_data[filename]:
                    files_pages_data[filename][page_number]=[]
                files_pages_data[filename][page_number].append({"bbox":bbox,"content_type":content_type})
        else:
            print(f'Error : RAG is ON, but Cannot Find Dataset')
            retriever = None
    else:
        print('RAG is OFF')
        retriever = None
    print('LLM Model Selected :')
    print(f'- Model UID : {model_para["uid"]}')
    print(f'- Base Model : {model_para["model"]}')
    print(f'- RAG : {retriever is not None}')
    if include_history:
        print("max_history_no: ", max_history_no)
        print("chat_history: ", chat_history["langchain"])
        print("len chat_history: ", len(chat_history["langchain"])/2)
        chat_record = chat_history["langchain"]
        if len(chat_history["langchain"]) - 2 > max_history_no:
            n_pairs = max_history_no * 2
            chat_record = chat_history["langchain"][-n_pairs:]
            print("chat_record:", chat_record)
    else:
        chat_record = None
    if prime_model_name.lower() == "llama":
        bot_response = llama_module.llama_response(user_input,
                                    system_prompt,
                                    condense_system_prompt,
                                    prompt_template,
                                    model_para,
                                    retriever,
                                    chat_record)
    elif prime_model_name.lower() == "openai":
        bot_response = openai_module.openai_response(user_input,
                                     system_prompt,
                                     condense_system_prompt,
                                     prompt_template,
                                     model_para,
                                     retriever,
                                     chat_record)
    elif prime_model_name.lower() == "mixtral":
        bot_response = mixtral_module.mixtral_response(
                                     user_input,
                                     system_prompt,
                                     condense_system_prompt,
                                     prompt_template,
                                     model_para,
                                     retriever,
                                     chat_record)
    a_time = datetime.now()
    new_chat = pd.DataFrame([{
        "q_msg": user_input,
        "a_msg": bot_response,
        "q_time": q_time.strftime("%Y-%m-%d %H:%M:%S"),
        "a_time": a_time.strftime("%Y-%m-%d %H:%M:%S"),
        "llm": model,
        "similarity": 1,
        "rating": 3,
        "chunks_list": files_pages_data,
    }])
    chat_history["df"] = pd.concat([chat_history["df"], new_chat], ignore_index=True)
    chat_history["df"].to_json(chat_history["dir"], orient="index", indent=4)
    new_chat = ChatMessageHistory()
    new_chat.add_user_message(user_input)
    new_chat.add_ai_message(bot_response)
    chat_history["langchain"] += new_chat.messages
    return jsonify({'response': bot_response, 'chunk_list': files_pages_data})
@app.route('/processed/<filename>')
def Get_file(filename):
    return send_from_directory(app.config['PROCESSED_FOLDER'], filename)
    data = request.get_json()
    list_of_chunks = data['chunk']
    files_pages_data={}
    for chunk in list_of_chunks:
        filename = chunk['source']
        filename = filename.replace('pdf', '.pdf')
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        page_number = chunk['page_num']
        bbox = chunk['bbox']
        content_type = chunk['content_type']
        if filename not in files_pages_data:
            files_pages_data[filename]={}
        if page_number not in files_pages_data[filename]:
            files_pages_data[filename][page_number]=[]
        files_pages_data[filename][page_number].append({"bbox":bbox,"content_type":content_type})
    print("Extracted Files, Page Numbers, and Bounding Boxes:", files_pages_data)
    return jsonify({'response': "finish processing chunk", 'data_extracted': files_pages_data})
@app.route('/draw_file', methods=['GET', 'POST'])
def draw_bbox():
    data = request.get_json()
    pages_data = data['pages_data']
    filename=data['filename']
    processed_files = []
    new_doc = fitz.open()  
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    doc = fitz.open(file_path) 
    for page_number, bboxes_data in pages_data.items():
        page_number=int(page_number)
        page = doc[page_number - 1]  
        for bbox_data in bboxes_data:
            content_type = bbox_data['content_type']
            bbox_coords = bbox_data['bbox']
            if content_type == "text":
                page.draw_rect(fitz.Rect(bbox_coords), color=(0, 1, 0), fill=None)
            elif content_type == "image":
                page.draw_rect(fitz.Rect(bbox_coords), color=(1, 0, 0), fill=None)
            elif content_type == "table":
                page.draw_rect(fitz.Rect(bbox_coords), color=(0, 0, 1), fill=None)
        temp_doc = fitz.open()
        temp_doc.insert_pdf(doc, from_page=page_number - 1, to_page=page_number - 1)
        new_doc.insert_pdf(temp_doc)
    os.makedirs("../processed", exist_ok=True)
    processed_file_path = os.path.join("../processed", filename)
    new_doc.save(processed_file_path)
    return jsonify({'response': "finish drawing bboxes", 'processed_file_path': processed_file_path})
@app.route('/')
def root():
    return redirect(url_for('chat'))
import psutil
def terminate_ollama_processes():
    for process in psutil.process_iter(['pid', 'name']):
        if process.info['name'] == 'ollama.exe':
            process.terminate()  
            process.wait()  
def start_ollama_service():
    base_path = os.path.dirname(os.path.abspath(__file__))
    model_path = os.path.join(base_path, 'ollama_models')
    os.environ['OLLAMA_MODELS'] = model_path
    subprocess.Popen(['ollama', 'serve'], creationflags=subprocess.CREATE_NO_WINDOW)
def is_ollama_running():
    for process in psutil.process_iter(['name']):
        if process.info['name'] == 'ollama.exe':
            return True
    return False
def try_start_ollama():
    ollama_path = os.path.join('C:', 'Program Files', 'Ollama', 'ollama.exe')  
    try:
        subprocess.Popen([ollama_path], creationflags=subprocess.CREATE_NO_WINDOW)
        return True
    except Exception as e:
        print(f"Failed to start ollama.exe: {e}")
        return False
if __name__ == '__main__':
    if __name__ == '__main__':
        app.debug = True
        app.run(host='0.0.0.0', port=8033)