"""

    Module Name :           chatbot
    Last Modified Date :    23 Jan 2024

"""
import json
import shutil

from flask import Flask, render_template, request, jsonify, send_from_directory
import os
import pandas as pd
from langchain.memory import ChatMessageHistory
from langchain.document_loaders import DirectoryLoader
from datetime import datetime

from langchain_community.llms.ollama import Ollama
from werkzeug.utils import secure_filename
from datetime import datetime
import torch

import warnings

import mixtral_module_generate
import mixtral_module_llamaindex
import mixtral_module_org
import test_module_2

warnings.filterwarnings('ignore')

# Import Self-defined Libraries
import env
import preprocessing
import prompt_module
import vectorstore_module_lite
import openai_module
import llama_module
import mixtral_module
import abandoned_instruct_mixtral_module
import init_interface
import test_module

app = Flask(__name__)

# File Upload Configuration
os.makedirs(env.default_upload_folder, exist_ok=True)
app.config['UPLOAD_FOLDER'] = env.default_upload_folder     # save all files in the default folder
app.config['ALLOWED_EXTENSIONS'] = env.valid_file_extension # valid file formats

""" Temp """
prompt_template_list = init_interface.load_prompt_template()
model_dict, basemodel_list = init_interface.load_model_template()

""" Inititalization """
# Retrieval of Default Configuration
config_dir = os.path.join(env.default_dir, env.default_config_dir)
config = pd.read_json(config_dir, typ="series")

# Retriveval of Preprocessing Configuration
chunk_mode = 'chunk-Alan'
embedding_mode = 'embedding-1'
chunk_method, embedding_method = init_interface.load_preprocessing_config(chunk_mode, embedding_mode)

# Retrieval of System Prompt Parameters
prompt_dir = os.path.join(env.default_dir, env.default_prompt_dir)
print(prompt_dir)
system_prompt_para = pd.read_json(prompt_dir, typ="series")

# Retrieval of Vector Store Configuration
vs_dir = os.path.join(env.vs_dir_root, "milvus" + env.vs_config_suffix)
vs_config = pd.read_json(vs_dir, typ="series")

dataset_list = vectorstore_module.init_vsdb(vs_config)
print(*dataset_list, sep='\n')


# *** To convert to input parameter
vsschema_uid = 'vsschema-1'
schema_config_dir = env.vs_dir_root + vsschema_uid + env.vsschema_config_fmt
schema_config = pd.read_json(schema_config_dir, typ='series')


# Retrieval RAG Configuration
search_method = 'search-1'
search_config = pd.read_json(env.search_config_dir + \
                             search_method + \
                             env.search_config_suffix, typ="series")


# Retrieval of User Information
""" *** Refine Later >>> Build simple user authentication function """
raw_uid = 0o03
uid_digit = config["uid_digit"]
user_id = f"%0{uid_digit}d" %raw_uid

# User Authentication
user_fullform = env.user_prefix + str(user_id)
user_dir = os.path.join(env.user_dir_root, user_fullform)

# Retrieval of User Profile
user_profile = os.path.join(user_dir, user_fullform + env.user_prof_suffix)

# Retrieval of User Chat History
user_history = os.path.join(user_dir, user_fullform + env.user_chat_suffix)
chat_history = {"dir": None,
                "df": None,
                "langchain": None}

chat_history["langchain"] = ChatMessageHistory()
chat_history["dir"] = user_history
if not os.path.exists(chat_history["dir"]):
    chat_history["df"] = pd.DataFrame()
    chat_history["langchain"].add_user_message("")
    chat_history["langchain"].add_ai_message("")

else:
    df = pd.read_json(chat_history["dir"], orient="index")
    chat_history["df"] = df

    # Convert Chat History to langchain Format
    for q_msg, a_msg in zip(df["q_msg"].to_list(),
                            df["a_msg"].to_list()):
        chat_history["langchain"].add_user_message(q_msg)
        chat_history["langchain"].add_ai_message(a_msg)
        # print("chat_history:", chat_history)
        # print("chat_history[langchain]:", chat_history["langchain"])

    chat_history["langchain"] = chat_history["langchain"].messages

# Init Collection
loaded_dataset = None
loaded_dataset_name = None
loaded_files = []


# ***** Retrival of stroed collections and documents
#  {'dataset_name': 'Dataset_Woody_Test',
#  'document_list': ['kyototxt', 'alicetxt']}]
dataset_list = vectorstore_module.init_vsdb(vs_config)

# initate llm pipeline
torch.set_default_tensor_type(torch.cuda.HalfTensor)
mixtral_config = r"model/mixtral/mixtral-1_config.json"
pipeline_starttime = datetime.now()
# mixtral_pipeline = mixtral_module.init_mixtral_piepeline(pd.read_json(mixtral_config, typ='series'))
# mixtral_pipeline = mixtral_module_org.init_mixtral_piepeline_2()
llama_config = r"model/llama/llama+rag-1_config.json"
# llama_pipeline = llama_module.init_llama_piepeline(pd.read_json(llama_config, typ='series'))
pipeline_endtime = datetime.now()
print(f"Pipeline Loading Time = {pipeline_endtime-pipeline_starttime}")

# print(mixtral_pipeline)
""" Chatbot """
@app.route('/', methods=['GET'])
def index():
    # return render_template('index.html')
    return render_template('index_json_database_new_ui_11.html')

selected_dataset = None # dataset storing files
@app.route('/selected-dataset', methods=['POST'])
def point_selected_dataset():
    global selected_dataset
    dataset = request.get_json()
    selected_dataset = dataset['selectedDataset']
    print(f'Selected Dataset: <{selected_dataset}>')
    return 'Received Dataset Selection'

selected_instrcut = 'role-default'
@app.route('/select-instruct', methods=['POST'])
def select_instruct():
    global selected_instrcut
    data = request.json

    selected_instrcut = data['selectedInstruct']
    print(f'Selected instruct option <{selected_instrcut}>')
    return jsonify({'message': 'Received instruct option: ' + selected_instrcut})


@app.route('/upload', methods=['POST'])
def upload_file():
    global vs_config
    global selected_dataset
    global schema_config
    global vs_df # log file for vector store

    selected_dataset = request.form.get('selectedDataset', None)


    if not selected_dataset:
        selected_dataset = env.default_dataset_name
        print(f'* No Selected Dataset Name : Default Name <{selected_dataset}> is Used')

    # Create Collection
    # Return <True> if created; Return <False> if Collection Name Already Used
    created = vectorstore_module.create_vscollection(
        vs_config,
        selected_dataset,
        schema_config
    )

    print(f'... Uploading the File into the Selected Dataset <{selected_dataset}>')
    # upload_files = request.files['file']
    uploaded_files = request.files.getlist('file')
    print("uploaded_files: ", uploaded_files)

    if not uploaded_files:
        print('Error: No Uploading Files Found!')
        return jsonify({'status': 'error', 'message': 'No files found for upload'})

    for file in uploaded_files:
        if file:
            # Copy File to Upload Folder
            filename = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
            file.save(filename)

            loader = DirectoryLoader(app.config['UPLOAD_FOLDER'])
            document = loader.load()
            if not document:
                print('Warning: Cannot Find Document in Upload Folder')

            # Split content into Different Chunks
            chunked_content = preprocessing.chunk_document(document,
                                                           chunk_method)
            chunked_rawtexts = [chunk.page_content for chunk in chunked_content]
            embedded_vectors = embedding_method.embed_documents(chunked_rawtexts)

            # Create Parition
            # partition_name = file.filename.replace(".","").replace(" ", "").replace("-", "")
            partition_name = ''.join(e for e in file.filename if e.isalnum())
            created = vectorstore_module.create_vspartition(
                vs_config,
                selected_dataset,
                partition_name=partition_name
            )

            # Insert Vectors into Vector Store
            vectorstore_module.insert_vectors(
                vs_config,
                selected_dataset,
                partition_name,
                schema_config,
                chunked_rawtexts,
                embedded_vectors,
                field_dict={
                    "source": file.filename,
                    "extension": file.filename.split('.')[-1],
                    "language": "US", # *** To be converted as an input
                    "permission": 3,   # *** To be converted as an input
                    "date": datetime.now().strftime("%Y-%m-%d, %H:%M:%S"),
                    "uploader": user_id
                }
            )

            # # Generate metadata for vector store
            # chunks_with_metadata = vectorstore_module.gen_metadata(file.filename, chunked_content)

            # # Add/Create Vector Store
            # # dataset name = collection name
            # vectorstore_module.add_to_collection(vs_config,
            #                                      chunks_with_metadata,
            #                                      embedding_method,
            #                                      selected_dataset)

            print(f'Successfully Added the Uploaded File <{file.filename}> into Vector Store')

            # Update log file for vector store
            new_file = {"dataset": selected_dataset,
                        "filename": file.filename,
                        "chunk_size": len(chunked_content),
                        "permission_level": 3,
                        "uploader": user_id,
                        "uplaod_time": datetime.now().strftime("%Y-%m-%d, %H:%M:%S")}

            vs_df = vectorstore_module.update_vs_df(new_file)

            # Remove File after Storing into Vector Store
            os.remove(filename)
            print(f'Deleted File <{file.filename}> from Upload Folder')
        # try:
        #     for filename in os.listdir(app.config['UPLOAD_FOLDER']):
        #         file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        #         if os.path.isfile(file_path) or os.path.islink(file_path):
        #             os.unlink(file_path)
        #         elif os.path.isdir(file_path):
        #             shutil.rmtree(file_path)
        #     print(f'Cleared temporary upload folder {app.config["UPLOAD_FOLDER"]}')
        # except Exception as e:
        #     print(f'Error occurred while clearing temporary upload folder: {e}')

    return jsonify({'status': 'success', 'message': 'File uploaded successfully'})

selected_files = []
@app.route('/selected-files', methods=['POST'])
def point_selected_files():
    global selected_files
    selected_files.clear()  # clear global_file_list

    data = request.get_json()
    selected_files = data['selectedFiles']  # new files in the global_file_list
    print('Selected Files:')
    print(*selected_files, sep='\n')

    return jsonify({'status': 'success', 'message': 'Selected files received'})

@app.route('/get-datasets', methods=['GET'])
def get_datasets():
    data = pd.read_json(env.vsdb_log_dir)
    return jsonify(data)

@app.route('/vectorstore/<filename>', methods=['GET'])
def log_file(filename):
    return send_from_directory('../vectorstore', filename)

@app.route('/get-files', methods=['GET'])
def get_files(vsdb_log_dir=env.vsdb_log_dir):
    global vs_df
    # Load and return the list of files from file
    vs_df = pd.read_json(vsdb_log_dir)
    unique_files = vs_df['filename'].unique().tolist()
    return jsonify(unique_files)

@app.route('/delete-file', methods=['POST'])
def delete_file():
    global vs_df
    data = request.get_json()
    del_file = data['file_name']
    selected_dataset = data['dataset']
    # Add logic to delete the file from  server
    # os.remove(os.path.join(UPLOAD_FOLDER, file_to_delete))
    print(f"Deleting File: {del_file}")

    # ***** Newly Added
    del_file = ''.join(e for e in del_file if e.isalnum())

    vectorstore_module.delete_entities(vs_config,
                                       del_file,
                                       selected_dataset)
    print('Deleted File Entities from Vector Store')

    vs_df = vectorstore_module.delete_vs_df(data['file_name'])
    print('Deleted File from File Log')
    return jsonify({'status': 'success', 'message': 'File deleted successfully'})

@app.route('/delete-dataset', methods=['POST'])
def delete_dataset():
    global vs_df
    data = request.get_json()
    selected_dataset = data['dataset']
    vectorstore_module.delete_collection(vs_config, selected_dataset)
    return jsonify({'message': 'Dataset deleted successfully'})


@app.route('/get-prompt-templates', methods=['GET'])
def get_prompt_templates():
    return jsonify({'prompt_template_list': prompt_template_list})

USER_DATA_DIR = '../user/'

@app.route('/get-all-users-data')
def get_all_users_data():
    all_users_data = {}
    for user_dir in os.listdir(USER_DATA_DIR):
        user_dir_path = os.path.join(USER_DATA_DIR, user_dir)
        if os.path.isdir(user_dir_path):
            user_id = user_dir
            chat_file_path = os.path.join(user_dir_path, f'{user_id}_chat.json')
            if os.path.exists(chat_file_path):
                with open(chat_file_path, 'r', encoding='utf-8') as file:
                    user_data = json.load(file)
                    first_entry = next(iter(user_data.values())) if user_data else {}
                    first_question = first_entry.get('q_msg', 'No question available') if first_entry else 'No question available'
                    all_users_data[user_id] = first_question
    return jsonify(all_users_data)

@app.route('/get-user-data/<user_id>')
def get_user_data(user_id):
    user_data_path = os.path.join(USER_DATA_DIR, user_id, f'{user_id}_chat.json')
    if os.path.exists(user_data_path):
        with open(user_data_path, 'r', encoding='utf-8') as file:
            user_data = json.load(file)
            return jsonify(user_data)
    else:
        return jsonify({'error': 'User data not found'}), 404

@app.route('/chat', methods=['POST'])
def chat():
    global selected_dataset

    # Check file extension
    def allowed_file(filename):
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

    # User Input
    user_input = request.form.get('user_input', '')
    mode = request.form.get('mode', 'online')
    model = request.form.get('model', 'gpt-3.5')
    rag = request.form.get('rag', 'off')
    selected_dataset = request.form.get('selectedDataset', '')
    include_history = request.form.get('include_history', 'false') == 'true'
    max_history_no = request.form.get('max_history_no', '7')  # Get the value as a string, providing

    # Convert max_history_no to integer, using 0 as default if conversion fails
    try:
        max_history_no = int(max_history_no)
    except ValueError:
        max_history_no = 0  # Fallback value if conversion fails

    # *** Refine Model Name on UI Later
    if model.lower() == "gpt-3.5":
        model = "openai"
    elif model.lower() == "llama":
        if rag == "on":
            model = "llama+rag-1"
        else:
            model = "llama-1"
    elif model.lower() == "mixtral":
        if rag == "on":
            model = "mixtral+rag-1"
        else:
            model = "mixtral-1"

    q_time = datetime.now()

    # Initialize bot_response to an empty string or a default value
    bot_response = ""

    # Prompt Engineering
    system_prompt = prompt_module.gen_system_prompt(system_prompt_para) # Using default values
    condense_system_prompt = prompt_module.gen_condense_system_prompt(system_prompt_para)
    prompt_df = pd.read_json(env.prompt_tempte_dir + "role-default" + '.json')
    prompt_template = '\n'.join(prompt_df["prompt"])

    # Model Retrieval
    model_name = model.split('-')[0] # Split Model Number
    prime_model_name = model_name.split('+')[0] # Split + for RAG
    prime_model_root = os.path.join(env.model_dir_root, prime_model_name)
    model_config_dir = os.path.join(prime_model_root, model + env.model_config_suffix)

    # Check if Model Config File Exists
    if not os.path.exists(model_config_dir):
        err_msg = f" Input Error : The Model ID <{model}> could not be Found in {prime_model_root}"
        print(err_msg)
        bot_response = err_msg

    # Retrieve Model Config File
    else:
        print("Successfully Retrieved Model Config File")
        model_para = pd.read_json(model_config_dir, typ='series')



    # Get retriever for RAG; Return <None> if RAG is OFF
    retriever = None
    if rag == "on":
        if selected_dataset:
            print(f'RAG is ON. Loaded Dataset {selected_dataset}')
            retriever = vectorstore_module.get_retriever2(vs_config,
                                                         embedding_method,
                                                         selected_dataset,
                                                         selected_files,
                                                         search_config)
            # retriever = vectorstore_module.get_retriever(vs_config,
            #                                              embedding_method,
            #                                              selected_dataset,
            #                                              search_config)
        else:
            print(f'Error : RAG is ON, but Cannot Find Dataset')
            retriever = None
    else:
        print('RAG is OFF')
        retriever = None

    # # Retrieval of Files for RAG Vector Store
    # # Find directory path for RAG; Return <None> if RAG is Off
    # if rag == "on":

    #     # Check whether Files are Selected for RAG
    #     if selected_dataset:
    #         print(f'RAG is ON. Loaded Dataset {selected_dataset}')
    #         rag_dataset = selected_dataset
    #     else:
    #         print(f'Error : RAG is ON, but Cannot Find Dataset')
    #         rag_dataset = []

    # else:
    #     rag_dataset = []

    # Generate AI Response
    print('LLM Model Selected :')
    print(f'- Model UID : {model_para["uid"]}')
    print(f'- Base Model : {model_para["model"]}')
    print(f'- RAG : {retriever is not None}')

    if include_history:
        print("max_history_no: ", max_history_no)
        print("chat_history: ", chat_history["langchain"])
        print("len chat_history: ", len(chat_history["langchain"])/2)
        chat_record = chat_history["langchain"]
        if len(chat_history["langchain"]) - 2 > max_history_no:
            n_pairs = max_history_no * 2
            chat_record = chat_history["langchain"][-n_pairs:]
            print("chat_record:", chat_record)

    else:
        chat_record = None

    if prime_model_name.lower() == "llama":
        bot_response = llama_module.llama_response(user_input,
                                    system_prompt,
                                    condense_system_prompt,
                                    prompt_template,
                                    model_para,
                                    retriever,
                                    chat_record)

    elif prime_model_name.lower() == "openai":
        bot_response = openai_module.openai_response(user_input,
                                     system_prompt,
                                     condense_system_prompt,
                                     prompt_template,
                                     model_para,
                                     retriever,
                                     chat_record)

    elif prime_model_name.lower() == "mixtral":
        bot_response = mixtral_module.mixtral_response(
                                     user_input,
                                     system_prompt,
                                     condense_system_prompt,
                                     prompt_template,
                                     model_para,
                                     retriever,
                                     chat_record)

    # elif prime_model_name.lower() == "instructmixtral":
    #     bot_response = instruct_mixtral_module.mixtral_response(user_input,
    #                                  system_prompt,
    #                                  condense_system_prompt,
    #                                  prompt_template,
    #                                  model_para,
    #                                  mixtral_pipeline,
    #                                  retriever,
    #                                  chat_history["langchain"])
    # Ball sir output json file
    # test_module_2.output_report(bot_response)

    a_time = datetime.now()

    # Update Chat History
    new_chat = pd.DataFrame([{
        "q_msg": user_input,
        "a_msg": bot_response,
        "q_time": q_time.strftime("%Y-%m-%d, %H:%M:%S"),
        "a_time": a_time.strftime("%Y-%m-%d, %H:%M:%S"),
        "llm": model,
        "similarity": 1,
        "rating": 3
    }])

    chat_history["df"] = pd.concat([chat_history["df"], new_chat], ignore_index=True)
    chat_history["df"].to_json(chat_history["dir"], orient="index", indent=4)

    new_chat = ChatMessageHistory()
    new_chat.add_user_message(user_input)
    new_chat.add_ai_message(bot_response)
    chat_history["langchain"] += new_chat.messages

    return jsonify({'response': bot_response})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8011)