#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug虚拟环境输出
查看MinerU脚本的实际输出
"""

import os
import subprocess
import tempfile

def debug_venv_script_output():
    """调试虚拟环境脚本输出"""
    print("🔍 调试虚拟环境脚本输出")
    print("=" * 50)
    
    venv_path = os.path.join(os.getcwd(), "mineru_venv")
    if not os.path.exists(venv_path):
        print("❌ 虚拟环境不存在")
        return
    
    python_path = os.path.join(venv_path, 'bin', 'python') if os.name != 'nt' else os.path.join(venv_path, 'Scripts', 'python.exe')
    
    # 创建一个简单的测试PDF
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f:
        pdf_content = b"""%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj
3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Contents 4 0 R>>endobj
4 0 obj<</Length 44>>stream
BT/F1 12 Tf 72 720 Td(Hello MinerU!)Tj ET
endstream endobj
xref 0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer<</Size 5/Root 1 0 R>>
startxref 300
%%EOF"""
        f.write(pdf_content)
        test_pdf_path = f.name
    
    output_dir = tempfile.mkdtemp()
    
    # 创建测试脚本
    script_content = f'''
import sys
import json
import os
from pathlib import Path

print("=== SCRIPT START ===")
print("DEBUG: 脚本开始执行")

try:
    print("DEBUG: 尝试导入MinerU...")
    
    # 检查是否能导入mineru.api
    try:
        from mineru.api import parse_doc
        print("DEBUG: ✅ 成功导入 mineru.api.parse_doc")
        api_available = True
    except ImportError as e:
        print(f"DEBUG: ❌ 导入 mineru.api 失败: {{e}}")
        api_available = False
    
    # 检查基础模块
    try:
        import magic_pdf
        print("DEBUG: ✅ 基础 magic_pdf 模块可用")
        base_available = True
    except ImportError as e:
        print(f"DEBUG: ❌ 基础 magic_pdf 模块不可用: {{e}}")
        base_available = False
    
    if not api_available and not base_available:
        print("ERROR")
        print("无法导入任何MinerU模块")
        sys.exit(1)
    
    pdf_path = r"{test_pdf_path}"
    output_dir = r"{output_dir}"
    
    print(f"DEBUG: PDF路径: {{pdf_path}}")
    print(f"DEBUG: 输出目录: {{output_dir}}")
    print(f"DEBUG: PDF文件存在: {{os.path.exists(pdf_path)}}")
    
    if api_available:
        print("DEBUG: 使用真实的MinerU API...")
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            print("DEBUG: 调用 parse_doc...")
            parse_doc(
                path_list=[Path(pdf_path)],
                output_dir=output_dir,
                lang="ch",
                backend="pipeline",
                method="auto"
            )
            print("DEBUG: parse_doc 调用完成")
            
            # 查找输出文件
            output_files = []
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    output_files.append(os.path.join(root, file))
            
            print(f"DEBUG: 找到输出文件: {{output_files}}")
            
            # 查找middle.json文件
            middle_json_files = [f for f in output_files if f.endswith('_middle.json')]
            
            if middle_json_files:
                middle_json_path = middle_json_files[0]
                print(f"DEBUG: 读取middle.json: {{middle_json_path}}")
                
                with open(middle_json_path, 'r', encoding='utf-8') as f:
                    middle_json = json.load(f)
                
                print("SUCCESS")
                print(json.dumps(middle_json, ensure_ascii=False, default=str))
            else:
                print("PARTIAL_SUCCESS")
                print(json.dumps({{"message": "MinerU处理完成但未找到middle.json", "files": output_files}}, ensure_ascii=False))
                
        except Exception as api_e:
            print(f"DEBUG: API调用失败: {{api_e}}")
            print("ERROR")
            print(str(api_e))
    else:
        print("DEBUG: 使用基础模块创建模拟结果...")
        
        # 创建模拟结果
        mock_result = {{
            "pdf_info": [
                {{
                    "page_idx": 0,
                    "preproc_blocks": [
                        {{
                            "type": "text",
                            "bbox": [0, 0, 500, 50],
                            "lines": [
                                {{
                                    "spans": [
                                        {{
                                            "type": "text",
                                            "content": "基础模块模拟结果 - Hello MinerU!"
                                        }}
                                    ]
                                }}
                            ]
                        }}
                    ]
                }}
            ]
        }}
        
        print("SUCCESS")
        print(json.dumps(mock_result, ensure_ascii=False))

except Exception as e:
    print(f"DEBUG: 脚本执行出错: {{e}}")
    print("ERROR")
    print(str(e))
    import traceback
    print("TRACEBACK:")
    traceback.print_exc()

print("=== SCRIPT END ===")
'''
    
    # 写入临时脚本
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
        f.write(script_content)
        script_path = f.name
    
    try:
        print(f"🧪 执行测试脚本: {script_path}")
        print(f"📄 测试PDF: {test_pdf_path}")
        print(f"📁 输出目录: {output_dir}")
        
        result = subprocess.run([python_path, script_path], 
                              capture_output=True, text=True, timeout=120)
        
        print(f"\n📊 执行结果:")
        print(f"返回码: {result.returncode}")
        print(f"标准输出长度: {len(result.stdout)}")
        print(f"标准错误长度: {len(result.stderr)}")
        
        print(f"\n📋 标准输出:")
        print("=" * 50)
        print(result.stdout)
        
        if result.stderr:
            print(f"\n⚠️  标准错误:")
            print("=" * 50)
            print(result.stderr)
        
        # 分析输出
        lines = result.stdout.strip().split('\n')
        print(f"\n🔍 输出分析:")
        print(f"总行数: {len(lines)}")
        
        for i, line in enumerate(lines):
            if line.strip() in ["SUCCESS", "FAILED", "ERROR", "PARTIAL_SUCCESS"]:
                print(f"找到状态行 {i}: {line}")
                if i + 1 < len(lines):
                    print(f"后续内容: {lines[i+1][:100]}...")
        
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
    finally:
        # 清理
        try:
            os.unlink(test_pdf_path)
            os.unlink(script_path)
        except:
            pass

def main():
    """主函数"""
    print("🔍 虚拟环境输出调试")
    print("=" * 50)
    
    debug_venv_script_output()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  调试被用户中断")
    except Exception as e:
        print(f"\n❌ 调试过程出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
