#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查真实的MinerU API
找出正确的调用方式
"""

import os
import subprocess
import sys

def check_mineru_structure():
    """检查MinerU的真实结构"""
    print("🔍 检查虚拟环境中MinerU的真实结构")
    print("=" * 50)
    
    venv_path = os.path.join(os.getcwd(), "mineru_venv")
    if not os.path.exists(venv_path):
        print("❌ 虚拟环境不存在")
        return
    
    python_path = os.path.join(venv_path, 'bin', 'python') if os.name != 'nt' else os.path.join(venv_path, 'Scripts', 'python.exe')
    
    # 检查MinerU的真实结构
    check_code = '''
import sys
import os

print("🔍 检查MinerU包结构...")

try:
    import magic_pdf
    print(f"✅ magic_pdf 导入成功")
    print(f"📁 magic_pdf 路径: {magic_pdf.__file__}")
    
    # 检查magic_pdf目录结构
    magic_pdf_dir = os.path.dirname(magic_pdf.__file__)
    print(f"📁 magic_pdf 目录: {magic_pdf_dir}")
    
    if os.path.exists(magic_pdf_dir):
        print("📋 magic_pdf 目录内容:")
        for item in sorted(os.listdir(magic_pdf_dir)):
            item_path = os.path.join(magic_pdf_dir, item)
            if os.path.isdir(item_path):
                print(f"  📁 {item}/")
                # 检查子目录
                try:
                    sub_items = os.listdir(item_path)[:5]  # 只显示前5个
                    for sub_item in sub_items:
                        print(f"    📄 {sub_item}")
                    if len(os.listdir(item_path)) > 5:
                        print(f"    ... 还有 {len(os.listdir(item_path)) - 5} 个文件")
                except:
                    pass
            else:
                print(f"  📄 {item}")
    
    print("\\n🔍 尝试导入各种API...")
    
    # 尝试各种可能的导入
    apis_to_test = [
        "magic_pdf.api",
        "magic_pdf.api.magic_pdf_parse", 
        "magic_pdf.pipe",
        "magic_pdf.pipe.UNIPipe",
        "magic_pdf.tools",
        "magic_pdf.cli",
        "magic_pdf.model",
        "magic_pdf.layout"
    ]
    
    available_apis = []
    for api in apis_to_test:
        try:
            __import__(api)
            print(f"  ✅ {api}")
            available_apis.append(api)
        except ImportError as e:
            print(f"  ❌ {api}: {e}")
    
    print(f"\\n📊 可用的API: {len(available_apis)}")
    for api in available_apis:
        print(f"  ✅ {api}")
    
    # 如果有可用的API，尝试获取更多信息
    if available_apis:
        print("\\n🔍 检查可用API的详细信息...")
        
        for api in available_apis:
            try:
                module = __import__(api, fromlist=[''])
                print(f"\\n📋 {api} 模块内容:")
                attrs = [attr for attr in dir(module) if not attr.startswith('_')]
                for attr in attrs[:10]:  # 只显示前10个
                    print(f"  - {attr}")
                if len(attrs) > 10:
                    print(f"  ... 还有 {len(attrs) - 10} 个属性")
            except Exception as e:
                print(f"  ❌ 检查 {api} 失败: {e}")
    
    # 检查是否有命令行工具
    print("\\n🔍 检查命令行工具...")
    try:
        import magic_pdf
        if hasattr(magic_pdf, '__version__'):
            print(f"📦 MinerU版本: {magic_pdf.__version__}")
    except:
        pass
    
    # 尝试找到正确的处理函数
    print("\\n🔍 寻找PDF处理函数...")
    
    processing_functions = []
    
    # 检查常见的处理函数名
    function_names = [
        "parse_pdf", "process_pdf", "extract_pdf", "analyze_pdf",
        "pdf_parse", "pdf_process", "pdf_extract", "pdf_analyze"
    ]
    
    try:
        import magic_pdf
        for func_name in function_names:
            if hasattr(magic_pdf, func_name):
                processing_functions.append(f"magic_pdf.{func_name}")
                print(f"  ✅ magic_pdf.{func_name}")
    except:
        pass
    
    # 检查子模块中的函数
    for api in available_apis:
        try:
            module = __import__(api, fromlist=[''])
            for func_name in function_names:
                if hasattr(module, func_name):
                    processing_functions.append(f"{api}.{func_name}")
                    print(f"  ✅ {api}.{func_name}")
        except:
            pass
    
    print(f"\\n🎯 找到的处理函数: {len(processing_functions)}")
    for func in processing_functions:
        print(f"  ✅ {func}")
    
    if not processing_functions:
        print("⚠️  没有找到明显的PDF处理函数")
        print("💡 可能需要查看MinerU的官方文档或示例")
    
except ImportError as e:
    print(f"❌ magic_pdf 导入失败: {e}")
except Exception as e:
    print(f"❌ 检查过程出错: {e}")
    import traceback
    traceback.print_exc()
'''
    
    try:
        print("🧪 执行MinerU结构检查...")
        result = subprocess.run([python_path, '-c', check_code], 
                              capture_output=True, text=True, timeout=60)
        print("📋 检查结果:")
        print(result.stdout)
        if result.stderr:
            print("⚠️  错误信息:")
            print(result.stderr)
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")

def suggest_real_api():
    """建议真实的API调用方式"""
    print("\n💡 建议的解决方案")
    print("=" * 50)
    
    print("基于检查结果，我们可以:")
    print("1. 🔍 找到真实的MinerU API调用方式")
    print("2. 🔧 修改虚拟环境脚本使用正确的API")
    print("3. 🚀 获得真实的PDF处理能力")
    print()
    print("📚 如果没有找到合适的API，可能需要:")
    print("- 查看MinerU官方文档")
    print("- 尝试不同版本的MinerU")
    print("- 或者使用命令行方式调用MinerU")

def main():
    """主函数"""
    print("🔍 MinerU真实API检查")
    print("=" * 50)
    
    check_mineru_structure()
    suggest_real_api()
    
    print("\n📋 下一步:")
    print("1. 查看上面的检查结果")
    print("2. 找到可用的API或处理函数")
    print("3. 我会根据结果修改虚拟环境脚本")
    print("4. 然后你就能获得真实的MinerU处理能力了!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  检查被用户中断")
    except Exception as e:
        print(f"\n❌ 检查过程出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
