"""

    Module Name :           mixtral_module
    Last Modified Date :    3 Jan 2024

"""

# Import Open-source Libraries
from langchain import HuggingFacePipeline
from langchain.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain.schema import StrOutputParser
from langchain_community.llms.ollama import Ollama
from langchain_core.runnables import RunnablePassthrough
from langchain_core.messages import HumanMessage
import transformers
from transformers import AutoTokenizer
import torch
from huggingface_hub import hf_hub_download
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain.llms import LlamaCpp
from langchain import PromptTemplate, LLMChain
from datetime import datetime

# Import Self-defined Modules
import preprocessing

# init mixtral pipeline
def init_mixtral_piepeline(model_para):
    tokenizer = AutoTokenizer.from_pretrained(model_para["model"])
    pipeline = transformers.pipeline(
        model_para["task"],
        model=model_para["model"],
        tokenizer=tokenizer,
        device_map=model_para["device_map"],
        model_kwargs={"torch_dtype": eval(model_para["torch_dtype"])},
        max_new_tokens=model_para["max_new_tokens"],
        repetition_penalty=model_para["repetition_penalty"],
        return_full_text=eval(model_para["return_full_text"])
    )
    return pipeline


# mixtral Module
def mixtral_response(question,
                    system_prompt,
                    condense_system_prompt,
                    prompt_template,
                    model_para,
                    # pipeline,
                    retriever,
                    chat_history=None):

    llm = Ollama(base_url="http://*************:11434", model="mixtral:8x7b-instruct-v0.1-fp16")

    print("... Generating AI Response")
    ai_msg_content = ''

    # Without Vector Store
    if not retriever:
        messages = [{"role": "user", "content": question}]
        prompt = pipeline.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )

        outputs = pipeline(
            prompt,
            max_new_tokens=10000,
            do_sample=True,
            temperature=0.7,
            top_k=50,
            top_p=0.95)

        ai_msg_content = outputs[0]["generated_text"].replace('<s>[INST] who is Alice? [/INST]', '')


    # With Vector Store
    else:
        aimodel_starttime = datetime.now()
        # llm = HuggingFacePipeline(pipeline=pipeline)
        aimodel_endtime = datetime.now()
        print(f"AI Model Loading Time = {aimodel_endtime - aimodel_starttime}")

        custom_rag_prompt = PromptTemplate.from_template(prompt_template)
        rag_chain = (
                {"context": retriever | preprocessing.format_docs, "question": RunnablePassthrough()}
                | custom_rag_prompt
                | llm
                | StrOutputParser()
        )

        airespone_starttime = datetime.now()
        # for chunk in rag_chain.stream(question):
        #     print(chunk, end="", flush=True)
        ai_msg_content = rag_chain.invoke(question)
        airesponse_endtime = datetime.now()
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")
        # print(ai_msg_content)

    print('>>> Generated AI Response')

    return ai_msg_content # Return only the content of the AI's response
