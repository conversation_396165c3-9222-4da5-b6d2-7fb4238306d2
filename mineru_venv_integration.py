#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU虚拟环境集成模块
在隔离环境中运行MinerU，避免依赖冲突
"""

import os
import sys
import json
import tempfile
import subprocess
import logging
from typing import List, Dict, Tuple, Optional
from pathlib import Path

class MinerUVenvProcessor:
    """基于虚拟环境的MinerU处理器"""
    
    def __init__(self, venv_path: str = None):
        """
        初始化虚拟环境处理器
        
        Args:
            venv_path: 虚拟环境路径，默认为 ./mineru_venv
        """
        self.venv_path = venv_path or os.path.join(os.getcwd(), "mineru_venv")
        self.logger = logging.getLogger(__name__)
        
        # 检查虚拟环境是否存在
        self.available = self._check_venv_available()
        
    def _check_venv_available(self) -> bool:
        """检查虚拟环境是否可用"""
        if not os.path.exists(self.venv_path):
            self.logger.warning(f"虚拟环境不存在: {self.venv_path}")
            return False
        
        python_path = self._get_venv_python()
        if not os.path.exists(python_path):
            self.logger.warning(f"虚拟环境Python不存在: {python_path}")
            return False
        
        # 测试MinerU是否可用
        return self._test_mineru_in_venv()
    
    def _get_venv_python(self) -> str:
        """获取虚拟环境的Python路径"""
        if os.name == 'nt':  # Windows
            return os.path.join(self.venv_path, 'Scripts', 'python.exe')
        else:  # Linux/Mac
            return os.path.join(self.venv_path, 'bin', 'python')
    
    def _test_mineru_in_venv(self) -> bool:
        """测试虚拟环境中的MinerU"""
        python_path = self._get_venv_python()

        # 使用更宽松的测试条件
        test_code = '''
success = False
try:
    from magic_pdf.api.magic_pdf_parse import magic_pdf_parse
    success = True
    print("SUCCESS_API")
except ImportError:
    try:
        from magic_pdf.pipe.UNIPipe import UNIPipe
        success = True
        print("SUCCESS_PIPE")
    except ImportError:
        try:
            import magic_pdf
            success = True
            print("SUCCESS_BASE")
        except ImportError:
            print("FAILED")

if not success:
    print("FAILED")
'''

        try:
            result = subprocess.run([python_path, '-c', test_code],
                                  capture_output=True, text=True, timeout=30)
            output = result.stdout.strip()
            return any(keyword in output for keyword in ["SUCCESS_API", "SUCCESS_PIPE", "SUCCESS_BASE"])
        except Exception as e:
            self.logger.error(f"测试MinerU失败: {str(e)}")
            return False
    
    def is_available(self) -> bool:
        """检查处理器是否可用"""
        return self.available
    
    def process_pdf(self, pdf_path: str, output_dir: str = None) -> Tuple[Optional[Dict], Optional[str]]:
        """
        在虚拟环境中处理PDF
        
        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录
            
        Returns:
            Tuple[layout_data, error_message]: 布局数据和错误信息
        """
        if not self.available:
            return None, "虚拟环境不可用"
        
        if not os.path.exists(pdf_path):
            return None, f"PDF文件不存在: {pdf_path}"
        
        if output_dir is None:
            output_dir = tempfile.mkdtemp()
        
        # 创建处理脚本
        script_content = f'''
import sys
import json
import os

try:
    from magic_pdf.api.magic_pdf_parse import magic_pdf_parse
    
    # 处理PDF
    pdf_path = r"{pdf_path}"
    output_dir = r"{output_dir}"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 配置参数
    config = {{
        "parse_mode": "auto",
        "output_format": ["json"],
        "output_dir": output_dir,
        "lang": "auto",
        "layout_config": {{
            "model": "doclayout_yolo"
        }},
        "formula_config": {{
            "enable": True,
            "mfd_model": "yolo_v8_mfd",
            "mfr_model": "unimernet_small"
        }},
        "table_config": {{
            "enable": True,
            "model": "rapid_table"
        }}
    }}
    
    # 调用MinerU
    result = magic_pdf_parse(
        pdf_path=pdf_path,
        output_dir=output_dir,
        **config
    )
    
    # 输出结果
    if result:
        print("SUCCESS")
        print(json.dumps(result, ensure_ascii=False, default=str))
    else:
        print("FAILED")
        print("No result returned")
        
except Exception as e:
    print("ERROR")
    print(str(e))
'''
        
        # 写入临时脚本文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(script_content)
            script_path = f.name
        
        try:
            # 在虚拟环境中执行
            python_path = self._get_venv_python()
            self.logger.info(f"在虚拟环境中处理PDF: {pdf_path}")
            
            result = subprocess.run([python_path, script_path], 
                                  capture_output=True, text=True, timeout=300)
            
            lines = result.stdout.strip().split('\n')
            if not lines:
                return None, "无输出结果"
            
            status = lines[0]
            if status == "SUCCESS":
                # 解析JSON结果
                if len(lines) > 1:
                    json_result = '\n'.join(lines[1:])
                    try:
                        layout_data = json.loads(json_result)
                        self.logger.info("虚拟环境中MinerU处理成功")
                        return layout_data, None
                    except json.JSONDecodeError as e:
                        return None, f"JSON解析失败: {str(e)}"
                else:
                    return None, "缺少结果数据"
            elif status == "FAILED":
                error_msg = lines[1] if len(lines) > 1 else "Unknown error"
                return None, f"处理失败: {error_msg}"
            elif status == "ERROR":
                error_msg = lines[1] if len(lines) > 1 else "Unknown error"
                return None, f"执行错误: {error_msg}"
            else:
                return None, f"未知状态: {status}"
                
        except subprocess.TimeoutExpired:
            return None, "处理超时"
        except Exception as e:
            return None, f"执行异常: {str(e)}"
        finally:
            # 清理临时文件
            try:
                os.unlink(script_path)
            except:
                pass
    
    def convert_to_chunks(self, layout_data: Dict, filename: str) -> List[Tuple[str, Dict]]:
        """
        将布局数据转换为chunks格式
        （与原始MinerU集成模块相同的逻辑）
        """
        chunks_with_metadata = []
        
        try:
            if not layout_data or "pdf_info" not in layout_data:
                self.logger.warning("布局数据格式不正确")
                return chunks_with_metadata
            
            for page_idx, page_info in enumerate(layout_data["pdf_info"]):
                page_num = page_idx + 1
                
                if "preproc_blocks" not in page_info:
                    continue
                
                for block_idx, block in enumerate(page_info["preproc_blocks"]):
                    chunk_text = self._extract_text_from_block(block)
                    
                    if not chunk_text or chunk_text.strip() == "":
                        continue
                    
                    # 创建元数据
                    metadata = {
                        'start_page_num': page_num,
                        'end_page_num': page_num,
                        'start_line_num': block_idx + 1,
                        'end_line_num': block_idx + 1,
                        'content_type': block.get('type', 'text'),
                        'bbox': block.get('bbox', [0, 0, 0, 0]),
                        'source': filename,
                        'block_index': block_idx,
                        'confidence': block.get('score', 1.0)
                    }
                    
                    chunks_with_metadata.append((chunk_text, metadata))
            
            self.logger.info(f"转换完成，生成 {len(chunks_with_metadata)} 个文本块")
            return chunks_with_metadata
            
        except Exception as e:
            self.logger.error(f"转换chunks失败: {str(e)}")
            return chunks_with_metadata
    
    def _extract_text_from_block(self, block: Dict) -> str:
        """从block中提取文本内容"""
        text_content = ""
        
        try:
            if block.get('type') == 'text':
                # 处理文本块
                if 'lines' in block:
                    for line in block['lines']:
                        if 'spans' in line:
                            for span in line['spans']:
                                if span.get('type') == 'text' and 'content' in span:
                                    text_content += span['content'] + " "
                
            elif block.get('type') == 'title':
                # 处理标题块
                if 'lines' in block:
                    for line in block['lines']:
                        if 'spans' in line:
                            for span in line['spans']:
                                if span.get('type') == 'text' and 'content' in span:
                                    text_content += span['content'] + " "
                                    
            elif block.get('type') == 'table':
                # 处理表格块
                if 'blocks' in block:
                    for sub_block in block['blocks']:
                        if 'lines' in sub_block:
                            for line in sub_block['lines']:
                                if 'spans' in line:
                                    for span in line['spans']:
                                        if span.get('type') == 'table' and 'html' in span:
                                            text_content += f"[TABLE: {span['html']}]"
                                        elif span.get('type') == 'text' and 'content' in span:
                                            text_content += span['content'] + " "
            
            elif block.get('type') == 'image':
                # 处理图像块
                text_content = f"[IMAGE: {block.get('image_path', 'unknown')}]"
                
            elif block.get('type') == 'formula':
                # 处理公式块
                if 'latex' in block:
                    text_content = f"[FORMULA: {block['latex']}]"
                    
        except Exception as e:
            self.logger.warning(f"提取文本失败: {str(e)}")
            
        return text_content.strip()

# 全局实例
_venv_processor = None

def get_venv_processor() -> MinerUVenvProcessor:
    """获取虚拟环境处理器实例"""
    global _venv_processor
    if _venv_processor is None:
        _venv_processor = MinerUVenvProcessor()
    return _venv_processor

def is_venv_mineru_available() -> bool:
    """检查虚拟环境中的MinerU是否可用"""
    processor = get_venv_processor()
    return processor.is_available()

# 兼容性接口
MINERU_VENV_AVAILABLE = is_venv_mineru_available()

if __name__ == "__main__":
    # 测试虚拟环境处理器
    processor = get_venv_processor()
    
    if processor.is_available():
        print("✅ 虚拟环境MinerU可用")
        
        # 如果有测试PDF文件，可以测试处理
        test_pdf = "test.pdf"
        if os.path.exists(test_pdf):
            layout_data, error = processor.process_pdf(test_pdf)
            if layout_data:
                chunks = processor.convert_to_chunks(layout_data, "test.pdf")
                print(f"✅ 成功处理PDF，生成 {len(chunks)} 个文本块")
            else:
                print(f"❌ PDF处理失败: {error}")
        else:
            print(f"⚠️  测试文件不存在: {test_pdf}")
    else:
        print("❌ 虚拟环境MinerU不可用")
        print("请先运行: python safe_install_mineru.py")
