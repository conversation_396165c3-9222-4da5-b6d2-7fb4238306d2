import fitz  # PyMuPDF
import os
from typing import List, Dict, Any

def annotate_pdf_with_chunks(
    input_pdf_path: str,
    output_pdf_path: str,
    chunks_info: List[Dict[str, Any]]
) -> None:
    """
    在PDF上标注不同类型的内容区域
    
    Args:
        input_pdf_path: 输入PDF文件路径
        output_pdf_path: 输出PDF文件路径
        chunks_info: 包含bbox、page_num、content_type等信息的列表
    """
    
    # 定义颜色映射
    color_mapping = {
        'text_with_title': (0, 1, 0, 0.3),  # 绿色，半透明
        'text': (0, 1, 0, 0.3),             # 绿色，半透明
        'table': (0, 0, 1, 0.3),            # 蓝色，半透明
        'image': (1, 0, 0, 0.3)             # 红色，半透明
    }
    
    # 打开PDF文件
    pdf_document = fitz.open(input_pdf_path)
    
    print(f"PDF共有 {pdf_document.page_count} 页")
    print(f"将处理 {len(chunks_info)} 个区域")
    
    # 按页面分组chunks
    chunks_by_page = {}
    for chunk in chunks_info:
        page_num = chunk['page_num']
        if page_num not in chunks_by_page:
            chunks_by_page[page_num] = []
        chunks_by_page[page_num].append(chunk)
    
    # 处理每一页
    for page_num, page_chunks in chunks_by_page.items():
        # 注意：fitz使用0基索引，而我们的数据使用1基索引
        page_index = page_num - 1
        
        if page_index >= pdf_document.page_count:
            print(f"警告：页面 {page_num} 超出PDF范围，跳过")
            continue
            
        page = pdf_document[page_index]
        print(f"处理第 {page_num} 页，共 {len(page_chunks)} 个区域")
        
        # 在该页面上添加所有标注
        for chunk in page_chunks:
            bbox = chunk['bbox']
            content_type = chunk['content_type']
            
            # 获取对应的颜色
            color = color_mapping.get(content_type, (0.5, 0.5, 0.5, 0.3))  # 默认灰色
            
            # 创建矩形区域
            rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
            
            # 添加高亮标注
            highlight = page.add_highlight_annot(rect)
            highlight.set_colors({"stroke": color[:3], "fill": color[:3]})
            highlight.set_opacity(color[3])
            highlight.update()
            
            # 添加文本标注显示内容类型
            text_rect = fitz.Rect(bbox[0], bbox[1] - 15, bbox[0] + 100, bbox[1])
            text_annot = page.add_text_annot(fitz.Point(bbox[0], bbox[1] - 5), content_type)
            text_annot.set_info(content=f"类型: {content_type}")
            text_annot.update()
            
            print(f"  - 添加 {content_type} 标注: {bbox}")
    
    # 保存标注后的PDF
    pdf_document.save(output_pdf_path)
    pdf_document.close()
    
    print(f"标注完成！新PDF已保存为: {output_pdf_path}")

def main():
    """主函数"""
    
    # 输入和输出文件路径
    input_pdf = "uploads/amber_place.pdf"
    output_pdf = "uploads/amber_place_annotated.pdf"
    
    # 您提供的chunks_info数据
    chunks_info = [
        {'bbox': [64.0, 486.0, 408.0, 630.0], 'page_num': 3, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
        {'bbox': [64.0, 666.0, 408.0, 842.0], 'page_num': 3, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
        {'bbox': [450.0, 358.0, 791.0, 430.0], 'page_num': 3, 'content_type': 'text', 'source': 'amberplacepdf'},
        {'bbox': [449.0, 571.0, 791.0, 618.0], 'page_num': 3, 'content_type': 'text', 'source': 'amberplacepdf'},
        {'bbox': [455.0, 651.0, 791.0, 723.0], 'page_num': 3, 'content_type': 'text', 'source': 'amberplacepdf'},
        {'bbox': [45.0, 129.0, 689.0, 172.0], 'page_num': 4, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
        {'bbox': [449.0, 298.0, 791.0, 334.0], 'page_num': 5, 'content_type': 'text', 'source': 'amberplacepdf'},
        {'bbox': [44.0, 129.0, 689.0, 172.0], 'page_num': 6, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
        {'bbox': [63.0, 556.0, 408.0, 673.0], 'page_num': 6, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
        {'bbox': [64.0, 491.0, 407.0, 568.0], 'page_num': 7, 'content_type': 'text_with_title', 'source': 'amberplacepdf'}
    ]
    
    # 检查输入文件是否存在
    if not os.path.exists(input_pdf):
        print(f"错误：找不到输入文件 {input_pdf}")
        return
    
    print("开始处理PDF标注...")
    print(f"输入文件: {input_pdf}")
    print(f"输出文件: {output_pdf}")
    print("\n颜色说明:")
    print("- text_with_title 和 text: 绿色")
    print("- table: 蓝色")
    print("- image: 红色")
    print()
    
    try:
        # 执行标注
        annotate_pdf_with_chunks(input_pdf, output_pdf, chunks_info)
        
        print(f"\n✅ 标注完成！")
        print(f"原PDF: {input_pdf}")
        print(f"标注后PDF: {output_pdf}")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 