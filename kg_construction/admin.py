# This file contains some serious operations
# Anything related to using a CustomNebulaPropertyGraphStore instance is put here. 
# Will open a new connection each time
# TODO:
# initalize of NebulaGraph default space,


from .CustomNebulaPropertyGraphStore import CustomNebulaPropertyGraphStore
from .mock_data import generate_mock
from .mock_data2 import generate_mock as generate_mock2
from .RelationshipFinder import RelationshipFinder
import nest_asyncio
nest_asyncio.apply()
import time
from .CustomNodes import *

def delete_all_spaces():
    graph_store = CustomNebulaPropertyGraphStore(space="default",overwrite= False)
    space_list = [element['Name'] for element in graph_store.structured_query("SHOW SPACES")]
    space_list = ["basketballplayer","llama_index"]
    for space in space_list:
        if space!="default":
            insert_query = "DROP SPACE IF EXISTS "+space
            graph_store.structured_query(insert_query)

    graph_store._client.close()

