from llama_index.graph_stores.nebula import NebulaPropertyGraphStore
from typing import Any, List, Dict, Optional, Tuple

from .CustomNodes import *
from nebula3.gclient.net.SessionPool import SessionPool
from nebula3.gclient.net.base import BaseExecutor

from llama_index.graph_stores.nebula.utils import (
    build_param_map,
    remove_empty_values,
    url_scheme_parse,
)
from .mock_data import ImageNode
from .constants import *
class CustomNebulaPropertyGraphStore(NebulaPropertyGraphStore):
    def __init__(
        self,
        space: str,
        client: Optional[BaseExecutor] = None,
        username: str = "root",
        password: str = "nebula",
        url: str = "nebula://192.168.0.205:9669",
        overwrite: bool = False,
        props_schema: str = DEFAULT_PROPS_SCHEMA,
        refresh_schema: bool = True,
        sanitize_query_output: bool = False,  # We don't put Embedding-Like values as Properties
        enhanced_schema: bool = False,
    ) -> None:
        self.sanitize_query_output = sanitize_query_output
        self.enhcnaced_schema = enhanced_schema

        self._space = space
        if client:
            self._client = client
        else:
            session_pool = SessionPool(
                username,
                password,
                self._space,
                [url_scheme_parse(url)],
            )
            session_pool.init()
            self._client = session_pool
        self._client.execute(DDL.render(props_schema=props_schema))
        self._client.execute(INDEX_DDL)
        if overwrite:
            self._client.execute(f"CLEAR SPACE {self._space};")

        self.structured_schema = {}
        if refresh_schema:
            try:
                self.refresh_schema()
            except Exception:
                # fails to refresh for the first time
                pass

        self.supports_structured_queries = True
    
    #Assume no symbol "
    def build_nodes(self, nodes: List[LabelledNode]) -> None:
        # meta tag Entity__ is used to store the entity name
        # meta tag Chunk__ is used to store the chunk text
        # other labels are used to store the entity properties
        # which must be created before upserting the nodes

        # Lists to hold separated types
        #print("UPSERTED NODES ARE: ", nodes)
        entity_list: List[EntityNode] = []
        chunk_list: List[ChunkNode] = []
        image_list: List[ImageNode] = []
        document_list: List[ImageNode] = []

        other_list: List[LabelledNode] = []

        # Sort by type
        for item in nodes:
            if isinstance(item, EntityNode):
                entity_list.append(item)
            elif isinstance(item, ChunkNode):
                chunk_list.append(item)
            elif isinstance(item,ImageNode):
                image_list.append(item)
            elif isinstance(item,DocumentNode):
                document_list.append(item)
            else:
                other_list.append(item)

        if chunk_list:
            insert_query = "INSERT VERTEX Chunk__(text) VALUES "
            for i, chunk in enumerate(chunk_list):
                self.structured_query(
                    f'INSERT VERTEX Chunk__(text) VALUES "{chunk.id}":("{chunk.text}")'
                )

        if entity_list:
            for i, entity in enumerate(entity_list):
                self.structured_query(
                    f'INSERT VERTEX Entity__(name) VALUES "{entity.id}":("{entity.name}")'
                )

        if image_list:
            insert_query = "INSERT VERTEX Image__(description) VALUES "
            for i, image in enumerate(image_list):
                self.structured_query(
                    f'INSERT VERTEX Image__(description) VALUES "{image.id}":("{image.description}")'
                )
            
        if document_list:
            insert_query = "INSERT VERTEX Document__() VALUES "
            for i, document in enumerate(document_list):
                insert_query += f'"{document.id}":(),'
            insert_query = insert_query[:-1]  # Remove trailing comma
            self.structured_query(
                insert_query
            )

        for i, entity in enumerate(nodes):

            stmt = (
                f'INSERT VERTEX Node__ (label, parent, followed_by, metadata) VALUES "{entity.id}":("{entity.label}","{entity.parent}","{entity.followed_by}", "{entity.metadata}");'
            )
            self.structured_query(stmt)
    

    #given a list of image, build "contains" from chunk to the image or document to chunk.
    def build_parent(self, nodes: List[LabelledNode]):
        entity_list: List[EntityNode] = []
        chunk_list: List[ChunkNode] = []
        image_list: List[ImageNode] = []
        other_list: List[LabelledNode] = []

        # Sort by type
        for item in nodes:
            if isinstance(item, EntityNode):
                entity_list.append(item)
            elif isinstance(item, ChunkNode):
                chunk_list.append(item)
            elif isinstance(item,ImageNode):
                image_list.append(item)
            else:
                other_list.append(item)

        if image_list:
            self.structured_query("CREATE EDGE IF NOT EXISTS contains();")
            insert_query = "INSERT EDGE contains() VALUES "
            for i, image in enumerate(image_list):
                insert_query += f'"{image.parent}"->"{image.id}":(),'
            insert_query = insert_query[:-1]  # Remove trailing comma
            self.structured_query(
                insert_query
            )
        if chunk_list:
            self.structured_query("CREATE EDGE IF NOT EXISTS contains();")
            insert_query = "INSERT EDGE contains() VALUES "
            for i, chunk in enumerate(chunk_list):
                insert_query += f'"{chunk.parent}"->"{chunk.id}":(),'
            insert_query = insert_query[:-1]  # Remove trailing comma
            self.structured_query(
                insert_query
            )

    #given a list of image, build "followed_by" from chunk to the chunk or image to image.
    def build_follow(self, nodes: List[LabelledNode]):
        entity_list: List[EntityNode] = []
        chunk_list: List[ChunkNode] = []
        image_list: List[ImageNode] = []
        other_list: List[LabelledNode] = []

        # Sort by type
        for item in nodes:
            if isinstance(item, EntityNode):
                entity_list.append(item)
            elif isinstance(item, ChunkNode):
                chunk_list.append(item)
            elif isinstance(item,ImageNode):
                image_list.append(item)
            else:
                other_list.append(item)

        if image_list:
            self.structured_query("CREATE EDGE IF NOT EXISTS followed_by();")
            insert_query = "INSERT EDGE followed_by() VALUES "
            for i, image in enumerate(image_list):
                insert_query += f'"{image.followed_by}"->"{image.id}":(),'
            insert_query = insert_query[:-1]  # Remove trailing comma
            self.structured_query(
                insert_query
            )
        if chunk_list:
            self.structured_query("CREATE EDGE IF NOT EXISTS followed_by();")
            insert_query = "INSERT EDGE followed_by() VALUES "
            for i, chunk in enumerate(chunk_list):
                insert_query += f'"{chunk.followed_by}"->"{chunk.id}":(),'
            insert_query = insert_query[:-1]  # Remove trailing comma
            self.structured_query(
                insert_query
            )

    # Input is a list of (id1, id2, score, relation name (optional))
    def build_relationships(self, relationships:list[tuple[str,str,float,str|None]]):

        for (id1, id2, score, relation) in relationships:
            print((id1,id2,score,relation))
            
            input_relation = "related" if not relation else relation 
            insert_query = "INSERT EDGE related (score, type) VALUES "
            self.structured_query(
                insert_query+"\""+id2+"\"->\""+id1+"\":("+str(score)+" , \""+input_relation+"\");"
            )
            self.structured_query(
                insert_query+"\""+id1+"\"->\""+id2+"\":("+str(score)+" , \""+input_relation+"\");"
            )
    
    def build_entities(self, relations: list[str,list[str]]):
        for node_id, entity_list in relations:
            if entity_list:
                entity_nodes= []
                for entity in entity_list:
                    entity_nodes.append(EntityNode(name=entity))
                self.build_nodes(entity_nodes)

                insert_query = "INSERT EDGE mentions() VALUES "
                for i, entity in enumerate(entity_list):
                    insert_query += f'"{node_id}"->"{entity}":(),'
                insert_query = insert_query[:-1]  # Remove trailing comma
                self.structured_query(
                    insert_query
                )

        #Expect a list of vid
    def delete_list(self, vids):
        if vids:
            delete_stmt = "DELETE VERTEX "
            for node in vids:
                delete_stmt += "\""+node+"\", "
            delete_stmt = delete_stmt[:-2] #delete the ending comma and whitespace
            print(delete_stmt)
            self.structured_query(delete_stmt)

    # 1. Find all nodes have been a source or have been a destination. 
    # 2. Delete all source nodes, and check whether the destination nodes (image, entity and chunk) are being pointed by things in other documents. 
    # 3. If no then delete them.
    # Possible room for edit: chunk with no outward edge maybe pointed by a "related" edge in future design. It need to be removed too.
    def delete_document(self,id):
        subgraph = self.structured_query(f"GO 1 TO 4 STEPS FROM \"{id}\" OVER contains,mentions,related, followed_by YIELD src(edge) AS src, dst(edge) AS dst")
        src = set([element['src'] for element in subgraph])
        dst = set([element['dst'] for element in subgraph])
        self.delete_list(src)      
        dst = dst - src
        dst_string = str(list(dst))
        if dst:
            query_stmt = f"match (n) WHERE id(n) in {dst_string} AND size((n)<--())==0 return id(n) as id"
            isolated_node = self.structured_query(query_stmt)
            isolated_node = set([element['id'] for element in isolated_node])
            self.delete_list(isolated_node)
    