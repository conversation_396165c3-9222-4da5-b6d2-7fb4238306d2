from jinja2 import Template
DDL = Template(
"""
CREATE TAG IF NOT EXISTS Entity__ (name STRING);
CREATE TAG IF NOT EXISTS Chunk__ (text STRING);
CREATE TAG IF NOT EXISTS Node__ (label STRING, followed_by STRING, parent STRING, metadata STRING);
CREATE TAG IF NOT EXISTS Image__ (description STRING);
CREATE TAG IF NOT EXISTS Document__ ();
CREATE EDGE IF NOT EXISTS related (type STRING, score FLOAT);
CREATE EDGE IF NOT EXISTS mentions();
CREATE EDGE IF NOT EXISTS followed_by();
CREATE EDGE IF NOT EXISTS contains();
CREATE TAG IF NOT EXISTS Props__ ({{props_schema}});
CREATE EDGE IF NOT EXISTS Relation__ (label STRING{% if props_schema != "" %}, {{props_schema}}{% endif%});
"""
)
#CREATE EDGE IF NOT EXISTS `__meta__node_label__` (label STRING, props_json STRING);
#CREATE EDGE IF NOT EXISTS `__meta__rel_label__` (label STRING, props_json STRING);


# TODO from llama_index: need to define Props__ Indexes based on all the properties
INDEX_DDL = """
CREATE TAG INDEX IF NOT EXISTS idx_Entity__ ON Entity__(name);
CREATE TAG INDEX IF NOT EXISTS idx_Chunk__ ON Chunk__(text);
CREATE TAG INDEX IF NOT EXISTS idx_Node__ ON Node__(label);
CREATE EDGE INDEX IF NOT EXISTS idx_Relation__ ON Relation__(label);

CREATE EDGE INDEX IF NOT EXISTS idx_meta__node_label__ ON __meta__node_label__(label);
CREATE EDGE INDEX IF NOT EXISTS idx_meta__rel_label__ ON __meta__rel_label__(label);
"""

# Hard coded default schema, which is union of
# document metadata: file_path STRING, file_name STRING, file_type STRING, file_size INT, creation_date STRING, last_modified_date STRING
# llamaindex_node: _node_content STRING, _node_type STRING, document_id STRING, doc_id STRING, ref_doc_id STRING
# introduced by PropertyGraph: triplet_source_id STRING
DEFAULT_PROPS_SCHEMA = "file_path STRING, file_name STRING, file_type STRING, file_size INT, creation_date STRING, last_modified_date STRING, node_content STRING, node_type STRING, document_id STRING, doc_id STRING, ref_doc_id STRING, triplet_source_id STRING"
