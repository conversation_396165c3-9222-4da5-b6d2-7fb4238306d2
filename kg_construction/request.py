import requests

def request_llamacpp(prompt):
    url = 'http://192.168.0.205:5002/v1/chat/completions'
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json'
    }
    data = {
        'messages': [
            {
            'content' : prompt,
            'role' : 'system'
            },
        ],
        'max_tokens': 1000,
        'temperature': 0,
    }
    response = requests.post(url, headers=headers, json=data)
    completion_tokens = response.json()['usage']['completion_tokens']
    prompt_tokens = response.json()['usage']['prompt_tokens']
    response = response.json()['choices'][0]['message']['content']
    return (response, prompt_tokens, completion_tokens)

def request_ollama(prompt):
    url = 'http://192.168.0.207:30222/api/generate'
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json'
    }
    data = {
        #"model" : "qwen2:7b",
        # "model" : "mixtral",
        "model" : "qwen2.5",
        "prompt": prompt,
        "stream" : False,
        "options": {"num_predict": 200, "num_parallel":1,"num_ctx":16384},
    }
    response = requests.post(url, headers=headers, json=data).json()
    try:
        result = (response['response'], response['prompt_eval_count'], response['eval_count'])
    except:
        print("RESPONSE FORMAT WRONG: ", response)
        result = ("",0,0)
    print("prompt: ", prompt)
    print("result: ",result)
    return result


if __name__ == "__main__":
    print(request_ollama("what is deloitte"))