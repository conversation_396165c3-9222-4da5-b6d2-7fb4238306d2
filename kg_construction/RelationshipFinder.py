import ast
from typing import Any, List, Dict, Optional, Tuple
from .CustomNodes import *
import requests
import re
import multiprocessing
from .request import request_ollama, request_llamacpp
PROCESS_NUMBER = 16

class RelationshipFinder:
    # Input a list of all nodes,
    # For each node, find its siblings then check their relationships
    # Output a list of id1, id2 , score, edge name (can be none)
    @staticmethod
    def extract_relations(text1: str, text2:str) -> float: #tuple[float,str|None]:
        prompt = f"Text1: \"\"\"{text1}\"\"\"\nText2: \"\"\"{text2}\"\"\" \nYou are an smart and helpful AI assistant. Given the following above texts, give a relational score base on how similar their context is. Output one single value only, without comments. Assistant: "
        response,_,_ = request_ollama(prompt)
        print(response)
        try:
            result = float(response)
            return result if result>=0 and result<=1 else float(0)
        except:
            return float(0)

    # Take in a list of nodes, invoke the LLM and return all relationships between them.
    @staticmethod
    def find_all_relationship(nodes: List[LabelledNode]) -> list[tuple[str,str,float,str|None]]:
        print("enter relationships")
        with multiprocessing.Pool(processes=PROCESS_NUMBER) as pool:
            # Find relationships between all images
            image_results = []
            chunk_results = []
            image_node_list = filter(lambda x: isinstance(x, ImageNode), nodes)
            if image_node_list:
                image_node_list = list(image_node_list)
                for x, node1 in enumerate(image_node_list):
                    if (x<len(image_node_list)-1):
                        # Pass 2 strings into each extraction process
                        # node 2 will be the first 5 nodes starting from node 1
                        image_results += [(node1.id,node2.id,pool.apply_async(RelationshipFinder.extract_relations, 
                                                                              args=(node1.description,node2.description,))) 
                                                                              for node2 in chunk_node_list[x+1:min(len(chunk_node_list),x+5)]]

            # Find relationships between all chunks
            chunk_node_list = filter(lambda x: isinstance(x, ChunkNode), nodes)
            if chunk_node_list:
                chunk_node_list = list(chunk_node_list)
                for x, node1 in enumerate(chunk_node_list):
                    if (x<len(chunk_node_list)-1):
                        # Pass 2 strings into each extraction process
                        # node 2 will be the first 5 nodes starting from node 1
                        chunk_results += [(node1.id,node2.id,pool.apply_async(RelationshipFinder.extract_relations, 
                                                                              args=(node1.text,node2.text,))) 
                                                                              for node2 in chunk_node_list[x+1:min(len(chunk_node_list),x+5)]]

            pool.close()
            pool.join()
            results = [(r[0],r[1],r[2].get(),"related") for r in chunk_results+image_results]
            results = [r for r in results if r[2] >= 0.85]
            return results
            




    @staticmethod
    def extract_entities(text:str) -> list :

        # if text.startswith("ImageDescription:["):
        #     ValueError("Not extracting table")


        # Text 1: \"\"\"I lost a pen when I am having science lesson this morning in that tall building. It costs 5 dollars.\"\"\" \n
        # AI assistant: The meaningful objects in text 1 are ["Pen", "Science", "Tall Building"].\n
        prompt = f"""        
        Text:  \"\"\""{text}"\"\"\""\n

        System: You are an smart and helpful AI assistant. You are in the preparation process of making a report.
        Given the above text, identify and extract the really essential names that may appear in other documents into a list of string, with \"\".
        Strictly exclude: Dates/time expressions, Numerical values, Single-character Chinese words.
        Focus on multi-word proper nouns that have contextual importance across documents.        
        Output one single list of string only, after the list please add explanation about why adding those objects.
        \n

        AI assistant: [\""
        """
        response, completion_token, prompt_token = request_ollama(prompt)
        # Add ending characters for incomplete messages
        if not (response.endswith("\"]") or response.endswith("\']")):
            if not (response.endswith("\"") or response.endswith("\'")):
                response += "\""
            if not response.endswith("]"):
                response += "]"
        if not (response.startswith("[\"") or response.startswith("[\'")):
            if not (response.startswith("\"") or response.startswith("\'")):
                response = "\"" + response        
            if not response.startswith("["):
                response = "[" + response
        print("Response: ", response)
        
        pattern = r'\[(.*?)\]'
        matches = re.findall(pattern, response)
        if len(matches)>0:
            response = f"[{matches[0]}]"
        print("Parsed Response: ", response)

        # Remove prohibited characters and limit length to 15 for each entity name
        def transform(result):
            rx = r'[\n；\"\\]'
            return re.sub(rx,"",str(result).lower()[:50]) #shorten for entitiy name and all lower cases

        try:
            result_list =  [transform(result) for result in ast.literal_eval(response)]
            return result_list, completion_token, prompt_token
        except Exception as e:
            print("Error is ",e)
            return [], 0, 0
    
    def find_all_entities(nodes: List[LabelledNode])->list:
        with multiprocessing.Pool(processes=PROCESS_NUMBER) as pool:
            # Submit tasks to the pool and collect the results
            results = [pool.apply_async(RelationshipFinder.extract_entities, 
                                        args=(node.description if isinstance(node, ImageNode) else node.text,)) 
                                        for i, node in enumerate(nodes)]
            pool.close()
            pool.join() 
            output = [r.get() for r in results]
        
        print("total tokens used: ",sum([element[1] for element in output]), sum([element[2] for element in output]))
        return [(node.id, element[0]) for node, element in zip(nodes, output)]