from .CustomNodes import *

def generate_mock():
    return [
        DocumentNode(id_="6981",text= "A document recording breakfast"),
        ChunkNode(id_="6981_3712",text = "彼得 did not eat breakfast today.", metadata="\{\Page: 32 \}", parent="6981", followed_by="3713"),
        ChunkNode(id_="6981_3713",text = "<PERSON> did eat breakfast today.",parent="6981"),
        ChunkNode(id_="6981_3714",text = "<PERSON> did not eat breakfast yesterday.",parent="6981"),
        <PERSON>kNode(id_="6981_3715",text = "<PERSON> did eat breakfast yesterday.",parent="6981"),
        ChunkNode(id_="6981_3716",text = "<PERSON> did not eat breakfast today.",parent="6981"),
        ChunkNode(id_="6981_3717",text = "<PERSON> did eat breakfast today.",parent="6981"),
        ImageNode(id_="6981_5432",description="A boy eating icecream",parent="3712"),
        ImageNode(id_="6981_5433",description="A boy eating apple",parent="3712"),
        
        ChunkNode(id_="6980_5477",text = "<PERSON> did not eat breakfast today.", parent="6980")
    ]
