from .CustomNodes import *
    
def generate_mock():
    return [
        DocumentNode(id_ = "5468"),
        ChunkNode(id_="5469",parent="5468",text = """SME OFFICESURE INSURANCE POLICY IMPORTANT - Please read this Policy carefully upon receipt and promptly request for any necessary amendments. This Policy terms and conditions, the Schedule and any endorsement attached or to be attached thereon shall be read together as one contract and any word or expression to which a specific meaning has been attached in any part of this Policy or of the Schedule shall bear such specific meaning wherever it may appear.The Proposal Form and Declaration made by the Insured shall form the basis of this Policy and are deemed to be incorporated herein as partof this Policy. In consideration of the Insured has applied to the Company for the insurance hereinafter contained and on the condition thatthe Insured has paid or agreed to pay the premium as specified for such insurance, and on condition that the information provided in theProposal Form and Declaration is true and correct, the Company will pay the benefits subject to the definitions of words, exclusions, limitations,terms and conditions herein, endorsed hereon, or attached hereto."""),
        # ChunkNode(id_=3715,text = <PERSON> did eat breakfast yesterday.),
        # ChunkNode(id_=3716,text = <PERSON> did not eat breakfast today.),
        # ChunkNode(id_=3717,text = <PERSON> did eat breakfast today.),
        ImageNode(id_="5470",description="A boy eating icecream",parent="5469"),
        ImageNode(id_="5471",description="A boy eating apple",parent="5469"),        
    ]
