#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的PDF标注API
只显示包含bbox的页面，不包含图例页面
"""

import requests
import json

def test_new_annotation_api():
    """
    测试新的 /annotate_pdf_with_chunks API
    只保留包含标注的页面，不添加图例
    """
    print("=== 测试新的chunks标注API ===")
    
    # API URL
    url = "http://localhost:8037/annotate_pdf_with_chunks"
    
    # 测试数据
    data = {
        "filename": "amber_place.pdf",
        "add_legend": False,  # 不添加图例页面
        "chunks_info": [
            {'bbox': [64.0, 486.0, 408.0, 630.0], 'page_num': 3, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
            {'bbox': [64.0, 666.0, 408.0, 842.0], 'page_num': 3, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
            {'bbox': [450.0, 358.0, 791.0, 430.0], 'page_num': 3, 'content_type': 'text', 'source': 'amberplacepdf'},
            {'bbox': [449.0, 571.0, 791.0, 618.0], 'page_num': 3, 'content_type': 'text', 'source': 'amberplacepdf'},
            {'bbox': [455.0, 651.0, 791.0, 723.0], 'page_num': 3, 'content_type': 'text', 'source': 'amberplacepdf'},
            {'bbox': [45.0, 129.0, 689.0, 172.0], 'page_num': 4, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
            {'bbox': [449.0, 298.0, 791.0, 334.0], 'page_num': 5, 'content_type': 'text', 'source': 'amberplacepdf'},
            {'bbox': [44.0, 129.0, 689.0, 172.0], 'page_num': 6, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
            {'bbox': [63.0, 556.0, 408.0, 673.0], 'page_num': 6, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
            {'bbox': [64.0, 491.0, 407.0, 568.0], 'page_num': 7, 'content_type': 'text_with_title', 'source': 'amberplacepdf'}
        ]
    }
    
    print(f"发送请求到: {url}")
    print(f"文件名: {data['filename']}")
    print(f"标注区域数量: {len(data['chunks_info'])}")
    print(f"添加图例: {data['add_legend']}")
    
    # 统计页面分布
    pages_with_bbox = set()
    for chunk in data['chunks_info']:
        pages_with_bbox.add(chunk['page_num'])
    
    print(f"包含标注的页面: {sorted(pages_with_bbox)} (共{len(pages_with_bbox)}页)")
    
    # 统计内容类型
    type_counts = {}
    for chunk in data['chunks_info']:
        content_type = chunk['content_type']
        type_counts[content_type] = type_counts.get(content_type, 0) + 1
    print(f"内容类型分布: {type_counts}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data)
        
        if response.status_code == 200:
            result = response.json()
            print("\n✅ 请求成功!")
            print(f"状态: {result.get('status')}")
            print(f"消息: {result.get('message')}")
            print(f"输出文件: {result.get('output_file')}")
            print(f"输出路径: {result.get('output_path')}")
            print(f"总标注数量: {result.get('total_annotations')}")
            print(f"包含bbox的页面数: {result.get('pages_with_bbox')}")
            print(f"原始PDF总页数: {result.get('original_total_pages', 'N/A')}")
            print(f"图例已添加: {result.get('legend_added')}")
            print(f"标注统计: {result.get('annotation_statistics')}")
            
            print(f"\n🎯 效果总结:")
            print(f"- 原始PDF可能有很多页，但只保留了包含标注的 {result.get('pages_with_bbox')} 页")
            print(f"- 生成的PDF文件更精简，只显示重要内容")
            if result.get('legend_added'):
                print(f"- 包含图例页面")
            else:
                print(f"- 不包含图例页面，更简洁")
            
        else:
            print(f"\n❌ 请求失败! 状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"\n❌ 网络错误: {e}")
    except Exception as e:
        print(f"\n❌ 其他错误: {e}")

def test_original_draw_file_api():
    """
    测试原始的 /draw_file API
    现在也只保留包含bbox的页面，不添加图例
    """
    print("\n=== 测试原始draw_file API ===")
    
    # API URL
    url = "http://localhost:8037/draw_file"
    
    # 测试数据 - 转换为原始API格式
    data = {
        "filename": "amber_place.pdf",
        "pages_data": {
            "3": [
                {"content_type": "text_with_title", "bbox": [64.0, 486.0, 408.0, 630.0]},
                {"content_type": "text_with_title", "bbox": [64.0, 666.0, 408.0, 842.0]},
                {"content_type": "text", "bbox": [450.0, 358.0, 791.0, 430.0]},
                {"content_type": "text", "bbox": [449.0, 571.0, 791.0, 618.0]},
                {"content_type": "text", "bbox": [455.0, 651.0, 791.0, 723.0]}
            ],
            "4": [
                {"content_type": "text_with_title", "bbox": [45.0, 129.0, 689.0, 172.0]}
            ],
            "5": [
                {"content_type": "text", "bbox": [449.0, 298.0, 791.0, 334.0]}
            ],
            "6": [
                {"content_type": "text_with_title", "bbox": [44.0, 129.0, 689.0, 172.0]},
                {"content_type": "text_with_title", "bbox": [63.0, 556.0, 408.0, 673.0]}
            ],
            "7": [
                {"content_type": "text_with_title", "bbox": [64.0, 491.0, 407.0, 568.0]}
            ]
        }
    }
    
    print(f"发送请求到: {url}")
    print(f"文件名: {data['filename']}")
    print(f"包含标注的页面: {list(data['pages_data'].keys())} (共{len(data['pages_data'])}页)")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data)
        
        if response.status_code == 200:
            result = response.json()
            print("\n✅ 请求成功!")
            print(f"响应: {result.get('response')}")
            print(f"输出路径: {result.get('processed_file_path')}")
            print(f"总标注数量: {result.get('total_annotations')}")
            print(f"包含bbox的页面数: {result.get('pages_with_bbox')}")
            print(f"图例已添加: {result.get('legend_added')}")
            print(f"标注统计: {result.get('annotation_statistics')}")
            print(f"消息: {result.get('message')}")
            
            print(f"\n🎯 效果总结:")
            print(f"- 只保留了包含标注的 {result.get('pages_with_bbox')} 页")
            print(f"- 不包含图例页面，PDF更简洁")
            print(f"- 支持text_with_title类型标注")
            
        else:
            print(f"\n❌ 请求失败! 状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"\n❌ 网络错误: {e}")
    except Exception as e:
        print(f"\n❌ 其他错误: {e}")

def test_with_legend():
    """
    测试添加图例的功能（可选）
    """
    print("\n=== 测试添加图例功能 ===")
    
    url = "http://localhost:8037/annotate_pdf_with_chunks"
    
    data = {
        "filename": "amber_place.pdf",
        "add_legend": True,  # 明确要求添加图例
        "chunks_info": [
            {'bbox': [64.0, 486.0, 408.0, 630.0], 'page_num': 3, 'content_type': 'text_with_title', 'source': 'amberplacepdf'},
            {'bbox': [450.0, 358.0, 791.0, 430.0], 'page_num': 3, 'content_type': 'text', 'source': 'amberplacepdf'}
        ]
    }
    
    print(f"测试add_legend=True的情况")
    
    try:
        response = requests.post(url, json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功! 图例添加状态: {result.get('legend_added')}")
            print(f"消息: {result.get('message')}")
        else:
            print(f"❌ 失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def main():
    print("🚀 测试改进的PDF标注功能")
    print("✨ 新功能: 只显示包含bbox的页面，默认不添加图例")
    print("=" * 60)
    
    # 测试新的API（不添加图例）
    test_new_annotation_api()
    
    # 测试原始API（不添加图例）
    test_original_draw_file_api()
    
    # 可选：测试添加图例的功能
    test_with_legend()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")
    print("\n📖 功能说明:")
    print("1. 两个API现在都只保留包含bbox标注的页面")
    print("2. 默认不添加图例页面，PDF更简洁")
    print("3. 如需图例，可设置 add_legend: true")
    print("4. 生成的PDF更精简，只显示重要内容")
    print("5. 支持text_with_title等多种内容类型")
    print("6. 返回详细的统计信息")

if __name__ == "__main__":
    main() 