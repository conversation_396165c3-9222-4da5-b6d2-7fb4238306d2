import re


def remove_comments(filename):
    # 读取文件内容
    with open(filename, 'r', encoding='utf-8') as file:
        content = file.read()

    # 使用正则表达式删除单行注释
    content = re.sub(r'#.*', '', content)

    # 使用正则表达式删除多行注释
    content = re.sub(r'\'\'\'[\s\S]*?\'\'\'', '', content)
    content = re.sub(r'"""[\s\S]*?"""', '', content)

    # 删除空行
    content = '\n'.join(line for line in content.splitlines() if line.strip())

    # 写入新文件
    new_filename = filename.rsplit('.', 1)[0] + '_no_comments.py'
    with open(new_filename, 'w', encoding='utf-8') as file:
        file.write(content)

    print(f"注释已被删除,新文件保存为: {new_filename}")


# 使用示例
if __name__ == "__main__":
    input_filename = input("请输入要处理的Python文件名: ")
    remove_comments(input_filename)