"""
This script downloads and saves the necessary models for offline use.
Run this script when you have internet access (e.g., with a working VPN),
then you can run the chatbot without internet access.
"""

import os
from sentence_transformers import SentenceTransformer
from transformers import <PERSON>Tokenizer, AutoModel
import torch
import shutil
from tqdm import tqdm
import requests

# Create directories
os.makedirs("models/embeddings", exist_ok=True)
os.makedirs("models/embeddings/text2vec-base-chinese", exist_ok=True)
os.makedirs("models/embeddings/all-MiniLM-L6-v2", exist_ok=True)
os.makedirs("models/embeddings/bge-m3", exist_ok=True)

def download_with_progress(url, save_path):
    response = requests.get(url, stream=True)
    total_size = int(response.headers.get('content-length', 0))
    
    with open(save_path, 'wb') as file, tqdm(
        desc=os.path.basename(save_path),
        total=total_size,
        unit='iB',
        unit_scale=True,
        unit_divisor=1024,
    ) as pbar:
        for data in response.iter_content(chunk_size=1024):
            size = file.write(data)
            pbar.update(size)

def download_and_save_model(model_name, save_path):
    print(f"Downloading {model_name}...")
    model = SentenceTransformer(model_name)
    print(f"Saving {model_name} to {save_path}...")
    model.save(save_path)
    print(f"Successfully saved {model_name}")

# Download BGE-M3 model
try:
    download_and_save_model("BAAI/bge-m3", "models/embeddings/bge-m3")
    print("✅ Successfully downloaded and saved BGE-M3 model")
except Exception as e:
    print(f"❌ Failed to download BGE-M3 model: {e}")

# Download main Chinese model
try:
    download_and_save_model("shibing624/text2vec-base-chinese", "models/embeddings/text2vec-base-chinese")
    print("✅ Successfully downloaded and saved Chinese embedding model")
except Exception as e:
    print(f"❌ Failed to download Chinese model: {e}")

# Download fallback model (smaller and more reliable)
try:
    download_and_save_model("sentence-transformers/all-MiniLM-L6-v2", "models/embeddings/all-MiniLM-L6-v2")
    print("✅ Successfully downloaded and saved fallback embedding model")
except Exception as e:
    print(f"❌ Failed to download fallback model: {e}")

print("\nModel download complete. You can now use the chatbot offline.")
print("To start the chatbot with local models, run: python start_with_local_model.py") 