#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from html.parser import HTMLParser
import re

def parse_html_table_to_text(html_content, show_separator=True, separator_style='auto'):
    """
    将HTML表格转换为格式化的纯文本
    保持表格结构但移除HTML标签
    
    Args:
        html_content: HTML表格字符串
        show_separator: 是否显示标题行后的分隔线
        separator_style: 分隔线样式 ('auto', 'fixed', 'minimal')
                        - 'auto': 根据列宽自动调整长度
                        - 'fixed': 固定长度分隔线（每列30字符）
                        - 'minimal': 最小分隔线（每列3个短横线）
    
    Returns:
        str: 格式化的纯文本表格
    """
    
    class TableParser(HTMLParser):
        def __init__(self):
            super().__init__()
            self.rows = []
            self.current_row = []
            self.current_cell = ""
            self.in_td = False
            self.in_th = False
            
        def handle_starttag(self, tag, attrs):
            if tag == 'tr':
                self.current_row = []
            elif tag in ['td', 'th']:
                self.in_td = True
                self.in_th = (tag == 'th')
                self.current_cell = ""
                
        def handle_endtag(self, tag):
            if tag == 'tr':
                if self.current_row:
                    self.rows.append(self.current_row)
            elif tag in ['td', 'th']:
                self.in_td = False
                self.in_th = False
                # 清理单元格内容
                cleaned_cell = re.sub(r'\s+', ' ', self.current_cell.strip())
                self.current_row.append(cleaned_cell)
                self.current_cell = ""
                
        def handle_data(self, data):
            if self.in_td or self.in_th:
                self.current_cell += data
    
    # 如果输入已经是纯文本，直接返回
    if not html_content.strip().startswith('<'):
        return html_content
        
    parser = TableParser()
    parser.feed(html_content)
    
    if not parser.rows:
        return html_content
    
    # 转换为格式化文本
    formatted_text = ""
    
    # 计算每列的最大宽度（用于对齐）
    max_widths = []
    for row in parser.rows:
        for i, cell in enumerate(row):
            if i >= len(max_widths):
                max_widths.append(0)
            # 考虑中英文字符宽度差异
            cell_width = len(cell) + len([c for c in cell if ord(c) > 127])
            max_widths[i] = max(max_widths[i], cell_width)
    
    # 生成格式化表格
    for i, row in enumerate(parser.rows):
        row_text = ""
        for j, cell in enumerate(row):
            if j < len(max_widths):
                # 计算需要的填充空格
                cell_display_width = len(cell) + len([c for c in cell if ord(c) > 127])
                padding = max_widths[j] - cell_display_width + 2
                row_text += cell + " " * max(padding, 1)
            else:
                row_text += cell + "  "
        
        formatted_text += row_text.rstrip() + "\n"
        
        # 在标题行后添加分隔线（可选）
        if show_separator and i == 0 and len(parser.rows) > 1:
            separator = ""
            for j, cell in enumerate(row):
                if j < len(max_widths):
                    if separator_style == 'minimal':
                        separator += "--- "
                    elif separator_style == 'fixed':
                        separator += "-" * min(30, max_widths[j]) + "  "
                    else:  # 'auto'
                        separator += "-" * (max_widths[j] + 2)
                else:
                    if separator_style == 'minimal':
                        separator += "--- "
                    elif separator_style == 'fixed':
                        separator += "-" * min(30, len(cell)) + "  "
                    else:  # 'auto'
                        separator += "-" * (len(cell) + 2)
            formatted_text += separator.rstrip() + "\n"
    
    return formatted_text.strip()

def convert_html_table_to_markdown(html_content):
    """
    将HTML表格转换为Markdown格式的表格
    """
    
    class MarkdownTableParser(HTMLParser):
        def __init__(self):
            super().__init__()
            self.rows = []
            self.current_row = []
            self.current_cell = ""
            self.in_td = False
            self.in_th = False
            
        def handle_starttag(self, tag, attrs):
            if tag == 'tr':
                self.current_row = []
            elif tag in ['td', 'th']:
                self.in_td = True
                self.in_th = (tag == 'th')
                self.current_cell = ""
                
        def handle_endtag(self, tag):
            if tag == 'tr':
                if self.current_row:
                    self.rows.append(self.current_row)
            elif tag in ['td', 'th']:
                self.in_td = False
                self.in_th = False
                cleaned_cell = re.sub(r'\s+', ' ', self.current_cell.strip())
                # 转义Markdown特殊字符
                cleaned_cell = cleaned_cell.replace('|', '\\|')
                self.current_row.append(cleaned_cell)
                self.current_cell = ""
                
        def handle_data(self, data):
            if self.in_td or self.in_th:
                self.current_cell += data
    
    if not html_content.strip().startswith('<'):
        return html_content
        
    parser = MarkdownTableParser()
    parser.feed(html_content)
    
    if not parser.rows:
        return html_content
    
    markdown_text = ""
    
    for i, row in enumerate(parser.rows):
        # 创建表格行
        markdown_text += "| " + " | ".join(row) + " |\n"
        
        # 在第一行后添加分隔行
        if i == 0:
            separator = "|" + "|".join([" --- " for _ in row]) + "|\n"
            markdown_text += separator
    
    return markdown_text.strip()

if __name__ == "__main__":
    # 测试HTML表格
    html_table = """<html><body><table><tr><td>Name of the Development</td><td>發展項目名稱</td></tr><tr><td>Amber Place</td><td>恒珀</td></tr><tr><td>The name of the street at which the Development is situated and the street number allocated by the Commissioner of Rating and Valuation for the purpose of distinguishing the Development</td><td>發展項目所位於的街道的名稱及由差餉物業估價署署長為識別發展項目的目的而 編配的門牌號數 昌華街1號</td></tr><tr><td>No.1 Cheung Wah Street</td><td></td></tr><tr><td>The Development consists of one multi-unit building</td><td>發展項目包含一幢多單位建築物</td></tr><tr><td>Total number of storeys of the multi-unit building 26 (exclusive of Roof, Upper Roof 1, Upper Roof 2 and Top Roof)</td><td>該幢多單位建築物的樓層的總數 26 層 (不包括天台丶上層天台1、上層天台2及頂層天台)</td></tr><tr><td>The floor numbering in the multi-unit building as provided in the approved</td><td></td></tr><tr><td>building plans for the Development G/F,1/F -3/F, U3/F,5/F-12/F,15/F -23/F,25/F- 28/F,R0of, Upper Ro0f1,Upper Roof 2 and Top Roof</td><td>發展項目的經批准的建築圖則所規定的該幢多單位建築物內的樓層號數 地下、1樓至3樓`U3樓`5樓至12樓`15樓至23樓`25樓至28樓、天台` 上層天台1、上層天台2及頂層天台</td></tr><tr><td>Omitted floor numbers in the multi-unit building in which the floor numbering is</td><td>有不依連續次序的樓層號數的該幢多單位建築物內被略去的樓層號數</td></tr><tr><td>not in consecutive order 4/F,13/F,14/F and 24/F are omitted</td><td>不設4樓、13樓、14樓及24樓</td></tr><tr><td></td><td></td></tr><tr><td>Refuge floor of the multi-unit building</td><td>該幢多單位建築物内的庇護層</td></tr></table></body></html>"""
    
    print("原始HTML表格:")
    print(html_table[:200] + "...")
    print("\n" + "="*100 + "\n")
    
    # 测试不同格式选项
    print("1. 不显示分隔线的格式:")
    no_separator = parse_html_table_to_text(html_table, show_separator=False)
    print(no_separator)
    print("\n" + "="*50 + "\n")
    
    print("2. 最小分隔线格式:")
    minimal_separator = parse_html_table_to_text(html_table, show_separator=True, separator_style='minimal')
    print(minimal_separator)
    print("\n" + "="*50 + "\n")
    
    print("3. 固定长度分隔线格式:")
    fixed_separator = parse_html_table_to_text(html_table, show_separator=True, separator_style='fixed')
    print(fixed_separator)
    print("\n" + "="*50 + "\n")
    
    print("4. Markdown格式:")
    markdown_text = convert_html_table_to_markdown(html_table)
    print(markdown_text) 
    print("\n" + "="*50 + "\n")
    
    print("推荐使用: 不显示分隔线或最小分隔线格式，避免过长的分隔线") 