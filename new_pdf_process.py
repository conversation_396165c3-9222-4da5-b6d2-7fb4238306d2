import fitz  # PyMuPDF
import pdfplumber
from langchain.text_splitter import RecursiveCharacterTextSplitter
from typing import List, Tuple, Dict
import os

def process_pdf(pdf_path: str, chunk_size: int = 1000, chunk_overlap: int = 200):
    chunks = []
    elements = []

    doc = fitz.open(pdf_path)

    full_text = ""
    page_texts = []
    page_lengths = []

    # 提取每页的文本并记录每页的长度
    for page_num in range(len(doc)):
        page = doc[page_num]
        text = page.get_text()
        if text:
            page_texts.append((text, page_num))
            page_lengths.append(len(text))
            full_text += text

    # 使用 LangChain 的 RecursiveCharacterTextSplitter 进行文本分块
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        length_function=len,
    )
    text_chunks = text_splitter.split_text(full_text)

    # 关联每个文本块到对应的页码
    current_pos = 0
    page_cumulative_lengths = [sum(page_lengths[:i+1]) for i in range(len(page_lengths))]

    for chunk in text_chunks:
        chunk_start = full_text.find(chunk, current_pos)
        chunk_end = chunk_start + len(chunk)
        current_pos = chunk_end

        # 确定该文本块所在的页码
        start_page = next((i for i, v in enumerate(page_cumulative_lengths) if v > chunk_start), len(page_cumulative_lengths)-1)
        end_page = next((i for i, v in enumerate(page_cumulative_lengths) if v >= chunk_end), len(page_cumulative_lengths)-1)
        chunk_page_numbers = list(range(start_page, end_page+1))

        # 创建文本块元数据
        chunk_metadata = {
            "type": "text",
            "pages": chunk_page_numbers,
            "content": chunk
        }

        chunks.append(chunk_metadata)

    doc.close()

    # 使用 pdfplumber 处理图像和表格
    with pdfplumber.open(pdf_path) as pdf:
        for page_num, page in enumerate(pdf.pages):
            # 处理图像
            for img in page.images:
                element = {
                    "type": "image",
                    "bbox": (img['x0'], img['top'], img['x1'], img['bottom']),
                    "page_num": page_num,
                    "content": f"Image on page {page_num + 1}"
                }
                elements.append(element)
                # 每个图像作为单独的 chunk
                chunks.append(element)

            # 处理表格
            for table in page.find_tables():
                element = {
                    "type": "table",
                    "bbox": table.bbox,
                    "page_num": page_num,
                    "content": f"Table on page {page_num + 1}"
                }
                elements.append(element)
                # 每个表格作为单独的 chunk
                chunks.append(element)

    return chunks, elements

def visualize_pdf_elements(input_pdf_path: str, output_pdf_path: str, chunks, elements):
    doc = fitz.open(input_pdf_path)
    for page_num in range(len(doc)):
        page = doc[page_num]

        # 绘制文本块（可选）
        for chunk in chunks:
            if chunk['type'] == 'text' and page_num in chunk['pages']:
                # 如果需要绘制文本块的位置，需要进一步处理以获取精确的 bbox
                pass

        # 绘制图像和表格
        for element in elements:
            if element['page_num'] == page_num:
                rect = fitz.Rect(element['bbox'])
                if element['type'] == 'image':
                    color = (1, 0, 0)  # 红色表示图像
                elif element['type'] == 'table':
                    color = (0, 0, 1)  # 蓝色表示表格
                else:
                    color = (0, 0, 0)  # 黑色表示其他
                # 添加透明度
                fill_color = color + (0.7,)  # 最后的0.2表示透明度
                page.draw_rect(rect, color=color, width=1)
                page.draw_rect(rect, fill=fill_color)

    doc.save(output_pdf_path)
    doc.close()
    print(f"已生成可视化的PDF文件：{output_pdf_path}")

# 使用示例
pdf_path = "uploads/240328_HR_Handbook_EN-34.pdf"
chunks, elements = process_pdf(pdf_path)
visualize_pdf_elements(pdf_path, "output_visualized.pdf", chunks, elements)
