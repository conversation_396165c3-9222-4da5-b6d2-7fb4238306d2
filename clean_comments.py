import re

def remove_comments(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 删除HTML注释 <!-- -->
    content = re.sub(r'<!--[\s\S]*?-->', '', content)
    
    # 删除CSS和JS多行注释 /* */
    content = re.sub(r'/\*[\s\S]*?\*/', '', content)
    
    # 删除JS单行注释 //
    content = re.sub(r'//.*?$', '', content, flags=re.MULTILINE)
    
    # 写入清理后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已成功删除注释并保存到 {output_file}")

if __name__ == "__main__":
    remove_comments("templates/index_clean.html", "templates/index_clean.html") 