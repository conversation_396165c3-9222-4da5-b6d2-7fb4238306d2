"""

    Module Name :           vectorstore_module
    Last Modified Date :    8 Jan 2024

"""

# Import Libraries
from langchain.vectorstores import Mi<PERSON>vus
from langchain.document_loaders import DirectoryLoader
from langchain.docstore.document import Document
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.embeddings import HuggingFaceInstructEmbeddings
import os
import pandas as pd

# Milvus Database
from pymilvus import (
    db,
    connections,
    utility,
    FieldSchema,
    CollectionSchema,
    DataType,
    Collection,
    Partition
)

import preprocessing
import env
#

def init_vsdb(vs_config):

    connections.connect(host=vs_config["host"],
                        port=vs_config["port"],
                        db_name="gloria")

    collection_list = utility.list_collections()
    dataset_list = []

    for collection_name in collection_list:
        collection = Collection(collection_name)
        dataset_dict = {
            "dataset_name": collection_name,
            "document_list": [partition.name for partition in collection.partitions]
        }

        try:
            dataset_dict["document_list"].remove("_default")
        except:
            pass

        dataset_list.append(dataset_dict)
    return dataset_list


def get_retriever(vs_config,
                  embeddings,
                  selected_dataset,
                  search_config):

    retriever = None

    # RAG is OFF
    if not selected_dataset:
        return retriever

    # RAG is ON
    # load vector store
    vs_db = load_collection(vs_config,
                            embeddings,
                            selected_dataset)

    retriever = vs_db.as_retriever(search_type=search_config["search_type"],
                                   search_kwargs=search_config["search_kwargs"])
    return retriever

def get_retriever2(vs_config,
                   embeddings,
                   selected_dataset,
                   selected_files,
                   search_config):

    retriever = None

    # RAG is OFF
    if not selected_dataset:
        return retriever

    expr = gen_query_expression(selected_files)
    print("selected_files: ", selected_files)
    print("search_expression: ", expr)
    vs_db = load_collection(vs_config,
                            embeddings,
                            selected_dataset)

    # retriever = vs_db.as_retriever(partition_key_field=expr,
    #                                search_type=search_config["search_type"],
    #                                search_kwargs=search_config["search_kwargs"])
    retriever = vs_db.as_retriever(
        search_kwargs={"expr": expr}
    )
    # retriever = vs_db.as_retriever(
    #     search_kwargs = {"filter": {"source": expr}}
    # )

    return retriever


""" Vector Store Management """
def create_vscollection(vs_config,
                        collection_name,
                        schema_config):

    def parse_vsschema(schema_config):
        fields = []
        for field, item in schema_config.items():

            # <field_0> must be uid
            if field == "field_0":
                fieldschema = FieldSchema(
                    name=item["name"],
                    dtype=eval(item["dtype"]),
                    auto_id=eval(item["auto_id"]),
                    is_primary=True
                )

            # name <vector> cannot be changed
            elif item["name"] == "vector":
                fieldschema = FieldSchema(
                    name=item["name"],
                    dtype=eval(item["dtype"]),
                    dim=item["dim"]
                )
            else:
                try:
                    # For VARCHAR Datatype
                    fieldschema = FieldSchema(
                        name=item["name"],
                        dtype=eval(item["dtype"]),
                        max_length = item["max_length"]
                    )
                except KeyError:
                    fieldschema = FieldSchema(
                        name=item["name"],
                        dtype=eval(item["dtype"]),
                    )
            fields.append(fieldschema)

        return fields

    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])

    # Check if Collection Exists
    if utility.has_collection(collection_name):
        print(f'> Collection Name <{collection_name}> Already Exists!')
        return False

    # Init Collection
    fields = parse_vsschema(schema_config)
    schema = CollectionSchema(fields)
    collection = Collection(collection_name, schema=schema)

    # Indexing the Vectors
    index_params = {
        "metric_type": "L2",
        "index_type": "HNSW",
        "params": {"M": 8,
                   "efConstruction": 64}
    }
    collection.create_index(
        field_name="vector",
        index_params=index_params
    )
    utility.index_building_progress(collection_name)

    collection.flush()
    collection.load()
    return True

def create_vspartition(vs_config,
                       collection_name,
                       partition_name):

    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])
    collection = Collection(collection_name)

    # Check if Partition Exists
    if collection.has_partition(partition_name):
        print(f'> Parition Name <{partition_name}> Already Exists!')
        return False

    # Create New Partition
    collection.create_partition(partition_name)
    return True

def insert_vectors(vs_config,
                   collection_name,
                   partition_name,
                   schema_config,
                   texts,
                   vectors,
                   field_dict):

    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])
    collection = Collection(collection_name)

    # Create Entities
    entities = [vectors, texts]
    vector_length = len(vectors)
    for field, item in schema_config.items():
        # Skip <field_0> uid
        if field == "field_0":
            continue

        # Skip <vector>
        elif item["name"] == "vector":
            continue

        # Skip <text>
        elif item["name"] == "text":
            continue

        else:
            try:
                value = field_dict[item["name"]]
            except KeyError:
                print('** Warning : The Field <{}> was not Given. "Undefined" is Assigned by Deafult')
                value = "Undefined"

            field_list = [value] * vector_length
            entities.append(field_list)

    # Insert Vectors into Milvus
    collection.insert(entities, partition_name=partition_name)
    collection.flush()
    # collection.load()
    return collection



def select_entities(vs_config,
                    collection_name,
                    filelist):
    connections.connect(host=vs_config["host"], port=vs_config["port"])
    collection = Collection(collection_name)
    collection.load()

    expr = gen_query_expression(filelist)
    res = collection.query(
        expr=expr,
        output_fields=["vector"]
    )
    return res





def load_collection(vs_config,
                    embeddings,
                    colleciton_name):

    milvusDb = Milvus(
        embeddings,
        collection_name=colleciton_name,
        connection_args={"host": vs_config["host"],
                         "port": vs_config["port"]},
    )
    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])
    return milvusDb


# *** Need to revise later
def gen_metadata(filename,
                 chunked_content,
                 title='T',
                 language='us'):

    chunks_with_metadata = []
    # split_docs = chunks
    for chunk in chunked_content:
        met = chunk.metadata
        met['title'] = title
        met['description'] = filename
        met['language'] = language
        chunks_with_metadata.append(Document(page_content=chunk.page_content,
                                             metadata=met))
    return chunks_with_metadata


def add_to_collection(vs_config,
                      chunks_with_metadata,
                      embedding,
                      colleciton_name):

    milvusDb = Milvus.from_documents(
        chunks_with_metadata,
        embedding=embedding,
        collection_name=colleciton_name,
        connection_args={"host": vs_config["host"],
                         "port": vs_config["port"]}
    )


def delete_entities(vs_config,
                    del_file,
                    selected_dataset):

    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])
    collection = Collection(selected_dataset)
    print(f"Before delete entities = {collection.num_entities}")
    partition = Partition(collection=selected_dataset, name=del_file)
    partition.release()
    collection.drop_partition(del_file)
    # collection.delete(expr)
    collection.flush()
    collection.load()
    print(f"After delete entities = {collection.num_entities}")
    print("Delete entities")




def delete_collection(vs_config,
                      selected_dataset):
    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])
    utility.drop_collection(selected_dataset)
    vs_df = load_vs_df()
    new_vs_df = vs_df.loc[vs_df["dataset"] != selected_dataset]
    new_vs_df.to_json(env.vsdb_log_dir, orient='records', indent=4)
    return new_vs_df

def format_docs(docs):
    return "\n\n".join(doc.page_content for doc in docs)


def gen_query_expression(filelist):
    if not filelist:
        return ""

    _list = '[' + ', '.join([f'\"{name}\"' for name in filelist]) + ']'

    expr = f"source in {_list}"

    # like_clauses = [f"source == '{filename}'" for filename in filelist]
    # expr = " || ".join(like_clauses)
    return expr


def gen_query_expression_2(filelist):
    if not filelist:
        return ""

    expr = " && ".join([f'source == "{name}"' for name in filelist])

    return expr


""" Vector Store Log Management """
def load_vs_df(vsdb_log_dir=env.vsdb_log_dir):
    # Load vs_df
    if os.path.exists(vsdb_log_dir):
        vs_df = pd.read_json(vsdb_log_dir, orient='records')

    # Cretae new vs_df
    else:
        vs_df = pd.DataFrame()

    return vs_df

def update_vs_df(new_file, vsdb_log_dir=env.vsdb_log_dir):

    # Load Existing Log
    vs_df = load_vs_df(vsdb_log_dir)
    new_df = pd.DataFrame([new_file])

    # Update vs_df log json
    if len(vs_df) == 0:
        index = 0
        offset = 0
    else:
        index = vs_df.iloc[-1]["index"] + 1
        offset = vs_df.iloc[-1]["offset"] + vs_df.iloc[-1]["chunk_size"]

    new_df["index"] = index
    new_df["offset"] = offset
    vs_df = pd.concat([vs_df, new_df], ignore_index=True)
    vs_df.to_json(vsdb_log_dir, orient='records', indent=4)
    print("Updated Vector Store Log File")

    return vs_df

def delete_vs_df(del_file, vsdb_log_dir=env.vsdb_log_dir):

    def reset_offset_vs_df(vs_df):
        # Update vs_df log json
        vs_df = vs_df.reset_index(drop=True)
        for i, row in vs_df.iterrows():
            print(row["filename"])
            if i == 0:
                index = 0
                offset = 0
            else:
                index = vs_df.iloc[i-1]["index"] + vs_df.iloc[i-1]["index"]
                offset = vs_df.iloc[i-1]["offset"] + vs_df.iloc[i-1]["chunk_size"]
            vs_df.at[i, "index"] = index
            vs_df.at[i, "offset"] = offset
        return vs_df

    # Load Existing Log
    vs_df = load_vs_df(vsdb_log_dir)
    new_vs_df = vs_df.loc[vs_df['filename'] != del_file]
    vs_df = reset_offset_vs_df(new_vs_df)
    vs_df.to_json(vsdb_log_dir, orient='records', indent=4)

    return vs_df

# def select_entities(host,
#                     port,
#                     filelist,
#                     collection_name):
#     connections.connect(host=host, port=port)
#     collection = Collection(collection_name)
#     collection.load()

#     expr = gen_query_expression(filelist)
#     res = collection.query(
#         expr=expr,
#         output_fields=["vector"]
#     )
#     return res

def init_partition(vs_config,
                   collection_name,
                   filelist):
    connections.connect(host=vs_config["host"], port=vs_config["port"])
    collection = Collection(collection_name)
    collection.load(filelist)
    return collection

def load_partition(collection, filelist):
    collection.load(filelist)
    return collection

def release_partition(collection, filelist):
    collection.release(filelist)
    return collection


# # Get Retriever
# def init_vectordb(directory,
#                   host,
#                   port,
#                   collection_name,
#                   embedding_config,
#                   textsplit_chunk_size=1000,
#                   textsplit_chunk_overlap=20,
#                   search_type="similarity",
#                   search_kwargs={"k":6}):
#
#     if not directory:
#         return None
#
#     # Remove Old Milvus collection
#     MILVUS_HOST = host
#     MILVUS_PORT = port
#     COLLECTION_NAME = collection_name
#     milvus_connection = connections.connect("default",
#                                             host=MILVUS_HOST,
#                                             port=MILVUS_PORT)
#
#     loader = DirectoryLoader(directory)
#     documents = loader.load()
#
#     """ *** Refine Later >>> Duplicated variable name """
#     text_splitter = CharacterTextSplitter(chunk_size=textsplit_chunk_size,
#                                           chunk_overlap=textsplit_chunk_overlap)
#     text_splitter = RecursiveCharacterTextSplitter(chunk_size=textsplit_chunk_size,
#                                                    chunk_overlap=textsplit_chunk_overlap)
#
#     split_docs = text_splitter.split_documents(documents)
#
#     new_doc = []
#     for doc in split_docs:
#         met = doc.metadata
#         met['title'] = "L"
#         met['description'] = "L"
#         met['language'] = 'us'
#         new_doc.append(Document(page_content=doc.page_content, metadata=met))
#     #    	continue
#
#     embeddings = preprocessing.load_embedding(embedding_config)
#
#     docsearch = ""
#     # Create New Milvus collection & Store new documents into the collection
#     if not utility.has_collection(COLLECTION_NAME):
#         docsearch = create_collection(new_doc, embeddings)
#
#     # Retrieve Existing Vector Store
#     else:
#         """ *** Refine Later >>> Where would this parameter be used? *** """
#         collection = Collection(COLLECTION_NAME)  # Get an existing collection
#         docsearch = load_collection(new_doc, embeddings)
#
#     retriever = docsearch.as_retriever(search_type=search_type, search_kwargs=search_kwargs)
#     return retriever