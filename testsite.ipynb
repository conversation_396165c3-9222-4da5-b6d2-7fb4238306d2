{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2024-02-21T08:05:09.753824336Z", "start_time": "2024-02-21T08:05:09.751764759Z"}}, "outputs": [], "source": ["\"\"\"\n", "\n", "    Module Name :           chatbot\n", "    Last Modified Date :    23 Jan 2024\n", "\n", "\"\"\"\n", "import json\n", "import shutil\n", "\n", "from flask import Flask, render_template, request, jsonify, send_from_directory\n", "import os\n", "import pandas as pd\n", "from langchain.memory import ChatMessageHistory\n", "from langchain.document_loaders import DirectoryLoader\n", "from datetime import datetime\n", "from werkzeug.utils import secure_filename\n", "from datetime import datetime\n", "import torch\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import Self-defined Libraries\n", "import env\n", "import preprocessing\n", "import prompt_module\n", "import vectorstore_module\n", "import openai_module\n", "import llama_module\n", "import mixtral_module\n", "import abandoned_instruct_mixtral_module\n", "import init_interface\n", "import test_module"]}, {"cell_type": "code", "outputs": [{"data": {"text/plain": "Loading checkpoint shards:   0%|          | 0/19 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6db4deb0bfb040f4b52f2cc950697ff9"}}, "metadata": {}, "output_type": "display_data"}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[6], line 5\u001b[0m\n\u001b[1;32m      3\u001b[0m mixtral_config \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mr\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel/mixtral/mixtral-1_config.json\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      4\u001b[0m pipeline_starttime \u001b[38;5;241m=\u001b[39m datetime\u001b[38;5;241m.\u001b[39mnow()\n\u001b[0;32m----> 5\u001b[0m mixtral_pipeline \u001b[38;5;241m=\u001b[39m \u001b[43mmixtral_module\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minit_mixtral_piepeline\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_json\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmixtral_config\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtyp\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mseries\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      6\u001b[0m llama_config \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mr\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel/llama/llama+rag-1_config.json\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      7\u001b[0m \u001b[38;5;66;03m# llama_pipeline = llama_module.init_llama_piepeline(pd.read_json(llama_config, typ='series'))\u001b[39;00m\n", "File \u001b[0;32m~/Trial_2 (copy)/mixtral_module.py:29\u001b[0m, in \u001b[0;36minit_mixtral_piepeline\u001b[0;34m(model_para)\u001b[0m\n\u001b[1;32m     28\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21minit_mixtral_piepeline\u001b[39m(model_para):\n\u001b[0;32m---> 29\u001b[0m     pipeline \u001b[38;5;241m=\u001b[39m \u001b[43mtransformers\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpipeline\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     30\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmodel_para\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtask\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     31\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmodel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel_para\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmodel\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     32\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdevice_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel_para\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mdevice_map\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     33\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmodel_kwargs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtorch_dtype\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43meval\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mmodel_para\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtorch_dtype\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     34\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmax_new_tokens\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel_para\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmax_new_tokens\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     35\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrepetition_penalty\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel_para\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mrepetition_penalty\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     36\u001b[0m \u001b[43m        \u001b[49m\u001b[43mreturn_full_text\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43meval\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mmodel_para\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mreturn_full_text\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     37\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     38\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m pipeline\n", "File \u001b[0;32m~/anaconda3/envs/llama/lib/python3.10/site-packages/transformers/pipelines/__init__.py:870\u001b[0m, in \u001b[0;36mpipeline\u001b[0;34m(task, model, config, tokenizer, feature_extractor, image_processor, framework, revision, use_fast, token, device, device_map, torch_dtype, trust_remote_code, model_kwargs, pipeline_class, **kwargs)\u001b[0m\n\u001b[1;32m    868\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(model, \u001b[38;5;28mstr\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m framework \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    869\u001b[0m     model_classes \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtf\u001b[39m\u001b[38;5;124m\"\u001b[39m: targeted_task[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtf\u001b[39m\u001b[38;5;124m\"\u001b[39m], \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpt\u001b[39m\u001b[38;5;124m\"\u001b[39m: targeted_task[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpt\u001b[39m\u001b[38;5;124m\"\u001b[39m]}\n\u001b[0;32m--> 870\u001b[0m     framework, model \u001b[38;5;241m=\u001b[39m \u001b[43minfer_framework_load_model\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    871\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    872\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmodel_classes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel_classes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    873\u001b[0m \u001b[43m        \u001b[49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    874\u001b[0m \u001b[43m        \u001b[49m\u001b[43mframework\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mframework\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    875\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    876\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mhub_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    877\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mmodel_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    878\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    880\u001b[0m model_config \u001b[38;5;241m=\u001b[39m model\u001b[38;5;241m.\u001b[39mconfig\n\u001b[1;32m    881\u001b[0m hub_kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_commit_hash\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m model\u001b[38;5;241m.\u001b[39mconfig\u001b[38;5;241m.\u001b[39m_commit_hash\n", "File \u001b[0;32m~/anaconda3/envs/llama/lib/python3.10/site-packages/transformers/pipelines/base.py:278\u001b[0m, in \u001b[0;36minfer_framework_load_model\u001b[0;34m(model, config, model_classes, task, framework, **model_kwargs)\u001b[0m\n\u001b[1;32m    272\u001b[0m     logger\u001b[38;5;241m.\u001b[39mwarning(\n\u001b[1;32m    273\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mModel might be a PyTorch model (ending with `.bin`) but PyTorch is not available. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    274\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTrying to load the model with Tensorflow.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    275\u001b[0m     )\n\u001b[1;32m    277\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 278\u001b[0m     model \u001b[38;5;241m=\u001b[39m \u001b[43mmodel_class\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_pretrained\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    279\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(model, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124meval\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[1;32m    280\u001b[0m         model \u001b[38;5;241m=\u001b[39m model\u001b[38;5;241m.\u001b[39meval()\n", "File \u001b[0;32m~/anaconda3/envs/llama/lib/python3.10/site-packages/transformers/models/auto/auto_factory.py:566\u001b[0m, in \u001b[0;36m_BaseAutoModelClass.from_pretrained\u001b[0;34m(cls, pretrained_model_name_or_path, *model_args, **kwargs)\u001b[0m\n\u001b[1;32m    564\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mtype\u001b[39m(config) \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m_model_mapping\u001b[38;5;241m.\u001b[39mkeys():\n\u001b[1;32m    565\u001b[0m     model_class \u001b[38;5;241m=\u001b[39m _get_model_class(config, \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m_model_mapping)\n\u001b[0;32m--> 566\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mmodel_class\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_pretrained\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    567\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mmodel_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mhub_kwargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\n\u001b[1;32m    568\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    569\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    570\u001b[0m     \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnrecognized configuration class \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mconfig\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m for this kind of AutoModel: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    571\u001b[0m     \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mModel type should be one of \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m.\u001b[39mjoin(c\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mfor\u001b[39;00m\u001b[38;5;250m \u001b[39mc\u001b[38;5;250m \u001b[39m\u001b[38;5;129;01min\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m_model_mapping\u001b[38;5;241m.\u001b[39mkeys())\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    572\u001b[0m )\n", "File \u001b[0;32m~/anaconda3/envs/llama/lib/python3.10/site-packages/transformers/modeling_utils.py:3850\u001b[0m, in \u001b[0;36mPreTrainedModel.from_pretrained\u001b[0;34m(cls, pretrained_model_name_or_path, config, cache_dir, ignore_mismatched_sizes, force_download, local_files_only, token, revision, use_safetensors, *model_args, **kwargs)\u001b[0m\n\u001b[1;32m   3841\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m dtype_orig \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   3842\u001b[0m         torch\u001b[38;5;241m.\u001b[39mset_default_dtype(dtype_orig)\n\u001b[1;32m   3843\u001b[0m     (\n\u001b[1;32m   3844\u001b[0m         model,\n\u001b[1;32m   3845\u001b[0m         missing_keys,\n\u001b[1;32m   3846\u001b[0m         unexpected_keys,\n\u001b[1;32m   3847\u001b[0m         mismatched_keys,\n\u001b[1;32m   3848\u001b[0m         offload_index,\n\u001b[1;32m   3849\u001b[0m         error_msgs,\n\u001b[0;32m-> 3850\u001b[0m     ) \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_load_pretrained_model\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   3851\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3852\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstate_dict\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3853\u001b[0m \u001b[43m        \u001b[49m\u001b[43mloaded_state_dict_keys\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# XXX: rename?\u001b[39;49;00m\n\u001b[1;32m   3854\u001b[0m \u001b[43m        \u001b[49m\u001b[43mresolved_archive_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3855\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3856\u001b[0m \u001b[43m        \u001b[49m\u001b[43mignore_mismatched_sizes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mignore_mismatched_sizes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3857\u001b[0m \u001b[43m        \u001b[49m\u001b[43msharded_metadata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msharded_metadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3858\u001b[0m \u001b[43m        \u001b[49m\u001b[43m_fast_init\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m_fast_init\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3859\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlow_cpu_mem_usage\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlow_cpu_mem_usage\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3860\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdevice_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdevice_map\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3861\u001b[0m \u001b[43m        \u001b[49m\u001b[43moffload_folder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moffload_folder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3862\u001b[0m \u001b[43m        \u001b[49m\u001b[43moffload_state_dict\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moffload_state_dict\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3863\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtorch_dtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3864\u001b[0m \u001b[43m        \u001b[49m\u001b[43mis_quantized\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mquantization_method\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m==\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mQuantizationMethod\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mBITS_AND_BYTES\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3865\u001b[0m \u001b[43m        \u001b[49m\u001b[43mkeep_in_fp32_modules\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkeep_in_fp32_modules\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3866\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3868\u001b[0m model\u001b[38;5;241m.\u001b[39mis_loaded_in_4bit \u001b[38;5;241m=\u001b[39m load_in_4bit\n\u001b[1;32m   3869\u001b[0m model\u001b[38;5;241m.\u001b[39mis_loaded_in_8bit \u001b[38;5;241m=\u001b[39m load_in_8bit\n", "File \u001b[0;32m~/anaconda3/envs/llama/lib/python3.10/site-packages/transformers/modeling_utils.py:4284\u001b[0m, in \u001b[0;36mPreTrainedModel._load_pretrained_model\u001b[0;34m(cls, model, state_dict, loaded_keys, resolved_archive_file, pretrained_model_name_or_path, ignore_mismatched_sizes, sharded_metadata, _fast_init, low_cpu_mem_usage, device_map, offload_folder, offload_state_dict, dtype, is_quantized, keep_in_fp32_modules)\u001b[0m\n\u001b[1;32m   4280\u001b[0m                     set_module_quantized_tensor_to_device(\n\u001b[1;32m   4281\u001b[0m                         model_to_load, key, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcpu\u001b[39m\u001b[38;5;124m\"\u001b[39m, torch\u001b[38;5;241m.\u001b[39mempty(\u001b[38;5;241m*\u001b[39mparam\u001b[38;5;241m.\u001b[39msize(), dtype\u001b[38;5;241m=\u001b[39mdtype)\n\u001b[1;32m   4282\u001b[0m                     )\n\u001b[1;32m   4283\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 4284\u001b[0m         new_error_msgs, offload_index, state_dict_index \u001b[38;5;241m=\u001b[39m \u001b[43m_load_state_dict_into_meta_model\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   4285\u001b[0m \u001b[43m            \u001b[49m\u001b[43mmodel_to_load\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4286\u001b[0m \u001b[43m            \u001b[49m\u001b[43mstate_dict\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4287\u001b[0m \u001b[43m            \u001b[49m\u001b[43mloaded_keys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4288\u001b[0m \u001b[43m            \u001b[49m\u001b[43mstart_prefix\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4289\u001b[0m \u001b[43m            \u001b[49m\u001b[43mexpected_keys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4290\u001b[0m \u001b[43m            \u001b[49m\u001b[43mdevice_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdevice_map\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4291\u001b[0m \u001b[43m            \u001b[49m\u001b[43moffload_folder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moffload_folder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4292\u001b[0m \u001b[43m            \u001b[49m\u001b[43moffload_index\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moffload_index\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4293\u001b[0m \u001b[43m            \u001b[49m\u001b[43mstate_dict_folder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstate_dict_folder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4294\u001b[0m \u001b[43m            \u001b[49m\u001b[43mstate_dict_index\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstate_dict_index\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4295\u001b[0m \u001b[43m            \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4296\u001b[0m \u001b[43m            \u001b[49m\u001b[43mis_quantized\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_quantized\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4297\u001b[0m \u001b[43m            \u001b[49m\u001b[43mis_safetensors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_safetensors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4298\u001b[0m \u001b[43m            \u001b[49m\u001b[43mkeep_in_fp32_modules\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkeep_in_fp32_modules\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4299\u001b[0m \u001b[43m            \u001b[49m\u001b[43munexpected_keys\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43munexpected_keys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   4300\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   4301\u001b[0m         error_msgs \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m new_error_msgs\n\u001b[1;32m   4302\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m~/anaconda3/envs/llama/lib/python3.10/site-packages/transformers/modeling_utils.py:805\u001b[0m, in \u001b[0;36m_load_state_dict_into_meta_model\u001b[0;34m(model, state_dict, loaded_state_dict_keys, start_prefix, expected_keys, device_map, offload_folder, offload_index, state_dict_folder, state_dict_index, dtype, is_quantized, is_safetensors, keep_in_fp32_modules, unexpected_keys)\u001b[0m\n\u001b[1;32m    802\u001b[0m     state_dict_index \u001b[38;5;241m=\u001b[39m offload_weight(param, param_name, state_dict_folder, state_dict_index)\n\u001b[1;32m    803\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m is_quantized:\n\u001b[1;32m    804\u001b[0m     \u001b[38;5;66;03m# For backward compatibility with older versions of `accelerate`\u001b[39;00m\n\u001b[0;32m--> 805\u001b[0m     \u001b[43mset_module_tensor_to_device\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparam_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparam_device\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mset_module_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    806\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m param\u001b[38;5;241m.\u001b[39mdtype \u001b[38;5;129;01min\u001b[39;00m (torch\u001b[38;5;241m.\u001b[39mint8, torch\u001b[38;5;241m.\u001b[39muint8) \u001b[38;5;129;01mand\u001b[39;00m is_quantized:\n\u001b[1;32m    807\u001b[0m     \u001b[38;5;66;03m# handling newly quantized weights and loaded quantized weights\u001b[39;00m\n\u001b[1;32m    808\u001b[0m     \u001b[38;5;66;03m# edit the param.dtype restrictions and is_quantized condition when adding new quant methods\u001b[39;00m\n\u001b[1;32m    809\u001b[0m     quantized_stats \u001b[38;5;241m=\u001b[39m {}\n", "File \u001b[0;32m~/anaconda3/envs/llama/lib/python3.10/site-packages/accelerate/utils/modeling.py:317\u001b[0m, in \u001b[0;36mset_module_tensor_to_device\u001b[0;34m(module, tensor_name, device, value, dtype, fp16_statistics)\u001b[0m\n\u001b[1;32m    315\u001b[0m             module\u001b[38;5;241m.\u001b[39m_parameters[tensor_name] \u001b[38;5;241m=\u001b[39m param_cls(new_value, requires_grad\u001b[38;5;241m=\u001b[39mold_value\u001b[38;5;241m.\u001b[39mrequires_grad)\n\u001b[1;32m    316\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(value, torch\u001b[38;5;241m.\u001b[39mTensor):\n\u001b[0;32m--> 317\u001b[0m     new_value \u001b[38;5;241m=\u001b[39m \u001b[43mvalue\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mto\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdevice\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    318\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    319\u001b[0m     new_value \u001b[38;5;241m=\u001b[39m torch\u001b[38;5;241m.\u001b[39mtensor(value, device\u001b[38;5;241m=\u001b[39mdevice)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# initate llm pipeline\n", "torch.set_default_tensor_type(torch.cuda.HalfTensor)\n", "mixtral_config = r\"model/mixtral/mixtral-1_config.json\"\n", "pipeline_starttime = datetime.now()\n", "mixtral_pipeline = mixtral_module.init_mixtral_piepeline(pd.read_json(mixtral_config, typ='series'))\n", "llama_config = r\"model/llama/llama+rag-1_config.json\"\n", "# llama_pipeline = llama_module.init_llama_piepeline(pd.read_json(llama_config, typ='series'))\n", "pipeline_endtime = datetime.now()\n", "print(f\"Pipeline Loading Time = {pipeline_endtime-pipeline_starttime}\")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-02-21T08:06:03.549651933Z", "start_time": "2024-02-21T08:05:41.151894036Z"}}, "execution_count": 6}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2024-02-21T08:05:09.751893480Z", "start_time": "2024-02-21T08:04:41.852001255Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["chunk_config:{'method': 'recursive_character_text_splitter', 'size': 1000, 'overlap': 20}\n", "load INSTRUCTOR_Transformer\n", "max_seq_length  512\n", "dataset_list:[{'dataset_name': 'dataset_test_ball', 'document_list': []}, {'dataset_name': 'Dataset_2', 'document_list': []}, {'dataset_name': 'dataset_woody', 'document_list': []}, {'dataset_name': 'dataset5', 'document_list': []}, {'dataset_name': 'Law_Kenneth_employment', 'document_list': []}, {'dataset_name': 'TestDataset', 'document_list': []}, {'dataset_name': 'dataset_test_1', 'document_list': []}, {'dataset_name': 'Dataset_Woody_Test', 'document_list': ['kyototxt', 'alicetxt', 'J2Semiconductor_official_infotxt']}, {'dataset_name': 'dataset_woody_2', 'document_list': ['J2Semiconductor_official_infotxt', 'J2Semiconductor_bingsearch_infotxt']}, {'dataset_name': 'Dataset_Record', 'document_list': []}]\n", " * Serving Flask app '__main__'\n", " * Debug mode: off\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:werkzeug:\u001b[31m\u001b[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.\u001b[0m\n", " * Running on all addresses (0.0.0.0)\n", " * Running on http://127.0.0.1:8007\n", " * Running on http://*************:8007\n", "INFO:werkzeug:\u001b[33mPress CTRL+C to quit\u001b[0m\n"]}], "source": ["app = Flask(__name__)\n", "\n", "# File Upload Configuration\n", "app.config['UPLOAD_FOLDER'] = env.default_upload_folder     # save all files in the default folder\n", "app.config['ALLOWED_EXTENSIONS'] = env.valid_file_extension # valid file formats\n", "\n", "\"\"\" Temp \"\"\"\n", "prompt_template_list = init_interface.load_prompt_template()\n", "model_dict, basemodel_list = init_interface.load_model_template()\n", "\n", "\n", "\"\"\" Inititalization \"\"\"\n", "# Retrieval of Default Configuration\n", "config_dir = os.path.join(env.default_dir, env.default_config_dir)\n", "config = pd.read_json(config_dir, typ=\"series\")\n", "\n", "# Retriveval of Preprocessing Configuration\n", "chunk_method = 'chunk-1'\n", "# chunk_config = pd.read_json(env.chunk_dir + \\\n", "#                             chunk_method + \\\n", "#                             env.chunk_config_suffix, typ=\"series\")\n", "with open(env.chunk_dir + chunk_method + env.chunk_config_suffix, 'r') as f:\n", "    chunk_config = json.load(f)\n", "print(f\"chunk_config:{chunk_config}\")\n", "embedding_method = 'embedding-1'\n", "embedding_config = pd.read_json(env.embedding_dir + \\\n", "                                embedding_method + \\\n", "                                env.embedding_config_suffix, typ=\"series\")\n", "embedding_method = preprocessing.load_embedding(embedding_config)\n", "\n", "# Retrieval of System Prompt Parameters\n", "prompt_dir = os.path.join(env.default_dir, env.default_prompt_dir)\n", "system_prompt_para = pd.read_json(prompt_dir, typ=\"series\")\n", "\n", "# Retrieval of Vector Store Configuration\n", "vs_dir = os.path.join(env.vs_dir_root, \"milvus\" + env.vs_config_suffix)\n", "vs_config = pd.read_json(vs_dir, typ=\"series\")\n", "\n", "# Retrieval RAG Configuration\n", "search_method = 'search-1'\n", "search_config = pd.read_json(env.search_config_dir + \\\n", "                             search_method + \\\n", "                             env.search_config_suffix, typ=\"series\")\n", "\n", "\n", "# Retrieval of User Information\n", "\"\"\" *** Refine Later >>> Build simple user authentication function \"\"\"\n", "raw_uid = 000\n", "uid_digit = config[\"uid_digit\"]\n", "user_id = f\"%0{uid_digit}d\" %raw_uid\n", "\n", "# User Authentication\n", "user_fullform = env.user_prefix + str(user_id)\n", "user_dir = os.path.join(env.user_dir_root, user_fullform)\n", "\n", "# Retrieval of User Profile\n", "user_profile = os.path.join(user_dir, user_fullform + env.user_prof_suffix)\n", "\n", "# Retrieval of User Chat History\n", "user_history = os.path.join(user_dir, user_fullform + env.user_chat_suffix)\n", "chat_history = {\"dir\": None,\n", "                \"df\": None,\n", "                \"langchain\": None}\n", "\n", "chat_history[\"dir\"] = user_history\n", "if not os.path.exists(chat_history[\"dir\"]):\n", "    chat_history[\"df\"] = pd.DataFrame()\n", "    chat_history[\"langchain\"] = ChatMessageHistory()\n", "    chat_history[\"langchain\"].add_user_message(\"What is the text about?\")\n", "    chat_history[\"langchain\"].add_ai_message(\"Cannot find document.\")\n", "    print(chat_history[\"langchain\"].messages)\n", "\n", "else:\n", "    df = pd.read_json(chat_history[\"dir\"], orient=\"index\")\n", "    chat_history[\"df\"] = df\n", "\n", "    # Convert Chat History to langchain Format\n", "    chat_history[\"langchain\"] = ChatMessageHistory()\n", "    for q_msg, a_msg in zip(df[\"q_msg\"].to_list(),\n", "                            df[\"a_msg\"].to_list()):\n", "        chat_history[\"langchain\"].add_user_message(q_msg)\n", "        chat_history[\"langchain\"].add_ai_message(a_msg)\n", "\n", "    chat_history[\"langchain\"] = chat_history[\"langchain\"].messages\n", "\n", "# ***** Retrival of stored collections and documents\n", "#  {'dataset_name': 'Dataset_Woody_Test',\n", "#  'document_list': ['kyototxt', 'alicetxt']}]\n", "# *** To convert to input parameter\n", "vsschema_uid = 'vsschema-1'\n", "schema_config_dir = env.vs_dir_root + vsschema_uid + env.vsschema_config_fmt\n", "schema_config = pd.read_json(schema_config_dir, typ='series')\n", "\n", "\n", "# Retrieval RAG Configuration\n", "search_method = 'search-1'\n", "search_config = pd.read_json(env.search_config_dir + \\\n", "                             search_method + \\\n", "                             env.search_config_suffix, typ=\"series\")\n", "\n", "\n", "# Retrieval of User Information\n", "\"\"\" *** Refine Later >>> Build simple user authentication function \"\"\"\n", "raw_uid = 000\n", "uid_digit = config[\"uid_digit\"]\n", "user_id = f\"%0{uid_digit}d\" %raw_uid\n", "\n", "# User Authentication\n", "user_fullform = env.user_prefix + str(user_id)\n", "user_dir = os.path.join(env.user_dir_root, user_fullform)\n", "\n", "# Retrieval of User Profile\n", "user_profile = os.path.join(user_dir, user_fullform + env.user_prof_suffix)\n", "\n", "# Retrieval of User Chat History\n", "user_history = os.path.join(user_dir, user_fullform + env.user_chat_suffix)\n", "chat_history = {\"dir\": None,\n", "                \"df\": None,\n", "                \"langchain\": None}\n", "\n", "chat_history[\"dir\"] = user_history\n", "if not os.path.exists(chat_history[\"dir\"]):\n", "    chat_history[\"df\"] = pd.DataFrame()\n", "\n", "else:\n", "    df = pd.read_json(chat_history[\"dir\"], orient=\"index\")\n", "    chat_history[\"df\"] = df\n", "\n", "    # Convert Chat History to langchain Format\n", "    chat_history[\"langchain\"] = ChatMessageHistory()\n", "    for q_msg, a_msg in zip(df[\"q_msg\"].to_list(),\n", "                            df[\"a_msg\"].to_list()):\n", "        chat_history[\"langchain\"].add_user_message(q_msg)\n", "        chat_history[\"langchain\"].add_ai_message(a_msg)\n", "\n", "\n", "# Init Collection\n", "loaded_dataset = None\n", "loaded_dataset_name = None\n", "loaded_files = []\n", "\n", "\n", "# ***** Retrival of stroed collections and documents\n", "#  {'dataset_name': 'Dataset_Woody_Test',\n", "#  'document_list': ['kyototxt', 'alicetxt']}]\n", "dataset_list = vectorstore_module.init_vsdb(vs_config)\n", "print(f\"dataset_list:{dataset_list}\")\n", "\n", "\n", "\n", "\"\"\" Chatbot \"\"\"\n", "@app.route('/', methods=['GET'])\n", "def index():\n", "    # return render_template('index.html')\n", "    return render_template('index_json_database_new_ui_8.html')\n", "\n", "selected_dataset = None # dataset storing files\n", "@app.route('/selected-dataset', methods=['POST'])\n", "def point_selected_dataset():\n", "    global selected_dataset\n", "    dataset = request.get_json()\n", "    selected_dataset = dataset['selectedDataset']\n", "    print(f'Selected Dataset: <{selected_dataset}>')\n", "    return 'Received Dataset Selection'\n", "\n", "selected_instrcut = 'role-ball'\n", "@app.route('/select-instruct', methods=['POST'])\n", "def select_instruct():\n", "    global selected_instrcut\n", "    data = request.json\n", "\n", "    selected_instrcut = data['selectedInstruct']\n", "    print(f'Selected instruct option <{selected_instrcut}>')\n", "    return jsonify({'message': 'Received instruct option: ' + selected_instrcut})\n", "\n", "\n", "@app.route('/upload', methods=['POST'])\n", "def upload_file():\n", "    global vs_config\n", "    global selected_dataset\n", "    global schema_config\n", "    global vs_df # log file for vector store\n", "\n", "    selected_dataset = request.form.get('selectedDataset', None)\n", "\n", "\n", "    if not selected_dataset:\n", "        selected_dataset = env.default_dataset_name\n", "        print(f'* No Selected Dataset Name : Default Name <{selected_dataset}> is Used')\n", "\n", "    # Create Collection\n", "    # Return <True> if created; Return <False> if Collection Name Already Used\n", "    created = vectorstore_module.create_vscollection(\n", "        vs_config,\n", "        selected_dataset,\n", "        schema_config\n", "    )\n", "\n", "    print(f'... Uploading the File into the Selected Dataset <{selected_dataset}>')\n", "    # upload_files = request.files['file']\n", "    uploaded_files = request.files.getlist('file')\n", "\n", "    if not uploaded_files:\n", "        print('Error: No Uploading Files Found!')\n", "        return jsonify({'status': 'error', 'message': 'No files found for upload'})\n", "\n", "    for file in uploaded_files:\n", "        if file:\n", "            # Copy File to Upload Folder\n", "            filename = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)\n", "            file.save(filename)\n", "\n", "            loader = DirectoryLoader(app.config['UPLOAD_FOLDER'])\n", "            document = loader.load()\n", "            if not document:\n", "                print('Warning: Cannot Find Document in Upload Folder')\n", "\n", "            # Split content into Different Chunks\n", "            chunked_content = preprocessing.chunk_document(document,\n", "                                                           chunk_config)\n", "            chunked_rawtexts = [chunk.page_content for chunk in chunked_content]\n", "            embedded_vectors = embedding_method.embed_documents(chunked_rawtexts)\n", "\n", "            # Create Parition\n", "            partition_name = file.filename.replace(\".\",\"\").replace(\" \", \"\").replace(\"-\", \"\")\n", "            created = vectorstore_module.create_vspartition(\n", "                vs_config,\n", "                selected_dataset,\n", "                partition_name=partition_name\n", "            )\n", "\n", "            # Insert Vectors into Vector Store\n", "            vectorstore_module.insert_vectors(\n", "                vs_config,\n", "                selected_dataset,\n", "                partition_name,\n", "                schema_config,\n", "                chunked_rawtexts,\n", "                embedded_vectors,\n", "                field_dict={\n", "                    \"source\": file.filename,\n", "                    \"extension\": file.filename.split('.')[-1],\n", "                    \"language\": \"US\", # *** To be converted as an input\n", "                    \"permission\": 3,   # *** To be converted as an input\n", "                    \"date\": datetime.now().strftime(\"%Y-%m-%d, %H:%M:%S\"),\n", "                    \"uploader\": user_id\n", "                }\n", "            )\n", "\n", "            # # Generate metadata for vector store\n", "            # chunks_with_metadata = vectorstore_module.gen_metadata(file.filename, chunked_content)\n", "\n", "            # # Add/Create Vector Store\n", "            # # dataset name = collection name\n", "            # vectorstore_module.add_to_collection(vs_config,\n", "            #                                      chunks_with_metadata,\n", "            #                                      embedding_method,\n", "            #                                      selected_dataset)\n", "\n", "            print(f'Successfully Added the Uploaded File <{file.filename}> into Vector Store')\n", "\n", "            # Update log file for vector store\n", "            new_file = {\"dataset\": selected_dataset,\n", "                        \"filename\": file.filename,\n", "                        \"chunk_size\": len(chunked_content),\n", "                        \"permission_level\": 3,\n", "                        \"uploader\": user_id,\n", "                        \"uplaod_time\": datetime.now().strftime(\"%Y-%m-%d, %H:%M:%S\")}\n", "\n", "            vs_df = vectorstore_module.update_vs_df(new_file)\n", "\n", "            # # Remove File after Storing into Vector Store\n", "            # os.remove(filename)\n", "            # print(f'Deleted File <{file.filename}> from Upload Folder')\n", "    try:\n", "        for filename in os.listdir(app.config['UPLOAD_FOLDER']):\n", "            file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)\n", "            if os.path.isfile(file_path) or os.path.islink(file_path):\n", "                os.unlink(file_path)\n", "            elif os.path.isdir(file_path):\n", "                shutil.rmtree(file_path)\n", "        print(f'Cleared temporary upload folder {app.config[\"UPLOAD_FOLDER\"]}')\n", "    except Exception as e:\n", "        print(f'Error occurred while clearing temporary upload folder: {e}')\n", "\n", "    return jsonify({'status': 'success', 'message': 'File uploaded successfully'})\n", "\n", "selected_files = []\n", "@app.route('/selected-files', methods=['POST'])\n", "def point_selected_files():\n", "    global selected_files\n", "    selected_files.clear()  # clear global_file_list\n", "\n", "    data = request.get_json()\n", "    selected_files = data['selectedFiles']  # new files in the global_file_list\n", "    print('Selected Files:')\n", "    print(*selected_files, sep='\\n')\n", "\n", "    return jsonify({'status': 'success', 'message': 'Selected files received'})\n", "\n", "# @app.route('/get-datasets', methods=['GET'])\n", "# def get_datasets():\n", "#     data = pd.read_json(env.vsdb_log_dir)\n", "#     return jsonify(data)\n", "\n", "@app.route('/vectorstore/<filename>', methods=['GET'])\n", "def log_file(filename):\n", "    return send_from_directory('vectorstore', filename)\n", "\n", "@app.route('/get-files', methods=['GET'])\n", "def get_files(vsdb_log_dir=env.vsdb_log_dir):\n", "    global vs_df\n", "    # Load and return the list of files from file\n", "    vs_df = pd.read_json(vsdb_log_dir)\n", "    unique_files = vs_df['filename'].unique().tolist()\n", "    return jsonify(unique_files)\n", "\n", "@app.route('/delete-file', methods=['POST'])\n", "def delete_file():\n", "    global vs_df\n", "    data = request.get_json()\n", "    del_file = data['file_name']\n", "    selected_dataset = data['dataset']\n", "    # Add logic to delete the file from  server\n", "    # os.remove(os.path.join(UPLOAD_FOLDER, file_to_delete))\n", "    print(f\"Deleting File: {del_file}\")\n", "    vectorstore_module.delete_entities(vs_config,\n", "                                       del_file,\n", "                                       selected_dataset)\n", "    print('Deleted File Entities from Vector Store')\n", "\n", "    vs_df = vectorstore_module.delete_vs_df(del_file)\n", "    print('Deleted File from File Log')\n", "    return jsonify({'status': 'success', 'message': 'File deleted successfully'})\n", "\n", "@app.route('/delete-dataset', methods=['POST'])\n", "def delete_dataset():\n", "    global vs_df\n", "    data = request.get_json()\n", "    selected_dataset = data['dataset']\n", "    vectorstore_module.delete_collection(vs_config, selected_dataset)\n", "    return jsonify({'message': 'Dataset deleted successfully'})\n", "\n", "\n", "@app.route('/get-prompt-templates', methods=['GET'])\n", "def get_prompt_templates():\n", "    return jsonify({'prompt_template_list': prompt_template_list})\n", "\n", "@app.route('/get-datasets', methods=['GET'])\n", "def get_datasets():\n", "    return jsonify(dataset_list)\n", "\n", "@app.route('/chat', methods=['POST'])\n", "def chat():\n", "    global selected_dataset\n", "\n", "    # Check file extension\n", "    def allowed_file(filename):\n", "        return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']\n", "\n", "    # User Input\n", "    user_input = request.form.get('user_input', '')\n", "    mode = request.form.get('mode', 'online')\n", "    model = request.form.get('model', 'gpt-3.5')\n", "    rag = request.form.get('rag', 'off')\n", "    selected_dataset = request.form.get('selectedDataset', '')\n", "\n", "    # *** Refine Model Name on UI Later\n", "    if model.lower() == \"gpt-3.5\":\n", "        model = \"openai\"\n", "    elif model.lower() == \"llama\":\n", "        if rag == \"on\":\n", "            model = \"llama+rag-1\"\n", "        else:\n", "            model = \"llama-1\"\n", "    elif model.lower() == \"mixtral\":\n", "        if rag == \"on\":\n", "            model = \"mixtral+rag-1\"\n", "        else:\n", "            model = \"mixtral-1\"\n", "\n", "    q_time = datetime.now()\n", "\n", "    # Initialize bot_response to an empty string or a default value\n", "    bot_response = \"\"\n", "\n", "    # Prompt Engineering\n", "    system_prompt = prompt_module.gen_system_prompt(system_prompt_para) # Using default values\n", "    condense_system_prompt = prompt_module.gen_condense_system_prompt(system_prompt_para)\n", "    prompt_df = pd.read_json(env.prompt_tempte_dir + selected_instrcut + '.json')\n", "    prompt_template = '\\n'.join(prompt_df[\"prompt\"])\n", "\n", "    # Model Retrieval\n", "    model_name = model.split('-')[0] # Split Model Number\n", "    prime_model_name = model_name.split('+')[0] # Split + for RAG\n", "    prime_model_root = os.path.join(env.model_dir_root, prime_model_name)\n", "    model_config_dir = os.path.join(prime_model_root, model + env.model_config_suffix)\n", "\n", "    # Check if Model Config File Exists\n", "    if not os.path.exists(model_config_dir):\n", "        err_msg = f\" Input Error : The Model ID <{model}> could not be Found in {prime_model_root}\"\n", "        print(err_msg)\n", "        bot_response = err_msg\n", "\n", "    # Retrieve Model Config File\n", "    else:\n", "        print(\"Successfully Retrieved Model Config File\")\n", "        model_para = pd.read_json(model_config_dir, typ='series')\n", "\n", "\n", "\n", "    # Get retriever for RAG; Return <None> if RAG is OFF\n", "    retriever = None\n", "    if rag == \"on\":\n", "        if selected_dataset:\n", "            print(f'RAG is ON. Loaded Dataset {selected_dataset}')\n", "            retriever = vectorstore_module.get_retriever(vs_config,\n", "                                                         embedding_method,\n", "                                                         selected_dataset,\n", "                                                         search_config)\n", "        else:\n", "            print(f'Error : RAG is ON, but Cannot Find Dataset')\n", "            retriever = None\n", "    else:\n", "        print('RAG is OFF')\n", "        retriever = None\n", "\n", "    # # Retrieval of Files for RAG Vector Store\n", "    # # Find directory path for RAG; Return <None> if RAG is Off\n", "    # if rag == \"on\":\n", "\n", "    #     # Check whether Files are Selected for RAG\n", "    #     if selected_dataset:\n", "    #         print(f'RAG is ON. Loaded Dataset {selected_dataset}')\n", "    #         rag_dataset = selected_dataset\n", "    #     else:\n", "    #         print(f'Error : RAG is ON, but Cannot Find Dataset')\n", "    #         rag_dataset = []\n", "\n", "    # else:\n", "    #     rag_dataset = []\n", "\n", "    # Generate AI Response\n", "    print('LLM Model Selected :')\n", "    print(f'- Model UID : {model_para[\"uid\"]}')\n", "    print(f'- Base Model : {model_para[\"model\"]}')\n", "    print(f'- RAG : {retriever is not None}')\n", "\n", "    if prime_model_name.lower() == \"llama\":\n", "        bot_response = llama_module.llama_response(user_input,\n", "                                    system_prompt,\n", "                                    condense_system_prompt,\n", "                                    prompt_template,\n", "                                    model_para,\n", "                                    llama_pipeline,\n", "                                    retriever,\n", "                                    chat_history[\"langchain\"])\n", "\n", "    elif prime_model_name.lower() == \"openai\":\n", "        bot_response = openai_module.openai_response(user_input,\n", "                                     system_prompt,\n", "                                     condense_system_prompt,\n", "                                     prompt_template,\n", "                                     model_para,\n", "                                     retriever,\n", "                                     chat_history[\"langchain\"])\n", "\n", "    elif prime_model_name.lower() == \"mixtral\":\n", "        bot_response = mixtral_module.mixtral_response(user_input,\n", "                                     system_prompt,\n", "                                     condense_system_prompt,\n", "                                     prompt_template,\n", "                                     model_para,\n", "                                     mixtral_pipeline,\n", "                                     retriever,\n", "                                     chat_history[\"langchain\"])\n", "\n", "    # elif prime_model_name.lower() == \"instructmixtral\":\n", "    #     bot_response = instruct_mixtral_module.mixtral_response(user_input,\n", "    #                                  system_prompt,\n", "    #                                  condense_system_prompt,\n", "    #                                  prompt_template,\n", "    #                                  model_para,\n", "    #                                  mixtral_pipeline,\n", "    #                                  retriever,\n", "    #                                  chat_history[\"langchain\"])\n", "\n", "    test_module.output_report(bot_response)\n", "\n", "    a_time = datetime.now()\n", "\n", "    # Update Chat History\n", "    new_chat = pd.DataFrame([{\n", "        \"q_msg\": user_input,\n", "        \"a_msg\": bot_response,\n", "        \"q_time\": q_time.strftime(\"%Y-%m-%d, %H:%M:%S\"),\n", "        \"a_time\": a_time.strftime(\"%Y-%m-%d, %H:%M:%S\"),\n", "        \"llm\": model,\n", "        \"similarity\": 1,\n", "        \"rating\": 3\n", "    }])\n", "\n", "    chat_history[\"df\"] = pd.concat([chat_history[\"df\"], new_chat], ignore_index=True)\n", "    chat_history[\"df\"].to_json(chat_history[\"dir\"], orient=\"index\", indent=4)\n", "\n", "    return jsonify({'response': bot_response})\n", "\n", "if __name__ == '__main__':\n", "    app.run(host='0.0.0.0', port=8007)"]}, {"cell_type": "code", "outputs": [], "source": [], "metadata": {"collapsed": false}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}