from pymilvus import connections, Collection, DataType
import csv
import pandas as pd

# 第一步：连接 Milvus 并导出数据到 CSV

# Milvus 连接信息
host = "*************"
port = 30982
collection_name = "test_kg_chi"

# 连接到 Milvus
connections.connect(host=host, port=port)

# 加载集合
collection = Collection(collection_name)

# 获取集合的 schema 以确定字段名称和类型
schema = collection.schema
field_names = [field.name for field in schema.fields]

# 打开 CSV 文件用于写入
with open('output.csv', 'w', newline='') as csvfile:
    writer = csv.DictWriter(csvfile, fieldnames=field_names)
    writer.writeheader()  # 写入表头（字段名称）

    # 使用查询迭代器分批获取数据
    iterator = collection.query_iterator(
        batch_size=1000,  # 每批次获取 1000 条数据，可根据需要调整
        expr="",  # 空表达式表示获取所有数据
        output_fields=field_names  # 获取所有字段
    )

    while True:
        batch = iterator.next()  # 获取下一批数据
        if not batch:  # 如果没有更多数据则退出
            break
        for entity in batch:
            # 将向量字段序列化为字符串以兼容 CSV
            for field in schema.fields:
                if field.dtype == DataType.FLOAT_VECTOR:
                    entity[field.name] = str(entity[field.name])
            writer.writerow(entity)  # 将实体写入 CSV
    iterator.close()  # 关闭迭代器

# 第二步：从文档中提取关键词和摘要

# 从文档中提取的关键词
keywords = [
    "PRUHealth Baby Guardian Critical Illness Plan",
    "Critical illness protection",
    "Expectant Mother",
    "Child",
    "Live Birth",
    "Juvenile Disease Benefit",
    "Early Stage Major Disease Benefit",
    "Intensive Care Benefit",
    "Major Disease Benefit",
    "Extended Major Disease Benefit",
    "Postpartum Depression Benefit",
    "Severe Newborn Jaundice Benefit",
    "Compassionate Benefit",
    "Lifelong Annuity",
    "Severe Dementia",
    "Parkinson’s Disease",
    "Death Benefit",
    "Special Bonus",
    "Current Sum Assured",
    "Registered Specialist",
    "Qualified ICU Stay",
    "Pregnancy",
    "Benefit Adjustment Amount",
    "Activities of Daily Living",
    "Disease Condition",
    "Premium Waiver",
    "Parental Premium Waiver",
    "Total Premiums Paid",
    "Three-year Cancer Waiting Period",
    "Tertiary Care Hospital",
    "Prudential Hong Kong Limited",
    "Exclusions",
    "Claim process"
]
keywords_str = ", ".join(keywords)  # 将关键词列表转换为逗号分隔的字符串

# 从文档中提取的摘要
summary = (
    "The PRUHealth Baby Guardian Critical Illness Plan, offered by Prudential Hong Kong Limited, "
    "is an insurance product designed to provide critical illness protection for an Expectant Mother "
    "during pregnancy and her Child after Live Birth. Coverage starts with the Expectant Mother and "
    "transitions to the Child upon Live Birth, offering benefits such as Postpartum Depression Benefit, "
    "Severe Newborn Jaundice Benefit, Compassionate Benefit, Juvenile Disease Benefit, Early Stage Major "
    "Disease Benefit, Intensive Care Benefit, Major Disease Benefit, Extended Major Disease Benefit, "
    "Lifelong Annuity for Severe Dementia or Parkinson’s Disease, Death Benefit, Premium Waiver Benefits, "
    "and a Special Bonus. Benefits may be reduced if conditions occur within 90 days of Live Birth, with "
    "reductions added to a Benefit Adjustment Amount. Exclusions include self-inflicted injuries, "
    "pre-existing conditions, substance abuse, and high-risk activities. Claims require documentation "
    "within 180 days of diagnosis or hospital discharge."
)

# 第三步：修改 CSV 文件，添加关键词和摘要列

# 读取导出的 CSV 文件
df = pd.read_csv('output.csv')

# 添加新列
df['keywords'] = keywords_str
df['summary'] = summary

# 保存更新后的 CSV 文件
df.to_csv('final_output.csv', index=False)

print("最终完整的CSV已生成，文件名为 'final_output.csv'。谢谢！")