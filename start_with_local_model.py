import os
import subprocess
import sys

def download_model():
    # Check if model already exists
    local_model_path = "models/embeddings/text2vec-base-chinese"
    if os.path.exists(local_model_path):
        print(f"Model already exists at {local_model_path}")
        return True
    
    # If model doesn't exist, download it
    try:
        print("Downloading embedding model...")
        result = subprocess.run([sys.executable, "download_model.py"], check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error downloading model: {e}")
        return False

def start_chatbot():
    # Start the Flask app
    print("Starting chatbot...")
    subprocess.run([sys.executable, "chatbot_newui_new_version_pay.py"])

if __name__ == "__main__":
    # Make sure models directory exists
    os.makedirs("models/embeddings", exist_ok=True)
    
    # Download model if needed
    success = download_model()
    
    if success:
        # Start the chatbot
        start_chatbot()
    else:
        print("Failed to download model. Please check your internet connection.")
        print("If you need to use an offline version, make sure the model is downloaded beforehand.") 