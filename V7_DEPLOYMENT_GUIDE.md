# chatbot_newui_new_version_v7 部署指南

## 🚀 版本特性

**v7版本新增功能：**
- ✅ **MinerU集成**: 实时PDF布局分析和内容提取
- ✅ **智能回退机制**: 多层处理方式确保系统稳定性
- ✅ **配置管理**: 灵活的MinerU参数配置
- ✅ **处理统计**: 详细的文件处理方式记录
- ✅ **API端点**: MinerU状态查询和配置管理
- ✅ **状态页面**: 可视化的MinerU状态监控

## 📋 文件清单

### 核心文件
- `chatbot_newui_new_version_v7.py` - 主应用程序（v7版本）
- `mineru_integration.py` - MinerU集成模块
- `mineru_config.py` - 配置管理模块
- `start_v7.py` - 启动脚本

### 工具文件
- `install_mineru.py` - MinerU自动安装脚本
- `test_mineru_integration.py` - 集成测试脚本
- `mineru_api_client.py` - API客户端（备选方案）

### 模板文件
- `templates/mineru_status.html` - MinerU状态页面

### 文档文件
- `MINERU_INTEGRATION_README.md` - 技术方案详细说明
- `V7_DEPLOYMENT_GUIDE.md` - 本部署指南

## 🔧 快速部署

### 方法1：使用启动脚本（推荐）

```bash
# 1. 启动应用（会自动检查依赖和提供安装选项）
python start_v7.py

# 2. 仅检查状态
python start_v7.py --check

# 3. 查看帮助
python start_v7.py --help
```

### 方法2：手动部署

```bash
# 1. 安装MinerU依赖
python install_mineru.py

# 2. 测试集成
python test_mineru_integration.py

# 3. 启动应用
python chatbot_newui_new_version_v7.py
```

## 📦 MinerU安装

### 自动安装（推荐）
```bash
python install_mineru.py
```

### 手动安装
```bash
# 基础版本
pip install magic-pdf[full]

# 如果需要GPU支持
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### 验证安装
```bash
python test_mineru_integration.py
```

## ⚙️ 配置管理

### 配置文件
- `mineru_user_config.json` - 用户自定义配置
- `mineru_sample_config.json` - 示例配置文件

### 主要配置项
```json
{
  "enabled": true,
  "parse_mode": "auto",
  "lang": "auto",
  "layout_config": {
    "model": "doclayout_yolo",
    "confidence_threshold": 0.5
  },
  "formula_config": {
    "enable": true,
    "mfd_model": "yolo_v8_mfd",
    "mfr_model": "unimernet_small"
  },
  "table_config": {
    "enable": true,
    "model": "rapid_table"
  }
}
```

## 🌐 访问地址

启动成功后，可以访问以下地址：

- **主聊天界面**: http://localhost:5000/chat
- **MinerU状态页面**: http://localhost:5000/mineru
- **MinerU状态API**: http://localhost:5000/mineru-status
- **MinerU配置API**: http://localhost:5000/mineru-config

## 📊 功能验证

### 1. 检查MinerU状态
访问 http://localhost:5000/mineru 查看：
- ✅ 安装状态
- ✅ 配置状态
- ✅ 支持格式
- ✅ 文件限制

### 2. 测试PDF处理
1. 上传PDF文件到系统
2. 查看控制台日志，确认使用了MinerU处理
3. 检查生成的文本块质量

### 3. 验证回退机制
1. 禁用MinerU（设置 `enabled: false`）
2. 上传PDF文件
3. 确认系统回退到原始处理方式

## 🔄 处理流程

```mermaid
graph TD
    A[用户上传PDF] --> B{检查预计算文件}
    B -->|存在| C[使用预计算布局]
    B -->|不存在| D{MinerU可用且启用?}
    D -->|是| E[MinerU实时处理]
    D -->|否| F[使用原始处理方式]
    E --> G{处理成功?}
    G -->|是| H[转换为chunks格式]
    G -->|否| I[回退到Markdown文件]
    I --> J{Markdown存在?}
    J -->|是| K[使用Markdown处理]
    J -->|否| F
    C --> L[存储到向量数据库]
    H --> M[保存MinerU结果]
    M --> L
    K --> L
    F --> L
```

## 🛠️ 故障排除

### 常见问题

#### 1. MinerU导入失败
```bash
# 错误: ImportError: No module named 'magic_pdf'
# 解决: 安装MinerU
python install_mineru.py
```

#### 2. 配置文件缺失
```bash
# 错误: 配置未加载
# 解决: 创建配置文件
python mineru_config.py
```

#### 3. 处理速度慢
```bash
# 解决: 启用GPU加速或调整配置
# 编辑 mineru_user_config.json:
{
  "performance_config": {
    "gpu_enabled": true,
    "batch_size": 2
  }
}
```

#### 4. 内存不足
```bash
# 解决: 降低内存使用
{
  "performance_config": {
    "memory_limit": "2GB",
    "max_workers": 2
  }
}
```

### 调试方法

#### 1. 查看详细日志
```bash
# 启动时会显示详细的处理日志
python chatbot_newui_new_version_v7.py
```

#### 2. 测试单个功能
```bash
# 测试MinerU集成
python test_mineru_integration.py

# 测试配置
python mineru_config.py
```

#### 3. API调试
```bash
# 检查MinerU状态
curl http://localhost:5000/mineru-status

# 获取配置
curl http://localhost:5000/mineru-config
```

## 📈 性能优化

### 1. GPU加速
```json
{
  "performance_config": {
    "gpu_enabled": true
  }
}
```

### 2. 批处理优化
```json
{
  "performance_config": {
    "batch_size": 4,
    "max_workers": 8
  }
}
```

### 3. 缓存设置
```json
{
  "save_results": true,
  "output_config": {
    "save_images": false,
    "save_tables": true
  }
}
```

## 🔒 安全考虑

### 1. 文件大小限制
```json
{
  "file_filter": {
    "max_file_size": "100MB",
    "max_pages": 1000
  }
}
```

### 2. 支持格式限制
```json
{
  "file_filter": {
    "supported_formats": [".pdf"]
  }
}
```

## 📝 更新日志

### v7.0.0 (2025-06-23)
- ✅ 新增MinerU集成功能
- ✅ 新增智能回退机制
- ✅ 新增配置管理系统
- ✅ 新增状态监控页面
- ✅ 新增API端点
- ✅ 优化错误处理
- ✅ 增强日志记录

## 🤝 技术支持

如果遇到问题，请：

1. 查看控制台日志
2. 访问MinerU状态页面检查状态
3. 运行测试脚本验证功能
4. 查看详细的技术文档：`MINERU_INTEGRATION_README.md`

## 📚 相关文档

- [MinerU集成技术方案](MINERU_INTEGRATION_README.md)
- [MinerU官方文档](https://github.com/opendatalab/MinerU)
- [配置参数说明](mineru_config.py)

---

🎉 **恭喜！你现在拥有了一个集成MinerU的强大PDF处理系统！**
