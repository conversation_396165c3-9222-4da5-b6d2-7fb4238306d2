"""

    Module Name :           vectorstore_module
    Last Modified Date :    8 Jan 2024

"""

# Import Libraries
from langchain.vectorstores import Milvus
from langchain.document_loaders import DirectoryLoader
from langchain.docstore.document import Document
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.embeddings import HuggingFaceInstructEmbeddings
import os
import pandas as pd

from graphstore_module_newui import graphstore_module_newui

# Milvus Database
from pymilvus import (
    db,
    connections,
    utility,
    FieldSchema,
    CollectionSchema,
    DataType,
    Collection,
    Partition,
    MilvusClient
)

import preprocessing
import env
#

def init_vsdb(vs_config):

    connections.connect(host=vs_config["host"],
                        port=vs_config["port"],
                        db_name="default")

    collection_list = utility.list_collections()
    dataset_list = []

    for collection_name in collection_list:
        collection = Collection(collection_name)
        dataset_dict = {
            "dataset_name": collection_name,
            "document_list": [partition.name for partition in collection.partitions]
        }

        try:
            dataset_dict["document_list"].remove("_default")
        except:
            pass

        dataset_list.append(dataset_dict)
    return dataset_list


def get_retriever(vs_config,
                  embeddings,
                  selected_dataset,
                  search_config):

    retriever = None

    # RAG is OFF
    if not selected_dataset:
        return retriever

    # RAG is ON
    # load vector store
    vs_db = load_collection(vs_config,
                            embeddings,
                            selected_dataset)

    retriever = vs_db.as_retriever(search_type=search_config["search_type"],
                                   search_kwargs=search_config["search_kwargs"])
    return retriever

def get_retriever2(vs_config,
                   embeddings,
                   selected_dataset,
                   selected_files,
                   search_config):

    retriever = None

    # RAG is OFF
    if not selected_dataset:
        return retriever

    expr = gen_query_expression(selected_files)
    print("selected_files: ", selected_files)
    print("search_expression: ", expr)
    vs_db = load_collection(vs_config,
                            embeddings,
                            selected_dataset)

    # retriever = vs_db.as_retriever(partition_key_field=expr,
    #                                search_type=search_config["search_type"],
    #                                search_kwargs=search_config["search_kwargs"])
    retriever = vs_db.as_retriever(
        search_kwargs={"expr": expr}
    )
    # retriever = vs_db.as_retriever(
    #     search_kwargs = {"filter": {"source": expr}}
    # )

    return retriever

def get_similar_nodes_from_milvus(vs_config, embedding, collection_name, selected_files, search_config, query):
    # client = MilvusClient(
    #     uri=f"http://{vs_config['host']}:{vs_config['port']}"
    # )
    # client.search(
    #     collection_name=collection_name
    #     data=[[0.3580376395471989, -0.6023495712049978, 0.18414012509913835, -0.26286205330961354, 0.9029438446296592]],
    #     limit=search_config["search_kwargs"]['k'], # Max. number of search results to return
    #     filter='source in [' + ', '.join([f'\"{selected_file}\"' for selected_file in selected_files])+']')

    vs_db = load_collection(vs_config, embedding, collection_name)
    k=search_config["search_kwargs"]['k']
    # k=50
    search_kwarg = {'k': k, "expr": 'source in ['+', '.join([f'\"{selected_file}\"' for selected_file in selected_files])+']', "param": {"ef": k * 4}}
    # search_kwarg = {'k': k}
    
    print(search_kwarg)
    retriever = vs_db.as_retriever(search_type=search_config["search_type"],
                                   search_kwargs=search_kwarg)
    milvus_result = retriever.invoke(query)
    result = [item.metadata['node_id'] for item in milvus_result]
    return result

from langchain_core.retrievers import BaseRetriever
from typing import List
from langchain_core.callbacks.manager import CallbackManagerForRetrieverRun


class NeighbourRetriever(BaseRetriever):
    vectorstore : Milvus
    search_kwargs : dict
    collection_name : str
    vs_config: dict
    def __init__(self, vs_config, embeddings, colleciton_name,search_kwargs):
        super().__init__(vectorstore = Milvus(embeddings,
            collection_name=colleciton_name,
            connection_args={"host": vs_config["host"],
                                "port": vs_config["port"]},
        ),search_kwargs=search_kwargs, collection_name=colleciton_name, vs_config=vs_config
        )
        
    def _get_relevant_documents(
        self, query: str, *, run_manager: CallbackManagerForRetrieverRun
    ) -> List[Document]:
        docs = self.vectorstore.max_marginal_relevance_search(
                query, **self.search_kwargs
            )

        # client = MilvusClient(uri=f"http://{self.vs_config['host']}:{self.vs_config['port']}")
        
        # for doc in docs:
        #     print(doc.metadata["node_id"])
        #     if doc.metadata["content_type"] != "text":
        #         follow_res = client.query(
        #             collection_name=self.collection_name,
        #             filter='node_id in ['  +', '.join([f'\"{node_id}\"' for node_id in graphstore_module_newui(self.collection_name).get_follow_neighbour(doc.metadata["node_id"])])+']',
        #             output_fields=["text"])
        #         follow_text_list = [mulvis_row["text"] for mulvis_row in follow_res]
        #         follow_text = f"{follow_text_list[0]} {doc.page_content} {follow_text_list[1]}"
        #         # print(follow_text)
        #         doc.page_content = follow_text
        return docs


def get_retriever3(vs_config, embedding, collection_name, selected_files, search_config, query):
    client = MilvusClient(uri=f"http://{vs_config['host']}:{vs_config['port']}")
    
    # search_config[""]
    similar_nodes = get_similar_nodes_from_milvus(vs_config, embedding, collection_name, selected_files, search_config, query)
    print(f"Mulvis: {similar_nodes}")
    similar_nodes_neighbour = graphstore_module_newui(collection_name).get_neighbour_nodes_from_node_id_list(similar_nodes, selected_files, 30)
    
    document_list = []
    # all_similar_nodes = similar_nodes
    all_similar_nodes = list(set([item for elem in list(similar_nodes_neighbour.values()) for item in elem]) - set(similar_nodes))
    print(all_similar_nodes)
    vs_db = load_collection(vs_config, embedding, collection_name)
    # k = search_config["search_kwargs"]['k']
    k = 10 # For the Prudential demo
    # k = 5
    search_kwarg = {'k': k, "expr": 'source in ['+', '.join([f'\"{selected_file}\"' for selected_file in selected_files])+'] and node_id in [' +', '.join([f'\"{node_id}\"' for node_id in all_similar_nodes])+']', "param": {"ef": k * 4}}
    retriever = vs_db.as_retriever(search_type=search_config["search_type"],
                                   search_kwargs=search_kwarg)
    milvus_result = retriever.invoke(query)
    kg_node = [item.metadata['node_id'] for item in milvus_result]
    milvus_node = similar_nodes[:10] # For the Prudential demo
    # milvus_node = similar_nodes[:5]
    all_node = milvus_node + kg_node
    # all_node = milvus_node
    k = 20 # For the Prudential demo
    # k = 5
    search_kwarg = {'k': k, "expr": 'source in ['+', '.join([f'\"{selected_file}\"' for selected_file in selected_files])+'] and node_id in [' +', '.join([f'\"{node_id}\"' for node_id in all_node])+']', "param": {"ef": k * 4}}
    retriever = vs_db.as_retriever(search_type=search_config["search_type"],
                                   search_kwargs=search_kwarg)
    chunks_bbox_res = client.query(collection_name=collection_name,
                                   # filter='source in [' + ', '.join([f'\"{selected_file}\"' for selected_file in
                                   #                                   selected_files]) + '] and node_id in [' + ', '.join(
                                   #     [f'\"{node_id}\"' for node_id in all_node]) + ']',

                                   filter='source in ['+', '.join([f'\"{selected_file}\"' for selected_file in selected_files])+'] and node_id in [' +', '.join([f'\"{node_id}\"' for node_id in all_node])+'] and text != \"\"',
        output_fields=["bbox", "start_page_num", "content_type", "source"])
    chunks_bbox_text = [milvus_row["bbox"] for milvus_row in chunks_bbox_res]
    chunks_bbox = [[float(bbox_text.strip()) for bbox_text in chunk_bbox_text.replace('(', '').replace(')', '').split(',')] for chunk_bbox_text in chunks_bbox_text]
    
    chunks_page_num = [milvus_row["start_page_num"] for milvus_row in chunks_bbox_res]

    chunk_content_type = [milvus_row["content_type"] for milvus_row in chunks_bbox_res]
    chunk_source = [milvus_row["source"] for milvus_row in chunks_bbox_res]

    chunks_info = [{"bbox": chunks_bbox[i], "page_num": chunks_page_num[i], "content_type": chunk_content_type[i],"source": chunk_source[i]} for i in range(len(chunks_bbox))]

    print(f"milvus_node: {milvus_node}")
    print(f"kg_node: {kg_node}")
    print(f"all_node: {all_node}")
    print(f"chunks_info: {chunks_info}")
    return retriever, chunks_info
    # return NeighbourRetriever(vs_config, embedding, collection_name, search_kwargs)

    # for node_id in all_similar_nodes:
    #     res = client.query(
    #         collection_name=collection_name,
    #         filter='node_id in ['  + f'\"{node_id}\"'+']',
    #         output_fields=["text", "content_type"])
    #     if res[0]["content_type"] != "text":

    #         follow_res = client.query(
    #             collection_name=collection_name,
    #             filter='node_id in ['  +', '.join([f'\"{node_id}\"' for node_id in graphstore_module_newui(collection_name).get_follow_neighbour(node_id)])+']',
    #             output_fields=["text"])
    #         follow_text_list = [mulvis_row["text"] for mulvis_row in follow_res]
    #         follow_text = f"{follow_text_list[0]} {res[0]['text']} {follow_text_list[1]}"
    #         print(follow_text)
    #         document_list.append(Document(page_content=follow_text))
    #     else:
    #         document_list.append(Document(page_content=res[0]["text"]))

    # for (key, value) in similar_nodes_neighbour.items():
    #     for node_id in value:
    #         res = client.query(
    #             collection_name=collection_name,
    #             filter='node_id in ['  + f'\"{node_id}\"'+']',
    #             output_fields=["text", "content_type"])
    #         if res[0]["content_type"] != "text":

    #             follow_res = client.query(
    #                 collection_name=collection_name,
    #                 filter='node_id in ['  +', '.join([f'\"{node_id}\"' for node_id in graphstore_module_newui(collection_name).get_follow_neighbour(node_id)])+']',
    #                 output_fields=["text"])
    #             follow_text_list = [mulvis_row["text"] for mulvis_row in follow_res]
    #             follow_text = f"{follow_text_list[0]} {res[0]['text']} {follow_text_list[1]}"
    #             print(follow_text)
    #             document_list.append(Document(page_content=follow_text))
    #         else:
    #             document_list.append(Document(page_content=res[0]["text"]))

    # for (key, value) in similar_nodes_neighbour.items():
    #     res = client.query(
    #         collection_name=collection_name,
    #         filter='node_id in ['  +', '.join([f'\"{node_id}\"' for node_id in value])+']',
    #         output_fields=["text"])
    #     similar_text_list = [mulvis_row["text"] for mulvis_row in res]
    #     similar_text = "\n".join(similar_text_list)
    #     document_list.append(Document(page_content=similar_text))
    # return NeighbourRetriever(docs = document_list)



    # all_similar_nodes = list(set([item for elem in list(similar_nodes_neighbour.values()) for item in elem]+similar_nodes))
    # print(all_similar_nodes)
    # vs_db = load_collection(vs_config, embedding, collection_name)
    # retriever = vs_db.as_retriever(
    #     search_kwargs={"expr": 'node_id in ['+', '.join([f'\"{node_id}\"' for node_id in all_similar_nodes])+']'}
    # )
    # return retriever



""" Vector Store Management """
def create_vscollection(vs_config,
                        collection_name,
                        schema_config):

    def parse_vsschema(schema_config):
        fields = []
        for field, item in schema_config.items():

            # <field_0> must be uid
            if field == "field_0":
                fieldschema = FieldSchema(
                    name=item["name"],
                    dtype=eval(item["dtype"]),
                    auto_id=eval(item["auto_id"]),
                    is_primary=True
                )

            # name <vector> cannot be changed
            elif item["name"] == "vector":
                fieldschema = FieldSchema(
                    name=item["name"],
                    dtype=eval(item["dtype"]),
                    dim=item["dim"]
                )
            else:
                try:
                    # For VARCHAR Datatype
                    fieldschema = FieldSchema(
                        name=item["name"],
                        dtype=eval(item["dtype"]),
                        max_length = item["max_length"]
                    )
                except KeyError:
                    fieldschema = FieldSchema(
                        name=item["name"],
                        dtype=eval(item["dtype"]),
                    )
            fields.append(fieldschema)

        return fields

    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])

    # Check if Collection Exists
    if utility.has_collection(collection_name):
        print(f'> Collection Name <{collection_name}> Already Exists!')
        return False

    # Init Collection
    fields = parse_vsschema(schema_config)
    schema = CollectionSchema(fields)
    collection = Collection(collection_name, schema=schema)

    # Indexing the Vectors
    index_params = {
        "metric_type": "L2",
        "index_type": "HNSW",
        "params": {"M": 8,
                   "efConstruction": 64}
    }
    collection.create_index(
        field_name="vector",
        index_params=index_params,
        index_name = "vector"
    )    
    # collection.create_index(
    #     field_name="bbox",
    #     index_name = "bbox"
    # )
    utility.index_building_progress(collection_name)

    collection.flush()
    collection.load()
    return True

def create_vspartition(vs_config,
                       collection_name,
                       partition_name):

    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])
    collection = Collection(collection_name)

    # Check if Partition Exists
    if collection.has_partition(partition_name):
        print(f'> Parition Name <{partition_name}> Already Exists!')
        return False

    # Create New Partition
    collection.create_partition(partition_name)
    return True

def insert_vectors(vs_config,
                   collection_name,
                   partition_name,
                   schema_config,
                   texts,
                   vectors,
                   field_dict):

    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])
    collection = Collection(collection_name)

    # Create Entities
    entities = [vectors, texts]
    vector_length = len(vectors)
    for field, item in schema_config.items():
        # Skip <field_0> uid
        if field == "field_0":
            continue

        # Skip <vector>
        elif item["name"] == "vector":
            continue

        # Skip <text>
        elif item["name"] == "text":
            continue

        else:
            try:
                value = field_dict[item["name"]]
            except KeyError:
                print('** Warning : The Field <{}> was not Given. "Undefined" is Assigned by Deafult')
                value = "Undefined"

            field_list = [value] * vector_length
            entities.append(field_list)

    # Insert Vectors into Milvus
    collection.insert(entities, partition_name=partition_name)
    collection.flush()
    # collection.load()
    return collection

def insert_vectors_2(vs_config,
                   collection_name,
                   partition_name,
                   schema_config,
                   texts,
                   vectors,
                   field_dicts):
    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])
    collection = Collection(collection_name)
    # Create Entities
    entities = [vectors, texts]
    vector_length = len(vectors)
    for field, item in schema_config.items():
        # Skip <field_0> uid
        if field == "field_0":
            continue
        # Skip <vector>
        elif item["name"] == "vector":
            continue
        # Skip <text>
        elif item["name"] == "text":
            continue
        else:
            field_list = []
            for field_dict in field_dicts:
                try:
                    value = field_dict[item["name"]]
                except KeyError:
                    print(f'** Warning : The Field <{item["name"]}> was not Given. "Undefined" is Assigned by Default')
                    value = "Undefined"
                field_list.append(value)
            entities.append(field_list)
    # Insert Vectors into Milvus
    collection.insert(entities, partition_name=partition_name)
    collection.flush()
    # collection.load()
    return collection


def select_entities(vs_config,
                    collection_name,
                    filelist):
    connections.connect(host=vs_config["host"], port=vs_config["port"])
    collection = Collection(collection_name)
    collection.load()

    expr = gen_query_expression(filelist)
    res = collection.query(
        expr=expr,
        output_fields=["vector"]
    )
    return res





def load_collection(vs_config,
                    embeddings,
                    colleciton_name):

    milvusDb = Milvus(
        embeddings,
        collection_name=colleciton_name,
        connection_args={"host": vs_config["host"],
                         "port": vs_config["port"]},
    )
    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])
    return milvusDb


# *** Need to revise later
def gen_metadata(filename,
                 chunked_content,
                 title='T',
                 language='us'):

    chunks_with_metadata = []
    # split_docs = chunks
    for chunk in chunked_content:
        met = chunk.metadata
        met['title'] = title
        met['description'] = filename
        met['language'] = language
        chunks_with_metadata.append(Document(page_content=chunk.page_content,
                                             metadata=met))
    return chunks_with_metadata


def add_to_collection(vs_config,
                      chunks_with_metadata,
                      embedding,
                      colleciton_name):

    milvusDb = Milvus.from_documents(
        chunks_with_metadata,
        embedding=embedding,
        collection_name=colleciton_name,
        connection_args={"host": vs_config["host"],
                         "port": vs_config["port"]}
    )


def delete_entities(vs_config,
                    del_file,
                    selected_dataset):

    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])
    collection = Collection(selected_dataset)
    print(f"Before delete entities = {collection.num_entities}")
    partition = Partition(collection=selected_dataset, name=del_file)
    partition.release()
    collection.drop_partition(del_file)
    # collection.delete(expr)
    collection.flush()
    collection.load()
    print(f"After delete entities = {collection.num_entities}")
    print("Delete entities")




def delete_collection(vs_config,
                      selected_dataset):
    connections.connect(host=vs_config["host"],
                        port=vs_config["port"])
    utility.drop_collection(selected_dataset)
    # vs_df = load_vs_df()
    # new_vs_df = vs_df.loc[vs_df["dataset"] != selected_dataset]
    # new_vs_df.to_json(env.vsdb_log_dir, orient='records', indent=4)
    # return new_vs_df

def format_docs(docs):
    return "\n\n".join(doc.page_content for doc in docs)


def gen_query_expression(filelist):
    if not filelist:
        return ""

    _list = '[' + ', '.join([f'\"{name}\"' for name in filelist]) + ']'

    expr = f"source in {_list}"

    # like_clauses = [f"source == '{filename}'" for filename in filelist]
    # expr = " || ".join(like_clauses)
    return expr


def gen_query_expression_2(filelist):
    if not filelist:
        return ""

    expr = " && ".join([f'source == "{name}"' for name in filelist])

    return expr


""" Vector Store Log Management """
def load_vs_df(vsdb_log_dir=env.vsdb_log_dir):
    # Load vs_df
    if os.path.exists(vsdb_log_dir):
        vs_df = pd.read_json(vsdb_log_dir, orient='records')

    # Cretae new vs_df
    else:
        vs_df = pd.DataFrame()

    return vs_df

def update_vs_df(new_file, vsdb_log_dir=env.vsdb_log_dir):

    # Load Existing Log
    vs_df = load_vs_df(vsdb_log_dir)
    new_df = pd.DataFrame([new_file])

    # Update vs_df log json
    if len(vs_df) == 0:
        index = 0
        offset = 0
    else:
        index = vs_df.iloc[-1]["index"] + 1
        offset = vs_df.iloc[-1]["offset"] + vs_df.iloc[-1]["chunk_size"]

    new_df["index"] = index
    new_df["offset"] = offset
    vs_df = pd.concat([vs_df, new_df], ignore_index=True)
    vs_df.to_json(vsdb_log_dir, orient='records', indent=4)
    print("Updated Vector Store Log File")

    return vs_df

def delete_vs_df(del_file, vsdb_log_dir=env.vsdb_log_dir):

    def reset_offset_vs_df(vs_df):
        # Update vs_df log json
        vs_df = vs_df.reset_index(drop=True)
        for i, row in vs_df.iterrows():
            print(row["filename"])
            if i == 0:
                index = 0
                offset = 0
            else:
                index = vs_df.iloc[i-1]["index"] + vs_df.iloc[i-1]["index"]
                offset = vs_df.iloc[i-1]["offset"] + vs_df.iloc[i-1]["chunk_size"]
            vs_df.at[i, "index"] = index
            vs_df.at[i, "offset"] = offset
        return vs_df

    # Load Existing Log
    vs_df = load_vs_df(vsdb_log_dir)
    new_vs_df = vs_df.loc[vs_df['filename'] != del_file]
    vs_df = reset_offset_vs_df(new_vs_df)
    vs_df.to_json(vsdb_log_dir, orient='records', indent=4)

    return vs_df

# def select_entities(host,
#                     port,
#                     filelist,
#                     collection_name):
#     connections.connect(host=host, port=port)
#     collection = Collection(collection_name)
#     collection.load()

#     expr = gen_query_expression(filelist)
#     res = collection.query(
#         expr=expr,
#         output_fields=["vector"]
#     )
#     return res

def init_partition(vs_config,
                   collection_name,
                   filelist):
    connections.connect(host=vs_config["host"], port=vs_config["port"])
    collection = Collection(collection_name)
    collection.load(filelist)
    return collection

def load_partition(collection, filelist):
    collection.load(filelist)
    return collection

def release_partition(collection, filelist):
    collection.release(filelist)
    return collection


# # Get Retriever
# def init_vectordb(directory,
#                   host,
#                   port,
#                   collection_name,
#                   embedding_config,
#                   textsplit_chunk_size=1000,
#                   textsplit_chunk_overlap=20,
#                   search_type="similarity",
#                   search_kwargs={"k":6}):
#
#     if not directory:
#         return None
#
#     # Remove Old Milvus collection
#     MILVUS_HOST = host
#     MILVUS_PORT = port
#     COLLECTION_NAME = collection_name
#     milvus_connection = connections.connect("default",
#                                             host=MILVUS_HOST,
#                                             port=MILVUS_PORT)
#
#     loader = DirectoryLoader(directory)
#     documents = loader.load()
#
#     """ *** Refine Later >>> Duplicated variable name """
#     text_splitter = CharacterTextSplitter(chunk_size=textsplit_chunk_size,
#                                           chunk_overlap=textsplit_chunk_overlap)
#     text_splitter = RecursiveCharacterTextSplitter(chunk_size=textsplit_chunk_size,
#                                                    chunk_overlap=textsplit_chunk_overlap)
#
#     split_docs = text_splitter.split_documents(documents)
#
#     new_doc = []
#     for doc in split_docs:
#         met = doc.metadata
#         met['title'] = "L"
#         met['description'] = "L"
#         met['language'] = 'us'
#         new_doc.append(Document(page_content=doc.page_content, metadata=met))
#     #    	continue
#
#     embeddings = preprocessing.load_embedding(embedding_config)
#
#     docsearch = ""
#     # Create New Milvus collection & Store new documents into the collection
#     if not utility.has_collection(COLLECTION_NAME):
#         docsearch = create_collection(new_doc, embeddings)
#
#     # Retrieve Existing Vector Store
#     else:
#         """ *** Refine Later >>> Where would this parameter be used? *** """
#         collection = Collection(COLLECTION_NAME)  # Get an existing collection
#         docsearch = load_collection(new_doc, embeddings)
#
#     retriever = docsearch.as_retriever(search_type=search_type, search_kwargs=search_kwargs)
#     return retriever

def get_retriever3_with_file_priority(vs_config, embedding, collection_name, selected_files, search_config, query):
    """
    改进的检索函数，支持文件优先级和智能多文件检索
    """
    client = MilvusClient(uri=f"http://{vs_config['host']}:{vs_config['port']}")
    
    # 如果只选择了一个文件，使用原有逻辑
    if len(selected_files) == 1:
        return get_retriever3(vs_config, embedding, collection_name, selected_files, search_config, query)
    
    # 多文件情况：分别对每个文件进行检索，然后根据相关性排序
    file_results = {}
    file_scores = {}
    
    print(f"多文件检索模式：对 {len(selected_files)} 个文件分别进行检索")
    
    for file in selected_files:
        print(f"正在检索文件：{file}")
        
        # 对单个文件进行检索
        vs_db = load_collection(vs_config, embedding, collection_name)
        k_per_file = max(5, search_config["search_kwargs"]['k'] // len(selected_files))  # 每个文件的检索数量
        
        search_kwarg = {
            'k': k_per_file, 
            "expr": f'source == \"{file}\"', 
            "param": {"ef": k_per_file * 4}
        }
        
        retriever = vs_db.as_retriever(
            search_type=search_config["search_type"],
            search_kwargs=search_kwarg
        )
        
        file_result = retriever.invoke(query)
        
        if file_result:
            # 计算该文件结果的平均相似度得分
            avg_score = sum([doc.metadata.get('score', 0.5) for doc in file_result]) / len(file_result)
            file_results[file] = file_result
            file_scores[file] = avg_score
            print(f"文件 {file} 检索到 {len(file_result)} 个结果，平均相似度：{avg_score:.3f}")
    
    # 根据文件相关性排序，优先展示最相关的文件内容
    sorted_files = sorted(file_scores.items(), key=lambda x: x[1], reverse=True)
    print(f"文件相关性排序：{[(f, score) for f, score in sorted_files]}")
    
    # 选择策略：
    # 1. 如果最高分文件的相关性显著高于其他文件，主要使用该文件
    # 2. 否则，按比例混合多个文件的结果
    
    if len(sorted_files) > 1:
        best_score = sorted_files[0][1]
        second_score = sorted_files[1][1] if len(sorted_files) > 1 else 0
        
        # 如果最佳文件的相关性比第二好的高出30%以上，主要使用最佳文件
        if best_score > second_score * 1.3:
            primary_file = sorted_files[0][0]
            print(f"检测到主要相关文件：{primary_file}，将优先使用该文件的内容")
            
            # 使用70%的结果来自最相关文件，30%来自其他文件
            primary_results = file_results[primary_file][:int(search_config["search_kwargs"]['k'] * 0.7)]
            
            other_results = []
            remaining_k = search_config["search_kwargs"]['k'] - len(primary_results)
            for file, _ in sorted_files[1:]:
                if remaining_k <= 0:
                    break
                take = min(remaining_k // (len(sorted_files) - 1), len(file_results[file]))
                other_results.extend(file_results[file][:take])
                remaining_k -= take
            
            combined_results = primary_results + other_results
        else:
            print("多个文件相关性接近，平衡使用各文件内容")
            # 平衡使用各文件的结果
            combined_results = []
            k_per_file = search_config["search_kwargs"]['k'] // len(sorted_files)
            
            for file, _ in sorted_files:
                combined_results.extend(file_results[file][:k_per_file])
    else:
        # 只有一个文件有结果
        combined_results = list(file_results.values())[0]
    
    # 获取组合结果的详细信息用于bbox显示
    all_node_ids = [doc.metadata['node_id'] for doc in combined_results]
    
    chunks_bbox_res = client.query(
        collection_name=collection_name,
        filter='source in ['+', '.join([f'\"{file}\"' for file in selected_files])+'] and node_id in [' +', '.join([f'\"{node_id}\"' for node_id in all_node_ids])+'] and text != \"\"',
        output_fields=["bbox", "start_page_num", "content_type", "source"]
    )
    
    chunks_bbox_text = [milvus_row["bbox"] for milvus_row in chunks_bbox_res]
    chunks_bbox = [[float(bbox_text.strip()) for bbox_text in chunk_bbox_text.replace('(', '').replace(')', '').split(',')] for chunk_bbox_text in chunks_bbox_text]
    chunks_page_num = [milvus_row["start_page_num"] for milvus_row in chunks_bbox_res]
    chunk_content_type = [milvus_row["content_type"] for milvus_row in chunks_bbox_res]
    chunk_source = [milvus_row["source"] for milvus_row in chunks_bbox_res]

    chunks_info = [{"bbox": chunks_bbox[i], "page_num": chunks_page_num[i], "content_type": chunk_content_type[i],"source": chunk_source[i]} for i in range(len(chunks_bbox))]
    
    # 创建一个虚拟的retriever用于返回组合结果
    class CombinedRetriever:
        def __init__(self, results):
            self.results = results
        
        def invoke(self, query):
            return self.results
    
    print(f"最终返回 {len(combined_results)} 个结果，来自文件相关性排序")
    print(f"chunks_info: {chunks_info}")
    
    return CombinedRetriever(combined_results), chunks_info


def get_retriever3_file_separated(vs_config, embedding, collection_name, selected_files, search_config, query):
    """
    按文件分组的检索函数，在检索结果中明确标注文件来源
    """
    client = MilvusClient(uri=f"http://{vs_config['host']}:{vs_config['port']}")
    
    # 使用原有的检索逻辑，但在结果中添加更明确的文件来源信息
    similar_nodes = get_similar_nodes_from_milvus(vs_config, embedding, collection_name, selected_files, search_config, query)
    print(f"Milvus检索结果: {similar_nodes}")
    
    similar_nodes_neighbour = graphstore_module_newui(collection_name).get_neighbour_nodes_from_node_id_list(similar_nodes, selected_files, 30)
    
    all_similar_nodes = list(set([item for elem in list(similar_nodes_neighbour.values()) for item in elem]) - set(similar_nodes))
    
    vs_db = load_collection(vs_config, embedding, collection_name)
    k = 10  # For the Prudential demo
    
    search_kwarg = {'k': k, "expr": 'source in ['+', '.join([f'\"{selected_file}\"' for selected_file in selected_files])+'] and node_id in [' +', '.join([f'\"{node_id}\"' for node_id in all_similar_nodes])+']', "param": {"ef": k * 4}}
    retriever = vs_db.as_retriever(search_type=search_config["search_type"], search_kwargs=search_kwarg)
    milvus_result = retriever.invoke(query)
    
    kg_node = [item.metadata['node_id'] for item in milvus_result]
    milvus_node = similar_nodes[:10]
    all_node = milvus_node + kg_node
    
    k = 20
    search_kwarg = {'k': k, "expr": 'source in ['+', '.join([f'\"{selected_file}\"' for selected_file in selected_files])+'] and node_id in [' +', '.join([f'\"{node_id}\"' for node_id in all_node])+']', "param": {"ef": k * 4}}
    retriever = vs_db.as_retriever(search_type=search_config["search_type"], search_kwargs=search_kwarg)
    
    # 获取详细的检索结果，包含更多文件信息
    chunks_bbox_res = client.query(
        collection_name=collection_name,
        filter='source in ['+', '.join([f'\"{selected_file}\"' for selected_file in selected_files])+'] and node_id in [' +', '.join([f'\"{node_id}\"' for node_id in all_node])+'] and text != \"\"',
        output_fields=["bbox", "start_page_num", "content_type", "source", "text"]
    )
    
    # 按文件分组组织结果
    results_by_file = {}
    for result in chunks_bbox_res:
        file_name = result["source"]
        if file_name not in results_by_file:
            results_by_file[file_name] = []
        results_by_file[file_name].append(result)
    
    print("按文件分组的检索结果：")
    for file_name, file_results in results_by_file.items():
        print(f"  {file_name}: {len(file_results)} 个结果")
    
    # 生成用于显示的chunks_info
    chunks_bbox_text = [milvus_row["bbox"] for milvus_row in chunks_bbox_res]
    chunks_bbox = [[float(bbox_text.strip()) for bbox_text in chunk_bbox_text.replace('(', '').replace(')', '').split(',')] for chunk_bbox_text in chunks_bbox_text]
    chunks_page_num = [milvus_row["start_page_num"] for milvus_row in chunks_bbox_res]
    chunk_content_type = [milvus_row["content_type"] for milvus_row in chunks_bbox_res]
    chunk_source = [milvus_row["source"] for milvus_row in chunks_bbox_res]

    chunks_info = [{"bbox": chunks_bbox[i], "page_num": chunks_page_num[i], "content_type": chunk_content_type[i],"source": chunk_source[i]} for i in range(len(chunks_bbox))]
    
    # 在retriever中添加文件来源标注功能
    class FileAwareRetriever:
        def __init__(self, original_retriever, results_by_file):
            self.original_retriever = original_retriever
            self.results_by_file = results_by_file
        
        def invoke(self, query):
            results = self.original_retriever.invoke(query)
            
            # 为每个结果添加更明确的文件来源标注
            for doc in results:
                source_file = doc.metadata.get('source', 'unknown')
                # 在文档内容前添加文件来源标注
                doc.page_content = f"[来源文件: {source_file}]\n{doc.page_content}"
            
            return results
    
    return FileAwareRetriever(retriever, results_by_file), chunks_info