#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的MinerU API
"""

import os
import tempfile

def test_real_mineru_api():
    """测试真实的MinerU API"""
    print("🧪 测试真实的MinerU API")
    print("=" * 40)
    
    try:
        from mineru_venv_integration import get_venv_processor
        
        processor = get_venv_processor()
        print(f"✅ 处理器获取成功: {type(processor)}")
        print(f"🔍 处理器可用: {processor.is_available()}")
        
        if processor.is_available():
            # 创建一个简单的PDF文件来测试
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f:
                # 创建一个最小的PDF文件
                pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Hello MinerU!) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
                f.write(pdf_content)
                test_pdf_path = f.name
            
            try:
                print(f"🔍 测试处理PDF: {test_pdf_path}")
                print(f"📊 PDF文件大小: {os.path.getsize(test_pdf_path)} bytes")
                
                layout_data, error = processor.process_pdf(test_pdf_path)
                
                print(f"🔍 处理结果:")
                print(f"  - layout_data存在: {layout_data is not None}")
                print(f"  - error: {error}")
                
                if layout_data:
                    print(f"  - layout_data类型: {type(layout_data)}")
                    
                    # 检查数据结构
                    if isinstance(layout_data, dict):
                        print(f"  - 顶级键: {list(layout_data.keys())}")
                        
                        if "pdf_info" in layout_data:
                            pdf_info = layout_data["pdf_info"]
                            print(f"  - pdf_info类型: {type(pdf_info)}")
                            print(f"  - 页面数量: {len(pdf_info) if isinstance(pdf_info, list) else 'unknown'}")
                            
                            if isinstance(pdf_info, list) and len(pdf_info) > 0:
                                first_page = pdf_info[0]
                                print(f"  - 第一页键: {list(first_page.keys()) if isinstance(first_page, dict) else 'not dict'}")
                    
                    # 测试转换为chunks
                    print(f"🔄 测试转换为chunks...")
                    chunks = processor.convert_to_chunks(layout_data, "test.pdf")
                    print(f"  - 生成chunks数量: {len(chunks)}")
                    
                    if chunks:
                        print(f"  - 第一个chunk文本: {chunks[0][0][:100]}...")
                        print(f"  - 第一个chunk元数据: {chunks[0][1]}")
                        
                        # 显示所有chunks的简要信息
                        for i, (text, metadata) in enumerate(chunks[:3]):  # 只显示前3个
                            print(f"  - Chunk {i+1}: {len(text)} 字符, 类型: {metadata.get('content_type', 'unknown')}")
                    
                    return True
                else:
                    print(f"❌ 处理失败: {error}")
                    return False
                    
            finally:
                # 清理临时文件
                try:
                    os.unlink(test_pdf_path)
                except:
                    pass
        else:
            print("❌ 处理器不可用")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 真实MinerU API测试")
    print("=" * 50)
    
    success = test_real_mineru_api()
    
    print(f"\n📊 测试结果")
    print("=" * 50)
    
    if success:
        print("🎉 真实MinerU API测试成功!")
        print("💡 现在可以重新测试v7版本:")
        print("   python chatbot_newui_new_version_v7.py")
        print("📋 上传PDF时应该会看到:")
        print("   ✅ [PROCESSOR_DEBUG] 使用虚拟环境处理器")
        print("   DEBUG: 使用真实的MinerU API...")
        print("   DEBUG: ✅ 成功导入 mineru.api.parse_doc")
        print("   🔍 [DEBUG] 处理结果: layout_data=True")
        print("   ✅ MinerU成功生成 X 个文本块")
    else:
        print("❌ 真实MinerU API测试失败")
        print("💡 可能的原因:")
        print("   1. MinerU版本不兼容")
        print("   2. 缺少必要的依赖")
        print("   3. 需要下载模型文件")
        print("💡 建议:")
        print("   1. 检查MinerU版本: pip list | grep magic-pdf")
        print("   2. 查看MinerU官方文档")
        print("   3. 或者继续使用原始PDF处理方式")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 测试通过! 真实的MinerU API正常工作!")
        else:
            print("\n❌ 测试失败，但不影响系统正常使用")
    except Exception as e:
        print(f"\n❌ 测试过程出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
