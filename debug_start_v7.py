#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug启动脚本 - chatbot_newui_new_version_v7
带有详细的调试信息
"""

import os
import sys
import subprocess

def debug_environment():
    """调试环境信息"""
    print("🔍 [DEBUG] 环境信息检查")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"工作目录: {os.getcwd()}")
    print(f"系统平台: {sys.platform}")
    print()

def debug_files():
    """调试文件存在性"""
    print("🔍 [DEBUG] 文件存在性检查")
    print("=" * 60)
    
    required_files = [
        'chatbot_newui_new_version_v7.py',
        'mineru_integration.py',
        'mineru_config.py',
        'mineru_venv_integration.py'
    ]
    
    for file in required_files:
        exists = os.path.exists(file)
        size = os.path.getsize(file) if exists else 0
        print(f"{'✅' if exists else '❌'} {file}: {'存在' if exists else '不存在'} ({size} bytes)")
    
    # 检查虚拟环境
    venv_path = os.path.join(os.getcwd(), "mineru_venv")
    venv_exists = os.path.exists(venv_path)
    print(f"{'✅' if venv_exists else '❌'} mineru_venv/: {'存在' if venv_exists else '不存在'}")
    
    if venv_exists:
        python_path = os.path.join(venv_path, 'bin', 'python') if os.name != 'nt' else os.path.join(venv_path, 'Scripts', 'python.exe')
        python_exists = os.path.exists(python_path)
        print(f"  {'✅' if python_exists else '❌'} Python: {python_path} ({'存在' if python_exists else '不存在'})")
    print()

def debug_imports():
    """调试导入情况"""
    print("🔍 [DEBUG] 模块导入检查")
    print("=" * 60)
    
    # 测试系统环境导入
    print("📦 系统环境导入测试:")
    modules_to_test = [
        'mineru_integration',
        'mineru_config', 
        'mineru_venv_integration'
    ]
    
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"  ✅ {module}: 成功")
        except ImportError as e:
            print(f"  ❌ {module}: {str(e)}")
        except Exception as e:
            print(f"  ⚠️  {module}: {str(e)}")
    
    # 测试MinerU相关导入
    print("\n🔍 MinerU相关导入测试:")
    try:
        from mineru_integration import MINERU_AVAILABLE
        print(f"  ✅ MINERU_AVAILABLE: {MINERU_AVAILABLE}")
    except Exception as e:
        print(f"  ❌ MINERU_AVAILABLE: {str(e)}")
    
    try:
        from mineru_venv_integration import MINERU_VENV_AVAILABLE
        print(f"  ✅ MINERU_VENV_AVAILABLE: {MINERU_VENV_AVAILABLE}")
    except Exception as e:
        print(f"  ❌ MINERU_VENV_AVAILABLE: {str(e)}")
    
    print()

def debug_venv_mineru():
    """调试虚拟环境中的MinerU"""
    print("🔍 [DEBUG] 虚拟环境MinerU检查")
    print("=" * 60)
    
    venv_path = os.path.join(os.getcwd(), "mineru_venv")
    if not os.path.exists(venv_path):
        print("❌ 虚拟环境不存在")
        return
    
    python_path = os.path.join(venv_path, 'bin', 'python') if os.name != 'nt' else os.path.join(venv_path, 'Scripts', 'python.exe')
    if not os.path.exists(python_path):
        print(f"❌ 虚拟环境Python不存在: {python_path}")
        return
    
    print(f"✅ 虚拟环境Python: {python_path}")
    
    # 测试虚拟环境中的包
    test_code = '''
import sys
print(f"虚拟环境Python: {sys.executable}")

print("\\n已安装的相关包:")
try:
    import pkg_resources
    installed = [d.project_name for d in pkg_resources.working_set]
    relevant = [p for p in installed if any(k in p.lower() for k in ['magic', 'pdf', 'torch'])]
    for pkg in sorted(relevant):
        try:
            version = pkg_resources.get_distribution(pkg).version
            print(f"  {pkg}: {version}")
        except:
            print(f"  {pkg}: 已安装")
except Exception as e:
    print(f"  检查包失败: {e}")

print("\\nMinerU导入测试:")
success = False

try:
    from magic_pdf.api.magic_pdf_parse import magic_pdf_parse
    print("  ✅ magic_pdf.api.magic_pdf_parse")
    success = True
except ImportError as e:
    print(f"  ❌ magic_pdf.api.magic_pdf_parse: {e}")

if not success:
    try:
        from magic_pdf.pipe.UNIPipe import UNIPipe
        print("  ✅ magic_pdf.pipe.UNIPipe")
        success = True
    except ImportError as e:
        print(f"  ❌ magic_pdf.pipe.UNIPipe: {e}")

if not success:
    try:
        import magic_pdf
        print("  ✅ magic_pdf (基础模块)")
        print(f"    路径: {magic_pdf.__file__}")
        success = True
    except ImportError as e:
        print(f"  ❌ magic_pdf: {e}")

print(f"\\n最终结果: {'成功' if success else '失败'}")
'''
    
    try:
        print("🧪 执行虚拟环境测试...")
        result = subprocess.run([python_path, '-c', test_code], 
                              capture_output=True, text=True, timeout=60)
        print("📋 测试输出:")
        print(result.stdout)
        if result.stderr:
            print("⚠️  错误输出:")
            print(result.stderr)
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    print()

def start_with_debug():
    """带debug信息启动"""
    print("🚀 [DEBUG] 启动v7版本")
    print("=" * 60)
    
    try:
        cmd = [sys.executable, 'chatbot_newui_new_version_v7.py']
        print(f"执行命令: {' '.join(cmd)}")
        print("🌐 访问地址:")
        print("   - 主界面: http://localhost:5000/chat")
        print("   - MinerU状态: http://localhost:5000/mineru")
        print()
        print("⏳ 启动中... (按Ctrl+C停止)")
        print("=" * 60)
        
        # 启动应用
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n⚠️  应用被用户中断")
    except Exception as e:
        print(f"\n❌ 启动失败: {str(e)}")

def main():
    """主函数"""
    print("🔍 chatbot_newui_new_version_v7 Debug启动器")
    print("详细调试信息模式")
    print("=" * 60)
    
    # 1. 环境信息
    debug_environment()
    
    # 2. 文件检查
    debug_files()
    
    # 3. 导入检查
    debug_imports()
    
    # 4. 虚拟环境MinerU检查
    debug_venv_mineru()
    
    # 5. 询问是否继续
    print("🤔 是否继续启动应用? (y/N): ", end="")
    choice = input().strip().lower()
    
    if choice in ['y', 'yes']:
        start_with_debug()
    else:
        print("❌ 启动已取消")
        print("\n💡 根据上面的debug信息，你可以:")
        print("1. 检查缺失的文件")
        print("2. 修复导入问题")
        print("3. 重新安装MinerU")
        print("4. 或者直接运行: python chatbot_newui_new_version_v7.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  Debug被用户中断")
    except Exception as e:
        print(f"\n❌ Debug过程出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
