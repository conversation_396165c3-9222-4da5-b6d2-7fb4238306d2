#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试debug集成
验证所有组件是否正常工作
"""

import os
import sys

def test_basic_imports():
    """测试基础导入"""
    print("🔍 [TEST] 基础导入测试")
    print("=" * 40)
    
    try:
        print("导入 mineru_venv_integration...")
        from mineru_venv_integration import MinerUVenvProcessor, MINERU_VENV_AVAILABLE, get_venv_processor
        print(f"✅ 导入成功")
        print(f"🔍 MINERU_VENV_AVAILABLE = {MINERU_VENV_AVAILABLE}")
        
        if MINERU_VENV_AVAILABLE:
            print("🔍 获取处理器实例...")
            processor = get_venv_processor()
            print(f"✅ 处理器实例: {type(processor)}")
            print(f"🔍 处理器可用: {processor.is_available()}")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_v7_integration():
    """测试v7集成"""
    print("\n🔍 [TEST] v7集成测试")
    print("=" * 40)
    
    try:
        # 模拟v7的导入逻辑
        print("模拟v7导入逻辑...")
        
        MINERU_AVAILABLE = False
        mineru_config = None
        
        try:
            from mineru_integration import MinerUProcessor, MINERU_AVAILABLE as DIRECT_AVAILABLE
            from mineru_config import get_mineru_config
            print(f"✅ 直接集成可用: {DIRECT_AVAILABLE}")
            if DIRECT_AVAILABLE:
                MINERU_AVAILABLE = True
                mineru_config = get_mineru_config()
        except ImportError as e:
            print(f"⚠️  直接集成不可用: {str(e)}")
            
            # 尝试虚拟环境版本
            try:
                from mineru_venv_integration import MinerUVenvProcessor, MINERU_VENV_AVAILABLE
                print(f"🔍 虚拟环境版本可用: {MINERU_VENV_AVAILABLE}")
                if MINERU_VENV_AVAILABLE:
                    MINERU_AVAILABLE = True
                    print("✅ 设置为使用虚拟环境版本")
            except ImportError as ve:
                print(f"❌ 虚拟环境版本也不可用: {str(ve)}")
        
        print(f"🎯 最终 MINERU_AVAILABLE = {MINERU_AVAILABLE}")
        print(f"🎯 配置状态 = {mineru_config is not None}")
        
        return MINERU_AVAILABLE
        
    except Exception as e:
        print(f"❌ v7集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_processing_simulation():
    """模拟PDF处理测试"""
    print("\n🔍 [TEST] PDF处理模拟测试")
    print("=" * 40)
    
    try:
        # 模拟v7的处理逻辑
        filename = "test.pdf"  # 模拟文件名
        json_path = "test.json"  # 模拟预计算文件路径
        
        print(f"🔍 模拟文件: {filename}")
        print(f"🔍 是PDF: {filename.lower().endswith('.pdf')}")
        print(f"🔍 预计算文件存在: {os.path.exists(json_path)}")
        
        # 检查MinerU可用性
        MINERU_AVAILABLE = False
        mineru_config = None
        
        try:
            from mineru_venv_integration import MINERU_VENV_AVAILABLE
            if MINERU_VENV_AVAILABLE:
                MINERU_AVAILABLE = True
                print("✅ 虚拟环境MinerU可用")
        except:
            print("❌ 虚拟环境MinerU不可用")
        
        # 检查处理条件
        should_use_mineru = (
            filename.lower().endswith(".pdf") and 
            MINERU_AVAILABLE and 
            not os.path.exists(json_path) and
            (mineru_config is None or mineru_config.is_enabled())
        )
        
        print(f"🔍 应该使用MinerU: {should_use_mineru}")
        
        if should_use_mineru:
            print("🔄 模拟MinerU处理...")
            
            # 尝试初始化处理器
            try:
                from mineru_venv_integration import get_venv_processor
                processor = get_venv_processor()
                print(f"✅ 处理器初始化成功: {type(processor)}")
                print(f"🔍 处理器可用: {processor.is_available()}")
                
                if processor.is_available():
                    print("✅ 模拟处理成功 - 将使用MinerU")
                    return True
                else:
                    print("⚠️  处理器不可用 - 将回退到原始处理")
                    return False
                    
            except Exception as pe:
                print(f"❌ 处理器初始化失败: {str(pe)}")
                return False
        else:
            print("⚠️  不满足MinerU处理条件 - 将使用原始处理")
            return False
            
    except Exception as e:
        print(f"❌ PDF处理模拟失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 Debug集成测试")
    print("=" * 50)
    
    results = []
    
    # 1. 基础导入测试
    results.append(test_basic_imports())
    
    # 2. v7集成测试
    results.append(test_v7_integration())
    
    # 3. PDF处理模拟测试
    results.append(test_pdf_processing_simulation())
    
    # 总结
    print("\n📊 测试结果总结")
    print("=" * 50)
    
    test_names = ["基础导入", "v7集成", "PDF处理模拟"]
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过! MinerU集成应该正常工作")
        print("💡 现在可以运行: python debug_start_v7.py")
    elif success_count > 0:
        print("⚠️  部分测试通过，可能有一些功能可用")
        print("💡 建议运行: python debug_start_v7.py 查看详细信息")
    else:
        print("❌ 所有测试失败，需要检查安装")
        print("💡 建议:")
        print("   1. 检查虚拟环境: ls -la mineru_venv/")
        print("   2. 重新安装: python safe_install_mineru.py")
        print("   3. 或运行修复: python fix_mineru_installation.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
