"""

    Module Name :           user_profile
    Last Modified Date :    8 Jan 2024

"""

# Import Open-source Libraries
import os
import shutil
import pandas as pd
import datetime

# Import Self-defined Libraries
import env

class UserProfile():

    def __init__(self, 
                 uid=None,
                 user_name="", 
                 create_date="", 
                 last_modified_date="",
                 last_login_date="", 
                 department="",
                 permission=1,
                 prompt_preference=dict(),
                 llm_preference=dict()):
        
        self.uid = uid
        self.user_name = user_name
        self.create_date = create_date
        self.last_modified_date = last_modified_date
        self.last_login_date = last_login_date
        self.department = department
        self.permission = permission
        self.prompt_preference = prompt_preference
        self.llm_preference = llm_preference


    """ *** Refine Later >>> Build Table to Store All User Profiles """
    def generate_uid(uid=None, uid_digit=None):
        
        # Retrieve uid_digit if not specified
        if not uid_digit:
            config_dir = os.path.join(env.default_dir, env.default_config_dir)
            config = pd.read_json(config_dir, typ="series")
            uid_digit = config["uid_digit"]
        
        # Default uid naming convention : +1 from last uid
        if not uid:
            new_uid = len(os.listdir("user"))
        
        uid = f"%0{uid_digit}d" %new_uid
        return uid


    @classmethod
    def create_userprofile(cls, 
                           uid=None, 
                           user_name="",
                           department="",
                           permission=1,
                           prompt_preference=dict(),
                           llm_preference=dict()):
        
        # Create new uid
        if uid == None:
            uid = cls.generate_uid()

        user_profile = {
            "uid": uid,
            "user_name": user_name,
            "create_date": datetime.now(),
            "last_modified_date": datetime.now(),
            "last_login_date": datetime.now(),
            "department": department,
            "permission": permission,
            "prompt_preference": prompt_preference,
            "llm_preference": llm_preference
            }
        
        # Create new user directory
        user_fullform = env.user_prefix + str(uid)
        user_dir = os.path.join(env.user_dir_root, user_fullform)
        os.makedirs(user_dir, exist_ok=True)

        # Create new user profile
        user_profile_dir = os.path.join(user_dir, user_fullform + env.user_prof_suffix)
        df = pd.DataFrame([user_profile])
        df.to_json(user_profile_dir, orient="records")

        # Create user chat history
        user_history_dir = os.path.join(env.user_dir_root, user_fullform + env.user_chat_suffix)
        df = pd.DataFrame()
        df.to_json(user_history_dir, orient="records")


    def read_userprofile(cls, uid):

        user_fullform, user_dir = cls.check_if_uid_exists(uid)

        # Read user profile
        user_profile_dir = os.path.join(user_dir, user_fullform + env.user_prof_suffix)
        df_profile = pd.read_json(user_profile_dir, orient="records")
        
        # Read user chat history
        user_history_dir = os.path.join(env.user_dir_root, user_fullform + env.user_chat_suffix)
        df_history = pd.read_json(user_history_dir, orient="records")

        return df_profile, df_history


    def update_userprofile(cls, uid, df_profile=None, action=dict()):
        
        user_fullform, user_dir = cls.check_if_uid_exists(uid)
        user_profile_dir = os.path.join(user_dir, user_fullform + env.user_prof_suffix)

        if df_profile == None:
            df_profile = pd.read_json(user_profile_dir, orient="records")

        for key, value in action.items():
            try:
                df_profile[key] = value
            except KeyError:
                print("Caution : Key is not Found in the update_userprofile !")
        
        df_profile.to_json(user_profile_dir, orient="records")
        return df_profile
    

    def delete_userprofile(cls, uid, action="ALL"):

        user_fullform, user_dir = cls.check_if_uid_exists(uid)
        if not user_dir:
            print(f'Error : Cannot Find the Directory for User <{uid}>')
            return None
        
        if action.upper() in ["ALL", "A"]:
            shutil.rmtree(user_dir)
            print(f'Successfully Deleted Directory of User <{uid}> !')
        
        elif action.upper() in ["PROFILE", "P"]:
            user_profile_dir = os.path.join(user_dir, user_fullform + env.user_prof_suffix)
            os.remove(user_profile_dir)
            print(f'Successfully Deleted Profile of User <{uid}> !')

        elif action.upper() in ["HISTORY", "H"]:
            user_profile_dir = os.path.join(user_dir, user_fullform + env.user_prof_suffix)
            os.remove(user_profile_dir)
            print(f'Successfully Deleted Profile of User <{uid}> !')

        else:
            print(f"Error : Unknonw Action in Deleting User Profile !")


    def check_if_uid_exists(uid):

        user_fullform = env.user_prefix + str(uid)
        user_dir = os.path.join(env.user_dir_root, user_fullform)

        # Check if User directory exists
        if os.path.exists(user_dir) == False:
            raise Exception ("Caution : User Profile does not Exist !")
        
        return user_fullform, user_dir