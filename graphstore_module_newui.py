# Each time request will initalize then go to one of the following actions ONLY. All action will create a new session.

from kg_construction.main import insert_chunks, delete_chunks, create_space, delete_space, clear_space,search_node, search_neighbour, search_follow_neighbour
from kg_construction.admin import *
from kg_construction.CustomNodes import *
from kg_construction.mock_data import generate_mock
from kg_construction.mock_data2 import generate_mock as generate_mock2
from kg_construction.RelationshipFinder import RelationshipFinder
import json 
import time
import re

class graphstore_module_newui():

    # try to create a space, do not save the space, only save the time of creation and whether it is new.
    def __init__(self,selected_dataset = None):
        self.start_time = time.time()
        if selected_dataset:
            self.space = selected_dataset
            try:
                create_space(selected_dataset)
                self.new_space = True
            except:
                self.new_space = False
        else:
            self.space = "default"
            self.new_space = False

    def chunk_to_nodes(self,chunked_rawtexts:list[str],field_dicts:list[object], id_list,partition_name):

        print("Enter graph module")

        def transform(result):
            rx = r'[\n；\"\\]'
            return re.sub(rx,"",result)
        
        field_dicts = [transform(json.dumps(dict_obj)) for dict_obj in field_dicts]
        chunked_rawtexts = [transform(raw_text) for raw_text in chunked_rawtexts]

        chunk_nodes = [DocumentNode(id_=partition_name,text="")]
        chunk_nodes += [ChunkNode(text=transform(rawtext),metadata=dict_obj,id_=id,parent=partition_name, followed_by= id_list[i-1] if i>0 else None) 
                        for i, (rawtext, dict_obj,id) in enumerate(zip(chunked_rawtexts,field_dicts,id_list))]
        return chunk_nodes

    # A method called by programs outside this module. Will process the input into suitable format for the insert_chunk function.
    def insert_document(self,chunked_rawtexts:list[str],field_dicts:list[object], id_list,partition_name):
        chunk_nodes = self.chunk_to_nodes(chunked_rawtexts,field_dicts,id_list,partition_name)
        insert_chunks(chunk_nodes,self.space,self.start_time)


    def delete_document(self,id:str):
        if self.new_space == False:
            delete_chunks(id,selected_dataset=self.space)

    def delete_dataset(self, selected_dataset):
        delete_space(selected_dataset)
    
    def clear_space(self, selected_dataset):
        clear_space(selected_dataset)
    # Tony's version of retrival, not used.
    # def retrival(self, id_list):
    #     result = {}
    #     for id in id_list:
    #         result[id] = search_neighbour(id,selected_dataset=self.space)
    #     return result
    def get_follow_neighbour(self, node_id):
        return search_follow_neighbour(node_id, self.space)

    def get_neighbour_nodes_from_node_id_list(self, node_id_list: list, selected_files, entities_neighbour_limit) -> dict:
        neighbour_node_dict = {}
        for node_id in node_id_list:
            neighbour_node_dict[node_id] = search_neighbour(node_id, self.space, selected_files, entities_neighbour_limit)
        return neighbour_node_dict

    

    def test(self):
        pass

if __name__ == "__main__":
    graph_store = graphstore_module_newui("llama_index3")
    graph_store.test()