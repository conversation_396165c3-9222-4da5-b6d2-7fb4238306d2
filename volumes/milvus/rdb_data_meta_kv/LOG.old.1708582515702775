2024/02/22-06:10:58.597917 73 RocksDB version: 6.29.5
2024/02/22-06:10:58.598092 73 Git sha 0
2024/02/22-06:10:58.598097 73 Compile date 2023-11-07 23:58:42
2024/02/22-06:10:58.598115 73 DB SUMMARY
2024/02/22-06:10:58.598118 73 DB Session ID:  4X2Y90AXFQ7RZCQG7GE8
2024/02/22-06:10:58.598238 73 CURRENT file:  CURRENT
2024/02/22-06:10:58.598243 73 IDENTITY file:  IDENTITY
2024/02/22-06:10:58.598258 73 MANIFEST file:  MANIFEST-000049 size: 379 Bytes
2024/02/22-06:10:58.598262 73 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 3, files: 000038.sst 000042.sst 000048.sst 
2024/02/22-06:10:58.598268 73 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000050.log size: 15947 ; 
2024/02/22-06:10:58.598274 73                         Options.error_if_exists: 0
2024/02/22-06:10:58.598277 73                       Options.create_if_missing: 1
2024/02/22-06:10:58.598279 73                         Options.paranoid_checks: 1
2024/02/22-06:10:58.598282 73             Options.flush_verify_memtable_count: 1
2024/02/22-06:10:58.598284 73                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-06:10:58.598286 73                                     Options.env: 0x7f2c5c1ac2c0
2024/02/22-06:10:58.598289 73                                      Options.fs: PosixFileSystem
2024/02/22-06:10:58.598292 73                                Options.info_log: 0x7f2a90650050
2024/02/22-06:10:58.598294 73                Options.max_file_opening_threads: 16
2024/02/22-06:10:58.598296 73                              Options.statistics: (nil)
2024/02/22-06:10:58.598299 73                               Options.use_fsync: 0
2024/02/22-06:10:58.598301 73                       Options.max_log_file_size: 0
2024/02/22-06:10:58.598303 73                  Options.max_manifest_file_size: 1073741824
2024/02/22-06:10:58.598306 73                   Options.log_file_time_to_roll: 0
2024/02/22-06:10:58.598308 73                       Options.keep_log_file_num: 1000
2024/02/22-06:10:58.598310 73                    Options.recycle_log_file_num: 0
2024/02/22-06:10:58.598312 73                         Options.allow_fallocate: 1
2024/02/22-06:10:58.598314 73                        Options.allow_mmap_reads: 0
2024/02/22-06:10:58.598316 73                       Options.allow_mmap_writes: 0
2024/02/22-06:10:58.598318 73                        Options.use_direct_reads: 0
2024/02/22-06:10:58.598320 73                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-06:10:58.598322 73          Options.create_missing_column_families: 0
2024/02/22-06:10:58.598324 73                              Options.db_log_dir: 
2024/02/22-06:10:58.598326 73                                 Options.wal_dir: 
2024/02/22-06:10:58.598328 73                Options.table_cache_numshardbits: 6
2024/02/22-06:10:58.598331 73                         Options.WAL_ttl_seconds: 0
2024/02/22-06:10:58.598333 73                       Options.WAL_size_limit_MB: 0
2024/02/22-06:10:58.598335 73                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-06:10:58.598337 73             Options.manifest_preallocation_size: 4194304
2024/02/22-06:10:58.598339 73                     Options.is_fd_close_on_exec: 1
2024/02/22-06:10:58.598341 73                   Options.advise_random_on_open: 1
2024/02/22-06:10:58.598343 73                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-06:10:58.598349 73                    Options.db_write_buffer_size: 0
2024/02/22-06:10:58.598352 73                    Options.write_buffer_manager: 0x7f2a906070a0
2024/02/22-06:10:58.598354 73         Options.access_hint_on_compaction_start: 1
2024/02/22-06:10:58.598356 73  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-06:10:58.598359 73           Options.random_access_max_buffer_size: 1048576
2024/02/22-06:10:58.598361 73                      Options.use_adaptive_mutex: 0
2024/02/22-06:10:58.598363 73                            Options.rate_limiter: (nil)
2024/02/22-06:10:58.598369 73     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-06:10:58.598371 73                       Options.wal_recovery_mode: 2
2024/02/22-06:10:58.598403 73                  Options.enable_thread_tracking: 0
2024/02/22-06:10:58.598405 73                  Options.enable_pipelined_write: 0
2024/02/22-06:10:58.598407 73                  Options.unordered_write: 0
2024/02/22-06:10:58.598409 73         Options.allow_concurrent_memtable_write: 1
2024/02/22-06:10:58.598411 73      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-06:10:58.598413 73             Options.write_thread_max_yield_usec: 100
2024/02/22-06:10:58.598415 73            Options.write_thread_slow_yield_usec: 3
2024/02/22-06:10:58.598417 73                               Options.row_cache: None
2024/02/22-06:10:58.598419 73                              Options.wal_filter: None
2024/02/22-06:10:58.598422 73             Options.avoid_flush_during_recovery: 0
2024/02/22-06:10:58.598424 73             Options.allow_ingest_behind: 0
2024/02/22-06:10:58.598426 73             Options.preserve_deletes: 0
2024/02/22-06:10:58.598428 73             Options.two_write_queues: 0
2024/02/22-06:10:58.598430 73             Options.manual_wal_flush: 0
2024/02/22-06:10:58.598432 73             Options.atomic_flush: 0
2024/02/22-06:10:58.598477 73             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-06:10:58.598479 73                 Options.persist_stats_to_disk: 0
2024/02/22-06:10:58.598481 73                 Options.write_dbid_to_manifest: 0
2024/02/22-06:10:58.598483 73                 Options.log_readahead_size: 0
2024/02/22-06:10:58.598485 73                 Options.file_checksum_gen_factory: Unknown
2024/02/22-06:10:58.598488 73                 Options.best_efforts_recovery: 0
2024/02/22-06:10:58.598490 73                Options.max_bgerror_resume_count: 2147483647
2024/02/22-06:10:58.598492 73            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-06:10:58.598494 73             Options.allow_data_in_errors: 0
2024/02/22-06:10:58.598496 73             Options.db_host_id: __hostname__
2024/02/22-06:10:58.598500 73             Options.max_background_jobs: 4
2024/02/22-06:10:58.598502 73             Options.max_background_compactions: -1
2024/02/22-06:10:58.598504 73             Options.max_subcompactions: 1
2024/02/22-06:10:58.598506 73             Options.avoid_flush_during_shutdown: 0
2024/02/22-06:10:58.598532 73           Options.writable_file_max_buffer_size: 1048576
2024/02/22-06:10:58.598534 73             Options.delayed_write_rate : 16777216
2024/02/22-06:10:58.598536 73             Options.max_total_wal_size: 0
2024/02/22-06:10:58.598538 73             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-06:10:58.598540 73                   Options.stats_dump_period_sec: 600
2024/02/22-06:10:58.598542 73                 Options.stats_persist_period_sec: 600
2024/02/22-06:10:58.598544 73                 Options.stats_history_buffer_size: 1048576
2024/02/22-06:10:58.598546 73                          Options.max_open_files: -1
2024/02/22-06:10:58.598548 73                          Options.bytes_per_sync: 0
2024/02/22-06:10:58.598551 73                      Options.wal_bytes_per_sync: 0
2024/02/22-06:10:58.598553 73                   Options.strict_bytes_per_sync: 0
2024/02/22-06:10:58.598555 73       Options.compaction_readahead_size: 0
2024/02/22-06:10:58.598579 73                  Options.max_background_flushes: 1
2024/02/22-06:10:58.598590 73 Compression algorithms supported:
2024/02/22-06:10:58.598593 73 	kZSTDNotFinalCompression supported: 1
2024/02/22-06:10:58.598596 73 	kZSTD supported: 1
2024/02/22-06:10:58.598598 73 	kXpressCompression supported: 0
2024/02/22-06:10:58.598600 73 	kLZ4HCCompression supported: 0
2024/02/22-06:10:58.598604 73 	kLZ4Compression supported: 0
2024/02/22-06:10:58.598606 73 	kBZip2Compression supported: 0
2024/02/22-06:10:58.598609 73 	kZlibCompression supported: 0
2024/02/22-06:10:58.598611 73 	kSnappyCompression supported: 0
2024/02/22-06:10:58.598616 73 Fast CRC32 supported: Not supported on x86
2024/02/22-06:10:58.598938 73 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000049
2024/02/22-06:10:58.599676 73 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-06:10:58.599682 73               Options.comparator: leveldb.BytewiseComparator
2024/02/22-06:10:58.599685 73           Options.merge_operator: None
2024/02/22-06:10:58.599687 73        Options.compaction_filter: None
2024/02/22-06:10:58.599689 73        Options.compaction_filter_factory: None
2024/02/22-06:10:58.599691 73  Options.sst_partitioner_factory: None
2024/02/22-06:10:58.599693 73         Options.memtable_factory: SkipListFactory
2024/02/22-06:10:58.599695 73            Options.table_factory: BlockBasedTable
2024/02/22-06:10:58.599744 73            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f2a906000e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f2a90607010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-06:10:58.599757 73        Options.write_buffer_size: 67108864
2024/02/22-06:10:58.599760 73  Options.max_write_buffer_number: 2
2024/02/22-06:10:58.599765 73        Options.compression[0]: NoCompression
2024/02/22-06:10:58.599767 73        Options.compression[1]: NoCompression
2024/02/22-06:10:58.599770 73        Options.compression[2]: ZSTD
2024/02/22-06:10:58.599772 73        Options.compression[3]: ZSTD
2024/02/22-06:10:58.599774 73        Options.compression[4]: ZSTD
2024/02/22-06:10:58.599776 73                  Options.bottommost_compression: Disabled
2024/02/22-06:10:58.599778 73       Options.prefix_extractor: nullptr
2024/02/22-06:10:58.599780 73   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-06:10:58.599782 73             Options.num_levels: 5
2024/02/22-06:10:58.599784 73        Options.min_write_buffer_number_to_merge: 1
2024/02/22-06:10:58.599786 73     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-06:10:58.599788 73     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-06:10:58.599790 73            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-06:10:58.599792 73                  Options.bottommost_compression_opts.level: 32767
2024/02/22-06:10:58.599795 73               Options.bottommost_compression_opts.strategy: 0
2024/02/22-06:10:58.599797 73         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-06:10:58.599799 73         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:10:58.599801 73         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-06:10:58.599803 73                  Options.bottommost_compression_opts.enabled: false
2024/02/22-06:10:58.599805 73         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:10:58.599807 73            Options.compression_opts.window_bits: -14
2024/02/22-06:10:58.599809 73                  Options.compression_opts.level: 32767
2024/02/22-06:10:58.599811 73               Options.compression_opts.strategy: 0
2024/02/22-06:10:58.599813 73         Options.compression_opts.max_dict_bytes: 0
2024/02/22-06:10:58.599815 73         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:10:58.599848 73         Options.compression_opts.parallel_threads: 1
2024/02/22-06:10:58.599850 73                  Options.compression_opts.enabled: false
2024/02/22-06:10:58.599852 73         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:10:58.599855 73      Options.level0_file_num_compaction_trigger: 4
2024/02/22-06:10:58.599857 73          Options.level0_slowdown_writes_trigger: 20
2024/02/22-06:10:58.599859 73              Options.level0_stop_writes_trigger: 36
2024/02/22-06:10:58.599861 73                   Options.target_file_size_base: 67108864
2024/02/22-06:10:58.599863 73             Options.target_file_size_multiplier: 2
2024/02/22-06:10:58.599865 73                Options.max_bytes_for_level_base: 268435456
2024/02/22-06:10:58.599867 73 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-06:10:58.599869 73          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-06:10:58.599873 73 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-06:10:58.599875 73 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-06:10:58.599877 73 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-06:10:58.599879 73 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-06:10:58.599881 73 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-06:10:58.599883 73 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-06:10:58.599885 73 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-06:10:58.599887 73       Options.max_sequential_skip_in_iterations: 8
2024/02/22-06:10:58.599889 73                    Options.max_compaction_bytes: 1677721600
2024/02/22-06:10:58.599892 73                        Options.arena_block_size: 1048576
2024/02/22-06:10:58.599894 73   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-06:10:58.599896 73   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-06:10:58.599898 73       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-06:10:58.599900 73                Options.disable_auto_compactions: 0
2024/02/22-06:10:58.599904 73                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-06:10:58.599908 73                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-06:10:58.599910 73 Options.compaction_options_universal.size_ratio: 1
2024/02/22-06:10:58.599912 73 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-06:10:58.599914 73 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-06:10:58.599916 73 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-06:10:58.599918 73 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-06:10:58.599921 73 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-06:10:58.599923 73 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-06:10:58.599925 73 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-06:10:58.599931 73                   Options.table_properties_collectors: 
2024/02/22-06:10:58.599933 73                   Options.inplace_update_support: 0
2024/02/22-06:10:58.599935 73                 Options.inplace_update_num_locks: 10000
2024/02/22-06:10:58.599937 73               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-06:10:58.599940 73               Options.memtable_whole_key_filtering: 0
2024/02/22-06:10:58.599942 73   Options.memtable_huge_page_size: 0
2024/02/22-06:10:58.599944 73                           Options.bloom_locality: 0
2024/02/22-06:10:58.599946 73                    Options.max_successive_merges: 0
2024/02/22-06:10:58.599948 73                Options.optimize_filters_for_hits: 0
2024/02/22-06:10:58.599950 73                Options.paranoid_file_checks: 0
2024/02/22-06:10:58.599952 73                Options.force_consistency_checks: 1
2024/02/22-06:10:58.599954 73                Options.report_bg_io_stats: 0
2024/02/22-06:10:58.599956 73                               Options.ttl: 2592000
2024/02/22-06:10:58.599982 73          Options.periodic_compaction_seconds: 0
2024/02/22-06:10:58.599984 73                       Options.enable_blob_files: false
2024/02/22-06:10:58.599986 73                           Options.min_blob_size: 0
2024/02/22-06:10:58.599988 73                          Options.blob_file_size: 268435456
2024/02/22-06:10:58.599991 73                   Options.blob_compression_type: NoCompression
2024/02/22-06:10:58.599993 73          Options.enable_blob_garbage_collection: false
2024/02/22-06:10:58.599995 73      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-06:10:58.599997 73 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-06:10:58.600000 73          Options.blob_compaction_readahead_size: 0
2024/02/22-06:10:58.604298 73 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000049 succeeded,manifest_file_number is 49, next_file_number is 51, last_sequence is 425839, log_number is 45,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2024/02/22-06:10:58.604308 73 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 45
2024/02/22-06:10:58.604533 73 [db/version_set.cc:4409] Creating manifest 53
2024/02/22-06:10:58.607459 73 EVENT_LOG_v1 {"time_micros": 1708582258607433, "job": 1, "event": "recovery_started", "wal_files": [50]}
2024/02/22-06:10:58.607469 73 [db/db_impl/db_impl_open.cc:888] Recovering log #50 mode 2
2024/02/22-06:10:58.609086 73 EVENT_LOG_v1 {"time_micros": 1708582258609040, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 54, "file_size": 1050, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 112, "index_size": 23, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 100, "raw_average_key_size": 33, "raw_value_size": 24, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 3, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708582258, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "be375e1e-b4c8-47d2-9072-ae2e62899d79", "db_session_id": "4X2Y90AXFQ7RZCQG7GE8", "orig_file_number": 54}}
2024/02/22-06:10:58.609186 73 [db/version_set.cc:4409] Creating manifest 55
2024/02/22-06:10:58.610708 73 EVENT_LOG_v1 {"time_micros": 1708582258610703, "job": 1, "event": "recovery_finished"}
2024/02/22-06:10:58.613811 73 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000050.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:10:58.613896 73 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f2a90655000
2024/02/22-06:10:58.614005 73 DB pointer 0x7f2a9063b000
2024/02/22-06:10:58.614079 83 (Original Log Time 2024/02/22-06:10:58.614044) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/22-06:10:58.614851 103 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-06:10:58.614888 103 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.08 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    4.98 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.07 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.07 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f2a90607010#8 capacity: 4.00 GB collections: 1 last_copies: 0 last_secs: 0.000154 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
