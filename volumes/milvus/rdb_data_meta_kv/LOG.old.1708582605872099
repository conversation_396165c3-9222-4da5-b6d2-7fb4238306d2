2024/02/22-06:15:15.703875 65 RocksDB version: 6.29.5
2024/02/22-06:15:15.704042 65 Git sha 0
2024/02/22-06:15:15.704046 65 Compile date 2023-11-07 23:58:42
2024/02/22-06:15:15.704063 65 DB SUMMARY
2024/02/22-06:15:15.704066 65 DB Session ID:  JVQXPWUKC85GPMLYUJYE
2024/02/22-06:15:15.704195 65 CURRENT file:  CURRENT
2024/02/22-06:15:15.704198 65 IDENTITY file:  IDENTITY
2024/02/22-06:15:15.704213 65 MANIFEST file:  MANIFEST-000055 size: 471 Bytes
2024/02/22-06:15:15.704217 65 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 4, files: 000038.sst 000042.sst 000048.sst 000054.sst 
2024/02/22-06:15:15.704223 65 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000056.log size: 16765 ; 
2024/02/22-06:15:15.704228 65                         Options.error_if_exists: 0
2024/02/22-06:15:15.704231 65                       Options.create_if_missing: 1
2024/02/22-06:15:15.704233 65                         Options.paranoid_checks: 1
2024/02/22-06:15:15.704235 65             Options.flush_verify_memtable_count: 1
2024/02/22-06:15:15.704237 65                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-06:15:15.704240 65                                     Options.env: 0x7fc799adb2c0
2024/02/22-06:15:15.704243 65                                      Options.fs: PosixFileSystem
2024/02/22-06:15:15.704246 65                                Options.info_log: 0x7fc5d2050050
2024/02/22-06:15:15.704248 65                Options.max_file_opening_threads: 16
2024/02/22-06:15:15.704250 65                              Options.statistics: (nil)
2024/02/22-06:15:15.704253 65                               Options.use_fsync: 0
2024/02/22-06:15:15.704255 65                       Options.max_log_file_size: 0
2024/02/22-06:15:15.704257 65                  Options.max_manifest_file_size: 1073741824
2024/02/22-06:15:15.704260 65                   Options.log_file_time_to_roll: 0
2024/02/22-06:15:15.704262 65                       Options.keep_log_file_num: 1000
2024/02/22-06:15:15.704264 65                    Options.recycle_log_file_num: 0
2024/02/22-06:15:15.704266 65                         Options.allow_fallocate: 1
2024/02/22-06:15:15.704268 65                        Options.allow_mmap_reads: 0
2024/02/22-06:15:15.704271 65                       Options.allow_mmap_writes: 0
2024/02/22-06:15:15.704273 65                        Options.use_direct_reads: 0
2024/02/22-06:15:15.704275 65                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-06:15:15.704277 65          Options.create_missing_column_families: 0
2024/02/22-06:15:15.704281 65                              Options.db_log_dir: 
2024/02/22-06:15:15.704284 65                                 Options.wal_dir: 
2024/02/22-06:15:15.704286 65                Options.table_cache_numshardbits: 6
2024/02/22-06:15:15.704288 65                         Options.WAL_ttl_seconds: 0
2024/02/22-06:15:15.704290 65                       Options.WAL_size_limit_MB: 0
2024/02/22-06:15:15.704292 65                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-06:15:15.704294 65             Options.manifest_preallocation_size: 4194304
2024/02/22-06:15:15.704297 65                     Options.is_fd_close_on_exec: 1
2024/02/22-06:15:15.704299 65                   Options.advise_random_on_open: 1
2024/02/22-06:15:15.704302 65                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-06:15:15.704310 65                    Options.db_write_buffer_size: 0
2024/02/22-06:15:15.704312 65                    Options.write_buffer_manager: 0x7fc5d20070a0
2024/02/22-06:15:15.704315 65         Options.access_hint_on_compaction_start: 1
2024/02/22-06:15:15.704318 65  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-06:15:15.704320 65           Options.random_access_max_buffer_size: 1048576
2024/02/22-06:15:15.704322 65                      Options.use_adaptive_mutex: 0
2024/02/22-06:15:15.704324 65                            Options.rate_limiter: (nil)
2024/02/22-06:15:15.704329 65     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-06:15:15.704331 65                       Options.wal_recovery_mode: 2
2024/02/22-06:15:15.704365 65                  Options.enable_thread_tracking: 0
2024/02/22-06:15:15.704369 65                  Options.enable_pipelined_write: 0
2024/02/22-06:15:15.704371 65                  Options.unordered_write: 0
2024/02/22-06:15:15.704373 65         Options.allow_concurrent_memtable_write: 1
2024/02/22-06:15:15.704375 65      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-06:15:15.704377 65             Options.write_thread_max_yield_usec: 100
2024/02/22-06:15:15.704379 65            Options.write_thread_slow_yield_usec: 3
2024/02/22-06:15:15.704381 65                               Options.row_cache: None
2024/02/22-06:15:15.704383 65                              Options.wal_filter: None
2024/02/22-06:15:15.704386 65             Options.avoid_flush_during_recovery: 0
2024/02/22-06:15:15.704388 65             Options.allow_ingest_behind: 0
2024/02/22-06:15:15.704390 65             Options.preserve_deletes: 0
2024/02/22-06:15:15.704393 65             Options.two_write_queues: 0
2024/02/22-06:15:15.704395 65             Options.manual_wal_flush: 0
2024/02/22-06:15:15.704397 65             Options.atomic_flush: 0
2024/02/22-06:15:15.704399 65             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-06:15:15.704401 65                 Options.persist_stats_to_disk: 0
2024/02/22-06:15:15.704403 65                 Options.write_dbid_to_manifest: 0
2024/02/22-06:15:15.704406 65                 Options.log_readahead_size: 0
2024/02/22-06:15:15.704408 65                 Options.file_checksum_gen_factory: Unknown
2024/02/22-06:15:15.704410 65                 Options.best_efforts_recovery: 0
2024/02/22-06:15:15.704413 65                Options.max_bgerror_resume_count: 2147483647
2024/02/22-06:15:15.704415 65            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-06:15:15.704417 65             Options.allow_data_in_errors: 0
2024/02/22-06:15:15.704419 65             Options.db_host_id: __hostname__
2024/02/22-06:15:15.704423 65             Options.max_background_jobs: 4
2024/02/22-06:15:15.704425 65             Options.max_background_compactions: -1
2024/02/22-06:15:15.704427 65             Options.max_subcompactions: 1
2024/02/22-06:15:15.704429 65             Options.avoid_flush_during_shutdown: 0
2024/02/22-06:15:15.704431 65           Options.writable_file_max_buffer_size: 1048576
2024/02/22-06:15:15.704433 65             Options.delayed_write_rate : 16777216
2024/02/22-06:15:15.704436 65             Options.max_total_wal_size: 0
2024/02/22-06:15:15.704438 65             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-06:15:15.704440 65                   Options.stats_dump_period_sec: 600
2024/02/22-06:15:15.704442 65                 Options.stats_persist_period_sec: 600
2024/02/22-06:15:15.704444 65                 Options.stats_history_buffer_size: 1048576
2024/02/22-06:15:15.704447 65                          Options.max_open_files: -1
2024/02/22-06:15:15.704449 65                          Options.bytes_per_sync: 0
2024/02/22-06:15:15.704451 65                      Options.wal_bytes_per_sync: 0
2024/02/22-06:15:15.704453 65                   Options.strict_bytes_per_sync: 0
2024/02/22-06:15:15.704455 65       Options.compaction_readahead_size: 0
2024/02/22-06:15:15.704458 65                  Options.max_background_flushes: 1
2024/02/22-06:15:15.704460 65 Compression algorithms supported:
2024/02/22-06:15:15.704463 65 	kZSTDNotFinalCompression supported: 1
2024/02/22-06:15:15.704465 65 	kZSTD supported: 1
2024/02/22-06:15:15.704468 65 	kXpressCompression supported: 0
2024/02/22-06:15:15.704473 65 	kLZ4HCCompression supported: 0
2024/02/22-06:15:15.704475 65 	kLZ4Compression supported: 0
2024/02/22-06:15:15.704478 65 	kBZip2Compression supported: 0
2024/02/22-06:15:15.704480 65 	kZlibCompression supported: 0
2024/02/22-06:15:15.704482 65 	kSnappyCompression supported: 0
2024/02/22-06:15:15.704487 65 Fast CRC32 supported: Not supported on x86
2024/02/22-06:15:15.704764 65 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000055
2024/02/22-06:15:15.705407 65 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-06:15:15.705414 65               Options.comparator: leveldb.BytewiseComparator
2024/02/22-06:15:15.705449 65           Options.merge_operator: None
2024/02/22-06:15:15.705451 65        Options.compaction_filter: None
2024/02/22-06:15:15.705453 65        Options.compaction_filter_factory: None
2024/02/22-06:15:15.705455 65  Options.sst_partitioner_factory: None
2024/02/22-06:15:15.705458 65         Options.memtable_factory: SkipListFactory
2024/02/22-06:15:15.705460 65            Options.table_factory: BlockBasedTable
2024/02/22-06:15:15.705516 65            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fc5d20000e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fc5d2007010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-06:15:15.705529 65        Options.write_buffer_size: 67108864
2024/02/22-06:15:15.705531 65  Options.max_write_buffer_number: 2
2024/02/22-06:15:15.705537 65        Options.compression[0]: NoCompression
2024/02/22-06:15:15.705540 65        Options.compression[1]: NoCompression
2024/02/22-06:15:15.705542 65        Options.compression[2]: ZSTD
2024/02/22-06:15:15.705544 65        Options.compression[3]: ZSTD
2024/02/22-06:15:15.705546 65        Options.compression[4]: ZSTD
2024/02/22-06:15:15.705549 65                  Options.bottommost_compression: Disabled
2024/02/22-06:15:15.705551 65       Options.prefix_extractor: nullptr
2024/02/22-06:15:15.705553 65   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-06:15:15.705555 65             Options.num_levels: 5
2024/02/22-06:15:15.705557 65        Options.min_write_buffer_number_to_merge: 1
2024/02/22-06:15:15.705559 65     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-06:15:15.705562 65     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-06:15:15.705564 65            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-06:15:15.705566 65                  Options.bottommost_compression_opts.level: 32767
2024/02/22-06:15:15.705568 65               Options.bottommost_compression_opts.strategy: 0
2024/02/22-06:15:15.705571 65         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-06:15:15.705573 65         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:15:15.705575 65         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-06:15:15.705577 65                  Options.bottommost_compression_opts.enabled: false
2024/02/22-06:15:15.705580 65         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:15:15.705582 65            Options.compression_opts.window_bits: -14
2024/02/22-06:15:15.705584 65                  Options.compression_opts.level: 32767
2024/02/22-06:15:15.705586 65               Options.compression_opts.strategy: 0
2024/02/22-06:15:15.705589 65         Options.compression_opts.max_dict_bytes: 0
2024/02/22-06:15:15.705591 65         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:15:15.705626 65         Options.compression_opts.parallel_threads: 1
2024/02/22-06:15:15.705629 65                  Options.compression_opts.enabled: false
2024/02/22-06:15:15.705631 65         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:15:15.705633 65      Options.level0_file_num_compaction_trigger: 4
2024/02/22-06:15:15.705635 65          Options.level0_slowdown_writes_trigger: 20
2024/02/22-06:15:15.705637 65              Options.level0_stop_writes_trigger: 36
2024/02/22-06:15:15.705639 65                   Options.target_file_size_base: 67108864
2024/02/22-06:15:15.705641 65             Options.target_file_size_multiplier: 2
2024/02/22-06:15:15.705643 65                Options.max_bytes_for_level_base: 268435456
2024/02/22-06:15:15.705646 65 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-06:15:15.705648 65          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-06:15:15.705652 65 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-06:15:15.705655 65 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-06:15:15.705657 65 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-06:15:15.705659 65 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-06:15:15.705661 65 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-06:15:15.705663 65 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-06:15:15.705665 65 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-06:15:15.705667 65       Options.max_sequential_skip_in_iterations: 8
2024/02/22-06:15:15.705669 65                    Options.max_compaction_bytes: 1677721600
2024/02/22-06:15:15.705671 65                        Options.arena_block_size: 1048576
2024/02/22-06:15:15.705674 65   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-06:15:15.705676 65   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-06:15:15.705678 65       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-06:15:15.705680 65                Options.disable_auto_compactions: 0
2024/02/22-06:15:15.705683 65                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-06:15:15.705686 65                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-06:15:15.705688 65 Options.compaction_options_universal.size_ratio: 1
2024/02/22-06:15:15.705690 65 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-06:15:15.705693 65 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-06:15:15.705695 65 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-06:15:15.705697 65 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-06:15:15.705700 65 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-06:15:15.705702 65 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-06:15:15.705704 65 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-06:15:15.705710 65                   Options.table_properties_collectors: 
2024/02/22-06:15:15.705712 65                   Options.inplace_update_support: 0
2024/02/22-06:15:15.705714 65                 Options.inplace_update_num_locks: 10000
2024/02/22-06:15:15.705716 65               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-06:15:15.705719 65               Options.memtable_whole_key_filtering: 0
2024/02/22-06:15:15.705721 65   Options.memtable_huge_page_size: 0
2024/02/22-06:15:15.705723 65                           Options.bloom_locality: 0
2024/02/22-06:15:15.705725 65                    Options.max_successive_merges: 0
2024/02/22-06:15:15.705727 65                Options.optimize_filters_for_hits: 0
2024/02/22-06:15:15.705729 65                Options.paranoid_file_checks: 0
2024/02/22-06:15:15.705732 65                Options.force_consistency_checks: 1
2024/02/22-06:15:15.705734 65                Options.report_bg_io_stats: 0
2024/02/22-06:15:15.705736 65                               Options.ttl: 2592000
2024/02/22-06:15:15.705760 65          Options.periodic_compaction_seconds: 0
2024/02/22-06:15:15.705762 65                       Options.enable_blob_files: false
2024/02/22-06:15:15.705765 65                           Options.min_blob_size: 0
2024/02/22-06:15:15.705767 65                          Options.blob_file_size: 268435456
2024/02/22-06:15:15.705769 65                   Options.blob_compression_type: NoCompression
2024/02/22-06:15:15.705771 65          Options.enable_blob_garbage_collection: false
2024/02/22-06:15:15.705773 65      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-06:15:15.705776 65 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-06:15:15.705802 65          Options.blob_compaction_readahead_size: 0
2024/02/22-06:15:15.710252 65 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000055 succeeded,manifest_file_number is 55, next_file_number is 57, last_sequence is 426088, log_number is 51,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2024/02/22-06:15:15.710262 65 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 51
2024/02/22-06:15:15.710472 65 [db/version_set.cc:4409] Creating manifest 59
2024/02/22-06:15:15.712867 65 EVENT_LOG_v1 {"time_micros": 1708582515712844, "job": 1, "event": "recovery_started", "wal_files": [56]}
2024/02/22-06:15:15.712877 65 [db/db_impl/db_impl_open.cc:888] Recovering log #56 mode 2
2024/02/22-06:15:15.714406 65 EVENT_LOG_v1 {"time_micros": 1708582515714360, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 60, "file_size": 1050, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 112, "index_size": 23, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 100, "raw_average_key_size": 33, "raw_value_size": 24, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 3, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708582515, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "be375e1e-b4c8-47d2-9072-ae2e62899d79", "db_session_id": "JVQXPWUKC85GPMLYUJYE", "orig_file_number": 60}}
2024/02/22-06:15:15.714489 65 [db/version_set.cc:4409] Creating manifest 61
2024/02/22-06:15:15.715890 65 EVENT_LOG_v1 {"time_micros": 1708582515715885, "job": 1, "event": "recovery_finished"}
2024/02/22-06:15:15.718988 65 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000056.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:15:15.719067 65 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fc5d2055000
2024/02/22-06:15:15.719171 65 DB pointer 0x7fc5d203b000
2024/02/22-06:15:15.719478 80 [db/compaction/compaction_job.cc:2331] [default] [JOB 3] Compacting 4@0 + 1@1 files to L1, score 1.00
2024/02/22-06:15:15.719499 80 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 3 Base level 0, inputs: [60(1050B) 54(1050B) 48(1050B) 42(1050B)], [38(1949B)]
2024/02/22-06:15:15.719565 80 EVENT_LOG_v1 {"time_micros": 1708582515719533, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [60, 54, 48, 42], "files_L1": [38], "score": 1, "input_data_size": 6149}
2024/02/22-06:15:15.720009 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-06:15:15.720043 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      4/4    4.10 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/1    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      5/5    6.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.07 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.07 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fc5d2007010#8 capacity: 4.00 GB collections: 1 last_copies: 0 last_secs: 0.000109 secs_since: 0
Block cache entry stats(count,size,portion): Misc(2,0.19 KB,4.47035e-06%)

** File Read Latency Histogram By Level [default] **
2024/02/22-06:15:15.721156 80 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #65: 37 keys, 1949 bytes
2024/02/22-06:15:15.721202 80 EVENT_LOG_v1 {"time_micros": 1708582515721169, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 65, "file_size": 1949, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 959, "index_size": 49, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 1540, "raw_average_key_size": 41, "raw_value_size": 238, "raw_average_value_size": 6, "num_data_blocks": 1, "num_entries": 37, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1703207242, "oldest_key_time": 0, "file_creation_time": 1708582515, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "be375e1e-b4c8-47d2-9072-ae2e62899d79", "db_session_id": "JVQXPWUKC85GPMLYUJYE", "orig_file_number": 65}}
2024/02/22-06:15:15.721713 80 [db/compaction/compaction_job.cc:1998] [default] [JOB 3] Compacted 4@0 + 1@1 files to L1 => 1949 bytes
2024/02/22-06:15:15.722154 80 (Original Log Time 2024/02/22-06:15:15.722024) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 1 0 0 0] max score 0.00, MB/sec: 3.7 rd, 1.2 wr, level 1, files in(4, 1) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(1.9) write-amplify(0.5) OK, records in: 49, records dropped: 12 output_compression: NoCompression
2024/02/22-06:15:15.722159 80 (Original Log Time 2024/02/22-06:15:15.722062) EVENT_LOG_v1 {"time_micros": 1708582515722035, "job": 3, "event": "compaction_finished", "compaction_time_micros": 1670, "compaction_time_cpu_micros": 1151, "output_level": 1, "num_output_files": 1, "total_output_size": 1949, "num_input_records": 49, "num_output_records": 37, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 1, 0, 0, 0]}
2024/02/22-06:15:15.722308 80 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000060.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:15:15.722321 80 EVENT_LOG_v1 {"time_micros": 1708582515722317, "job": 3, "event": "table_file_deletion", "file_number": 60}
2024/02/22-06:15:15.722399 80 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000054.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:15:15.722404 80 EVENT_LOG_v1 {"time_micros": 1708582515722403, "job": 3, "event": "table_file_deletion", "file_number": 54}
2024/02/22-06:15:15.722480 80 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000048.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:15:15.722484 80 EVENT_LOG_v1 {"time_micros": 1708582515722483, "job": 3, "event": "table_file_deletion", "file_number": 48}
2024/02/22-06:15:15.722541 80 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000042.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:15:15.722545 80 EVENT_LOG_v1 {"time_micros": 1708582515722543, "job": 3, "event": "table_file_deletion", "file_number": 42}
2024/02/22-06:15:15.722612 80 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000038.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:15:15.722617 80 EVENT_LOG_v1 {"time_micros": 1708582515722615, "job": 3, "event": "table_file_deletion", "file_number": 38}
2024/02/22-06:15:15.722658 80 (Original Log Time 2024/02/22-06:15:15.722655) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
