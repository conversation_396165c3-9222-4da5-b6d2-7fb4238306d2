2023/12/21-08:45:04.951662 19 RocksDB version: 6.29.5
2023/12/21-08:45:04.951794 19 Git sha 0
2023/12/21-08:45:04.951796 19 Compile date 2023-11-07 23:58:42
2023/12/21-08:45:04.952075 19 DB SUMMARY
2023/12/21-08:45:04.952078 19 DB Session ID:  6GC3LBFAJNN3L0VL1VTA
2023/12/21-08:45:04.952125 19 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 0, files: 
2023/12/21-08:45:04.952128 19 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 
2023/12/21-08:45:04.952130 19                         Options.error_if_exists: 0
2023/12/21-08:45:04.952131 19                       Options.create_if_missing: 1
2023/12/21-08:45:04.952131 19                         Options.paranoid_checks: 1
2023/12/21-08:45:04.952132 19             Options.flush_verify_memtable_count: 1
2023/12/21-08:45:04.952132 19                               Options.track_and_verify_wals_in_manifest: 0
2023/12/21-08:45:04.952133 19                                     Options.env: 0x7fdd98e692c0
2023/12/21-08:45:04.952134 19                                      Options.fs: PosixFileSystem
2023/12/21-08:45:04.952135 19                                Options.info_log: 0x7fdd57a50050
2023/12/21-08:45:04.952135 19                Options.max_file_opening_threads: 16
2023/12/21-08:45:04.952136 19                              Options.statistics: (nil)
2023/12/21-08:45:04.952137 19                               Options.use_fsync: 0
2023/12/21-08:45:04.952137 19                       Options.max_log_file_size: 0
2023/12/21-08:45:04.952138 19                  Options.max_manifest_file_size: 1073741824
2023/12/21-08:45:04.952139 19                   Options.log_file_time_to_roll: 0
2023/12/21-08:45:04.952139 19                       Options.keep_log_file_num: 1000
2023/12/21-08:45:04.952140 19                    Options.recycle_log_file_num: 0
2023/12/21-08:45:04.952140 19                         Options.allow_fallocate: 1
2023/12/21-08:45:04.952141 19                        Options.allow_mmap_reads: 0
2023/12/21-08:45:04.952141 19                       Options.allow_mmap_writes: 0
2023/12/21-08:45:04.952142 19                        Options.use_direct_reads: 0
2023/12/21-08:45:04.952143 19                        Options.use_direct_io_for_flush_and_compaction: 0
2023/12/21-08:45:04.952143 19          Options.create_missing_column_families: 0
2023/12/21-08:45:04.952144 19                              Options.db_log_dir: 
2023/12/21-08:45:04.952144 19                                 Options.wal_dir: 
2023/12/21-08:45:04.952145 19                Options.table_cache_numshardbits: 6
2023/12/21-08:45:04.952145 19                         Options.WAL_ttl_seconds: 0
2023/12/21-08:45:04.952146 19                       Options.WAL_size_limit_MB: 0
2023/12/21-08:45:04.952146 19                        Options.max_write_batch_group_size_bytes: 1048576
2023/12/21-08:45:04.952147 19             Options.manifest_preallocation_size: 4194304
2023/12/21-08:45:04.952147 19                     Options.is_fd_close_on_exec: 1
2023/12/21-08:45:04.952148 19                   Options.advise_random_on_open: 1
2023/12/21-08:45:04.952149 19                   Options.experimental_mempurge_threshold: 0.000000
2023/12/21-08:45:04.952151 19                    Options.db_write_buffer_size: 0
2023/12/21-08:45:04.952152 19                    Options.write_buffer_manager: 0x7fdd57a040a0
2023/12/21-08:45:04.952152 19         Options.access_hint_on_compaction_start: 1
2023/12/21-08:45:04.952153 19  Options.new_table_reader_for_compaction_inputs: 0
2023/12/21-08:45:04.952153 19           Options.random_access_max_buffer_size: 1048576
2023/12/21-08:45:04.952154 19                      Options.use_adaptive_mutex: 0
2023/12/21-08:45:04.952154 19                            Options.rate_limiter: (nil)
2023/12/21-08:45:04.952156 19     Options.sst_file_manager.rate_bytes_per_sec: 0
2023/12/21-08:45:04.952157 19                       Options.wal_recovery_mode: 2
2023/12/21-08:45:04.952157 19                  Options.enable_thread_tracking: 0
2023/12/21-08:45:04.952158 19                  Options.enable_pipelined_write: 0
2023/12/21-08:45:04.952158 19                  Options.unordered_write: 0
2023/12/21-08:45:04.952179 19         Options.allow_concurrent_memtable_write: 1
2023/12/21-08:45:04.952180 19      Options.enable_write_thread_adaptive_yield: 1
2023/12/21-08:45:04.952181 19             Options.write_thread_max_yield_usec: 100
2023/12/21-08:45:04.952182 19            Options.write_thread_slow_yield_usec: 3
2023/12/21-08:45:04.952182 19                               Options.row_cache: None
2023/12/21-08:45:04.952183 19                              Options.wal_filter: None
2023/12/21-08:45:04.952183 19             Options.avoid_flush_during_recovery: 0
2023/12/21-08:45:04.952184 19             Options.allow_ingest_behind: 0
2023/12/21-08:45:04.952185 19             Options.preserve_deletes: 0
2023/12/21-08:45:04.952185 19             Options.two_write_queues: 0
2023/12/21-08:45:04.952186 19             Options.manual_wal_flush: 0
2023/12/21-08:45:04.952186 19             Options.atomic_flush: 0
2023/12/21-08:45:04.952187 19             Options.avoid_unnecessary_blocking_io: 0
2023/12/21-08:45:04.952187 19                 Options.persist_stats_to_disk: 0
2023/12/21-08:45:04.952188 19                 Options.write_dbid_to_manifest: 0
2023/12/21-08:45:04.952188 19                 Options.log_readahead_size: 0
2023/12/21-08:45:04.952189 19                 Options.file_checksum_gen_factory: Unknown
2023/12/21-08:45:04.952190 19                 Options.best_efforts_recovery: 0
2023/12/21-08:45:04.952190 19                Options.max_bgerror_resume_count: 2147483647
2023/12/21-08:45:04.952191 19            Options.bgerror_resume_retry_interval: 1000000
2023/12/21-08:45:04.952191 19             Options.allow_data_in_errors: 0
2023/12/21-08:45:04.952192 19             Options.db_host_id: __hostname__
2023/12/21-08:45:04.952193 19             Options.max_background_jobs: 1
2023/12/21-08:45:04.952194 19             Options.max_background_compactions: -1
2023/12/21-08:45:04.952195 19             Options.max_subcompactions: 1
2023/12/21-08:45:04.952195 19             Options.avoid_flush_during_shutdown: 0
2023/12/21-08:45:04.952196 19           Options.writable_file_max_buffer_size: 1048576
2023/12/21-08:45:04.952196 19             Options.delayed_write_rate : 16777216
2023/12/21-08:45:04.952197 19             Options.max_total_wal_size: 0
2023/12/21-08:45:04.952198 19             Options.delete_obsolete_files_period_micros: 21600000000
2023/12/21-08:45:04.952198 19                   Options.stats_dump_period_sec: 600
2023/12/21-08:45:04.952199 19                 Options.stats_persist_period_sec: 600
2023/12/21-08:45:04.952199 19                 Options.stats_history_buffer_size: 1048576
2023/12/21-08:45:04.952200 19                          Options.max_open_files: -1
2023/12/21-08:45:04.952200 19                          Options.bytes_per_sync: 0
2023/12/21-08:45:04.952201 19                      Options.wal_bytes_per_sync: 0
2023/12/21-08:45:04.952201 19                   Options.strict_bytes_per_sync: 0
2023/12/21-08:45:04.952202 19       Options.compaction_readahead_size: 0
2023/12/21-08:45:04.952202 19                  Options.max_background_flushes: 1
2023/12/21-08:45:04.952203 19 Compression algorithms supported:
2023/12/21-08:45:04.952204 19 	kZSTDNotFinalCompression supported: 1
2023/12/21-08:45:04.952205 19 	kZSTD supported: 1
2023/12/21-08:45:04.952205 19 	kXpressCompression supported: 0
2023/12/21-08:45:04.952206 19 	kLZ4HCCompression supported: 0
2023/12/21-08:45:04.952207 19 	kLZ4Compression supported: 0
2023/12/21-08:45:04.952207 19 	kBZip2Compression supported: 0
2023/12/21-08:45:04.952208 19 	kZlibCompression supported: 0
2023/12/21-08:45:04.952208 19 	kSnappyCompression supported: 0
2023/12/21-08:45:04.952210 19 Fast CRC32 supported: Not supported on x86
2023/12/21-08:45:04.953825 19 [db/db_impl/db_impl_open.cc:307] Creating manifest 1 
2023/12/21-08:45:04.957376 19 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000001
2023/12/21-08:45:04.959022 19 [db/column_family.cc:605] --------------- Options for column family [default]:
2023/12/21-08:45:04.959044 19               Options.comparator: leveldb.BytewiseComparator
2023/12/21-08:45:04.959046 19           Options.merge_operator: None
2023/12/21-08:45:04.959047 19        Options.compaction_filter: None
2023/12/21-08:45:04.959047 19        Options.compaction_filter_factory: None
2023/12/21-08:45:04.959048 19  Options.sst_partitioner_factory: None
2023/12/21-08:45:04.959049 19         Options.memtable_factory: SkipListFactory
2023/12/21-08:45:04.959049 19            Options.table_factory: BlockBasedTable
2023/12/21-08:45:04.959081 19            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fdd57a160c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fdd57a04010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1074680217
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2023/12/21-08:45:04.959091 19        Options.write_buffer_size: 67108864
2023/12/21-08:45:04.959092 19  Options.max_write_buffer_number: 2
2023/12/21-08:45:04.959094 19        Options.compression[0]: NoCompression
2023/12/21-08:45:04.959095 19        Options.compression[1]: NoCompression
2023/12/21-08:45:04.959096 19        Options.compression[2]: ZSTD
2023/12/21-08:45:04.959096 19        Options.compression[3]: ZSTD
2023/12/21-08:45:04.959097 19        Options.compression[4]: ZSTD
2023/12/21-08:45:04.959098 19                  Options.bottommost_compression: Disabled
2023/12/21-08:45:04.959098 19       Options.prefix_extractor: nullptr
2023/12/21-08:45:04.959099 19   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2023/12/21-08:45:04.959100 19             Options.num_levels: 5
2023/12/21-08:45:04.959100 19        Options.min_write_buffer_number_to_merge: 1
2023/12/21-08:45:04.959101 19     Options.max_write_buffer_number_to_maintain: 0
2023/12/21-08:45:04.959102 19     Options.max_write_buffer_size_to_maintain: 0
2023/12/21-08:45:04.959102 19            Options.bottommost_compression_opts.window_bits: -14
2023/12/21-08:45:04.959103 19                  Options.bottommost_compression_opts.level: 32767
2023/12/21-08:45:04.959104 19               Options.bottommost_compression_opts.strategy: 0
2023/12/21-08:45:04.959104 19         Options.bottommost_compression_opts.max_dict_bytes: 0
2023/12/21-08:45:04.959105 19         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2023/12/21-08:45:04.959105 19         Options.bottommost_compression_opts.parallel_threads: 1
2023/12/21-08:45:04.959106 19                  Options.bottommost_compression_opts.enabled: false
2023/12/21-08:45:04.959106 19         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2023/12/21-08:45:04.959107 19            Options.compression_opts.window_bits: -14
2023/12/21-08:45:04.959108 19                  Options.compression_opts.level: 32767
2023/12/21-08:45:04.959108 19               Options.compression_opts.strategy: 0
2023/12/21-08:45:04.959109 19         Options.compression_opts.max_dict_bytes: 0
2023/12/21-08:45:04.959109 19         Options.compression_opts.zstd_max_train_bytes: 0
2023/12/21-08:45:04.959110 19         Options.compression_opts.parallel_threads: 1
2023/12/21-08:45:04.959110 19                  Options.compression_opts.enabled: false
2023/12/21-08:45:04.959126 19         Options.compression_opts.max_dict_buffer_bytes: 0
2023/12/21-08:45:04.959127 19      Options.level0_file_num_compaction_trigger: 4
2023/12/21-08:45:04.959128 19          Options.level0_slowdown_writes_trigger: 20
2023/12/21-08:45:04.959128 19              Options.level0_stop_writes_trigger: 36
2023/12/21-08:45:04.959129 19                   Options.target_file_size_base: 67108864
2023/12/21-08:45:04.959129 19             Options.target_file_size_multiplier: 2
2023/12/21-08:45:04.959130 19                Options.max_bytes_for_level_base: 268435456
2023/12/21-08:45:04.959131 19 Options.level_compaction_dynamic_level_bytes: 0
2023/12/21-08:45:04.959131 19          Options.max_bytes_for_level_multiplier: 10.000000
2023/12/21-08:45:04.959133 19 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2023/12/21-08:45:04.959133 19 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2023/12/21-08:45:04.959134 19 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2023/12/21-08:45:04.959134 19 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2023/12/21-08:45:04.959135 19 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2023/12/21-08:45:04.959135 19 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2023/12/21-08:45:04.959136 19 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2023/12/21-08:45:04.959137 19       Options.max_sequential_skip_in_iterations: 8
2023/12/21-08:45:04.959137 19                    Options.max_compaction_bytes: 1677721600
2023/12/21-08:45:04.959138 19                        Options.arena_block_size: 1048576
2023/12/21-08:45:04.959138 19   Options.soft_pending_compaction_bytes_limit: 68719476736
2023/12/21-08:45:04.959139 19   Options.hard_pending_compaction_bytes_limit: 274877906944
2023/12/21-08:45:04.959139 19       Options.rate_limit_delay_max_milliseconds: 100
2023/12/21-08:45:04.959140 19                Options.disable_auto_compactions: 0
2023/12/21-08:45:04.959141 19                        Options.compaction_style: kCompactionStyleLevel
2023/12/21-08:45:04.959142 19                          Options.compaction_pri: kMinOverlappingRatio
2023/12/21-08:45:04.959143 19 Options.compaction_options_universal.size_ratio: 1
2023/12/21-08:45:04.959143 19 Options.compaction_options_universal.min_merge_width: 2
2023/12/21-08:45:04.959144 19 Options.compaction_options_universal.max_merge_width: 4294967295
2023/12/21-08:45:04.959144 19 Options.compaction_options_universal.max_size_amplification_percent: 200
2023/12/21-08:45:04.959145 19 Options.compaction_options_universal.compression_size_percent: -1
2023/12/21-08:45:04.959146 19 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2023/12/21-08:45:04.959146 19 Options.compaction_options_fifo.max_table_files_size: 1073741824
2023/12/21-08:45:04.959147 19 Options.compaction_options_fifo.allow_compaction: 0
2023/12/21-08:45:04.959149 19                   Options.table_properties_collectors: 
2023/12/21-08:45:04.959150 19                   Options.inplace_update_support: 0
2023/12/21-08:45:04.959150 19                 Options.inplace_update_num_locks: 10000
2023/12/21-08:45:04.959151 19               Options.memtable_prefix_bloom_size_ratio: 0.000000
2023/12/21-08:45:04.959152 19               Options.memtable_whole_key_filtering: 0
2023/12/21-08:45:04.959152 19   Options.memtable_huge_page_size: 0
2023/12/21-08:45:04.959153 19                           Options.bloom_locality: 0
2023/12/21-08:45:04.959153 19                    Options.max_successive_merges: 0
2023/12/21-08:45:04.959154 19                Options.optimize_filters_for_hits: 0
2023/12/21-08:45:04.959154 19                Options.paranoid_file_checks: 0
2023/12/21-08:45:04.959155 19                Options.force_consistency_checks: 1
2023/12/21-08:45:04.959155 19                Options.report_bg_io_stats: 0
2023/12/21-08:45:04.959156 19                               Options.ttl: 2592000
2023/12/21-08:45:04.959157 19          Options.periodic_compaction_seconds: 0
2023/12/21-08:45:04.959157 19                       Options.enable_blob_files: false
2023/12/21-08:45:04.959171 19                           Options.min_blob_size: 0
2023/12/21-08:45:04.959172 19                          Options.blob_file_size: 268435456
2023/12/21-08:45:04.959173 19                   Options.blob_compression_type: NoCompression
2023/12/21-08:45:04.959174 19          Options.enable_blob_garbage_collection: false
2023/12/21-08:45:04.959174 19      Options.blob_garbage_collection_age_cutoff: 0.250000
2023/12/21-08:45:04.959175 19 Options.blob_garbage_collection_force_threshold: 1.000000
2023/12/21-08:45:04.959176 19          Options.blob_compaction_readahead_size: 0
2023/12/21-08:45:04.965705 19 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2023/12/21-08:45:04.965708 19 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2023/12/21-08:45:04.965945 19 [db/version_set.cc:4409] Creating manifest 4
2023/12/21-08:45:04.970996 19 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fdd57a55000
2023/12/21-08:45:04.971024 19 DB pointer 0x7fdd57a3b000
2023/12/21-08:45:04.971344 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-08:45:04.971351 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 1 last_copies: 0 last_secs: 7.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2023/12/21-08:55:04.971896 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-08:55:04.971936 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 600.0 total, 600.0 interval
Cumulative writes: 18 writes, 35 keys, 18 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 18 writes, 0 syncs, 18.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 18 writes, 35 keys, 18 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 18 writes, 0 syncs, 18.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 2 last_copies: 0 last_secs: 4.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2023/12/21-09:05:04.972376 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-09:05:04.972416 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1200.0 total, 600.0 interval
Cumulative writes: 18 writes, 35 keys, 18 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 18 writes, 0 syncs, 18.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1200.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 3 last_copies: 0 last_secs: 4.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2023/12/21-09:15:04.973088 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-09:15:04.973142 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1800.0 total, 600.0 interval
Cumulative writes: 18 writes, 35 keys, 18 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 18 writes, 0 syncs, 18.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1800.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 4 last_copies: 0 last_secs: 4.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2023/12/21-09:25:04.973375 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-09:25:04.973422 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2400.0 total, 600.0 interval
Cumulative writes: 18 writes, 35 keys, 18 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 18 writes, 0 syncs, 18.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2400.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 5 last_copies: 0 last_secs: 4.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2023/12/21-09:35:04.974278 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-09:35:04.974334 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3000.0 total, 600.0 interval
Cumulative writes: 18 writes, 35 keys, 18 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 18 writes, 0 syncs, 18.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3000.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 6 last_copies: 0 last_secs: 4.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2023/12/21-09:45:04.975243 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-09:45:04.975350 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3600.0 total, 600.0 interval
Cumulative writes: 514 writes, 531 keys, 514 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 514 writes, 0 syncs, 514.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 496 writes, 496 keys, 496 commit groups, 1.0 writes per commit group, ingest: 0.03 MB, 0.00 MB/s
Interval WAL: 496 writes, 0 syncs, 496.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 7 last_copies: 0 last_secs: 3.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2023/12/21-09:55:04.975976 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-09:55:04.976013 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4200.0 total, 600.0 interval
Cumulative writes: 3530 writes, 3547 keys, 3530 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 3530 writes, 0 syncs, 3530.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3016 writes, 3016 keys, 3016 commit groups, 1.0 writes per commit group, ingest: 0.16 MB, 0.00 MB/s
Interval WAL: 3016 writes, 0 syncs, 3016.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4200.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 8 last_copies: 0 last_secs: 3.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2023/12/21-10:05:04.976466 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-10:05:04.976500 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4800.0 total, 600.0 interval
Cumulative writes: 6530 writes, 6547 keys, 6530 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 6530 writes, 0 syncs, 6530.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.17 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4800.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 9 last_copies: 0 last_secs: 7.2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2023/12/21-10:15:04.977323 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-10:15:04.977379 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5400.0 total, 600.0 interval
Cumulative writes: 9530 writes, 9547 keys, 9530 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 9530 writes, 0 syncs, 9530.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.17 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5400.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 10 last_copies: 0 last_secs: 4.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2023/12/21-10:25:04.977634 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-10:25:04.977674 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6000.0 total, 600.0 interval
Cumulative writes: 11K writes, 11K keys, 11K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 11K writes, 0 syncs, 11690.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2160 writes, 2160 keys, 2160 commit groups, 1.0 writes per commit group, ingest: 0.12 MB, 0.00 MB/s
Interval WAL: 2160 writes, 0 syncs, 2160.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6000.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 11 last_copies: 0 last_secs: 4e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
