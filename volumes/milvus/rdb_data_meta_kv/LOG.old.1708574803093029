2024/02/22-04:05:09.674437 68 RocksDB version: 6.29.5
2024/02/22-04:05:09.674616 68 Git sha 0
2024/02/22-04:05:09.674621 68 Compile date 2023-11-07 23:58:42
2024/02/22-04:05:09.674650 68 DB SUMMARY
2024/02/22-04:05:09.674653 68 DB Session ID:  00DPFF01V9F7JEPQLH48
2024/02/22-04:05:09.674787 68 CURRENT file:  CURRENT
2024/02/22-04:05:09.674790 68 IDENTITY file:  IDENTITY
2024/02/22-04:05:09.674805 68 MANIFEST file:  MANIFEST-000028 size: 371 Bytes
2024/02/22-04:05:09.674809 68 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 3, files: 000009.sst 000015.sst 000027.sst 
2024/02/22-04:05:09.674816 68 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000029.log size: 126647 ; 
2024/02/22-04:05:09.674821 68                         Options.error_if_exists: 0
2024/02/22-04:05:09.674824 68                       Options.create_if_missing: 1
2024/02/22-04:05:09.674827 68                         Options.paranoid_checks: 1
2024/02/22-04:05:09.674829 68             Options.flush_verify_memtable_count: 1
2024/02/22-04:05:09.674831 68                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-04:05:09.674833 68                                     Options.env: 0x7f97a328a2c0
2024/02/22-04:05:09.674837 68                                      Options.fs: PosixFileSystem
2024/02/22-04:05:09.674840 68                                Options.info_log: 0x7f95dc050050
2024/02/22-04:05:09.674842 68                Options.max_file_opening_threads: 16
2024/02/22-04:05:09.674844 68                              Options.statistics: (nil)
2024/02/22-04:05:09.674847 68                               Options.use_fsync: 0
2024/02/22-04:05:09.674850 68                       Options.max_log_file_size: 0
2024/02/22-04:05:09.674852 68                  Options.max_manifest_file_size: 1073741824
2024/02/22-04:05:09.674855 68                   Options.log_file_time_to_roll: 0
2024/02/22-04:05:09.674857 68                       Options.keep_log_file_num: 1000
2024/02/22-04:05:09.674859 68                    Options.recycle_log_file_num: 0
2024/02/22-04:05:09.674862 68                         Options.allow_fallocate: 1
2024/02/22-04:05:09.674864 68                        Options.allow_mmap_reads: 0
2024/02/22-04:05:09.674866 68                       Options.allow_mmap_writes: 0
2024/02/22-04:05:09.674868 68                        Options.use_direct_reads: 0
2024/02/22-04:05:09.674870 68                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-04:05:09.674872 68          Options.create_missing_column_families: 0
2024/02/22-04:05:09.674874 68                              Options.db_log_dir: 
2024/02/22-04:05:09.674877 68                                 Options.wal_dir: 
2024/02/22-04:05:09.674879 68                Options.table_cache_numshardbits: 6
2024/02/22-04:05:09.674881 68                         Options.WAL_ttl_seconds: 0
2024/02/22-04:05:09.674883 68                       Options.WAL_size_limit_MB: 0
2024/02/22-04:05:09.674885 68                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-04:05:09.674887 68             Options.manifest_preallocation_size: 4194304
2024/02/22-04:05:09.674889 68                     Options.is_fd_close_on_exec: 1
2024/02/22-04:05:09.674891 68                   Options.advise_random_on_open: 1
2024/02/22-04:05:09.674893 68                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-04:05:09.674903 68                    Options.db_write_buffer_size: 0
2024/02/22-04:05:09.674905 68                    Options.write_buffer_manager: 0x7f95dc0070a0
2024/02/22-04:05:09.674907 68         Options.access_hint_on_compaction_start: 1
2024/02/22-04:05:09.674909 68  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-04:05:09.674911 68           Options.random_access_max_buffer_size: 1048576
2024/02/22-04:05:09.674913 68                      Options.use_adaptive_mutex: 0
2024/02/22-04:05:09.674916 68                            Options.rate_limiter: (nil)
2024/02/22-04:05:09.674922 68     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-04:05:09.674924 68                       Options.wal_recovery_mode: 2
2024/02/22-04:05:09.674955 68                  Options.enable_thread_tracking: 0
2024/02/22-04:05:09.674958 68                  Options.enable_pipelined_write: 0
2024/02/22-04:05:09.674960 68                  Options.unordered_write: 0
2024/02/22-04:05:09.674962 68         Options.allow_concurrent_memtable_write: 1
2024/02/22-04:05:09.674964 68      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-04:05:09.674966 68             Options.write_thread_max_yield_usec: 100
2024/02/22-04:05:09.674968 68            Options.write_thread_slow_yield_usec: 3
2024/02/22-04:05:09.674970 68                               Options.row_cache: None
2024/02/22-04:05:09.674973 68                              Options.wal_filter: None
2024/02/22-04:05:09.674975 68             Options.avoid_flush_during_recovery: 0
2024/02/22-04:05:09.674977 68             Options.allow_ingest_behind: 0
2024/02/22-04:05:09.674979 68             Options.preserve_deletes: 0
2024/02/22-04:05:09.674981 68             Options.two_write_queues: 0
2024/02/22-04:05:09.674984 68             Options.manual_wal_flush: 0
2024/02/22-04:05:09.674986 68             Options.atomic_flush: 0
2024/02/22-04:05:09.674988 68             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-04:05:09.674990 68                 Options.persist_stats_to_disk: 0
2024/02/22-04:05:09.674992 68                 Options.write_dbid_to_manifest: 0
2024/02/22-04:05:09.674995 68                 Options.log_readahead_size: 0
2024/02/22-04:05:09.674997 68                 Options.file_checksum_gen_factory: Unknown
2024/02/22-04:05:09.674999 68                 Options.best_efforts_recovery: 0
2024/02/22-04:05:09.675002 68                Options.max_bgerror_resume_count: 2147483647
2024/02/22-04:05:09.675004 68            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-04:05:09.675006 68             Options.allow_data_in_errors: 0
2024/02/22-04:05:09.675009 68             Options.db_host_id: __hostname__
2024/02/22-04:05:09.675013 68             Options.max_background_jobs: 4
2024/02/22-04:05:09.675015 68             Options.max_background_compactions: -1
2024/02/22-04:05:09.675017 68             Options.max_subcompactions: 1
2024/02/22-04:05:09.675019 68             Options.avoid_flush_during_shutdown: 0
2024/02/22-04:05:09.675021 68           Options.writable_file_max_buffer_size: 1048576
2024/02/22-04:05:09.675023 68             Options.delayed_write_rate : 16777216
2024/02/22-04:05:09.675025 68             Options.max_total_wal_size: 0
2024/02/22-04:05:09.675027 68             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-04:05:09.675030 68                   Options.stats_dump_period_sec: 600
2024/02/22-04:05:09.675032 68                 Options.stats_persist_period_sec: 600
2024/02/22-04:05:09.675034 68                 Options.stats_history_buffer_size: 1048576
2024/02/22-04:05:09.675036 68                          Options.max_open_files: -1
2024/02/22-04:05:09.675038 68                          Options.bytes_per_sync: 0
2024/02/22-04:05:09.675041 68                      Options.wal_bytes_per_sync: 0
2024/02/22-04:05:09.675043 68                   Options.strict_bytes_per_sync: 0
2024/02/22-04:05:09.675045 68       Options.compaction_readahead_size: 0
2024/02/22-04:05:09.675047 68                  Options.max_background_flushes: 1
2024/02/22-04:05:09.675050 68 Compression algorithms supported:
2024/02/22-04:05:09.675053 68 	kZSTDNotFinalCompression supported: 1
2024/02/22-04:05:09.675055 68 	kZSTD supported: 1
2024/02/22-04:05:09.675058 68 	kXpressCompression supported: 0
2024/02/22-04:05:09.675060 68 	kLZ4HCCompression supported: 0
2024/02/22-04:05:09.675063 68 	kLZ4Compression supported: 0
2024/02/22-04:05:09.675065 68 	kBZip2Compression supported: 0
2024/02/22-04:05:09.675067 68 	kZlibCompression supported: 0
2024/02/22-04:05:09.675069 68 	kSnappyCompression supported: 0
2024/02/22-04:05:09.675075 68 Fast CRC32 supported: Not supported on x86
2024/02/22-04:05:09.675387 68 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000028
2024/02/22-04:05:09.676408 68 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-04:05:09.676418 68               Options.comparator: leveldb.BytewiseComparator
2024/02/22-04:05:09.676421 68           Options.merge_operator: None
2024/02/22-04:05:09.676423 68        Options.compaction_filter: None
2024/02/22-04:05:09.676426 68        Options.compaction_filter_factory: None
2024/02/22-04:05:09.676428 68  Options.sst_partitioner_factory: None
2024/02/22-04:05:09.676430 68         Options.memtable_factory: SkipListFactory
2024/02/22-04:05:09.676433 68            Options.table_factory: BlockBasedTable
2024/02/22-04:05:09.676486 68            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f95dc0000e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f95dc007010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-04:05:09.676495 68        Options.write_buffer_size: 67108864
2024/02/22-04:05:09.676498 68  Options.max_write_buffer_number: 2
2024/02/22-04:05:09.676504 68        Options.compression[0]: NoCompression
2024/02/22-04:05:09.676533 68        Options.compression[1]: NoCompression
2024/02/22-04:05:09.676553 68        Options.compression[2]: ZSTD
2024/02/22-04:05:09.676556 68        Options.compression[3]: ZSTD
2024/02/22-04:05:09.676558 68        Options.compression[4]: ZSTD
2024/02/22-04:05:09.676560 68                  Options.bottommost_compression: Disabled
2024/02/22-04:05:09.676562 68       Options.prefix_extractor: nullptr
2024/02/22-04:05:09.676565 68   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-04:05:09.676567 68             Options.num_levels: 5
2024/02/22-04:05:09.676569 68        Options.min_write_buffer_number_to_merge: 1
2024/02/22-04:05:09.676571 68     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-04:05:09.676574 68     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-04:05:09.676576 68            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-04:05:09.676579 68                  Options.bottommost_compression_opts.level: 32767
2024/02/22-04:05:09.676581 68               Options.bottommost_compression_opts.strategy: 0
2024/02/22-04:05:09.676583 68         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-04:05:09.676585 68         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-04:05:09.676588 68         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-04:05:09.676590 68                  Options.bottommost_compression_opts.enabled: false
2024/02/22-04:05:09.676593 68         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-04:05:09.676595 68            Options.compression_opts.window_bits: -14
2024/02/22-04:05:09.676597 68                  Options.compression_opts.level: 32767
2024/02/22-04:05:09.676601 68               Options.compression_opts.strategy: 0
2024/02/22-04:05:09.676623 68         Options.compression_opts.max_dict_bytes: 0
2024/02/22-04:05:09.676625 68         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-04:05:09.676656 68         Options.compression_opts.parallel_threads: 1
2024/02/22-04:05:09.676658 68                  Options.compression_opts.enabled: false
2024/02/22-04:05:09.676660 68         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-04:05:09.676662 68      Options.level0_file_num_compaction_trigger: 4
2024/02/22-04:05:09.676664 68          Options.level0_slowdown_writes_trigger: 20
2024/02/22-04:05:09.676667 68              Options.level0_stop_writes_trigger: 36
2024/02/22-04:05:09.676672 68                   Options.target_file_size_base: 67108864
2024/02/22-04:05:09.676695 68             Options.target_file_size_multiplier: 2
2024/02/22-04:05:09.676715 68                Options.max_bytes_for_level_base: 268435456
2024/02/22-04:05:09.676718 68 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-04:05:09.676720 68          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-04:05:09.676725 68 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-04:05:09.676727 68 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-04:05:09.676729 68 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-04:05:09.676731 68 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-04:05:09.676733 68 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-04:05:09.676755 68 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-04:05:09.676757 68 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-04:05:09.676759 68       Options.max_sequential_skip_in_iterations: 8
2024/02/22-04:05:09.676761 68                    Options.max_compaction_bytes: 1677721600
2024/02/22-04:05:09.676764 68                        Options.arena_block_size: 1048576
2024/02/22-04:05:09.676766 68   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-04:05:09.676768 68   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-04:05:09.676770 68       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-04:05:09.676773 68                Options.disable_auto_compactions: 0
2024/02/22-04:05:09.676777 68                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-04:05:09.676781 68                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-04:05:09.676783 68 Options.compaction_options_universal.size_ratio: 1
2024/02/22-04:05:09.676786 68 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-04:05:09.676788 68 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-04:05:09.676790 68 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-04:05:09.676793 68 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-04:05:09.676795 68 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-04:05:09.676797 68 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-04:05:09.676799 68 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-04:05:09.676805 68                   Options.table_properties_collectors: 
2024/02/22-04:05:09.676808 68                   Options.inplace_update_support: 0
2024/02/22-04:05:09.676810 68                 Options.inplace_update_num_locks: 10000
2024/02/22-04:05:09.676812 68               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-04:05:09.676815 68               Options.memtable_whole_key_filtering: 0
2024/02/22-04:05:09.676817 68   Options.memtable_huge_page_size: 0
2024/02/22-04:05:09.676819 68                           Options.bloom_locality: 0
2024/02/22-04:05:09.676821 68                    Options.max_successive_merges: 0
2024/02/22-04:05:09.676823 68                Options.optimize_filters_for_hits: 0
2024/02/22-04:05:09.676825 68                Options.paranoid_file_checks: 0
2024/02/22-04:05:09.676827 68                Options.force_consistency_checks: 1
2024/02/22-04:05:09.676829 68                Options.report_bg_io_stats: 0
2024/02/22-04:05:09.676831 68                               Options.ttl: 2592000
2024/02/22-04:05:09.676867 68          Options.periodic_compaction_seconds: 0
2024/02/22-04:05:09.676870 68                       Options.enable_blob_files: false
2024/02/22-04:05:09.676874 68                           Options.min_blob_size: 0
2024/02/22-04:05:09.676877 68                          Options.blob_file_size: 268435456
2024/02/22-04:05:09.676880 68                   Options.blob_compression_type: NoCompression
2024/02/22-04:05:09.676884 68          Options.enable_blob_garbage_collection: false
2024/02/22-04:05:09.676887 68      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-04:05:09.676891 68 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-04:05:09.676895 68          Options.blob_compaction_readahead_size: 0
2024/02/22-04:05:09.681654 68 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000028 succeeded,manifest_file_number is 28, next_file_number is 30, last_sequence is 423402, log_number is 24,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2024/02/22-04:05:09.681665 68 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 24
2024/02/22-04:05:09.681913 68 [db/version_set.cc:4409] Creating manifest 32
2024/02/22-04:05:09.683837 68 EVENT_LOG_v1 {"time_micros": 1708574709683808, "job": 1, "event": "recovery_started", "wal_files": [29]}
2024/02/22-04:05:09.683847 68 [db/db_impl/db_impl_open.cc:888] Recovering log #29 mode 2
2024/02/22-04:05:09.687419 68 EVENT_LOG_v1 {"time_micros": 1708574709687367, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 33, "file_size": 1081, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 140, "index_size": 24, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 141, "raw_average_key_size": 35, "raw_value_size": 29, "raw_average_value_size": 7, "num_data_blocks": 1, "num_entries": 4, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708574709, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "be375e1e-b4c8-47d2-9072-ae2e62899d79", "db_session_id": "00DPFF01V9F7JEPQLH48", "orig_file_number": 33}}
2024/02/22-04:05:09.687519 68 [db/version_set.cc:4409] Creating manifest 34
2024/02/22-04:05:09.689158 68 EVENT_LOG_v1 {"time_micros": 1708574709689153, "job": 1, "event": "recovery_finished"}
2024/02/22-04:05:09.692491 68 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000029.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-04:05:09.692579 68 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f95dc055000
2024/02/22-04:05:09.692686 68 DB pointer 0x7f95dc03b000
2024/02/22-04:05:09.692944 85 [db/compaction/compaction_job.cc:2331] [default] [JOB 3] Compacting 4@0 files to L1, score 1.00
2024/02/22-04:05:09.692966 85 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 3 Base level 0, inputs: [33(1081B) 27(1244B) 15(1100B) 9(1774B)]
2024/02/22-04:05:09.693015 85 EVENT_LOG_v1 {"time_micros": 1708574709692988, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [33, 27, 15, 9], "score": 1, "input_data_size": 5199}
2024/02/22-04:05:09.693549 105 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-04:05:09.693668 105 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      4/4    5.08 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Sum      4/4    5.08 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.06 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.06 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f95dc007010#9 capacity: 4.00 GB collections: 1 last_copies: 0 last_secs: 0.000132 secs_since: 0
Block cache entry stats(count,size,portion): Misc(4,0.86 KB,2.04891e-05%)

** File Read Latency Histogram By Level [default] **
2024/02/22-04:05:09.694534 85 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #38: 37 keys, 1949 bytes
2024/02/22-04:05:09.694577 85 EVENT_LOG_v1 {"time_micros": 1708574709694546, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 38, "file_size": 1949, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 959, "index_size": 49, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 1540, "raw_average_key_size": 41, "raw_value_size": 238, "raw_average_value_size": 6, "num_data_blocks": 1, "num_entries": 37, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1703207242, "oldest_key_time": 0, "file_creation_time": 1708574709, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "be375e1e-b4c8-47d2-9072-ae2e62899d79", "db_session_id": "00DPFF01V9F7JEPQLH48", "orig_file_number": 38}}
2024/02/22-04:05:09.694949 85 [db/compaction/compaction_job.cc:1998] [default] [JOB 3] Compacted 4@0 files to L1 => 1949 bytes
2024/02/22-04:05:09.695407 85 (Original Log Time 2024/02/22-04:05:09.695291) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 1 0 0 0] max score 0.00, MB/sec: 3.2 rd, 1.2 wr, level 1, files in(4, 0) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(1.4) write-amplify(0.4) OK, records in: 51, records dropped: 14 output_compression: NoCompression
2024/02/22-04:05:09.695414 85 (Original Log Time 2024/02/22-04:05:09.695331) EVENT_LOG_v1 {"time_micros": 1708574709695302, "job": 3, "event": "compaction_finished", "compaction_time_micros": 1618, "compaction_time_cpu_micros": 1122, "output_level": 1, "num_output_files": 1, "total_output_size": 1949, "num_input_records": 51, "num_output_records": 37, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 1, 0, 0, 0]}
2024/02/22-04:05:09.695551 85 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000033.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-04:05:09.695561 85 EVENT_LOG_v1 {"time_micros": 1708574709695558, "job": 3, "event": "table_file_deletion", "file_number": 33}
2024/02/22-04:05:09.695647 85 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000027.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-04:05:09.695651 85 EVENT_LOG_v1 {"time_micros": 1708574709695649, "job": 3, "event": "table_file_deletion", "file_number": 27}
2024/02/22-04:05:09.695711 85 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000015.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-04:05:09.695715 85 EVENT_LOG_v1 {"time_micros": 1708574709695714, "job": 3, "event": "table_file_deletion", "file_number": 15}
2024/02/22-04:05:09.695766 85 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000009.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-04:05:09.695769 85 EVENT_LOG_v1 {"time_micros": 1708574709695768, "job": 3, "event": "table_file_deletion", "file_number": 9}
2024/02/22-04:05:09.695810 85 (Original Log Time 2024/02/22-04:05:09.695808) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
