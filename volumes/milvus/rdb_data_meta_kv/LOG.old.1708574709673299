2024/02/22-03:54:16.972457 65 RocksDB version: 6.29.5
2024/02/22-03:54:16.972607 65 Git sha 0
2024/02/22-03:54:16.972611 65 Compile date 2023-11-07 23:58:42
2024/02/22-03:54:16.972631 65 DB SUMMARY
2024/02/22-03:54:16.972633 65 DB Session ID:  9IIKCS8A12KB3I8RI0SO
2024/02/22-03:54:16.972774 65 CURRENT file:  CURRENT
2024/02/22-03:54:16.972777 65 IDENTITY file:  IDENTITY
2024/02/22-03:54:16.972791 65 MANIFEST file:  MANIFEST-000016 size: 280 Bytes
2024/02/22-03:54:16.972803 65 MANIFEST file:  MANIFEST-000022 size: 371 Bytes
2024/02/22-03:54:16.972807 65 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 3, files: 000009.sst 000015.sst 000021.sst 
2024/02/22-03:54:16.972812 65 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000017.log size: 19233394 ; 000023.log size: 4499292 ; 
2024/02/22-03:54:16.972817 65                         Options.error_if_exists: 0
2024/02/22-03:54:16.972820 65                       Options.create_if_missing: 1
2024/02/22-03:54:16.972822 65                         Options.paranoid_checks: 1
2024/02/22-03:54:16.972825 65             Options.flush_verify_memtable_count: 1
2024/02/22-03:54:16.972827 65                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-03:54:16.972829 65                                     Options.env: 0x7f9caa8c72c0
2024/02/22-03:54:16.972832 65                                      Options.fs: PosixFileSystem
2024/02/22-03:54:16.972834 65                                Options.info_log: 0x7f9ae4450050
2024/02/22-03:54:16.972837 65                Options.max_file_opening_threads: 16
2024/02/22-03:54:16.972839 65                              Options.statistics: (nil)
2024/02/22-03:54:16.972841 65                               Options.use_fsync: 0
2024/02/22-03:54:16.972843 65                       Options.max_log_file_size: 0
2024/02/22-03:54:16.972846 65                  Options.max_manifest_file_size: 1073741824
2024/02/22-03:54:16.972848 65                   Options.log_file_time_to_roll: 0
2024/02/22-03:54:16.972850 65                       Options.keep_log_file_num: 1000
2024/02/22-03:54:16.972852 65                    Options.recycle_log_file_num: 0
2024/02/22-03:54:16.972854 65                         Options.allow_fallocate: 1
2024/02/22-03:54:16.972856 65                        Options.allow_mmap_reads: 0
2024/02/22-03:54:16.972859 65                       Options.allow_mmap_writes: 0
2024/02/22-03:54:16.972861 65                        Options.use_direct_reads: 0
2024/02/22-03:54:16.972863 65                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-03:54:16.972865 65          Options.create_missing_column_families: 0
2024/02/22-03:54:16.972867 65                              Options.db_log_dir: 
2024/02/22-03:54:16.972869 65                                 Options.wal_dir: 
2024/02/22-03:54:16.972871 65                Options.table_cache_numshardbits: 6
2024/02/22-03:54:16.972873 65                         Options.WAL_ttl_seconds: 0
2024/02/22-03:54:16.972875 65                       Options.WAL_size_limit_MB: 0
2024/02/22-03:54:16.972877 65                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-03:54:16.972879 65             Options.manifest_preallocation_size: 4194304
2024/02/22-03:54:16.972881 65                     Options.is_fd_close_on_exec: 1
2024/02/22-03:54:16.972883 65                   Options.advise_random_on_open: 1
2024/02/22-03:54:16.972885 65                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-03:54:16.972891 65                    Options.db_write_buffer_size: 0
2024/02/22-03:54:16.972893 65                    Options.write_buffer_manager: 0x7f9ae44070a0
2024/02/22-03:54:16.972896 65         Options.access_hint_on_compaction_start: 1
2024/02/22-03:54:16.972898 65  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-03:54:16.972900 65           Options.random_access_max_buffer_size: 1048576
2024/02/22-03:54:16.972902 65                      Options.use_adaptive_mutex: 0
2024/02/22-03:54:16.972904 65                            Options.rate_limiter: (nil)
2024/02/22-03:54:16.972909 65     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-03:54:16.972943 65                       Options.wal_recovery_mode: 2
2024/02/22-03:54:16.972945 65                  Options.enable_thread_tracking: 0
2024/02/22-03:54:16.972948 65                  Options.enable_pipelined_write: 0
2024/02/22-03:54:16.972950 65                  Options.unordered_write: 0
2024/02/22-03:54:16.972952 65         Options.allow_concurrent_memtable_write: 1
2024/02/22-03:54:16.972954 65      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-03:54:16.972955 65             Options.write_thread_max_yield_usec: 100
2024/02/22-03:54:16.972958 65            Options.write_thread_slow_yield_usec: 3
2024/02/22-03:54:16.972960 65                               Options.row_cache: None
2024/02/22-03:54:16.972962 65                              Options.wal_filter: None
2024/02/22-03:54:16.972964 65             Options.avoid_flush_during_recovery: 0
2024/02/22-03:54:16.972966 65             Options.allow_ingest_behind: 0
2024/02/22-03:54:16.972968 65             Options.preserve_deletes: 0
2024/02/22-03:54:16.972970 65             Options.two_write_queues: 0
2024/02/22-03:54:16.972972 65             Options.manual_wal_flush: 0
2024/02/22-03:54:16.972974 65             Options.atomic_flush: 0
2024/02/22-03:54:16.972976 65             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-03:54:16.972978 65                 Options.persist_stats_to_disk: 0
2024/02/22-03:54:16.972980 65                 Options.write_dbid_to_manifest: 0
2024/02/22-03:54:16.972982 65                 Options.log_readahead_size: 0
2024/02/22-03:54:16.972984 65                 Options.file_checksum_gen_factory: Unknown
2024/02/22-03:54:16.972986 65                 Options.best_efforts_recovery: 0
2024/02/22-03:54:16.972989 65                Options.max_bgerror_resume_count: 2147483647
2024/02/22-03:54:16.972991 65            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-03:54:16.972993 65             Options.allow_data_in_errors: 0
2024/02/22-03:54:16.972995 65             Options.db_host_id: __hostname__
2024/02/22-03:54:16.972999 65             Options.max_background_jobs: 4
2024/02/22-03:54:16.973001 65             Options.max_background_compactions: -1
2024/02/22-03:54:16.973003 65             Options.max_subcompactions: 1
2024/02/22-03:54:16.973005 65             Options.avoid_flush_during_shutdown: 0
2024/02/22-03:54:16.973007 65           Options.writable_file_max_buffer_size: 1048576
2024/02/22-03:54:16.973009 65             Options.delayed_write_rate : 16777216
2024/02/22-03:54:16.973011 65             Options.max_total_wal_size: 0
2024/02/22-03:54:16.973013 65             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-03:54:16.973015 65                   Options.stats_dump_period_sec: 600
2024/02/22-03:54:16.973018 65                 Options.stats_persist_period_sec: 600
2024/02/22-03:54:16.973020 65                 Options.stats_history_buffer_size: 1048576
2024/02/22-03:54:16.973022 65                          Options.max_open_files: -1
2024/02/22-03:54:16.973024 65                          Options.bytes_per_sync: 0
2024/02/22-03:54:16.973026 65                      Options.wal_bytes_per_sync: 0
2024/02/22-03:54:16.973028 65                   Options.strict_bytes_per_sync: 0
2024/02/22-03:54:16.973030 65       Options.compaction_readahead_size: 0
2024/02/22-03:54:16.973032 65                  Options.max_background_flushes: 1
2024/02/22-03:54:16.973034 65 Compression algorithms supported:
2024/02/22-03:54:16.973036 65 	kZSTDNotFinalCompression supported: 1
2024/02/22-03:54:16.973039 65 	kZSTD supported: 1
2024/02/22-03:54:16.973041 65 	kXpressCompression supported: 0
2024/02/22-03:54:16.973043 65 	kLZ4HCCompression supported: 0
2024/02/22-03:54:16.973046 65 	kLZ4Compression supported: 0
2024/02/22-03:54:16.973048 65 	kBZip2Compression supported: 0
2024/02/22-03:54:16.973050 65 	kZlibCompression supported: 0
2024/02/22-03:54:16.973052 65 	kSnappyCompression supported: 0
2024/02/22-03:54:16.973056 65 Fast CRC32 supported: Not supported on x86
2024/02/22-03:54:16.973606 65 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000016
2024/02/22-03:54:16.974245 65 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-03:54:16.974251 65               Options.comparator: leveldb.BytewiseComparator
2024/02/22-03:54:16.974253 65           Options.merge_operator: None
2024/02/22-03:54:16.974255 65        Options.compaction_filter: None
2024/02/22-03:54:16.974258 65        Options.compaction_filter_factory: None
2024/02/22-03:54:16.974260 65  Options.sst_partitioner_factory: None
2024/02/22-03:54:16.974262 65         Options.memtable_factory: SkipListFactory
2024/02/22-03:54:16.974264 65            Options.table_factory: BlockBasedTable
2024/02/22-03:54:16.974311 65            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f9ae4400100)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f9ae4407010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-03:54:16.974324 65        Options.write_buffer_size: 67108864
2024/02/22-03:54:16.974327 65  Options.max_write_buffer_number: 2
2024/02/22-03:54:16.974332 65        Options.compression[0]: NoCompression
2024/02/22-03:54:16.974334 65        Options.compression[1]: NoCompression
2024/02/22-03:54:16.974336 65        Options.compression[2]: ZSTD
2024/02/22-03:54:16.974339 65        Options.compression[3]: ZSTD
2024/02/22-03:54:16.974341 65        Options.compression[4]: ZSTD
2024/02/22-03:54:16.974343 65                  Options.bottommost_compression: Disabled
2024/02/22-03:54:16.974345 65       Options.prefix_extractor: nullptr
2024/02/22-03:54:16.974347 65   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-03:54:16.974349 65             Options.num_levels: 5
2024/02/22-03:54:16.974351 65        Options.min_write_buffer_number_to_merge: 1
2024/02/22-03:54:16.974354 65     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-03:54:16.974356 65     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-03:54:16.974358 65            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-03:54:16.974361 65                  Options.bottommost_compression_opts.level: 32767
2024/02/22-03:54:16.974364 65               Options.bottommost_compression_opts.strategy: 0
2024/02/22-03:54:16.974366 65         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-03:54:16.974368 65         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-03:54:16.974370 65         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-03:54:16.974372 65                  Options.bottommost_compression_opts.enabled: false
2024/02/22-03:54:16.974374 65         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-03:54:16.974376 65            Options.compression_opts.window_bits: -14
2024/02/22-03:54:16.974378 65                  Options.compression_opts.level: 32767
2024/02/22-03:54:16.974380 65               Options.compression_opts.strategy: 0
2024/02/22-03:54:16.974383 65         Options.compression_opts.max_dict_bytes: 0
2024/02/22-03:54:16.974422 65         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-03:54:16.974424 65         Options.compression_opts.parallel_threads: 1
2024/02/22-03:54:16.974426 65                  Options.compression_opts.enabled: false
2024/02/22-03:54:16.974428 65         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-03:54:16.974431 65      Options.level0_file_num_compaction_trigger: 4
2024/02/22-03:54:16.974433 65          Options.level0_slowdown_writes_trigger: 20
2024/02/22-03:54:16.974435 65              Options.level0_stop_writes_trigger: 36
2024/02/22-03:54:16.974437 65                   Options.target_file_size_base: 67108864
2024/02/22-03:54:16.974439 65             Options.target_file_size_multiplier: 2
2024/02/22-03:54:16.974441 65                Options.max_bytes_for_level_base: 268435456
2024/02/22-03:54:16.974443 65 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-03:54:16.974445 65          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-03:54:16.974449 65 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-03:54:16.974451 65 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-03:54:16.974454 65 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-03:54:16.974456 65 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-03:54:16.974458 65 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-03:54:16.974460 65 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-03:54:16.974461 65 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-03:54:16.974464 65       Options.max_sequential_skip_in_iterations: 8
2024/02/22-03:54:16.974466 65                    Options.max_compaction_bytes: 1677721600
2024/02/22-03:54:16.974468 65                        Options.arena_block_size: 1048576
2024/02/22-03:54:16.974470 65   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-03:54:16.974472 65   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-03:54:16.974474 65       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-03:54:16.974476 65                Options.disable_auto_compactions: 0
2024/02/22-03:54:16.974479 65                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-03:54:16.974482 65                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-03:54:16.974484 65 Options.compaction_options_universal.size_ratio: 1
2024/02/22-03:54:16.974487 65 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-03:54:16.974489 65 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-03:54:16.974491 65 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-03:54:16.974493 65 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-03:54:16.974495 65 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-03:54:16.974497 65 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-03:54:16.974499 65 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-03:54:16.974505 65                   Options.table_properties_collectors: 
2024/02/22-03:54:16.974507 65                   Options.inplace_update_support: 0
2024/02/22-03:54:16.974509 65                 Options.inplace_update_num_locks: 10000
2024/02/22-03:54:16.974511 65               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-03:54:16.974514 65               Options.memtable_whole_key_filtering: 0
2024/02/22-03:54:16.974516 65   Options.memtable_huge_page_size: 0
2024/02/22-03:54:16.974518 65                           Options.bloom_locality: 0
2024/02/22-03:54:16.974521 65                    Options.max_successive_merges: 0
2024/02/22-03:54:16.974523 65                Options.optimize_filters_for_hits: 0
2024/02/22-03:54:16.974525 65                Options.paranoid_file_checks: 0
2024/02/22-03:54:16.974527 65                Options.force_consistency_checks: 1
2024/02/22-03:54:16.974529 65                Options.report_bg_io_stats: 0
2024/02/22-03:54:16.974554 65                               Options.ttl: 2592000
2024/02/22-03:54:16.974557 65          Options.periodic_compaction_seconds: 0
2024/02/22-03:54:16.974559 65                       Options.enable_blob_files: false
2024/02/22-03:54:16.974561 65                           Options.min_blob_size: 0
2024/02/22-03:54:16.974563 65                          Options.blob_file_size: 268435456
2024/02/22-03:54:16.974565 65                   Options.blob_compression_type: NoCompression
2024/02/22-03:54:16.974567 65          Options.enable_blob_garbage_collection: false
2024/02/22-03:54:16.974569 65      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-03:54:16.974572 65 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-03:54:16.974574 65          Options.blob_compaction_readahead_size: 0
2024/02/22-03:54:16.979119 65 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000016 succeeded,manifest_file_number is 16, next_file_number is 18, last_sequence is 41485, log_number is 12,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2024/02/22-03:54:16.979130 65 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 12
2024/02/22-03:54:16.979522 65 [db/version_set.cc:4409] Creating manifest 26
2024/02/22-03:54:16.982143 65 EVENT_LOG_v1 {"time_micros": 1708574056982118, "job": 1, "event": "recovery_started", "wal_files": [17, 23]}
2024/02/22-03:54:16.982155 65 [db/db_impl/db_impl_open.cc:888] Recovering log #17 mode 2
2024/02/22-03:54:17.363325 65 [db/db_impl/db_impl_open.cc:888] Recovering log #23 mode 2
2024/02/22-03:54:17.466120 65 EVENT_LOG_v1 {"time_micros": 1708574057466086, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 27, "file_size": 1244, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 303, "index_size": 24, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 308, "raw_average_key_size": 44, "raw_value_size": 55, "raw_average_value_size": 7, "num_data_blocks": 1, "num_entries": 7, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708574057, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "be375e1e-b4c8-47d2-9072-ae2e62899d79", "db_session_id": "9IIKCS8A12KB3I8RI0SO", "orig_file_number": 27}}
2024/02/22-03:54:17.466281 65 [db/version_set.cc:4409] Creating manifest 28
2024/02/22-03:54:17.467685 65 EVENT_LOG_v1 {"time_micros": 1708574057467682, "job": 1, "event": "recovery_finished"}
2024/02/22-03:54:17.470255 65 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000023.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-03:54:17.472937 65 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000017.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-03:54:17.472995 65 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f9ae4455000
2024/02/22-03:54:17.473068 65 DB pointer 0x7f9ae443b000
2024/02/22-03:54:17.473348 78 (Original Log Time 2024/02/22-03:54:17.473265) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/22-03:54:17.473709 98 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-03:54:17.473729 98 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.5 total, 0.5 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    4.02 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.017       0      0       0.0       0.0
 Sum      3/0    4.02 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.017       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.017       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.02              0.00         1    0.017       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.5 total, 0.5 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9ae4407010#8 capacity: 4.00 GB collections: 1 last_copies: 0 last_secs: 9.6e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
