2024/02/26-01:26:53.593301 76 RocksDB version: 6.29.5
2024/02/26-01:26:53.593427 76 Git sha 0
2024/02/26-01:26:53.593429 76 Compile date 2023-11-07 23:58:42
2024/02/26-01:26:53.594061 76 DB SUMMARY
2024/02/26-01:26:53.594064 76 DB Session ID:  FFQ0QZIVCWC2KSNV4YS5
2024/02/26-01:26:53.594172 76 CURRENT file:  CURRENT
2024/02/26-01:26:53.594174 76 IDENTITY file:  IDENTITY
2024/02/26-01:26:53.594182 76 MANIFEST file:  MANIFEST-000070 size: 287 Bytes
2024/02/26-01:26:53.594185 76 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 2, files: 000065.sst 000069.sst 
2024/02/26-01:26:53.594188 76 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000071.log size: 17675 ; 
2024/02/26-01:26:53.594191 76                         Options.error_if_exists: 0
2024/02/26-01:26:53.594193 76                       Options.create_if_missing: 1
2024/02/26-01:26:53.594194 76                         Options.paranoid_checks: 1
2024/02/26-01:26:53.594196 76             Options.flush_verify_memtable_count: 1
2024/02/26-01:26:53.594197 76                               Options.track_and_verify_wals_in_manifest: 0
2024/02/26-01:26:53.594198 76                                     Options.env: 0x7f92389d52c0
2024/02/26-01:26:53.594200 76                                      Options.fs: PosixFileSystem
2024/02/26-01:26:53.594201 76                                Options.info_log: 0x7f9066c50050
2024/02/26-01:26:53.594203 76                Options.max_file_opening_threads: 16
2024/02/26-01:26:53.594204 76                              Options.statistics: (nil)
2024/02/26-01:26:53.594206 76                               Options.use_fsync: 0
2024/02/26-01:26:53.594207 76                       Options.max_log_file_size: 0
2024/02/26-01:26:53.594209 76                  Options.max_manifest_file_size: 1073741824
2024/02/26-01:26:53.594210 76                   Options.log_file_time_to_roll: 0
2024/02/26-01:26:53.594211 76                       Options.keep_log_file_num: 1000
2024/02/26-01:26:53.594213 76                    Options.recycle_log_file_num: 0
2024/02/26-01:26:53.594214 76                         Options.allow_fallocate: 1
2024/02/26-01:26:53.594215 76                        Options.allow_mmap_reads: 0
2024/02/26-01:26:53.594216 76                       Options.allow_mmap_writes: 0
2024/02/26-01:26:53.594218 76                        Options.use_direct_reads: 0
2024/02/26-01:26:53.594219 76                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/26-01:26:53.594220 76          Options.create_missing_column_families: 0
2024/02/26-01:26:53.594221 76                              Options.db_log_dir: 
2024/02/26-01:26:53.594222 76                                 Options.wal_dir: 
2024/02/26-01:26:53.594224 76                Options.table_cache_numshardbits: 6
2024/02/26-01:26:53.594225 76                         Options.WAL_ttl_seconds: 0
2024/02/26-01:26:53.594226 76                       Options.WAL_size_limit_MB: 0
2024/02/26-01:26:53.594227 76                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/26-01:26:53.594228 76             Options.manifest_preallocation_size: 4194304
2024/02/26-01:26:53.594230 76                     Options.is_fd_close_on_exec: 1
2024/02/26-01:26:53.594231 76                   Options.advise_random_on_open: 1
2024/02/26-01:26:53.594232 76                   Options.experimental_mempurge_threshold: 0.000000
2024/02/26-01:26:53.594236 76                    Options.db_write_buffer_size: 0
2024/02/26-01:26:53.594238 76                    Options.write_buffer_manager: 0x7f9066c040a0
2024/02/26-01:26:53.594239 76         Options.access_hint_on_compaction_start: 1
2024/02/26-01:26:53.594240 76  Options.new_table_reader_for_compaction_inputs: 0
2024/02/26-01:26:53.594241 76           Options.random_access_max_buffer_size: 1048576
2024/02/26-01:26:53.594242 76                      Options.use_adaptive_mutex: 0
2024/02/26-01:26:53.594244 76                            Options.rate_limiter: (nil)
2024/02/26-01:26:53.594247 76     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/26-01:26:53.594248 76                       Options.wal_recovery_mode: 2
2024/02/26-01:26:53.594268 76                  Options.enable_thread_tracking: 0
2024/02/26-01:26:53.594269 76                  Options.enable_pipelined_write: 0
2024/02/26-01:26:53.594270 76                  Options.unordered_write: 0
2024/02/26-01:26:53.594271 76         Options.allow_concurrent_memtable_write: 1
2024/02/26-01:26:53.594272 76      Options.enable_write_thread_adaptive_yield: 1
2024/02/26-01:26:53.594274 76             Options.write_thread_max_yield_usec: 100
2024/02/26-01:26:53.594275 76            Options.write_thread_slow_yield_usec: 3
2024/02/26-01:26:53.594276 76                               Options.row_cache: None
2024/02/26-01:26:53.594277 76                              Options.wal_filter: None
2024/02/26-01:26:53.594279 76             Options.avoid_flush_during_recovery: 0
2024/02/26-01:26:53.594280 76             Options.allow_ingest_behind: 0
2024/02/26-01:26:53.594281 76             Options.preserve_deletes: 0
2024/02/26-01:26:53.594282 76             Options.two_write_queues: 0
2024/02/26-01:26:53.594283 76             Options.manual_wal_flush: 0
2024/02/26-01:26:53.594284 76             Options.atomic_flush: 0
2024/02/26-01:26:53.594286 76             Options.avoid_unnecessary_blocking_io: 0
2024/02/26-01:26:53.594287 76                 Options.persist_stats_to_disk: 0
2024/02/26-01:26:53.594288 76                 Options.write_dbid_to_manifest: 0
2024/02/26-01:26:53.594289 76                 Options.log_readahead_size: 0
2024/02/26-01:26:53.594290 76                 Options.file_checksum_gen_factory: Unknown
2024/02/26-01:26:53.594292 76                 Options.best_efforts_recovery: 0
2024/02/26-01:26:53.594293 76                Options.max_bgerror_resume_count: 2147483647
2024/02/26-01:26:53.594294 76            Options.bgerror_resume_retry_interval: 1000000
2024/02/26-01:26:53.594295 76             Options.allow_data_in_errors: 0
2024/02/26-01:26:53.594297 76             Options.db_host_id: __hostname__
2024/02/26-01:26:53.594299 76             Options.max_background_jobs: 4
2024/02/26-01:26:53.594300 76             Options.max_background_compactions: -1
2024/02/26-01:26:53.594301 76             Options.max_subcompactions: 1
2024/02/26-01:26:53.594302 76             Options.avoid_flush_during_shutdown: 0
2024/02/26-01:26:53.594304 76           Options.writable_file_max_buffer_size: 1048576
2024/02/26-01:26:53.594305 76             Options.delayed_write_rate : 16777216
2024/02/26-01:26:53.594306 76             Options.max_total_wal_size: 0
2024/02/26-01:26:53.594307 76             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/26-01:26:53.594308 76                   Options.stats_dump_period_sec: 600
2024/02/26-01:26:53.594310 76                 Options.stats_persist_period_sec: 600
2024/02/26-01:26:53.594311 76                 Options.stats_history_buffer_size: 1048576
2024/02/26-01:26:53.594312 76                          Options.max_open_files: -1
2024/02/26-01:26:53.594313 76                          Options.bytes_per_sync: 0
2024/02/26-01:26:53.594314 76                      Options.wal_bytes_per_sync: 0
2024/02/26-01:26:53.594316 76                   Options.strict_bytes_per_sync: 0
2024/02/26-01:26:53.594317 76       Options.compaction_readahead_size: 0
2024/02/26-01:26:53.594318 76                  Options.max_background_flushes: 1
2024/02/26-01:26:53.594319 76 Compression algorithms supported:
2024/02/26-01:26:53.594321 76 	kZSTDNotFinalCompression supported: 1
2024/02/26-01:26:53.594323 76 	kZSTD supported: 1
2024/02/26-01:26:53.594324 76 	kXpressCompression supported: 0
2024/02/26-01:26:53.594325 76 	kLZ4HCCompression supported: 0
2024/02/26-01:26:53.594327 76 	kLZ4Compression supported: 0
2024/02/26-01:26:53.594328 76 	kBZip2Compression supported: 0
2024/02/26-01:26:53.594329 76 	kZlibCompression supported: 0
2024/02/26-01:26:53.594331 76 	kSnappyCompression supported: 0
2024/02/26-01:26:53.594334 76 Fast CRC32 supported: Not supported on x86
2024/02/26-01:26:53.595104 76 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000070
2024/02/26-01:26:53.597043 76 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/26-01:26:53.597051 76               Options.comparator: leveldb.BytewiseComparator
2024/02/26-01:26:53.597054 76           Options.merge_operator: None
2024/02/26-01:26:53.597057 76        Options.compaction_filter: None
2024/02/26-01:26:53.597059 76        Options.compaction_filter_factory: None
2024/02/26-01:26:53.597061 76  Options.sst_partitioner_factory: None
2024/02/26-01:26:53.597063 76         Options.memtable_factory: SkipListFactory
2024/02/26-01:26:53.597065 76            Options.table_factory: BlockBasedTable
2024/02/26-01:26:53.597123 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f9066c160c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f9066c04010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/26-01:26:53.597142 76        Options.write_buffer_size: 67108864
2024/02/26-01:26:53.597145 76  Options.max_write_buffer_number: 2
2024/02/26-01:26:53.597150 76        Options.compression[0]: NoCompression
2024/02/26-01:26:53.597152 76        Options.compression[1]: NoCompression
2024/02/26-01:26:53.597155 76        Options.compression[2]: ZSTD
2024/02/26-01:26:53.597157 76        Options.compression[3]: ZSTD
2024/02/26-01:26:53.597159 76        Options.compression[4]: ZSTD
2024/02/26-01:26:53.597161 76                  Options.bottommost_compression: Disabled
2024/02/26-01:26:53.597163 76       Options.prefix_extractor: nullptr
2024/02/26-01:26:53.597165 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/26-01:26:53.597167 76             Options.num_levels: 5
2024/02/26-01:26:53.597169 76        Options.min_write_buffer_number_to_merge: 1
2024/02/26-01:26:53.597171 76     Options.max_write_buffer_number_to_maintain: 0
2024/02/26-01:26:53.597174 76     Options.max_write_buffer_size_to_maintain: 0
2024/02/26-01:26:53.597176 76            Options.bottommost_compression_opts.window_bits: -14
2024/02/26-01:26:53.597178 76                  Options.bottommost_compression_opts.level: 32767
2024/02/26-01:26:53.597181 76               Options.bottommost_compression_opts.strategy: 0
2024/02/26-01:26:53.597183 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/26-01:26:53.597187 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/26-01:26:53.597189 76         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/26-01:26:53.597192 76                  Options.bottommost_compression_opts.enabled: false
2024/02/26-01:26:53.597194 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/26-01:26:53.597196 76            Options.compression_opts.window_bits: -14
2024/02/26-01:26:53.597199 76                  Options.compression_opts.level: 32767
2024/02/26-01:26:53.597201 76               Options.compression_opts.strategy: 0
2024/02/26-01:26:53.597202 76         Options.compression_opts.max_dict_bytes: 0
2024/02/26-01:26:53.597204 76         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/26-01:26:53.597244 76         Options.compression_opts.parallel_threads: 1
2024/02/26-01:26:53.597247 76                  Options.compression_opts.enabled: false
2024/02/26-01:26:53.597249 76         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/26-01:26:53.597251 76      Options.level0_file_num_compaction_trigger: 4
2024/02/26-01:26:53.597253 76          Options.level0_slowdown_writes_trigger: 20
2024/02/26-01:26:53.597255 76              Options.level0_stop_writes_trigger: 36
2024/02/26-01:26:53.597257 76                   Options.target_file_size_base: 67108864
2024/02/26-01:26:53.597259 76             Options.target_file_size_multiplier: 2
2024/02/26-01:26:53.597261 76                Options.max_bytes_for_level_base: 268435456
2024/02/26-01:26:53.597263 76 Options.level_compaction_dynamic_level_bytes: 0
2024/02/26-01:26:53.597265 76          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/26-01:26:53.597269 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/26-01:26:53.597272 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/26-01:26:53.597274 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/26-01:26:53.597276 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/26-01:26:53.597278 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/26-01:26:53.597280 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/26-01:26:53.597282 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/26-01:26:53.597284 76       Options.max_sequential_skip_in_iterations: 8
2024/02/26-01:26:53.597286 76                    Options.max_compaction_bytes: 1677721600
2024/02/26-01:26:53.597288 76                        Options.arena_block_size: 1048576
2024/02/26-01:26:53.597290 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/26-01:26:53.597292 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/26-01:26:53.597294 76       Options.rate_limit_delay_max_milliseconds: 100
2024/02/26-01:26:53.597296 76                Options.disable_auto_compactions: 0
2024/02/26-01:26:53.597300 76                        Options.compaction_style: kCompactionStyleLevel
2024/02/26-01:26:53.597303 76                          Options.compaction_pri: kMinOverlappingRatio
2024/02/26-01:26:53.597305 76 Options.compaction_options_universal.size_ratio: 1
2024/02/26-01:26:53.597307 76 Options.compaction_options_universal.min_merge_width: 2
2024/02/26-01:26:53.597309 76 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/26-01:26:53.597311 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/26-01:26:53.597313 76 Options.compaction_options_universal.compression_size_percent: -1
2024/02/26-01:26:53.597315 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/26-01:26:53.597318 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/26-01:26:53.597320 76 Options.compaction_options_fifo.allow_compaction: 0
2024/02/26-01:26:53.597326 76                   Options.table_properties_collectors: 
2024/02/26-01:26:53.597328 76                   Options.inplace_update_support: 0
2024/02/26-01:26:53.597330 76                 Options.inplace_update_num_locks: 10000
2024/02/26-01:26:53.597332 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/26-01:26:53.597335 76               Options.memtable_whole_key_filtering: 0
2024/02/26-01:26:53.597337 76   Options.memtable_huge_page_size: 0
2024/02/26-01:26:53.597339 76                           Options.bloom_locality: 0
2024/02/26-01:26:53.597341 76                    Options.max_successive_merges: 0
2024/02/26-01:26:53.597343 76                Options.optimize_filters_for_hits: 0
2024/02/26-01:26:53.597345 76                Options.paranoid_file_checks: 0
2024/02/26-01:26:53.597347 76                Options.force_consistency_checks: 1
2024/02/26-01:26:53.597349 76                Options.report_bg_io_stats: 0
2024/02/26-01:26:53.597351 76                               Options.ttl: 2592000
2024/02/26-01:26:53.597375 76          Options.periodic_compaction_seconds: 0
2024/02/26-01:26:53.597377 76                       Options.enable_blob_files: false
2024/02/26-01:26:53.597380 76                           Options.min_blob_size: 0
2024/02/26-01:26:53.597382 76                          Options.blob_file_size: 268435456
2024/02/26-01:26:53.597384 76                   Options.blob_compression_type: NoCompression
2024/02/26-01:26:53.597386 76          Options.enable_blob_garbage_collection: false
2024/02/26-01:26:53.597388 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/26-01:26:53.597391 76 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/26-01:26:53.597393 76          Options.blob_compaction_readahead_size: 0
2024/02/26-01:26:53.604099 76 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000070 succeeded,manifest_file_number is 70, next_file_number is 72, last_sequence is 426620, log_number is 63,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2024/02/26-01:26:53.604110 76 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 63
2024/02/26-01:26:53.605102 76 [db/version_set.cc:4409] Creating manifest 74
2024/02/26-01:26:53.608429 76 EVENT_LOG_v1 {"time_micros": 1708910813608411, "job": 1, "event": "recovery_started", "wal_files": [71]}
2024/02/26-01:26:53.608439 76 [db/db_impl/db_impl_open.cc:888] Recovering log #71 mode 2
2024/02/26-01:26:53.610418 76 EVENT_LOG_v1 {"time_micros": 1708910813610356, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 75, "file_size": 1050, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 112, "index_size": 23, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 100, "raw_average_key_size": 33, "raw_value_size": 24, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 3, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708910813, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "be375e1e-b4c8-47d2-9072-ae2e62899d79", "db_session_id": "FFQ0QZIVCWC2KSNV4YS5", "orig_file_number": 75}}
2024/02/26-01:26:53.610517 76 [db/version_set.cc:4409] Creating manifest 76
2024/02/26-01:26:53.612075 76 EVENT_LOG_v1 {"time_micros": 1708910813612070, "job": 1, "event": "recovery_finished"}
2024/02/26-01:26:53.617192 76 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000071.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/26-01:26:53.617616 76 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f9066c55000
2024/02/26-01:26:53.617691 76 DB pointer 0x7f9066c3b000
2024/02/26-01:26:53.617879 82 (Original Log Time 2024/02/26-01:26:53.617833) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/26-01:26:53.618136 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-01:26:53.618158 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 1 last_copies: 0 last_secs: 7.2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-01:36:53.618546 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-01:36:53.618664 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 600.0 total, 600.0 interval
Cumulative writes: 6001 writes, 5999 keys, 5745 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 6001 writes, 0 syncs, 6001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6001 writes, 5999 keys, 5745 commit groups, 1.0 writes per commit group, ingest: 0.33 MB, 0.00 MB/s
Interval WAL: 6001 writes, 0 syncs, 6001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 2 last_copies: 0 last_secs: 0.000127 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-01:46:53.619165 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-01:46:53.619293 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1200.0 total, 600.0 interval
Cumulative writes: 12K writes, 11K keys, 11K commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 12K writes, 0 syncs, 12001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5777 commit groups, 1.0 writes per commit group, ingest: 0.33 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1200.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 3 last_copies: 0 last_secs: 0.000114 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-01:56:53.619685 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-01:56:53.619797 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1800.0 total, 600.0 interval
Cumulative writes: 18K writes, 17K keys, 16K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 18K writes, 0 syncs, 18001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5386 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1800.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 4 last_copies: 0 last_secs: 7.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-02:06:53.620312 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-02:06:53.620454 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2400.0 total, 600.0 interval
Cumulative writes: 24K writes, 23K keys, 22K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 24K writes, 0 syncs, 24001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5634 commit groups, 1.1 writes per commit group, ingest: 0.33 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2400.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 5 last_copies: 0 last_secs: 0.000123 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-02:16:53.620866 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-02:16:53.620986 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3000.0 total, 600.0 interval
Cumulative writes: 30K writes, 29K keys, 27K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 30K writes, 0 syncs, 30001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5456 commit groups, 1.1 writes per commit group, ingest: 0.33 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3000.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 6 last_copies: 0 last_secs: 0.000109 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-02:26:53.621421 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-02:26:53.621559 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3600.0 total, 600.0 interval
Cumulative writes: 36K writes, 35K keys, 33K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 36K writes, 0 syncs, 36001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5355 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 7 last_copies: 0 last_secs: 9.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-02:36:53.621990 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-02:36:53.622107 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4200.0 total, 600.0 interval
Cumulative writes: 42K writes, 41K keys, 38K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 42K writes, 0 syncs, 42001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5442 commit groups, 1.1 writes per commit group, ingest: 0.33 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4200.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 8 last_copies: 0 last_secs: 0.000107 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-02:46:53.622539 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-02:46:53.622680 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4800.0 total, 600.0 interval
Cumulative writes: 48K writes, 47K keys, 44K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 48K writes, 0 syncs, 48001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5277 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4800.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 9 last_copies: 0 last_secs: 0.000104 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-02:56:53.623139 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-02:56:53.623284 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5400.0 total, 600.0 interval
Cumulative writes: 54K writes, 53K keys, 49K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 54K writes, 0 syncs, 54001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5233 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5400.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 10 last_copies: 0 last_secs: 9.6e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-03:06:53.623778 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-03:06:53.623920 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6000.0 total, 600.0 interval
Cumulative writes: 60K writes, 59K keys, 54K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 60K writes, 0 syncs, 60001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5285 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6000.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 11 last_copies: 0 last_secs: 0.000123 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-03:16:53.624341 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-03:16:53.624479 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6600.0 total, 600.0 interval
Cumulative writes: 66K writes, 65K keys, 60K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 66K writes, 0 syncs, 66001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5692 commit groups, 1.1 writes per commit group, ingest: 0.33 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 12 last_copies: 0 last_secs: 0.000103 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-03:26:53.624957 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-03:26:53.625095 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7200.0 total, 600.0 interval
Cumulative writes: 72K writes, 71K keys, 65K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 72K writes, 0 syncs, 72001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5363 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7200.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 13 last_copies: 0 last_secs: 0.000102 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-03:36:53.625583 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-03:36:53.625722 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7800.0 total, 600.0 interval
Cumulative writes: 78K writes, 77K keys, 71K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 78K writes, 0 syncs, 78001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5433 commit groups, 1.1 writes per commit group, ingest: 0.33 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7800.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 14 last_copies: 0 last_secs: 0.000106 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-03:46:53.626176 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-03:46:53.626305 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 8400.0 total, 600.0 interval
Cumulative writes: 84K writes, 83K keys, 76K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 84K writes, 0 syncs, 84001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5411 commit groups, 1.1 writes per commit group, ingest: 0.33 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8400.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 15 last_copies: 0 last_secs: 9.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-03:56:53.626717 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-03:56:53.626848 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9000.0 total, 600.0 interval
Cumulative writes: 90K writes, 89K keys, 81K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 90K writes, 0 syncs, 90001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5334 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9000.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 16 last_copies: 0 last_secs: 9e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-04:06:53.627308 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-04:06:53.627442 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9600.0 total, 600.0 interval
Cumulative writes: 96K writes, 95K keys, 87K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 96K writes, 0 syncs, 96001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5402 commit groups, 1.1 writes per commit group, ingest: 0.33 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 17 last_copies: 0 last_secs: 0.000116 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-04:16:53.627981 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-04:16:53.628103 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10200.0 total, 600.0 interval
Cumulative writes: 102K writes, 101K keys, 92K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 102K writes, 0 syncs, 102001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5476 commit groups, 1.1 writes per commit group, ingest: 0.33 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10200.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 18 last_copies: 0 last_secs: 0.000112 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-04:26:53.628524 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-04:26:53.628645 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10800.0 total, 600.0 interval
Cumulative writes: 108K writes, 107K keys, 98K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 108K writes, 0 syncs, 108001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5514 commit groups, 1.1 writes per commit group, ingest: 0.33 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10800.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 19 last_copies: 0 last_secs: 0.000111 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-04:36:53.629079 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-04:36:53.629207 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 11400.0 total, 600.0 interval
Cumulative writes: 114K writes, 113K keys, 103K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 114K writes, 0 syncs, 114001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5277 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11400.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 20 last_copies: 0 last_secs: 9.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-04:46:53.629696 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-04:46:53.629821 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12000.0 total, 600.0 interval
Cumulative writes: 120K writes, 119K keys, 108K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 120K writes, 0 syncs, 120001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5289 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12000.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 21 last_copies: 0 last_secs: 9.6e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-04:56:53.630227 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-04:56:53.630370 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12600.0 total, 600.0 interval
Cumulative writes: 126K writes, 125K keys, 113K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 126K writes, 0 syncs, 126001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5194 commit groups, 1.2 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 22 last_copies: 0 last_secs: 8.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-05:06:53.630828 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-05:06:53.630941 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13200.0 total, 600.0 interval
Cumulative writes: 132K writes, 131K keys, 119K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 132K writes, 0 syncs, 132001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5426 commit groups, 1.1 writes per commit group, ingest: 0.33 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13200.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 23 last_copies: 0 last_secs: 0.000101 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-05:16:53.631342 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-05:16:53.631456 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13800.0 total, 600.0 interval
Cumulative writes: 138K writes, 137K keys, 124K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 138K writes, 0 syncs, 138001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5342 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13800.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 24 last_copies: 0 last_secs: 9.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-05:26:53.631955 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-05:26:53.632099 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 14400.0 total, 600.0 interval
Cumulative writes: 144K writes, 143K keys, 130K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 144K writes, 0 syncs, 144001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5392 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 14400.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 25 last_copies: 0 last_secs: 0.000112 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-05:36:53.632517 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-05:36:53.632639 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15000.0 total, 600.0 interval
Cumulative writes: 150K writes, 149K keys, 135K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 150K writes, 0 syncs, 150001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5252 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15000.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 26 last_copies: 0 last_secs: 0.000104 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-05:46:53.633028 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-05:46:53.633157 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15600.0 total, 600.0 interval
Cumulative writes: 156K writes, 155K keys, 140K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 156K writes, 0 syncs, 156001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5355 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 27 last_copies: 0 last_secs: 8.6e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-05:56:53.633600 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-05:56:53.633763 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16200.0 total, 600.0 interval
Cumulative writes: 162K writes, 161K keys, 145K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 162K writes, 0 syncs, 162001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5253 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16200.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 28 last_copies: 0 last_secs: 0.000103 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-06:06:53.634273 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-06:06:53.634418 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16800.0 total, 600.0 interval
Cumulative writes: 168K writes, 167K keys, 151K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 168K writes, 0 syncs, 168001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5152 commit groups, 1.2 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16800.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 29 last_copies: 0 last_secs: 0.000121 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-06:16:53.634889 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-06:16:53.635035 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 17400.0 total, 600.0 interval
Cumulative writes: 174K writes, 173K keys, 156K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 174K writes, 0 syncs, 174001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5263 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 17400.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 30 last_copies: 0 last_secs: 0.000106 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-06:26:53.635487 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-06:26:53.635623 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18000.0 total, 600.0 interval
Cumulative writes: 180K writes, 179K keys, 161K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 180K writes, 0 syncs, 180001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5216 commit groups, 1.2 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18000.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 31 last_copies: 0 last_secs: 9.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-06:36:53.636018 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-06:36:53.636141 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18600.0 total, 600.0 interval
Cumulative writes: 186K writes, 185K keys, 166K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 186K writes, 0 syncs, 186001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5162 commit groups, 1.2 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 32 last_copies: 0 last_secs: 9.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-06:46:53.636664 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-06:46:53.636789 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19200.0 total, 600.0 interval
Cumulative writes: 192K writes, 191K keys, 171K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 192K writes, 0 syncs, 192001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5128 commit groups, 1.2 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19200.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 33 last_copies: 0 last_secs: 0.000105 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-06:56:53.637255 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-06:56:53.637395 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19800.0 total, 600.0 interval
Cumulative writes: 198K writes, 197K keys, 177K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 198K writes, 0 syncs, 198001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5328 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19800.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 34 last_copies: 0 last_secs: 0.000105 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2024/02/26-07:06:53.637851 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/26-07:06:53.638001 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 20400.0 total, 600.0 interval
Cumulative writes: 204K writes, 203K keys, 182K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 204K writes, 0 syncs, 204001.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5305 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20400.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9066c04010#8 capacity: 4.00 GB collections: 35 last_copies: 0 last_secs: 0.000122 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,1.28 MB,0.0312768%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
