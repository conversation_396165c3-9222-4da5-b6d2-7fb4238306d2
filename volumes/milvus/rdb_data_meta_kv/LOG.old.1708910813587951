2024/02/22-06:16:45.873100 69 RocksDB version: 6.29.5
2024/02/22-06:16:45.873253 69 Git sha 0
2024/02/22-06:16:45.873257 69 Compile date 2023-11-07 23:58:42
2024/02/22-06:16:45.873274 69 DB SUMMARY
2024/02/22-06:16:45.873277 69 DB Session ID:  UGRLOJ85HPN78EC2Q1X7
2024/02/22-06:16:45.873409 69 CURRENT file:  CURRENT
2024/02/22-06:16:45.873412 69 IDENTITY file:  IDENTITY
2024/02/22-06:16:45.873426 69 MANIFEST file:  MANIFEST-000061 size: 708 Bytes
2024/02/22-06:16:45.873430 69 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 1, files: 000065.sst 
2024/02/22-06:16:45.873435 69 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000062.log size: 17377 ; 
2024/02/22-06:16:45.873441 69                         Options.error_if_exists: 0
2024/02/22-06:16:45.873444 69                       Options.create_if_missing: 1
2024/02/22-06:16:45.873446 69                         Options.paranoid_checks: 1
2024/02/22-06:16:45.873449 69             Options.flush_verify_memtable_count: 1
2024/02/22-06:16:45.873451 69                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-06:16:45.873453 69                                     Options.env: 0x7f20f46b12c0
2024/02/22-06:16:45.873456 69                                      Options.fs: PosixFileSystem
2024/02/22-06:16:45.873459 69                                Options.info_log: 0x7f1f28e50050
2024/02/22-06:16:45.873461 69                Options.max_file_opening_threads: 16
2024/02/22-06:16:45.873463 69                              Options.statistics: (nil)
2024/02/22-06:16:45.873466 69                               Options.use_fsync: 0
2024/02/22-06:16:45.873468 69                       Options.max_log_file_size: 0
2024/02/22-06:16:45.873471 69                  Options.max_manifest_file_size: 1073741824
2024/02/22-06:16:45.873473 69                   Options.log_file_time_to_roll: 0
2024/02/22-06:16:45.873475 69                       Options.keep_log_file_num: 1000
2024/02/22-06:16:45.873477 69                    Options.recycle_log_file_num: 0
2024/02/22-06:16:45.873480 69                         Options.allow_fallocate: 1
2024/02/22-06:16:45.873482 69                        Options.allow_mmap_reads: 0
2024/02/22-06:16:45.873484 69                       Options.allow_mmap_writes: 0
2024/02/22-06:16:45.873486 69                        Options.use_direct_reads: 0
2024/02/22-06:16:45.873488 69                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-06:16:45.873490 69          Options.create_missing_column_families: 0
2024/02/22-06:16:45.873492 69                              Options.db_log_dir: 
2024/02/22-06:16:45.873494 69                                 Options.wal_dir: 
2024/02/22-06:16:45.873496 69                Options.table_cache_numshardbits: 6
2024/02/22-06:16:45.873498 69                         Options.WAL_ttl_seconds: 0
2024/02/22-06:16:45.873500 69                       Options.WAL_size_limit_MB: 0
2024/02/22-06:16:45.873502 69                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-06:16:45.873505 69             Options.manifest_preallocation_size: 4194304
2024/02/22-06:16:45.873507 69                     Options.is_fd_close_on_exec: 1
2024/02/22-06:16:45.873509 69                   Options.advise_random_on_open: 1
2024/02/22-06:16:45.873511 69                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-06:16:45.873520 69                    Options.db_write_buffer_size: 0
2024/02/22-06:16:45.873522 69                    Options.write_buffer_manager: 0x7f1f28e070a0
2024/02/22-06:16:45.873524 69         Options.access_hint_on_compaction_start: 1
2024/02/22-06:16:45.873526 69  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-06:16:45.873528 69           Options.random_access_max_buffer_size: 1048576
2024/02/22-06:16:45.873530 69                      Options.use_adaptive_mutex: 0
2024/02/22-06:16:45.873532 69                            Options.rate_limiter: (nil)
2024/02/22-06:16:45.873538 69     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-06:16:45.873540 69                       Options.wal_recovery_mode: 2
2024/02/22-06:16:45.873570 69                  Options.enable_thread_tracking: 0
2024/02/22-06:16:45.873573 69                  Options.enable_pipelined_write: 0
2024/02/22-06:16:45.873575 69                  Options.unordered_write: 0
2024/02/22-06:16:45.873577 69         Options.allow_concurrent_memtable_write: 1
2024/02/22-06:16:45.873579 69      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-06:16:45.873581 69             Options.write_thread_max_yield_usec: 100
2024/02/22-06:16:45.873583 69            Options.write_thread_slow_yield_usec: 3
2024/02/22-06:16:45.873585 69                               Options.row_cache: None
2024/02/22-06:16:45.873587 69                              Options.wal_filter: None
2024/02/22-06:16:45.873589 69             Options.avoid_flush_during_recovery: 0
2024/02/22-06:16:45.873592 69             Options.allow_ingest_behind: 0
2024/02/22-06:16:45.873594 69             Options.preserve_deletes: 0
2024/02/22-06:16:45.873596 69             Options.two_write_queues: 0
2024/02/22-06:16:45.873598 69             Options.manual_wal_flush: 0
2024/02/22-06:16:45.873600 69             Options.atomic_flush: 0
2024/02/22-06:16:45.873603 69             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-06:16:45.873605 69                 Options.persist_stats_to_disk: 0
2024/02/22-06:16:45.873607 69                 Options.write_dbid_to_manifest: 0
2024/02/22-06:16:45.873609 69                 Options.log_readahead_size: 0
2024/02/22-06:16:45.873611 69                 Options.file_checksum_gen_factory: Unknown
2024/02/22-06:16:45.873613 69                 Options.best_efforts_recovery: 0
2024/02/22-06:16:45.873616 69                Options.max_bgerror_resume_count: 2147483647
2024/02/22-06:16:45.873618 69            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-06:16:45.873620 69             Options.allow_data_in_errors: 0
2024/02/22-06:16:45.873622 69             Options.db_host_id: __hostname__
2024/02/22-06:16:45.873626 69             Options.max_background_jobs: 4
2024/02/22-06:16:45.873628 69             Options.max_background_compactions: -1
2024/02/22-06:16:45.873630 69             Options.max_subcompactions: 1
2024/02/22-06:16:45.873633 69             Options.avoid_flush_during_shutdown: 0
2024/02/22-06:16:45.873635 69           Options.writable_file_max_buffer_size: 1048576
2024/02/22-06:16:45.873637 69             Options.delayed_write_rate : 16777216
2024/02/22-06:16:45.873639 69             Options.max_total_wal_size: 0
2024/02/22-06:16:45.873641 69             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-06:16:45.873643 69                   Options.stats_dump_period_sec: 600
2024/02/22-06:16:45.873645 69                 Options.stats_persist_period_sec: 600
2024/02/22-06:16:45.873647 69                 Options.stats_history_buffer_size: 1048576
2024/02/22-06:16:45.873650 69                          Options.max_open_files: -1
2024/02/22-06:16:45.873652 69                          Options.bytes_per_sync: 0
2024/02/22-06:16:45.873654 69                      Options.wal_bytes_per_sync: 0
2024/02/22-06:16:45.873656 69                   Options.strict_bytes_per_sync: 0
2024/02/22-06:16:45.873658 69       Options.compaction_readahead_size: 0
2024/02/22-06:16:45.873661 69                  Options.max_background_flushes: 1
2024/02/22-06:16:45.873663 69 Compression algorithms supported:
2024/02/22-06:16:45.873666 69 	kZSTDNotFinalCompression supported: 1
2024/02/22-06:16:45.873668 69 	kZSTD supported: 1
2024/02/22-06:16:45.873671 69 	kXpressCompression supported: 0
2024/02/22-06:16:45.873673 69 	kLZ4HCCompression supported: 0
2024/02/22-06:16:45.873675 69 	kLZ4Compression supported: 0
2024/02/22-06:16:45.873677 69 	kBZip2Compression supported: 0
2024/02/22-06:16:45.873679 69 	kZlibCompression supported: 0
2024/02/22-06:16:45.873681 69 	kSnappyCompression supported: 0
2024/02/22-06:16:45.873687 69 Fast CRC32 supported: Not supported on x86
2024/02/22-06:16:45.873971 69 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000061
2024/02/22-06:16:45.874623 69 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-06:16:45.874629 69               Options.comparator: leveldb.BytewiseComparator
2024/02/22-06:16:45.874632 69           Options.merge_operator: None
2024/02/22-06:16:45.874634 69        Options.compaction_filter: None
2024/02/22-06:16:45.874637 69        Options.compaction_filter_factory: None
2024/02/22-06:16:45.874639 69  Options.sst_partitioner_factory: None
2024/02/22-06:16:45.874641 69         Options.memtable_factory: SkipListFactory
2024/02/22-06:16:45.874643 69            Options.table_factory: BlockBasedTable
2024/02/22-06:16:45.874696 69            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1f28e00120)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f1f28e07010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-06:16:45.874706 69        Options.write_buffer_size: 67108864
2024/02/22-06:16:45.874708 69  Options.max_write_buffer_number: 2
2024/02/22-06:16:45.874714 69        Options.compression[0]: NoCompression
2024/02/22-06:16:45.874716 69        Options.compression[1]: NoCompression
2024/02/22-06:16:45.874719 69        Options.compression[2]: ZSTD
2024/02/22-06:16:45.874721 69        Options.compression[3]: ZSTD
2024/02/22-06:16:45.874723 69        Options.compression[4]: ZSTD
2024/02/22-06:16:45.874725 69                  Options.bottommost_compression: Disabled
2024/02/22-06:16:45.874727 69       Options.prefix_extractor: nullptr
2024/02/22-06:16:45.874729 69   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-06:16:45.874731 69             Options.num_levels: 5
2024/02/22-06:16:45.874733 69        Options.min_write_buffer_number_to_merge: 1
2024/02/22-06:16:45.874735 69     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-06:16:45.874737 69     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-06:16:45.874740 69            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-06:16:45.874742 69                  Options.bottommost_compression_opts.level: 32767
2024/02/22-06:16:45.874745 69               Options.bottommost_compression_opts.strategy: 0
2024/02/22-06:16:45.874747 69         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-06:16:45.874749 69         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:16:45.874752 69         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-06:16:45.874754 69                  Options.bottommost_compression_opts.enabled: false
2024/02/22-06:16:45.874756 69         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:16:45.874758 69            Options.compression_opts.window_bits: -14
2024/02/22-06:16:45.874761 69                  Options.compression_opts.level: 32767
2024/02/22-06:16:45.874763 69               Options.compression_opts.strategy: 0
2024/02/22-06:16:45.874765 69         Options.compression_opts.max_dict_bytes: 0
2024/02/22-06:16:45.874767 69         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:16:45.874769 69         Options.compression_opts.parallel_threads: 1
2024/02/22-06:16:45.874798 69                  Options.compression_opts.enabled: false
2024/02/22-06:16:45.874800 69         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:16:45.874802 69      Options.level0_file_num_compaction_trigger: 4
2024/02/22-06:16:45.874804 69          Options.level0_slowdown_writes_trigger: 20
2024/02/22-06:16:45.874806 69              Options.level0_stop_writes_trigger: 36
2024/02/22-06:16:45.874808 69                   Options.target_file_size_base: 67108864
2024/02/22-06:16:45.874811 69             Options.target_file_size_multiplier: 2
2024/02/22-06:16:45.874813 69                Options.max_bytes_for_level_base: 268435456
2024/02/22-06:16:45.874815 69 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-06:16:45.874817 69          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-06:16:45.874821 69 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-06:16:45.874824 69 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-06:16:45.874826 69 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-06:16:45.874828 69 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-06:16:45.874835 69 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-06:16:45.874837 69 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-06:16:45.874839 69 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-06:16:45.874841 69       Options.max_sequential_skip_in_iterations: 8
2024/02/22-06:16:45.874846 69                    Options.max_compaction_bytes: 1677721600
2024/02/22-06:16:45.874849 69                        Options.arena_block_size: 1048576
2024/02/22-06:16:45.874851 69   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-06:16:45.874911 69   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-06:16:45.874914 69       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-06:16:45.874916 69                Options.disable_auto_compactions: 0
2024/02/22-06:16:45.874919 69                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-06:16:45.874922 69                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-06:16:45.874924 69 Options.compaction_options_universal.size_ratio: 1
2024/02/22-06:16:45.874927 69 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-06:16:45.874929 69 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-06:16:45.874931 69 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-06:16:45.874933 69 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-06:16:45.874936 69 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-06:16:45.874938 69 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-06:16:45.874940 69 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-06:16:45.874946 69                   Options.table_properties_collectors: 
2024/02/22-06:16:45.874949 69                   Options.inplace_update_support: 0
2024/02/22-06:16:45.874951 69                 Options.inplace_update_num_locks: 10000
2024/02/22-06:16:45.874953 69               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-06:16:45.874956 69               Options.memtable_whole_key_filtering: 0
2024/02/22-06:16:45.874958 69   Options.memtable_huge_page_size: 0
2024/02/22-06:16:45.874960 69                           Options.bloom_locality: 0
2024/02/22-06:16:45.874962 69                    Options.max_successive_merges: 0
2024/02/22-06:16:45.874964 69                Options.optimize_filters_for_hits: 0
2024/02/22-06:16:45.874966 69                Options.paranoid_file_checks: 0
2024/02/22-06:16:45.874968 69                Options.force_consistency_checks: 1
2024/02/22-06:16:45.874970 69                Options.report_bg_io_stats: 0
2024/02/22-06:16:45.874972 69                               Options.ttl: 2592000
2024/02/22-06:16:45.874974 69          Options.periodic_compaction_seconds: 0
2024/02/22-06:16:45.875000 69                       Options.enable_blob_files: false
2024/02/22-06:16:45.875003 69                           Options.min_blob_size: 0
2024/02/22-06:16:45.875006 69                          Options.blob_file_size: 268435456
2024/02/22-06:16:45.875008 69                   Options.blob_compression_type: NoCompression
2024/02/22-06:16:45.875011 69          Options.enable_blob_garbage_collection: false
2024/02/22-06:16:45.875013 69      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-06:16:45.875015 69 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-06:16:45.875018 69          Options.blob_compaction_readahead_size: 0
2024/02/22-06:16:45.879304 69 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000061 succeeded,manifest_file_number is 61, next_file_number is 67, last_sequence is 426349, log_number is 57,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2024/02/22-06:16:45.879314 69 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 57
2024/02/22-06:16:45.879527 69 [db/version_set.cc:4409] Creating manifest 68
2024/02/22-06:16:45.881401 69 EVENT_LOG_v1 {"time_micros": 1708582605881381, "job": 1, "event": "recovery_started", "wal_files": [62]}
2024/02/22-06:16:45.881412 69 [db/db_impl/db_impl_open.cc:888] Recovering log #62 mode 2
2024/02/22-06:16:45.882980 69 EVENT_LOG_v1 {"time_micros": 1708582605882920, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 69, "file_size": 1050, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 112, "index_size": 23, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 100, "raw_average_key_size": 33, "raw_value_size": 24, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 3, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708582605, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "be375e1e-b4c8-47d2-9072-ae2e62899d79", "db_session_id": "UGRLOJ85HPN78EC2Q1X7", "orig_file_number": 69}}
2024/02/22-06:16:45.883065 69 [db/version_set.cc:4409] Creating manifest 70
2024/02/22-06:16:45.884739 69 EVENT_LOG_v1 {"time_micros": 1708582605884733, "job": 1, "event": "recovery_finished"}
2024/02/22-06:16:45.887798 69 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000062.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:16:45.887863 69 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f1f28e55000
2024/02/22-06:16:45.887961 69 DB pointer 0x7f1f28e3b000
2024/02/22-06:16:45.888049 80 (Original Log Time 2024/02/22-06:16:45.888018) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/22-06:16:45.888599 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-06:16:45.888620 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.03 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      2/0    2.93 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.07 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.07 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f1f28e07010#8 capacity: 4.00 GB collections: 1 last_copies: 0 last_secs: 0.000105 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
