2024/02/22-06:07:20.851543 78 RocksDB version: 6.29.5
2024/02/22-06:07:20.851648 78 Git sha 0
2024/02/22-06:07:20.851650 78 Compile date 2023-11-07 23:58:42
2024/02/22-06:07:20.852283 78 DB SUMMARY
2024/02/22-06:07:20.852286 78 DB Session ID:  0U3H9S1RUZCVZNWPT2PC
2024/02/22-06:07:20.852381 78 CURRENT file:  CURRENT
2024/02/22-06:07:20.852382 78 IDENTITY file:  IDENTITY
2024/02/22-06:07:20.852391 78 MANIFEST file:  MANIFEST-000043 size: 287 Bytes
2024/02/22-06:07:20.852394 78 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 2, files: 000038.sst 000042.sst 
2024/02/22-06:07:20.852398 78 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000044.log size: 15985 ; 
2024/02/22-06:07:20.852402 78                         Options.error_if_exists: 0
2024/02/22-06:07:20.852404 78                       Options.create_if_missing: 1
2024/02/22-06:07:20.852405 78                         Options.paranoid_checks: 1
2024/02/22-06:07:20.852407 78             Options.flush_verify_memtable_count: 1
2024/02/22-06:07:20.852408 78                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-06:07:20.852409 78                                     Options.env: 0x7f1ecb78c2c0
2024/02/22-06:07:20.852412 78                                      Options.fs: PosixFileSystem
2024/02/22-06:07:20.852414 78                                Options.info_log: 0x7f1cfc250050
2024/02/22-06:07:20.852416 78                Options.max_file_opening_threads: 16
2024/02/22-06:07:20.852418 78                              Options.statistics: (nil)
2024/02/22-06:07:20.852420 78                               Options.use_fsync: 0
2024/02/22-06:07:20.852422 78                       Options.max_log_file_size: 0
2024/02/22-06:07:20.852424 78                  Options.max_manifest_file_size: 1073741824
2024/02/22-06:07:20.852425 78                   Options.log_file_time_to_roll: 0
2024/02/22-06:07:20.852427 78                       Options.keep_log_file_num: 1000
2024/02/22-06:07:20.852428 78                    Options.recycle_log_file_num: 0
2024/02/22-06:07:20.852429 78                         Options.allow_fallocate: 1
2024/02/22-06:07:20.852431 78                        Options.allow_mmap_reads: 0
2024/02/22-06:07:20.852432 78                       Options.allow_mmap_writes: 0
2024/02/22-06:07:20.852433 78                        Options.use_direct_reads: 0
2024/02/22-06:07:20.852435 78                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-06:07:20.852436 78          Options.create_missing_column_families: 0
2024/02/22-06:07:20.852437 78                              Options.db_log_dir: 
2024/02/22-06:07:20.852439 78                                 Options.wal_dir: 
2024/02/22-06:07:20.852440 78                Options.table_cache_numshardbits: 6
2024/02/22-06:07:20.852441 78                         Options.WAL_ttl_seconds: 0
2024/02/22-06:07:20.852442 78                       Options.WAL_size_limit_MB: 0
2024/02/22-06:07:20.852444 78                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-06:07:20.852445 78             Options.manifest_preallocation_size: 4194304
2024/02/22-06:07:20.852446 78                     Options.is_fd_close_on_exec: 1
2024/02/22-06:07:20.852447 78                   Options.advise_random_on_open: 1
2024/02/22-06:07:20.852448 78                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-06:07:20.852451 78                    Options.db_write_buffer_size: 0
2024/02/22-06:07:20.852453 78                    Options.write_buffer_manager: 0x7f1cfc2070a0
2024/02/22-06:07:20.852454 78         Options.access_hint_on_compaction_start: 1
2024/02/22-06:07:20.852455 78  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-06:07:20.852456 78           Options.random_access_max_buffer_size: 1048576
2024/02/22-06:07:20.852458 78                      Options.use_adaptive_mutex: 0
2024/02/22-06:07:20.852459 78                            Options.rate_limiter: (nil)
2024/02/22-06:07:20.852463 78     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-06:07:20.852464 78                       Options.wal_recovery_mode: 2
2024/02/22-06:07:20.852482 78                  Options.enable_thread_tracking: 0
2024/02/22-06:07:20.852483 78                  Options.enable_pipelined_write: 0
2024/02/22-06:07:20.852484 78                  Options.unordered_write: 0
2024/02/22-06:07:20.852485 78         Options.allow_concurrent_memtable_write: 1
2024/02/22-06:07:20.852486 78      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-06:07:20.852488 78             Options.write_thread_max_yield_usec: 100
2024/02/22-06:07:20.852489 78            Options.write_thread_slow_yield_usec: 3
2024/02/22-06:07:20.852490 78                               Options.row_cache: None
2024/02/22-06:07:20.852492 78                              Options.wal_filter: None
2024/02/22-06:07:20.852493 78             Options.avoid_flush_during_recovery: 0
2024/02/22-06:07:20.852494 78             Options.allow_ingest_behind: 0
2024/02/22-06:07:20.852496 78             Options.preserve_deletes: 0
2024/02/22-06:07:20.852497 78             Options.two_write_queues: 0
2024/02/22-06:07:20.852499 78             Options.manual_wal_flush: 0
2024/02/22-06:07:20.852500 78             Options.atomic_flush: 0
2024/02/22-06:07:20.852501 78             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-06:07:20.852503 78                 Options.persist_stats_to_disk: 0
2024/02/22-06:07:20.852504 78                 Options.write_dbid_to_manifest: 0
2024/02/22-06:07:20.852506 78                 Options.log_readahead_size: 0
2024/02/22-06:07:20.852507 78                 Options.file_checksum_gen_factory: Unknown
2024/02/22-06:07:20.852508 78                 Options.best_efforts_recovery: 0
2024/02/22-06:07:20.852510 78                Options.max_bgerror_resume_count: 2147483647
2024/02/22-06:07:20.852511 78            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-06:07:20.852512 78             Options.allow_data_in_errors: 0
2024/02/22-06:07:20.852514 78             Options.db_host_id: __hostname__
2024/02/22-06:07:20.852516 78             Options.max_background_jobs: 4
2024/02/22-06:07:20.852517 78             Options.max_background_compactions: -1
2024/02/22-06:07:20.852519 78             Options.max_subcompactions: 1
2024/02/22-06:07:20.852520 78             Options.avoid_flush_during_shutdown: 0
2024/02/22-06:07:20.852521 78           Options.writable_file_max_buffer_size: 1048576
2024/02/22-06:07:20.852522 78             Options.delayed_write_rate : 16777216
2024/02/22-06:07:20.852523 78             Options.max_total_wal_size: 0
2024/02/22-06:07:20.852524 78             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-06:07:20.852526 78                   Options.stats_dump_period_sec: 600
2024/02/22-06:07:20.852527 78                 Options.stats_persist_period_sec: 600
2024/02/22-06:07:20.852529 78                 Options.stats_history_buffer_size: 1048576
2024/02/22-06:07:20.852530 78                          Options.max_open_files: -1
2024/02/22-06:07:20.852532 78                          Options.bytes_per_sync: 0
2024/02/22-06:07:20.852533 78                      Options.wal_bytes_per_sync: 0
2024/02/22-06:07:20.852534 78                   Options.strict_bytes_per_sync: 0
2024/02/22-06:07:20.852536 78       Options.compaction_readahead_size: 0
2024/02/22-06:07:20.852537 78                  Options.max_background_flushes: 1
2024/02/22-06:07:20.852539 78 Compression algorithms supported:
2024/02/22-06:07:20.852541 78 	kZSTDNotFinalCompression supported: 1
2024/02/22-06:07:20.852542 78 	kZSTD supported: 1
2024/02/22-06:07:20.852544 78 	kXpressCompression supported: 0
2024/02/22-06:07:20.852545 78 	kLZ4HCCompression supported: 0
2024/02/22-06:07:20.852547 78 	kLZ4Compression supported: 0
2024/02/22-06:07:20.852548 78 	kBZip2Compression supported: 0
2024/02/22-06:07:20.852550 78 	kZlibCompression supported: 0
2024/02/22-06:07:20.852551 78 	kSnappyCompression supported: 0
2024/02/22-06:07:20.852555 78 Fast CRC32 supported: Not supported on x86
2024/02/22-06:07:20.853320 78 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000043
2024/02/22-06:07:20.854888 78 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-06:07:20.854894 78               Options.comparator: leveldb.BytewiseComparator
2024/02/22-06:07:20.854897 78           Options.merge_operator: None
2024/02/22-06:07:20.854898 78        Options.compaction_filter: None
2024/02/22-06:07:20.854899 78        Options.compaction_filter_factory: None
2024/02/22-06:07:20.854901 78  Options.sst_partitioner_factory: None
2024/02/22-06:07:20.854902 78         Options.memtable_factory: SkipListFactory
2024/02/22-06:07:20.854904 78            Options.table_factory: BlockBasedTable
2024/02/22-06:07:20.854939 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1cfc200120)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f1cfc207010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-06:07:20.854944 78        Options.write_buffer_size: 67108864
2024/02/22-06:07:20.854946 78  Options.max_write_buffer_number: 2
2024/02/22-06:07:20.854950 78        Options.compression[0]: NoCompression
2024/02/22-06:07:20.854951 78        Options.compression[1]: NoCompression
2024/02/22-06:07:20.854953 78        Options.compression[2]: ZSTD
2024/02/22-06:07:20.854954 78        Options.compression[3]: ZSTD
2024/02/22-06:07:20.854955 78        Options.compression[4]: ZSTD
2024/02/22-06:07:20.854957 78                  Options.bottommost_compression: Disabled
2024/02/22-06:07:20.854958 78       Options.prefix_extractor: nullptr
2024/02/22-06:07:20.854959 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-06:07:20.854961 78             Options.num_levels: 5
2024/02/22-06:07:20.854962 78        Options.min_write_buffer_number_to_merge: 1
2024/02/22-06:07:20.854963 78     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-06:07:20.854964 78     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-06:07:20.854966 78            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-06:07:20.854967 78                  Options.bottommost_compression_opts.level: 32767
2024/02/22-06:07:20.854969 78               Options.bottommost_compression_opts.strategy: 0
2024/02/22-06:07:20.854970 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-06:07:20.854971 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:07:20.854973 78         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-06:07:20.854974 78                  Options.bottommost_compression_opts.enabled: false
2024/02/22-06:07:20.854976 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:07:20.854977 78            Options.compression_opts.window_bits: -14
2024/02/22-06:07:20.854979 78                  Options.compression_opts.level: 32767
2024/02/22-06:07:20.854980 78               Options.compression_opts.strategy: 0
2024/02/22-06:07:20.854982 78         Options.compression_opts.max_dict_bytes: 0
2024/02/22-06:07:20.854983 78         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:07:20.855002 78         Options.compression_opts.parallel_threads: 1
2024/02/22-06:07:20.855004 78                  Options.compression_opts.enabled: false
2024/02/22-06:07:20.855005 78         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:07:20.855006 78      Options.level0_file_num_compaction_trigger: 4
2024/02/22-06:07:20.855007 78          Options.level0_slowdown_writes_trigger: 20
2024/02/22-06:07:20.855008 78              Options.level0_stop_writes_trigger: 36
2024/02/22-06:07:20.855010 78                   Options.target_file_size_base: 67108864
2024/02/22-06:07:20.855011 78             Options.target_file_size_multiplier: 2
2024/02/22-06:07:20.855012 78                Options.max_bytes_for_level_base: 268435456
2024/02/22-06:07:20.855013 78 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-06:07:20.855014 78          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-06:07:20.855017 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-06:07:20.855019 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-06:07:20.855020 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-06:07:20.855021 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-06:07:20.855022 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-06:07:20.855023 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-06:07:20.855025 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-06:07:20.855026 78       Options.max_sequential_skip_in_iterations: 8
2024/02/22-06:07:20.855027 78                    Options.max_compaction_bytes: 1677721600
2024/02/22-06:07:20.855028 78                        Options.arena_block_size: 1048576
2024/02/22-06:07:20.855030 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-06:07:20.855031 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-06:07:20.855033 78       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-06:07:20.855034 78                Options.disable_auto_compactions: 0
2024/02/22-06:07:20.855037 78                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-06:07:20.855039 78                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-06:07:20.855040 78 Options.compaction_options_universal.size_ratio: 1
2024/02/22-06:07:20.855042 78 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-06:07:20.855043 78 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-06:07:20.855044 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-06:07:20.855045 78 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-06:07:20.855046 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-06:07:20.855048 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-06:07:20.855049 78 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-06:07:20.855056 78                   Options.table_properties_collectors: 
2024/02/22-06:07:20.855057 78                   Options.inplace_update_support: 0
2024/02/22-06:07:20.855058 78                 Options.inplace_update_num_locks: 10000
2024/02/22-06:07:20.855060 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-06:07:20.855061 78               Options.memtable_whole_key_filtering: 0
2024/02/22-06:07:20.855062 78   Options.memtable_huge_page_size: 0
2024/02/22-06:07:20.855064 78                           Options.bloom_locality: 0
2024/02/22-06:07:20.855065 78                    Options.max_successive_merges: 0
2024/02/22-06:07:20.855066 78                Options.optimize_filters_for_hits: 0
2024/02/22-06:07:20.855067 78                Options.paranoid_file_checks: 0
2024/02/22-06:07:20.855069 78                Options.force_consistency_checks: 1
2024/02/22-06:07:20.855070 78                Options.report_bg_io_stats: 0
2024/02/22-06:07:20.855071 78                               Options.ttl: 2592000
2024/02/22-06:07:20.855086 78          Options.periodic_compaction_seconds: 0
2024/02/22-06:07:20.855087 78                       Options.enable_blob_files: false
2024/02/22-06:07:20.855088 78                           Options.min_blob_size: 0
2024/02/22-06:07:20.855090 78                          Options.blob_file_size: 268435456
2024/02/22-06:07:20.855091 78                   Options.blob_compression_type: NoCompression
2024/02/22-06:07:20.855092 78          Options.enable_blob_garbage_collection: false
2024/02/22-06:07:20.855094 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-06:07:20.855095 78 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-06:07:20.855097 78          Options.blob_compaction_readahead_size: 0
2024/02/22-06:07:20.861399 78 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000043 succeeded,manifest_file_number is 43, next_file_number is 45, last_sequence is 425590, log_number is 36,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2024/02/22-06:07:20.861408 78 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 36
2024/02/22-06:07:20.862336 78 [db/version_set.cc:4409] Creating manifest 47
2024/02/22-06:07:20.864838 78 EVENT_LOG_v1 {"time_micros": 1708582040864823, "job": 1, "event": "recovery_started", "wal_files": [44]}
2024/02/22-06:07:20.864845 78 [db/db_impl/db_impl_open.cc:888] Recovering log #44 mode 2
2024/02/22-06:07:20.866380 78 EVENT_LOG_v1 {"time_micros": 1708582040866341, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 48, "file_size": 1050, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 112, "index_size": 23, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 100, "raw_average_key_size": 33, "raw_value_size": 24, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 3, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708582040, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "be375e1e-b4c8-47d2-9072-ae2e62899d79", "db_session_id": "0U3H9S1RUZCVZNWPT2PC", "orig_file_number": 48}}
2024/02/22-06:07:20.866446 78 [db/version_set.cc:4409] Creating manifest 49
2024/02/22-06:07:20.867572 78 EVENT_LOG_v1 {"time_micros": 1708582040867569, "job": 1, "event": "recovery_finished"}
2024/02/22-06:07:20.872499 78 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000044.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:07:20.872922 78 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f1cfc255000
2024/02/22-06:07:20.872989 78 DB pointer 0x7f1cfc23b000
2024/02/22-06:07:20.873040 83 (Original Log Time 2024/02/22-06:07:20.873026) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/22-06:07:20.873514 103 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-06:07:20.873536 103 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.05 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.9      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f1cfc207010#8 capacity: 4.00 GB collections: 1 last_copies: 0 last_secs: 9.4e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
