2024/02/22-04:06:43.093993 73 RocksDB version: 6.29.5
2024/02/22-04:06:43.094138 73 Git sha 0
2024/02/22-04:06:43.094141 73 Compile date 2023-11-07 23:58:42
2024/02/22-04:06:43.094158 73 DB SUMMARY
2024/02/22-04:06:43.094161 73 DB Session ID:  JLSV6FBC77UESSINXVMF
2024/02/22-04:06:43.094296 73 CURRENT file:  CURRENT
2024/02/22-04:06:43.094299 73 IDENTITY file:  IDENTITY
2024/02/22-04:06:43.094314 73 MANIFEST file:  MANIFEST-000034 size: 603 Bytes
2024/02/22-04:06:43.094318 73 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 1, files: 000038.sst 
2024/02/22-04:06:43.094323 73 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000035.log size: 14939 ; 
2024/02/22-04:06:43.094328 73                         Options.error_if_exists: 0
2024/02/22-04:06:43.094331 73                       Options.create_if_missing: 1
2024/02/22-04:06:43.094333 73                         Options.paranoid_checks: 1
2024/02/22-04:06:43.094336 73             Options.flush_verify_memtable_count: 1
2024/02/22-04:06:43.094338 73                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-04:06:43.094340 73                                     Options.env: 0x7f1a3b1792c0
2024/02/22-04:06:43.094343 73                                      Options.fs: PosixFileSystem
2024/02/22-04:06:43.094346 73                                Options.info_log: 0x7f1869a50050
2024/02/22-04:06:43.094348 73                Options.max_file_opening_threads: 16
2024/02/22-04:06:43.094350 73                              Options.statistics: (nil)
2024/02/22-04:06:43.094353 73                               Options.use_fsync: 0
2024/02/22-04:06:43.094355 73                       Options.max_log_file_size: 0
2024/02/22-04:06:43.094357 73                  Options.max_manifest_file_size: 1073741824
2024/02/22-04:06:43.094360 73                   Options.log_file_time_to_roll: 0
2024/02/22-04:06:43.094362 73                       Options.keep_log_file_num: 1000
2024/02/22-04:06:43.094364 73                    Options.recycle_log_file_num: 0
2024/02/22-04:06:43.094366 73                         Options.allow_fallocate: 1
2024/02/22-04:06:43.094368 73                        Options.allow_mmap_reads: 0
2024/02/22-04:06:43.094370 73                       Options.allow_mmap_writes: 0
2024/02/22-04:06:43.094372 73                        Options.use_direct_reads: 0
2024/02/22-04:06:43.094374 73                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-04:06:43.094376 73          Options.create_missing_column_families: 0
2024/02/22-04:06:43.094378 73                              Options.db_log_dir: 
2024/02/22-04:06:43.094381 73                                 Options.wal_dir: 
2024/02/22-04:06:43.094383 73                Options.table_cache_numshardbits: 6
2024/02/22-04:06:43.094385 73                         Options.WAL_ttl_seconds: 0
2024/02/22-04:06:43.094387 73                       Options.WAL_size_limit_MB: 0
2024/02/22-04:06:43.094389 73                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-04:06:43.094391 73             Options.manifest_preallocation_size: 4194304
2024/02/22-04:06:43.094393 73                     Options.is_fd_close_on_exec: 1
2024/02/22-04:06:43.094395 73                   Options.advise_random_on_open: 1
2024/02/22-04:06:43.094397 73                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-04:06:43.094403 73                    Options.db_write_buffer_size: 0
2024/02/22-04:06:43.094405 73                    Options.write_buffer_manager: 0x7f1869a040a0
2024/02/22-04:06:43.094407 73         Options.access_hint_on_compaction_start: 1
2024/02/22-04:06:43.094409 73  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-04:06:43.094411 73           Options.random_access_max_buffer_size: 1048576
2024/02/22-04:06:43.094413 73                      Options.use_adaptive_mutex: 0
2024/02/22-04:06:43.094415 73                            Options.rate_limiter: (nil)
2024/02/22-04:06:43.094420 73     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-04:06:43.094422 73                       Options.wal_recovery_mode: 2
2024/02/22-04:06:43.094454 73                  Options.enable_thread_tracking: 0
2024/02/22-04:06:43.094457 73                  Options.enable_pipelined_write: 0
2024/02/22-04:06:43.094459 73                  Options.unordered_write: 0
2024/02/22-04:06:43.094461 73         Options.allow_concurrent_memtable_write: 1
2024/02/22-04:06:43.094463 73      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-04:06:43.094465 73             Options.write_thread_max_yield_usec: 100
2024/02/22-04:06:43.094467 73            Options.write_thread_slow_yield_usec: 3
2024/02/22-04:06:43.094469 73                               Options.row_cache: None
2024/02/22-04:06:43.094471 73                              Options.wal_filter: None
2024/02/22-04:06:43.094474 73             Options.avoid_flush_during_recovery: 0
2024/02/22-04:06:43.094476 73             Options.allow_ingest_behind: 0
2024/02/22-04:06:43.094478 73             Options.preserve_deletes: 0
2024/02/22-04:06:43.094480 73             Options.two_write_queues: 0
2024/02/22-04:06:43.094482 73             Options.manual_wal_flush: 0
2024/02/22-04:06:43.094484 73             Options.atomic_flush: 0
2024/02/22-04:06:43.094486 73             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-04:06:43.094488 73                 Options.persist_stats_to_disk: 0
2024/02/22-04:06:43.094490 73                 Options.write_dbid_to_manifest: 0
2024/02/22-04:06:43.094492 73                 Options.log_readahead_size: 0
2024/02/22-04:06:43.094494 73                 Options.file_checksum_gen_factory: Unknown
2024/02/22-04:06:43.094497 73                 Options.best_efforts_recovery: 0
2024/02/22-04:06:43.094499 73                Options.max_bgerror_resume_count: 2147483647
2024/02/22-04:06:43.094501 73            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-04:06:43.094503 73             Options.allow_data_in_errors: 0
2024/02/22-04:06:43.094505 73             Options.db_host_id: __hostname__
2024/02/22-04:06:43.094508 73             Options.max_background_jobs: 4
2024/02/22-04:06:43.094511 73             Options.max_background_compactions: -1
2024/02/22-04:06:43.094513 73             Options.max_subcompactions: 1
2024/02/22-04:06:43.094515 73             Options.avoid_flush_during_shutdown: 0
2024/02/22-04:06:43.094517 73           Options.writable_file_max_buffer_size: 1048576
2024/02/22-04:06:43.094519 73             Options.delayed_write_rate : 16777216
2024/02/22-04:06:43.094522 73             Options.max_total_wal_size: 0
2024/02/22-04:06:43.094524 73             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-04:06:43.094526 73                   Options.stats_dump_period_sec: 600
2024/02/22-04:06:43.094528 73                 Options.stats_persist_period_sec: 600
2024/02/22-04:06:43.094530 73                 Options.stats_history_buffer_size: 1048576
2024/02/22-04:06:43.094532 73                          Options.max_open_files: -1
2024/02/22-04:06:43.094534 73                          Options.bytes_per_sync: 0
2024/02/22-04:06:43.094536 73                      Options.wal_bytes_per_sync: 0
2024/02/22-04:06:43.094538 73                   Options.strict_bytes_per_sync: 0
2024/02/22-04:06:43.094540 73       Options.compaction_readahead_size: 0
2024/02/22-04:06:43.094542 73                  Options.max_background_flushes: 1
2024/02/22-04:06:43.094545 73 Compression algorithms supported:
2024/02/22-04:06:43.094547 73 	kZSTDNotFinalCompression supported: 1
2024/02/22-04:06:43.094550 73 	kZSTD supported: 1
2024/02/22-04:06:43.094552 73 	kXpressCompression supported: 0
2024/02/22-04:06:43.094554 73 	kLZ4HCCompression supported: 0
2024/02/22-04:06:43.094556 73 	kLZ4Compression supported: 0
2024/02/22-04:06:43.094559 73 	kBZip2Compression supported: 0
2024/02/22-04:06:43.094561 73 	kZlibCompression supported: 0
2024/02/22-04:06:43.094563 73 	kSnappyCompression supported: 0
2024/02/22-04:06:43.094568 73 Fast CRC32 supported: Not supported on x86
2024/02/22-04:06:43.094825 73 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000034
2024/02/22-04:06:43.095502 73 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-04:06:43.095508 73               Options.comparator: leveldb.BytewiseComparator
2024/02/22-04:06:43.095511 73           Options.merge_operator: None
2024/02/22-04:06:43.095513 73        Options.compaction_filter: None
2024/02/22-04:06:43.095515 73        Options.compaction_filter_factory: None
2024/02/22-04:06:43.095517 73  Options.sst_partitioner_factory: None
2024/02/22-04:06:43.095519 73         Options.memtable_factory: SkipListFactory
2024/02/22-04:06:43.095522 73            Options.table_factory: BlockBasedTable
2024/02/22-04:06:43.095568 73            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1869a160c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f1869a04010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-04:06:43.095595 73        Options.write_buffer_size: 67108864
2024/02/22-04:06:43.095598 73  Options.max_write_buffer_number: 2
2024/02/22-04:06:43.095602 73        Options.compression[0]: NoCompression
2024/02/22-04:06:43.095606 73        Options.compression[1]: NoCompression
2024/02/22-04:06:43.095609 73        Options.compression[2]: ZSTD
2024/02/22-04:06:43.095611 73        Options.compression[3]: ZSTD
2024/02/22-04:06:43.095614 73        Options.compression[4]: ZSTD
2024/02/22-04:06:43.095616 73                  Options.bottommost_compression: Disabled
2024/02/22-04:06:43.095618 73       Options.prefix_extractor: nullptr
2024/02/22-04:06:43.095680 73   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-04:06:43.095682 73             Options.num_levels: 5
2024/02/22-04:06:43.095684 73        Options.min_write_buffer_number_to_merge: 1
2024/02/22-04:06:43.095686 73     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-04:06:43.095689 73     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-04:06:43.095691 73            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-04:06:43.095693 73                  Options.bottommost_compression_opts.level: 32767
2024/02/22-04:06:43.095695 73               Options.bottommost_compression_opts.strategy: 0
2024/02/22-04:06:43.095697 73         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-04:06:43.095699 73         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-04:06:43.095701 73         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-04:06:43.095703 73                  Options.bottommost_compression_opts.enabled: false
2024/02/22-04:06:43.095706 73         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-04:06:43.095708 73            Options.compression_opts.window_bits: -14
2024/02/22-04:06:43.095710 73                  Options.compression_opts.level: 32767
2024/02/22-04:06:43.095712 73               Options.compression_opts.strategy: 0
2024/02/22-04:06:43.095714 73         Options.compression_opts.max_dict_bytes: 0
2024/02/22-04:06:43.095716 73         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-04:06:43.095718 73         Options.compression_opts.parallel_threads: 1
2024/02/22-04:06:43.095756 73                  Options.compression_opts.enabled: false
2024/02/22-04:06:43.095758 73         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-04:06:43.095761 73      Options.level0_file_num_compaction_trigger: 4
2024/02/22-04:06:43.095763 73          Options.level0_slowdown_writes_trigger: 20
2024/02/22-04:06:43.095765 73              Options.level0_stop_writes_trigger: 36
2024/02/22-04:06:43.095767 73                   Options.target_file_size_base: 67108864
2024/02/22-04:06:43.095769 73             Options.target_file_size_multiplier: 2
2024/02/22-04:06:43.095771 73                Options.max_bytes_for_level_base: 268435456
2024/02/22-04:06:43.095773 73 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-04:06:43.095775 73          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-04:06:43.095779 73 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-04:06:43.095782 73 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-04:06:43.095784 73 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-04:06:43.095786 73 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-04:06:43.095788 73 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-04:06:43.095790 73 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-04:06:43.095792 73 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-04:06:43.095794 73       Options.max_sequential_skip_in_iterations: 8
2024/02/22-04:06:43.095796 73                    Options.max_compaction_bytes: 1677721600
2024/02/22-04:06:43.095798 73                        Options.arena_block_size: 1048576
2024/02/22-04:06:43.095801 73   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-04:06:43.095803 73   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-04:06:43.095805 73       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-04:06:43.095807 73                Options.disable_auto_compactions: 0
2024/02/22-04:06:43.095810 73                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-04:06:43.095813 73                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-04:06:43.095815 73 Options.compaction_options_universal.size_ratio: 1
2024/02/22-04:06:43.095817 73 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-04:06:43.095819 73 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-04:06:43.095821 73 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-04:06:43.095823 73 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-04:06:43.095826 73 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-04:06:43.095828 73 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-04:06:43.095830 73 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-04:06:43.095836 73                   Options.table_properties_collectors: 
2024/02/22-04:06:43.095838 73                   Options.inplace_update_support: 0
2024/02/22-04:06:43.095841 73                 Options.inplace_update_num_locks: 10000
2024/02/22-04:06:43.095843 73               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-04:06:43.095846 73               Options.memtable_whole_key_filtering: 0
2024/02/22-04:06:43.095848 73   Options.memtable_huge_page_size: 0
2024/02/22-04:06:43.095850 73                           Options.bloom_locality: 0
2024/02/22-04:06:43.095855 73                    Options.max_successive_merges: 0
2024/02/22-04:06:43.095879 73                Options.optimize_filters_for_hits: 0
2024/02/22-04:06:43.095881 73                Options.paranoid_file_checks: 0
2024/02/22-04:06:43.095883 73                Options.force_consistency_checks: 1
2024/02/22-04:06:43.095885 73                Options.report_bg_io_stats: 0
2024/02/22-04:06:43.095888 73                               Options.ttl: 2592000
2024/02/22-04:06:43.095890 73          Options.periodic_compaction_seconds: 0
2024/02/22-04:06:43.095923 73                       Options.enable_blob_files: false
2024/02/22-04:06:43.095925 73                           Options.min_blob_size: 0
2024/02/22-04:06:43.095927 73                          Options.blob_file_size: 268435456
2024/02/22-04:06:43.095930 73                   Options.blob_compression_type: NoCompression
2024/02/22-04:06:43.095932 73          Options.enable_blob_garbage_collection: false
2024/02/22-04:06:43.095934 73      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-04:06:43.095940 73 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-04:06:43.095961 73          Options.blob_compaction_readahead_size: 0
2024/02/22-04:06:43.100326 73 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000034 succeeded,manifest_file_number is 34, next_file_number is 40, last_sequence is 425353, log_number is 30,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2024/02/22-04:06:43.100336 73 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 30
2024/02/22-04:06:43.100559 73 [db/version_set.cc:4409] Creating manifest 41
2024/02/22-04:06:43.102829 73 EVENT_LOG_v1 {"time_micros": 1708574803102810, "job": 1, "event": "recovery_started", "wal_files": [35]}
2024/02/22-04:06:43.102839 73 [db/db_impl/db_impl_open.cc:888] Recovering log #35 mode 2
2024/02/22-04:06:43.104206 73 EVENT_LOG_v1 {"time_micros": 1708574803104157, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 42, "file_size": 1050, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 112, "index_size": 23, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 100, "raw_average_key_size": 33, "raw_value_size": 24, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 3, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708574803, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "be375e1e-b4c8-47d2-9072-ae2e62899d79", "db_session_id": "JLSV6FBC77UESSINXVMF", "orig_file_number": 42}}
2024/02/22-04:06:43.104309 73 [db/version_set.cc:4409] Creating manifest 43
2024/02/22-04:06:43.105816 73 EVENT_LOG_v1 {"time_micros": 1708574803105811, "job": 1, "event": "recovery_finished"}
2024/02/22-04:06:43.108770 73 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000035.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-04:06:43.108831 73 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f1869a55000
2024/02/22-04:06:43.108923 73 DB pointer 0x7f1869a3b000
2024/02/22-04:06:43.109005 84 (Original Log Time 2024/02/22-04:06:43.108967) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/22-04:06:43.109480 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-04:06:43.109501 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.03 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.0      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      1/0    1.90 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      2/0    2.93 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.0      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.0      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.0      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.07 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.07 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f1869a04010#8 capacity: 4.00 GB collections: 1 last_copies: 0 last_secs: 8.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
