2024/02/22-06:07:20.874807 78 RocksDB version: 6.29.5
2024/02/22-06:07:20.874857 78 Git sha 0
2024/02/22-06:07:20.874859 78 Compile date 2023-11-07 23:58:42
2024/02/22-06:07:20.874861 78 DB SUMMARY
2024/02/22-06:07:20.874862 78 DB Session ID:  0U3H9S1RUZCVZNWPT2PD
2024/02/22-06:07:20.874917 78 CURRENT file:  CURRENT
2024/02/22-06:07:20.874918 78 IDENTITY file:  IDENTITY
2024/02/22-06:07:20.874927 78 MANIFEST file:  MANIFEST-000052 size: 713 Bytes
2024/02/22-06:07:20.874929 78 SST files in /var/lib/milvus/rdb_data dir, Total Num: 4, files: 000040.sst 000041.sst 000045.sst 000051.sst 
2024/02/22-06:07:20.874931 78 Write Ahead Log file in /var/lib/milvus/rdb_data: 000053.log size: 19365 ; 
2024/02/22-06:07:20.874932 78                         Options.error_if_exists: 0
2024/02/22-06:07:20.874934 78                       Options.create_if_missing: 1
2024/02/22-06:07:20.874936 78                         Options.paranoid_checks: 1
2024/02/22-06:07:20.874937 78             Options.flush_verify_memtable_count: 1
2024/02/22-06:07:20.874938 78                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-06:07:20.874940 78                                     Options.env: 0x7f1ecb78c2c0
2024/02/22-06:07:20.874941 78                                      Options.fs: PosixFileSystem
2024/02/22-06:07:20.874943 78                                Options.info_log: 0x7f1cfc250140
2024/02/22-06:07:20.874944 78                Options.max_file_opening_threads: 16
2024/02/22-06:07:20.874945 78                              Options.statistics: (nil)
2024/02/22-06:07:20.874947 78                               Options.use_fsync: 0
2024/02/22-06:07:20.874948 78                       Options.max_log_file_size: 0
2024/02/22-06:07:20.874950 78                  Options.max_manifest_file_size: 1073741824
2024/02/22-06:07:20.874951 78                   Options.log_file_time_to_roll: 0
2024/02/22-06:07:20.874952 78                       Options.keep_log_file_num: 1000
2024/02/22-06:07:20.874954 78                    Options.recycle_log_file_num: 0
2024/02/22-06:07:20.874955 78                         Options.allow_fallocate: 1
2024/02/22-06:07:20.874956 78                        Options.allow_mmap_reads: 0
2024/02/22-06:07:20.874958 78                       Options.allow_mmap_writes: 0
2024/02/22-06:07:20.874959 78                        Options.use_direct_reads: 0
2024/02/22-06:07:20.874960 78                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-06:07:20.874961 78          Options.create_missing_column_families: 1
2024/02/22-06:07:20.874962 78                              Options.db_log_dir: 
2024/02/22-06:07:20.874963 78                                 Options.wal_dir: 
2024/02/22-06:07:20.874965 78                Options.table_cache_numshardbits: 6
2024/02/22-06:07:20.874966 78                         Options.WAL_ttl_seconds: 0
2024/02/22-06:07:20.874967 78                       Options.WAL_size_limit_MB: 0
2024/02/22-06:07:20.874968 78                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-06:07:20.874969 78             Options.manifest_preallocation_size: 4194304
2024/02/22-06:07:20.874970 78                     Options.is_fd_close_on_exec: 1
2024/02/22-06:07:20.874972 78                   Options.advise_random_on_open: 1
2024/02/22-06:07:20.874973 78                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-06:07:20.874975 78                    Options.db_write_buffer_size: 0
2024/02/22-06:07:20.874976 78                    Options.write_buffer_manager: 0x7f1cfc207280
2024/02/22-06:07:20.874978 78         Options.access_hint_on_compaction_start: 1
2024/02/22-06:07:20.874979 78  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-06:07:20.874980 78           Options.random_access_max_buffer_size: 1048576
2024/02/22-06:07:20.874981 78                      Options.use_adaptive_mutex: 0
2024/02/22-06:07:20.874982 78                            Options.rate_limiter: (nil)
2024/02/22-06:07:20.874984 78     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-06:07:20.874985 78                       Options.wal_recovery_mode: 2
2024/02/22-06:07:20.875000 78                  Options.enable_thread_tracking: 0
2024/02/22-06:07:20.875001 78                  Options.enable_pipelined_write: 0
2024/02/22-06:07:20.875003 78                  Options.unordered_write: 0
2024/02/22-06:07:20.875004 78         Options.allow_concurrent_memtable_write: 1
2024/02/22-06:07:20.875005 78      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-06:07:20.875006 78             Options.write_thread_max_yield_usec: 100
2024/02/22-06:07:20.875007 78            Options.write_thread_slow_yield_usec: 3
2024/02/22-06:07:20.875008 78                               Options.row_cache: None
2024/02/22-06:07:20.875010 78                              Options.wal_filter: None
2024/02/22-06:07:20.875011 78             Options.avoid_flush_during_recovery: 0
2024/02/22-06:07:20.875012 78             Options.allow_ingest_behind: 0
2024/02/22-06:07:20.875013 78             Options.preserve_deletes: 0
2024/02/22-06:07:20.875014 78             Options.two_write_queues: 0
2024/02/22-06:07:20.875015 78             Options.manual_wal_flush: 0
2024/02/22-06:07:20.875016 78             Options.atomic_flush: 0
2024/02/22-06:07:20.875018 78             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-06:07:20.875019 78                 Options.persist_stats_to_disk: 0
2024/02/22-06:07:20.875020 78                 Options.write_dbid_to_manifest: 0
2024/02/22-06:07:20.875021 78                 Options.log_readahead_size: 0
2024/02/22-06:07:20.875022 78                 Options.file_checksum_gen_factory: Unknown
2024/02/22-06:07:20.875024 78                 Options.best_efforts_recovery: 0
2024/02/22-06:07:20.875025 78                Options.max_bgerror_resume_count: 2147483647
2024/02/22-06:07:20.875026 78            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-06:07:20.875027 78             Options.allow_data_in_errors: 0
2024/02/22-06:07:20.875028 78             Options.db_host_id: __hostname__
2024/02/22-06:07:20.875030 78             Options.max_background_jobs: 4
2024/02/22-06:07:20.875031 78             Options.max_background_compactions: -1
2024/02/22-06:07:20.875032 78             Options.max_subcompactions: 1
2024/02/22-06:07:20.875033 78             Options.avoid_flush_during_shutdown: 0
2024/02/22-06:07:20.875034 78           Options.writable_file_max_buffer_size: 1048576
2024/02/22-06:07:20.875036 78             Options.delayed_write_rate : 16777216
2024/02/22-06:07:20.875037 78             Options.max_total_wal_size: 0
2024/02/22-06:07:20.875038 78             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-06:07:20.875039 78                   Options.stats_dump_period_sec: 600
2024/02/22-06:07:20.875040 78                 Options.stats_persist_period_sec: 600
2024/02/22-06:07:20.875042 78                 Options.stats_history_buffer_size: 1048576
2024/02/22-06:07:20.875043 78                          Options.max_open_files: -1
2024/02/22-06:07:20.875044 78                          Options.bytes_per_sync: 0
2024/02/22-06:07:20.875045 78                      Options.wal_bytes_per_sync: 0
2024/02/22-06:07:20.875046 78                   Options.strict_bytes_per_sync: 0
2024/02/22-06:07:20.875047 78       Options.compaction_readahead_size: 0
2024/02/22-06:07:20.875049 78                  Options.max_background_flushes: 1
2024/02/22-06:07:20.875050 78 Compression algorithms supported:
2024/02/22-06:07:20.875051 78 	kZSTDNotFinalCompression supported: 1
2024/02/22-06:07:20.875052 78 	kZSTD supported: 1
2024/02/22-06:07:20.875054 78 	kXpressCompression supported: 0
2024/02/22-06:07:20.875055 78 	kLZ4HCCompression supported: 0
2024/02/22-06:07:20.875056 78 	kLZ4Compression supported: 0
2024/02/22-06:07:20.875057 78 	kBZip2Compression supported: 0
2024/02/22-06:07:20.875058 78 	kZlibCompression supported: 0
2024/02/22-06:07:20.875060 78 	kSnappyCompression supported: 0
2024/02/22-06:07:20.875061 78 Fast CRC32 supported: Not supported on x86
2024/02/22-06:07:20.875159 78 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000052
2024/02/22-06:07:20.875347 78 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-06:07:20.875349 78               Options.comparator: leveldb.BytewiseComparator
2024/02/22-06:07:20.875351 78           Options.merge_operator: None
2024/02/22-06:07:20.875352 78        Options.compaction_filter: None
2024/02/22-06:07:20.875354 78        Options.compaction_filter_factory: None
2024/02/22-06:07:20.875355 78  Options.sst_partitioner_factory: None
2024/02/22-06:07:20.875356 78         Options.memtable_factory: SkipListFactory
2024/02/22-06:07:20.875358 78            Options.table_factory: BlockBasedTable
2024/02/22-06:07:20.875387 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1cfc200620)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f1cfc207010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-06:07:20.875390 78        Options.write_buffer_size: 67108864
2024/02/22-06:07:20.875391 78  Options.max_write_buffer_number: 2
2024/02/22-06:07:20.875393 78        Options.compression[0]: NoCompression
2024/02/22-06:07:20.875394 78        Options.compression[1]: NoCompression
2024/02/22-06:07:20.875396 78        Options.compression[2]: ZSTD
2024/02/22-06:07:20.875397 78        Options.compression[3]: ZSTD
2024/02/22-06:07:20.875398 78        Options.compression[4]: ZSTD
2024/02/22-06:07:20.875399 78                  Options.bottommost_compression: Disabled
2024/02/22-06:07:20.875401 78       Options.prefix_extractor: nullptr
2024/02/22-06:07:20.875402 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-06:07:20.875403 78             Options.num_levels: 5
2024/02/22-06:07:20.875405 78        Options.min_write_buffer_number_to_merge: 1
2024/02/22-06:07:20.875406 78     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-06:07:20.875408 78     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-06:07:20.875409 78            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-06:07:20.875411 78                  Options.bottommost_compression_opts.level: 32767
2024/02/22-06:07:20.875412 78               Options.bottommost_compression_opts.strategy: 0
2024/02/22-06:07:20.875413 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-06:07:20.875415 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:07:20.875416 78         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-06:07:20.875418 78                  Options.bottommost_compression_opts.enabled: false
2024/02/22-06:07:20.875419 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:07:20.875421 78            Options.compression_opts.window_bits: -14
2024/02/22-06:07:20.875422 78                  Options.compression_opts.level: 32767
2024/02/22-06:07:20.875423 78               Options.compression_opts.strategy: 0
2024/02/22-06:07:20.875424 78         Options.compression_opts.max_dict_bytes: 0
2024/02/22-06:07:20.875426 78         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:07:20.875427 78         Options.compression_opts.parallel_threads: 1
2024/02/22-06:07:20.875447 78                  Options.compression_opts.enabled: false
2024/02/22-06:07:20.875449 78         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:07:20.875450 78      Options.level0_file_num_compaction_trigger: 4
2024/02/22-06:07:20.875451 78          Options.level0_slowdown_writes_trigger: 20
2024/02/22-06:07:20.875452 78              Options.level0_stop_writes_trigger: 36
2024/02/22-06:07:20.875453 78                   Options.target_file_size_base: 67108864
2024/02/22-06:07:20.875454 78             Options.target_file_size_multiplier: 2
2024/02/22-06:07:20.875456 78                Options.max_bytes_for_level_base: 268435456
2024/02/22-06:07:20.875457 78 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-06:07:20.875458 78          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-06:07:20.875460 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-06:07:20.875462 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-06:07:20.875463 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-06:07:20.875464 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-06:07:20.875465 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-06:07:20.875466 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-06:07:20.875467 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-06:07:20.875469 78       Options.max_sequential_skip_in_iterations: 8
2024/02/22-06:07:20.875470 78                    Options.max_compaction_bytes: 1677721600
2024/02/22-06:07:20.875471 78                        Options.arena_block_size: 1048576
2024/02/22-06:07:20.875472 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-06:07:20.875473 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-06:07:20.875474 78       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-06:07:20.875476 78                Options.disable_auto_compactions: 0
2024/02/22-06:07:20.875478 78                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-06:07:20.875479 78                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-06:07:20.875480 78 Options.compaction_options_universal.size_ratio: 1
2024/02/22-06:07:20.875481 78 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-06:07:20.875483 78 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-06:07:20.875484 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-06:07:20.875485 78 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-06:07:20.875487 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-06:07:20.875488 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-06:07:20.875489 78 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-06:07:20.875494 78                   Options.table_properties_collectors: 
2024/02/22-06:07:20.875495 78                   Options.inplace_update_support: 0
2024/02/22-06:07:20.875497 78                 Options.inplace_update_num_locks: 10000
2024/02/22-06:07:20.875498 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-06:07:20.875499 78               Options.memtable_whole_key_filtering: 0
2024/02/22-06:07:20.875500 78   Options.memtable_huge_page_size: 0
2024/02/22-06:07:20.875502 78                           Options.bloom_locality: 0
2024/02/22-06:07:20.875503 78                    Options.max_successive_merges: 0
2024/02/22-06:07:20.875504 78                Options.optimize_filters_for_hits: 0
2024/02/22-06:07:20.875505 78                Options.paranoid_file_checks: 0
2024/02/22-06:07:20.875506 78                Options.force_consistency_checks: 1
2024/02/22-06:07:20.875507 78                Options.report_bg_io_stats: 0
2024/02/22-06:07:20.875508 78                               Options.ttl: 2592000
2024/02/22-06:07:20.875523 78          Options.periodic_compaction_seconds: 0
2024/02/22-06:07:20.875524 78                       Options.enable_blob_files: false
2024/02/22-06:07:20.875525 78                           Options.min_blob_size: 0
2024/02/22-06:07:20.875527 78                          Options.blob_file_size: 268435456
2024/02/22-06:07:20.875528 78                   Options.blob_compression_type: NoCompression
2024/02/22-06:07:20.875529 78          Options.enable_blob_garbage_collection: false
2024/02/22-06:07:20.875530 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-06:07:20.875532 78 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-06:07:20.875533 78          Options.blob_compaction_readahead_size: 0
2024/02/22-06:07:20.875718 78 [db/column_family.cc:605] --------------- Options for column family [properties]:
2024/02/22-06:07:20.875722 78               Options.comparator: leveldb.BytewiseComparator
2024/02/22-06:07:20.875724 78           Options.merge_operator: None
2024/02/22-06:07:20.875726 78        Options.compaction_filter: None
2024/02/22-06:07:20.875727 78        Options.compaction_filter_factory: None
2024/02/22-06:07:20.875728 78  Options.sst_partitioner_factory: None
2024/02/22-06:07:20.875730 78         Options.memtable_factory: SkipListFactory
2024/02/22-06:07:20.875731 78            Options.table_factory: BlockBasedTable
2024/02/22-06:07:20.875748 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1cfc200620)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f1cfc207010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-06:07:20.875753 78        Options.write_buffer_size: 67108864
2024/02/22-06:07:20.875754 78  Options.max_write_buffer_number: 2
2024/02/22-06:07:20.875756 78        Options.compression[0]: NoCompression
2024/02/22-06:07:20.875757 78        Options.compression[1]: NoCompression
2024/02/22-06:07:20.875758 78        Options.compression[2]: ZSTD
2024/02/22-06:07:20.875760 78        Options.compression[3]: ZSTD
2024/02/22-06:07:20.875761 78        Options.compression[4]: ZSTD
2024/02/22-06:07:20.875762 78                  Options.bottommost_compression: Disabled
2024/02/22-06:07:20.875763 78       Options.prefix_extractor: nullptr
2024/02/22-06:07:20.875764 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-06:07:20.875765 78             Options.num_levels: 5
2024/02/22-06:07:20.875766 78        Options.min_write_buffer_number_to_merge: 1
2024/02/22-06:07:20.875768 78     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-06:07:20.875769 78     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-06:07:20.875770 78            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-06:07:20.875771 78                  Options.bottommost_compression_opts.level: 32767
2024/02/22-06:07:20.875772 78               Options.bottommost_compression_opts.strategy: 0
2024/02/22-06:07:20.875773 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-06:07:20.875774 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:07:20.875791 78         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-06:07:20.875793 78                  Options.bottommost_compression_opts.enabled: false
2024/02/22-06:07:20.875794 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:07:20.875795 78            Options.compression_opts.window_bits: -14
2024/02/22-06:07:20.875796 78                  Options.compression_opts.level: 32767
2024/02/22-06:07:20.875798 78               Options.compression_opts.strategy: 0
2024/02/22-06:07:20.875799 78         Options.compression_opts.max_dict_bytes: 0
2024/02/22-06:07:20.875800 78         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:07:20.875801 78         Options.compression_opts.parallel_threads: 1
2024/02/22-06:07:20.875802 78                  Options.compression_opts.enabled: false
2024/02/22-06:07:20.875803 78         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:07:20.875804 78      Options.level0_file_num_compaction_trigger: 4
2024/02/22-06:07:20.875805 78          Options.level0_slowdown_writes_trigger: 20
2024/02/22-06:07:20.875807 78              Options.level0_stop_writes_trigger: 36
2024/02/22-06:07:20.875808 78                   Options.target_file_size_base: 67108864
2024/02/22-06:07:20.875809 78             Options.target_file_size_multiplier: 2
2024/02/22-06:07:20.875810 78                Options.max_bytes_for_level_base: 268435456
2024/02/22-06:07:20.875811 78 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-06:07:20.875812 78          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-06:07:20.875814 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-06:07:20.875816 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-06:07:20.875817 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-06:07:20.875818 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-06:07:20.875819 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-06:07:20.875820 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-06:07:20.875821 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-06:07:20.875823 78       Options.max_sequential_skip_in_iterations: 8
2024/02/22-06:07:20.875824 78                    Options.max_compaction_bytes: 1677721600
2024/02/22-06:07:20.875825 78                        Options.arena_block_size: 1048576
2024/02/22-06:07:20.875826 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-06:07:20.875827 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-06:07:20.875828 78       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-06:07:20.875830 78                Options.disable_auto_compactions: 0
2024/02/22-06:07:20.875831 78                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-06:07:20.875832 78                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-06:07:20.875833 78 Options.compaction_options_universal.size_ratio: 1
2024/02/22-06:07:20.875835 78 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-06:07:20.875836 78 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-06:07:20.875837 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-06:07:20.875838 78 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-06:07:20.875839 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-06:07:20.875840 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-06:07:20.875842 78 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-06:07:20.875844 78                   Options.table_properties_collectors: 
2024/02/22-06:07:20.875845 78                   Options.inplace_update_support: 0
2024/02/22-06:07:20.875846 78                 Options.inplace_update_num_locks: 10000
2024/02/22-06:07:20.875848 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-06:07:20.875861 78               Options.memtable_whole_key_filtering: 0
2024/02/22-06:07:20.875862 78   Options.memtable_huge_page_size: 0
2024/02/22-06:07:20.875864 78                           Options.bloom_locality: 0
2024/02/22-06:07:20.875865 78                    Options.max_successive_merges: 0
2024/02/22-06:07:20.875866 78                Options.optimize_filters_for_hits: 0
2024/02/22-06:07:20.875867 78                Options.paranoid_file_checks: 0
2024/02/22-06:07:20.875868 78                Options.force_consistency_checks: 1
2024/02/22-06:07:20.875869 78                Options.report_bg_io_stats: 0
2024/02/22-06:07:20.875870 78                               Options.ttl: 2592000
2024/02/22-06:07:20.875871 78          Options.periodic_compaction_seconds: 0
2024/02/22-06:07:20.875873 78                       Options.enable_blob_files: false
2024/02/22-06:07:20.875874 78                           Options.min_blob_size: 0
2024/02/22-06:07:20.875875 78                          Options.blob_file_size: 268435456
2024/02/22-06:07:20.875876 78                   Options.blob_compression_type: NoCompression
2024/02/22-06:07:20.875877 78          Options.enable_blob_garbage_collection: false
2024/02/22-06:07:20.875878 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-06:07:20.875880 78 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-06:07:20.875881 78          Options.blob_compaction_readahead_size: 0
2024/02/22-06:07:20.880023 78 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000052 succeeded,manifest_file_number is 52, next_file_number is 54, last_sequence is 425544, log_number is 48,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 22
2024/02/22-06:07:20.880029 78 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 48
2024/02/22-06:07:20.880031 78 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 48
2024/02/22-06:07:20.880257 78 [db/version_set.cc:4409] Creating manifest 56
2024/02/22-06:07:20.881635 78 EVENT_LOG_v1 {"time_micros": 1708582040881631, "job": 1, "event": "recovery_started", "wal_files": [53]}
2024/02/22-06:07:20.881639 78 [db/db_impl/db_impl_open.cc:888] Recovering log #53 mode 2
2024/02/22-06:07:20.883889 78 EVENT_LOG_v1 {"time_micros": 1708582040883870, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 57, "file_size": 8954, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 7976, "index_size": 59, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 12152, "raw_average_key_size": 49, "raw_value_size": 4216, "raw_average_value_size": 17, "num_data_blocks": 1, "num_entries": 248, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708582040, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "0U3H9S1RUZCVZNWPT2PD", "orig_file_number": 57}}
2024/02/22-06:07:20.883933 78 [db/version_set.cc:4409] Creating manifest 58
2024/02/22-06:07:20.885077 78 EVENT_LOG_v1 {"time_micros": 1708582040885074, "job": 1, "event": "recovery_finished"}
2024/02/22-06:07:20.886954 78 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000053.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:07:20.886990 78 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f1cfc255700
2024/02/22-06:07:20.887039 78 DB pointer 0x7f1cfc23cc00
2024/02/22-06:07:20.887126 84 (Original Log Time 2024/02/22-06:07:20.887101) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/22-06:07:23.887573 103 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-06:07:23.887622 103 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 28 writes, 28 keys, 27 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 28 writes, 0 syncs, 28.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 28 writes, 28 keys, 27 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 28 writes, 0 syncs, 28.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   619.42 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      4.4      0.00              0.00         1    0.002       0      0       0.0       0.0
  L1      2/0   112.75 MB   0.4      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      5/0   113.35 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      4.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      4.4      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      4.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f1cfc207010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 9.4e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f1cfc207010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 9.4e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
