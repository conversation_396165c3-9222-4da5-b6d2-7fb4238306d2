2024/02/22-03:54:17.473663 65 RocksDB version: 6.29.5
2024/02/22-03:54:17.473712 65 Git sha 0
2024/02/22-03:54:17.473714 65 Compile date 2023-11-07 23:58:42
2024/02/22-03:54:17.473716 65 DB SUMMARY
2024/02/22-03:54:17.473718 65 DB Session ID:  9IIKCS8A12KB3I8RI0SP
2024/02/22-03:54:17.473774 65 CURRENT file:  CURRENT
2024/02/22-03:54:17.473776 65 IDENTITY file:  IDENTITY
2024/02/22-03:54:17.473784 65 MANIFEST file:  MANIFEST-000018 size: 594 Bytes
2024/02/22-03:54:17.473791 65 MANIFEST file:  MANIFEST-000028 size: 1007 Bytes
2024/02/22-03:54:17.473793 65 SST files in /var/lib/milvus/rdb_data dir, Total Num: 5, files: 000011.sst 000017.sst 000023.sst 000032.sst 000033.sst 
2024/02/22-03:54:17.473795 65 Write Ahead Log file in /var/lib/milvus/rdb_data: 000022.log size: 39100578 ; 000029.log size: 6511613 ; 
2024/02/22-03:54:17.473797 65                         Options.error_if_exists: 0
2024/02/22-03:54:17.473798 65                       Options.create_if_missing: 1
2024/02/22-03:54:17.473800 65                         Options.paranoid_checks: 1
2024/02/22-03:54:17.473801 65             Options.flush_verify_memtable_count: 1
2024/02/22-03:54:17.473802 65                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-03:54:17.473804 65                                     Options.env: 0x7f9caa8c72c0
2024/02/22-03:54:17.473806 65                                      Options.fs: PosixFileSystem
2024/02/22-03:54:17.473807 65                                Options.info_log: 0x7f9ae4450140
2024/02/22-03:54:17.473808 65                Options.max_file_opening_threads: 16
2024/02/22-03:54:17.473809 65                              Options.statistics: (nil)
2024/02/22-03:54:17.473811 65                               Options.use_fsync: 0
2024/02/22-03:54:17.473812 65                       Options.max_log_file_size: 0
2024/02/22-03:54:17.473813 65                  Options.max_manifest_file_size: 1073741824
2024/02/22-03:54:17.473815 65                   Options.log_file_time_to_roll: 0
2024/02/22-03:54:17.473816 65                       Options.keep_log_file_num: 1000
2024/02/22-03:54:17.473817 65                    Options.recycle_log_file_num: 0
2024/02/22-03:54:17.473818 65                         Options.allow_fallocate: 1
2024/02/22-03:54:17.473820 65                        Options.allow_mmap_reads: 0
2024/02/22-03:54:17.473821 65                       Options.allow_mmap_writes: 0
2024/02/22-03:54:17.473822 65                        Options.use_direct_reads: 0
2024/02/22-03:54:17.473823 65                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-03:54:17.473825 65          Options.create_missing_column_families: 1
2024/02/22-03:54:17.473826 65                              Options.db_log_dir: 
2024/02/22-03:54:17.473827 65                                 Options.wal_dir: 
2024/02/22-03:54:17.473828 65                Options.table_cache_numshardbits: 6
2024/02/22-03:54:17.473829 65                         Options.WAL_ttl_seconds: 0
2024/02/22-03:54:17.473830 65                       Options.WAL_size_limit_MB: 0
2024/02/22-03:54:17.473832 65                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-03:54:17.473833 65             Options.manifest_preallocation_size: 4194304
2024/02/22-03:54:17.473834 65                     Options.is_fd_close_on_exec: 1
2024/02/22-03:54:17.473835 65                   Options.advise_random_on_open: 1
2024/02/22-03:54:17.473836 65                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-03:54:17.473839 65                    Options.db_write_buffer_size: 0
2024/02/22-03:54:17.473840 65                    Options.write_buffer_manager: 0x7f9ae4407280
2024/02/22-03:54:17.473841 65         Options.access_hint_on_compaction_start: 1
2024/02/22-03:54:17.473842 65  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-03:54:17.473844 65           Options.random_access_max_buffer_size: 1048576
2024/02/22-03:54:17.473845 65                      Options.use_adaptive_mutex: 0
2024/02/22-03:54:17.473846 65                            Options.rate_limiter: (nil)
2024/02/22-03:54:17.473848 65     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-03:54:17.473864 65                       Options.wal_recovery_mode: 2
2024/02/22-03:54:17.473866 65                  Options.enable_thread_tracking: 0
2024/02/22-03:54:17.473867 65                  Options.enable_pipelined_write: 0
2024/02/22-03:54:17.473868 65                  Options.unordered_write: 0
2024/02/22-03:54:17.473869 65         Options.allow_concurrent_memtable_write: 1
2024/02/22-03:54:17.473870 65      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-03:54:17.473871 65             Options.write_thread_max_yield_usec: 100
2024/02/22-03:54:17.473873 65            Options.write_thread_slow_yield_usec: 3
2024/02/22-03:54:17.473874 65                               Options.row_cache: None
2024/02/22-03:54:17.473875 65                              Options.wal_filter: None
2024/02/22-03:54:17.473876 65             Options.avoid_flush_during_recovery: 0
2024/02/22-03:54:17.473878 65             Options.allow_ingest_behind: 0
2024/02/22-03:54:17.473879 65             Options.preserve_deletes: 0
2024/02/22-03:54:17.473880 65             Options.two_write_queues: 0
2024/02/22-03:54:17.473881 65             Options.manual_wal_flush: 0
2024/02/22-03:54:17.473882 65             Options.atomic_flush: 0
2024/02/22-03:54:17.473883 65             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-03:54:17.473885 65                 Options.persist_stats_to_disk: 0
2024/02/22-03:54:17.473886 65                 Options.write_dbid_to_manifest: 0
2024/02/22-03:54:17.473887 65                 Options.log_readahead_size: 0
2024/02/22-03:54:17.473888 65                 Options.file_checksum_gen_factory: Unknown
2024/02/22-03:54:17.473890 65                 Options.best_efforts_recovery: 0
2024/02/22-03:54:17.473891 65                Options.max_bgerror_resume_count: 2147483647
2024/02/22-03:54:17.473892 65            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-03:54:17.473893 65             Options.allow_data_in_errors: 0
2024/02/22-03:54:17.473895 65             Options.db_host_id: __hostname__
2024/02/22-03:54:17.473896 65             Options.max_background_jobs: 4
2024/02/22-03:54:17.473897 65             Options.max_background_compactions: -1
2024/02/22-03:54:17.473898 65             Options.max_subcompactions: 1
2024/02/22-03:54:17.473899 65             Options.avoid_flush_during_shutdown: 0
2024/02/22-03:54:17.473900 65           Options.writable_file_max_buffer_size: 1048576
2024/02/22-03:54:17.473902 65             Options.delayed_write_rate : 16777216
2024/02/22-03:54:17.473903 65             Options.max_total_wal_size: 0
2024/02/22-03:54:17.473904 65             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-03:54:17.473905 65                   Options.stats_dump_period_sec: 600
2024/02/22-03:54:17.473907 65                 Options.stats_persist_period_sec: 600
2024/02/22-03:54:17.473908 65                 Options.stats_history_buffer_size: 1048576
2024/02/22-03:54:17.473909 65                          Options.max_open_files: -1
2024/02/22-03:54:17.473910 65                          Options.bytes_per_sync: 0
2024/02/22-03:54:17.473911 65                      Options.wal_bytes_per_sync: 0
2024/02/22-03:54:17.473912 65                   Options.strict_bytes_per_sync: 0
2024/02/22-03:54:17.473914 65       Options.compaction_readahead_size: 0
2024/02/22-03:54:17.473915 65                  Options.max_background_flushes: 1
2024/02/22-03:54:17.473916 65 Compression algorithms supported:
2024/02/22-03:54:17.473917 65 	kZSTDNotFinalCompression supported: 1
2024/02/22-03:54:17.473919 65 	kZSTD supported: 1
2024/02/22-03:54:17.473920 65 	kXpressCompression supported: 0
2024/02/22-03:54:17.473921 65 	kLZ4HCCompression supported: 0
2024/02/22-03:54:17.473922 65 	kLZ4Compression supported: 0
2024/02/22-03:54:17.473924 65 	kBZip2Compression supported: 0
2024/02/22-03:54:17.473925 65 	kZlibCompression supported: 0
2024/02/22-03:54:17.473926 65 	kSnappyCompression supported: 0
2024/02/22-03:54:17.473928 65 Fast CRC32 supported: Not supported on x86
2024/02/22-03:54:17.474156 65 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000018
2024/02/22-03:54:17.474366 65 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-03:54:17.474369 65               Options.comparator: leveldb.BytewiseComparator
2024/02/22-03:54:17.474370 65           Options.merge_operator: None
2024/02/22-03:54:17.474371 65        Options.compaction_filter: None
2024/02/22-03:54:17.474372 65        Options.compaction_filter_factory: None
2024/02/22-03:54:17.474373 65  Options.sst_partitioner_factory: None
2024/02/22-03:54:17.474375 65         Options.memtable_factory: SkipListFactory
2024/02/22-03:54:17.474376 65            Options.table_factory: BlockBasedTable
2024/02/22-03:54:17.474395 65            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f9ae44005c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f9ae4407010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-03:54:17.474397 65        Options.write_buffer_size: 67108864
2024/02/22-03:54:17.474399 65  Options.max_write_buffer_number: 2
2024/02/22-03:54:17.474400 65        Options.compression[0]: NoCompression
2024/02/22-03:54:17.474402 65        Options.compression[1]: NoCompression
2024/02/22-03:54:17.474403 65        Options.compression[2]: ZSTD
2024/02/22-03:54:17.474405 65        Options.compression[3]: ZSTD
2024/02/22-03:54:17.474406 65        Options.compression[4]: ZSTD
2024/02/22-03:54:17.474407 65                  Options.bottommost_compression: Disabled
2024/02/22-03:54:17.474408 65       Options.prefix_extractor: nullptr
2024/02/22-03:54:17.474409 65   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-03:54:17.474410 65             Options.num_levels: 5
2024/02/22-03:54:17.474412 65        Options.min_write_buffer_number_to_merge: 1
2024/02/22-03:54:17.474413 65     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-03:54:17.474414 65     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-03:54:17.474415 65            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-03:54:17.474416 65                  Options.bottommost_compression_opts.level: 32767
2024/02/22-03:54:17.474418 65               Options.bottommost_compression_opts.strategy: 0
2024/02/22-03:54:17.474419 65         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-03:54:17.474420 65         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-03:54:17.474421 65         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-03:54:17.474423 65                  Options.bottommost_compression_opts.enabled: false
2024/02/22-03:54:17.474424 65         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-03:54:17.474425 65            Options.compression_opts.window_bits: -14
2024/02/22-03:54:17.474426 65                  Options.compression_opts.level: 32767
2024/02/22-03:54:17.474427 65               Options.compression_opts.strategy: 0
2024/02/22-03:54:17.474429 65         Options.compression_opts.max_dict_bytes: 0
2024/02/22-03:54:17.474446 65         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-03:54:17.474448 65         Options.compression_opts.parallel_threads: 1
2024/02/22-03:54:17.474449 65                  Options.compression_opts.enabled: false
2024/02/22-03:54:17.474450 65         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-03:54:17.474451 65      Options.level0_file_num_compaction_trigger: 4
2024/02/22-03:54:17.474452 65          Options.level0_slowdown_writes_trigger: 20
2024/02/22-03:54:17.474454 65              Options.level0_stop_writes_trigger: 36
2024/02/22-03:54:17.474455 65                   Options.target_file_size_base: 67108864
2024/02/22-03:54:17.474456 65             Options.target_file_size_multiplier: 2
2024/02/22-03:54:17.474457 65                Options.max_bytes_for_level_base: 268435456
2024/02/22-03:54:17.474458 65 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-03:54:17.474460 65          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-03:54:17.474462 65 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-03:54:17.474463 65 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-03:54:17.474464 65 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-03:54:17.474465 65 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-03:54:17.474467 65 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-03:54:17.474468 65 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-03:54:17.474469 65 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-03:54:17.474470 65       Options.max_sequential_skip_in_iterations: 8
2024/02/22-03:54:17.474471 65                    Options.max_compaction_bytes: 1677721600
2024/02/22-03:54:17.474472 65                        Options.arena_block_size: 1048576
2024/02/22-03:54:17.474474 65   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-03:54:17.474475 65   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-03:54:17.474476 65       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-03:54:17.474477 65                Options.disable_auto_compactions: 0
2024/02/22-03:54:17.474479 65                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-03:54:17.474481 65                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-03:54:17.474482 65 Options.compaction_options_universal.size_ratio: 1
2024/02/22-03:54:17.474483 65 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-03:54:17.474484 65 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-03:54:17.474486 65 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-03:54:17.474487 65 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-03:54:17.474488 65 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-03:54:17.474489 65 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-03:54:17.474490 65 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-03:54:17.474495 65                   Options.table_properties_collectors: 
2024/02/22-03:54:17.474496 65                   Options.inplace_update_support: 0
2024/02/22-03:54:17.474497 65                 Options.inplace_update_num_locks: 10000
2024/02/22-03:54:17.474498 65               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-03:54:17.474500 65               Options.memtable_whole_key_filtering: 0
2024/02/22-03:54:17.474501 65   Options.memtable_huge_page_size: 0
2024/02/22-03:54:17.474502 65                           Options.bloom_locality: 0
2024/02/22-03:54:17.474503 65                    Options.max_successive_merges: 0
2024/02/22-03:54:17.474505 65                Options.optimize_filters_for_hits: 0
2024/02/22-03:54:17.474506 65                Options.paranoid_file_checks: 0
2024/02/22-03:54:17.474507 65                Options.force_consistency_checks: 1
2024/02/22-03:54:17.474508 65                Options.report_bg_io_stats: 0
2024/02/22-03:54:17.474522 65                               Options.ttl: 2592000
2024/02/22-03:54:17.474523 65          Options.periodic_compaction_seconds: 0
2024/02/22-03:54:17.474524 65                       Options.enable_blob_files: false
2024/02/22-03:54:17.474525 65                           Options.min_blob_size: 0
2024/02/22-03:54:17.474527 65                          Options.blob_file_size: 268435456
2024/02/22-03:54:17.474528 65                   Options.blob_compression_type: NoCompression
2024/02/22-03:54:17.474529 65          Options.enable_blob_garbage_collection: false
2024/02/22-03:54:17.474530 65      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-03:54:17.474532 65 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-03:54:17.474533 65          Options.blob_compaction_readahead_size: 0
2024/02/22-03:54:17.474725 65 [db/column_family.cc:605] --------------- Options for column family [properties]:
2024/02/22-03:54:17.474728 65               Options.comparator: leveldb.BytewiseComparator
2024/02/22-03:54:17.474729 65           Options.merge_operator: None
2024/02/22-03:54:17.474730 65        Options.compaction_filter: None
2024/02/22-03:54:17.474731 65        Options.compaction_filter_factory: None
2024/02/22-03:54:17.474732 65  Options.sst_partitioner_factory: None
2024/02/22-03:54:17.474734 65         Options.memtable_factory: SkipListFactory
2024/02/22-03:54:17.474735 65            Options.table_factory: BlockBasedTable
2024/02/22-03:54:17.474748 65            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f9ae44005c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f9ae4407010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-03:54:17.474751 65        Options.write_buffer_size: 67108864
2024/02/22-03:54:17.474752 65  Options.max_write_buffer_number: 2
2024/02/22-03:54:17.474753 65        Options.compression[0]: NoCompression
2024/02/22-03:54:17.474754 65        Options.compression[1]: NoCompression
2024/02/22-03:54:17.474756 65        Options.compression[2]: ZSTD
2024/02/22-03:54:17.474757 65        Options.compression[3]: ZSTD
2024/02/22-03:54:17.474758 65        Options.compression[4]: ZSTD
2024/02/22-03:54:17.474759 65                  Options.bottommost_compression: Disabled
2024/02/22-03:54:17.474760 65       Options.prefix_extractor: nullptr
2024/02/22-03:54:17.474761 65   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-03:54:17.474763 65             Options.num_levels: 5
2024/02/22-03:54:17.474764 65        Options.min_write_buffer_number_to_merge: 1
2024/02/22-03:54:17.474765 65     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-03:54:17.474766 65     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-03:54:17.474767 65            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-03:54:17.474768 65                  Options.bottommost_compression_opts.level: 32767
2024/02/22-03:54:17.474769 65               Options.bottommost_compression_opts.strategy: 0
2024/02/22-03:54:17.474771 65         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-03:54:17.474787 65         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-03:54:17.474789 65         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-03:54:17.474790 65                  Options.bottommost_compression_opts.enabled: false
2024/02/22-03:54:17.474791 65         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-03:54:17.474792 65            Options.compression_opts.window_bits: -14
2024/02/22-03:54:17.474794 65                  Options.compression_opts.level: 32767
2024/02/22-03:54:17.474795 65               Options.compression_opts.strategy: 0
2024/02/22-03:54:17.474796 65         Options.compression_opts.max_dict_bytes: 0
2024/02/22-03:54:17.474797 65         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-03:54:17.474798 65         Options.compression_opts.parallel_threads: 1
2024/02/22-03:54:17.474799 65                  Options.compression_opts.enabled: false
2024/02/22-03:54:17.474800 65         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-03:54:17.474802 65      Options.level0_file_num_compaction_trigger: 4
2024/02/22-03:54:17.474803 65          Options.level0_slowdown_writes_trigger: 20
2024/02/22-03:54:17.474804 65              Options.level0_stop_writes_trigger: 36
2024/02/22-03:54:17.474805 65                   Options.target_file_size_base: 67108864
2024/02/22-03:54:17.474806 65             Options.target_file_size_multiplier: 2
2024/02/22-03:54:17.474807 65                Options.max_bytes_for_level_base: 268435456
2024/02/22-03:54:17.474808 65 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-03:54:17.474810 65          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-03:54:17.474812 65 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-03:54:17.474813 65 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-03:54:17.474814 65 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-03:54:17.474815 65 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-03:54:17.474816 65 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-03:54:17.474818 65 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-03:54:17.474819 65 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-03:54:17.474820 65       Options.max_sequential_skip_in_iterations: 8
2024/02/22-03:54:17.474822 65                    Options.max_compaction_bytes: 1677721600
2024/02/22-03:54:17.474823 65                        Options.arena_block_size: 1048576
2024/02/22-03:54:17.474824 65   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-03:54:17.474825 65   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-03:54:17.474826 65       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-03:54:17.474828 65                Options.disable_auto_compactions: 0
2024/02/22-03:54:17.474829 65                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-03:54:17.474830 65                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-03:54:17.474832 65 Options.compaction_options_universal.size_ratio: 1
2024/02/22-03:54:17.474833 65 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-03:54:17.474834 65 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-03:54:17.474835 65 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-03:54:17.474836 65 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-03:54:17.474837 65 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-03:54:17.474839 65 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-03:54:17.474840 65 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-03:54:17.474842 65                   Options.table_properties_collectors: 
2024/02/22-03:54:17.474844 65                   Options.inplace_update_support: 0
2024/02/22-03:54:17.474845 65                 Options.inplace_update_num_locks: 10000
2024/02/22-03:54:17.474858 65               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-03:54:17.474859 65               Options.memtable_whole_key_filtering: 0
2024/02/22-03:54:17.474861 65   Options.memtable_huge_page_size: 0
2024/02/22-03:54:17.474862 65                           Options.bloom_locality: 0
2024/02/22-03:54:17.474863 65                    Options.max_successive_merges: 0
2024/02/22-03:54:17.474864 65                Options.optimize_filters_for_hits: 0
2024/02/22-03:54:17.474865 65                Options.paranoid_file_checks: 0
2024/02/22-03:54:17.474866 65                Options.force_consistency_checks: 1
2024/02/22-03:54:17.474867 65                Options.report_bg_io_stats: 0
2024/02/22-03:54:17.474869 65                               Options.ttl: 2592000
2024/02/22-03:54:17.474870 65          Options.periodic_compaction_seconds: 0
2024/02/22-03:54:17.474871 65                       Options.enable_blob_files: false
2024/02/22-03:54:17.474872 65                           Options.min_blob_size: 0
2024/02/22-03:54:17.474873 65                          Options.blob_file_size: 268435456
2024/02/22-03:54:17.474874 65                   Options.blob_compression_type: NoCompression
2024/02/22-03:54:17.474876 65          Options.enable_blob_garbage_collection: false
2024/02/22-03:54:17.474877 65      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-03:54:17.474878 65 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-03:54:17.474879 65          Options.blob_compaction_readahead_size: 0
2024/02/22-03:54:17.479215 65 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000018 succeeded,manifest_file_number is 18, next_file_number is 25, last_sequence is 107620, log_number is 22,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 22
2024/02/22-03:54:17.479221 65 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 22
2024/02/22-03:54:17.479223 65 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 14
2024/02/22-03:54:17.479478 65 [db/version_set.cc:4409] Creating manifest 34
2024/02/22-03:54:17.480940 65 EVENT_LOG_v1 {"time_micros": 1708574057480935, "job": 1, "event": "recovery_started", "wal_files": [22, 29]}
2024/02/22-03:54:17.480945 65 [db/db_impl/db_impl_open.cc:888] Recovering log #22 mode 2
2024/02/22-03:54:17.897863 65 [db/db_impl/db_impl_open.cc:888] Recovering log #29 mode 2
2024/02/22-03:54:18.232921 65 EVENT_LOG_v1 {"time_micros": 1708574058232888, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 35, "file_size": 32281096, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 32271494, "index_size": 8672, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 15471094, "raw_average_key_size": 48, "raw_value_size": 27514267, "raw_average_value_size": 87, "num_data_blocks": 162, "num_entries": 315738, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708574057, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "9IIKCS8A12KB3I8RI0SP", "orig_file_number": 35}}
2024/02/22-03:54:18.234713 65 [db/version_set.cc:4409] Creating manifest 36
2024/02/22-03:54:18.236169 65 EVENT_LOG_v1 {"time_micros": 1708574058236166, "job": 1, "event": "recovery_finished"}
2024/02/22-03:54:18.239051 65 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000029.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-03:54:18.244684 65 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000022.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-03:54:18.244737 65 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f9ae4455700
2024/02/22-03:54:18.244875 65 DB pointer 0x7f9ae443cc00
2024/02/22-03:54:18.245307 79 [db/compaction/compaction_job.cc:2331] [default] [JOB 3] Compacting 4@0 files to L1, score 1.00
2024/02/22-03:54:18.245345 79 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 5 Base level 0, inputs: [35(30MB) 23(46MB) 17(17MB) 11(17MB)]
2024/02/22-03:54:18.245421 79 EVENT_LOG_v1 {"time_micros": 1708574058245383, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [35, 23, 17, 11], "score": 1, "input_data_size": 118224414}
2024/02/22-03:54:18.984230 79 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #40: 300572 keys, 67365763 bytes
2024/02/22-03:54:18.984291 79 EVENT_LOG_v1 {"time_micros": 1708574058984262, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 40, "file_size": 67365763, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67354219, "index_size": 10591, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 14727802, "raw_average_key_size": 48, "raw_value_size": 62827288, "raw_average_value_size": 209, "num_data_blocks": 197, "num_entries": 300572, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1703207242, "oldest_key_time": 0, "file_creation_time": 1708574058, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "9IIKCS8A12KB3I8RI0SP", "orig_file_number": 40}}
2024/02/22-03:54:19.400741 79 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #41: 122786 keys, 50856953 bytes
2024/02/22-03:54:19.400791 79 EVENT_LOG_v1 {"time_micros": 1708574059400764, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 41, "file_size": 50856953, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 50851751, "index_size": 4250, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 6016514, "raw_average_key_size": 49, "raw_value_size": 49002238, "raw_average_value_size": 399, "num_data_blocks": 79, "num_entries": 122786, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1703207242, "oldest_key_time": 0, "file_creation_time": 1708574058, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "9IIKCS8A12KB3I8RI0SP", "orig_file_number": 41}}
2024/02/22-03:54:19.401460 79 [db/compaction/compaction_job.cc:1998] [default] [JOB 3] Compacted 4@0 files to L1 => 118222716 bytes
2024/02/22-03:54:19.401901 79 (Original Log Time 2024/02/22-03:54:19.401798) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 2 0 0 0] max score 0.44, MB/sec: 102.3 rd, 102.3 wr, level 1, files in(4, 0) out(2 +0 blob) MB in(112.7, 0.0 +0.0 blob) out(112.7 +0.0 blob), read-write-amplify(2.0) write-amplify(1.0) OK, records in: 423358, records dropped: 0 output_compression: NoCompression
2024/02/22-03:54:19.401906 79 (Original Log Time 2024/02/22-03:54:19.401834) EVENT_LOG_v1 {"time_micros": 1708574059401810, "job": 3, "event": "compaction_finished", "compaction_time_micros": 1155434, "compaction_time_cpu_micros": 1002992, "output_level": 1, "num_output_files": 2, "total_output_size": 118222716, "num_input_records": 423358, "num_output_records": 423358, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 2, 0, 0, 0]}
2024/02/22-03:54:19.409889 79 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000035.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-03:54:19.409914 79 EVENT_LOG_v1 {"time_micros": 1708574059409908, "job": 3, "event": "table_file_deletion", "file_number": 35}
2024/02/22-03:54:19.416625 79 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000023.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-03:54:19.416635 79 EVENT_LOG_v1 {"time_micros": 1708574059416633, "job": 3, "event": "table_file_deletion", "file_number": 23}
2024/02/22-03:54:19.419238 79 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000017.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-03:54:19.419244 79 EVENT_LOG_v1 {"time_micros": 1708574059419242, "job": 3, "event": "table_file_deletion", "file_number": 17}
2024/02/22-03:54:19.421920 79 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000011.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-03:54:19.421930 79 EVENT_LOG_v1 {"time_micros": 1708574059421924, "job": 3, "event": "table_file_deletion", "file_number": 11}
2024/02/22-03:54:19.422031 79 (Original Log Time 2024/02/22-03:54:19.422018) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/22-03:54:21.245543 98 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-03:54:21.245584 98 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.8 total, 3.8 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    126.7      0.24              0.00         1    0.243       0      0       0.0       0.0
  L1      2/0   112.75 MB   0.4      0.1     0.1      0.0       0.1      0.1       0.0   1.0     97.6     97.6      1.16              1.00         1    1.155    423K      0       0.0       0.0
 Sum      2/0   112.75 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   4.7     80.6    102.6      1.40              1.00         2    0.699    423K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   4.7     80.6    102.6      1.40              1.00         2    0.699    423K      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.0     97.6     97.6      1.16              1.00         1    1.155    423K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    126.7      0.24              0.00         1    0.243       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.8 total, 3.8 interval
Flush(GB): cumulative 0.030, interval 0.030
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.14 GB write, 38.06 MB/s write, 0.11 GB read, 29.90 MB/s read, 1.4 seconds
Interval compaction: 0.14 GB write, 38.06 MB/s write, 0.11 GB read, 29.90 MB/s read, 1.4 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9ae4407010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 9.6e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.8 total, 3.8 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f9ae4407010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 9.6e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
