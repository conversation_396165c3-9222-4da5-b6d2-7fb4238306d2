2024/02/22-06:16:45.888617 69 RocksDB version: 6.29.5
2024/02/22-06:16:45.888698 69 Git sha 0
2024/02/22-06:16:45.888701 69 Compile date 2023-11-07 23:58:42
2024/02/22-06:16:45.888704 69 DB SUMMARY
2024/02/22-06:16:45.888706 69 DB Session ID:  UGRLOJ85HPN78EC2Q1X6
2024/02/22-06:16:45.888797 69 CURRENT file:  CURRENT
2024/02/22-06:16:45.888799 69 IDENTITY file:  IDENTITY
2024/02/22-06:16:45.888812 69 MANIFEST file:  MANIFEST-000074 size: 581 Bytes
2024/02/22-06:16:45.888816 69 SST files in /var/lib/milvus/rdb_data dir, Total Num: 3, files: 000068.sst 000069.sst 000073.sst 
2024/02/22-06:16:45.888819 69 Write Ahead Log file in /var/lib/milvus/rdb_data: 000075.log size: 21163 ; 
2024/02/22-06:16:45.888822 69                         Options.error_if_exists: 0
2024/02/22-06:16:45.888824 69                       Options.create_if_missing: 1
2024/02/22-06:16:45.888827 69                         Options.paranoid_checks: 1
2024/02/22-06:16:45.888829 69             Options.flush_verify_memtable_count: 1
2024/02/22-06:16:45.888831 69                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-06:16:45.888833 69                                     Options.env: 0x7f20f46b12c0
2024/02/22-06:16:45.888835 69                                      Options.fs: PosixFileSystem
2024/02/22-06:16:45.888838 69                                Options.info_log: 0x7f1f28e50140
2024/02/22-06:16:45.888840 69                Options.max_file_opening_threads: 16
2024/02/22-06:16:45.888842 69                              Options.statistics: (nil)
2024/02/22-06:16:45.888845 69                               Options.use_fsync: 0
2024/02/22-06:16:45.888847 69                       Options.max_log_file_size: 0
2024/02/22-06:16:45.888849 69                  Options.max_manifest_file_size: 1073741824
2024/02/22-06:16:45.888852 69                   Options.log_file_time_to_roll: 0
2024/02/22-06:16:45.888854 69                       Options.keep_log_file_num: 1000
2024/02/22-06:16:45.888856 69                    Options.recycle_log_file_num: 0
2024/02/22-06:16:45.888858 69                         Options.allow_fallocate: 1
2024/02/22-06:16:45.888860 69                        Options.allow_mmap_reads: 0
2024/02/22-06:16:45.888862 69                       Options.allow_mmap_writes: 0
2024/02/22-06:16:45.888864 69                        Options.use_direct_reads: 0
2024/02/22-06:16:45.888866 69                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-06:16:45.888868 69          Options.create_missing_column_families: 1
2024/02/22-06:16:45.888870 69                              Options.db_log_dir: 
2024/02/22-06:16:45.888873 69                                 Options.wal_dir: 
2024/02/22-06:16:45.888875 69                Options.table_cache_numshardbits: 6
2024/02/22-06:16:45.888877 69                         Options.WAL_ttl_seconds: 0
2024/02/22-06:16:45.888879 69                       Options.WAL_size_limit_MB: 0
2024/02/22-06:16:45.888881 69                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-06:16:45.888883 69             Options.manifest_preallocation_size: 4194304
2024/02/22-06:16:45.888885 69                     Options.is_fd_close_on_exec: 1
2024/02/22-06:16:45.888887 69                   Options.advise_random_on_open: 1
2024/02/22-06:16:45.888889 69                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-06:16:45.888893 69                    Options.db_write_buffer_size: 0
2024/02/22-06:16:45.888896 69                    Options.write_buffer_manager: 0x7f1f28e07280
2024/02/22-06:16:45.888898 69         Options.access_hint_on_compaction_start: 1
2024/02/22-06:16:45.888900 69  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-06:16:45.888902 69           Options.random_access_max_buffer_size: 1048576
2024/02/22-06:16:45.888904 69                      Options.use_adaptive_mutex: 0
2024/02/22-06:16:45.888906 69                            Options.rate_limiter: (nil)
2024/02/22-06:16:45.888908 69     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-06:16:45.888911 69                       Options.wal_recovery_mode: 2
2024/02/22-06:16:45.888936 69                  Options.enable_thread_tracking: 0
2024/02/22-06:16:45.888939 69                  Options.enable_pipelined_write: 0
2024/02/22-06:16:45.888941 69                  Options.unordered_write: 0
2024/02/22-06:16:45.888943 69         Options.allow_concurrent_memtable_write: 1
2024/02/22-06:16:45.888945 69      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-06:16:45.888947 69             Options.write_thread_max_yield_usec: 100
2024/02/22-06:16:45.888949 69            Options.write_thread_slow_yield_usec: 3
2024/02/22-06:16:45.888951 69                               Options.row_cache: None
2024/02/22-06:16:45.888953 69                              Options.wal_filter: None
2024/02/22-06:16:45.888955 69             Options.avoid_flush_during_recovery: 0
2024/02/22-06:16:45.888957 69             Options.allow_ingest_behind: 0
2024/02/22-06:16:45.888959 69             Options.preserve_deletes: 0
2024/02/22-06:16:45.888961 69             Options.two_write_queues: 0
2024/02/22-06:16:45.888963 69             Options.manual_wal_flush: 0
2024/02/22-06:16:45.888965 69             Options.atomic_flush: 0
2024/02/22-06:16:45.888967 69             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-06:16:45.888969 69                 Options.persist_stats_to_disk: 0
2024/02/22-06:16:45.888971 69                 Options.write_dbid_to_manifest: 0
2024/02/22-06:16:45.888973 69                 Options.log_readahead_size: 0
2024/02/22-06:16:45.888975 69                 Options.file_checksum_gen_factory: Unknown
2024/02/22-06:16:45.888977 69                 Options.best_efforts_recovery: 0
2024/02/22-06:16:45.888979 69                Options.max_bgerror_resume_count: 2147483647
2024/02/22-06:16:45.888981 69            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-06:16:45.888983 69             Options.allow_data_in_errors: 0
2024/02/22-06:16:45.888985 69             Options.db_host_id: __hostname__
2024/02/22-06:16:45.888987 69             Options.max_background_jobs: 4
2024/02/22-06:16:45.888989 69             Options.max_background_compactions: -1
2024/02/22-06:16:45.888991 69             Options.max_subcompactions: 1
2024/02/22-06:16:45.888993 69             Options.avoid_flush_during_shutdown: 0
2024/02/22-06:16:45.888996 69           Options.writable_file_max_buffer_size: 1048576
2024/02/22-06:16:45.888998 69             Options.delayed_write_rate : 16777216
2024/02/22-06:16:45.889000 69             Options.max_total_wal_size: 0
2024/02/22-06:16:45.889002 69             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-06:16:45.889004 69                   Options.stats_dump_period_sec: 600
2024/02/22-06:16:45.889006 69                 Options.stats_persist_period_sec: 600
2024/02/22-06:16:45.889008 69                 Options.stats_history_buffer_size: 1048576
2024/02/22-06:16:45.889010 69                          Options.max_open_files: -1
2024/02/22-06:16:45.889012 69                          Options.bytes_per_sync: 0
2024/02/22-06:16:45.889014 69                      Options.wal_bytes_per_sync: 0
2024/02/22-06:16:45.889016 69                   Options.strict_bytes_per_sync: 0
2024/02/22-06:16:45.889018 69       Options.compaction_readahead_size: 0
2024/02/22-06:16:45.889020 69                  Options.max_background_flushes: 1
2024/02/22-06:16:45.889022 69 Compression algorithms supported:
2024/02/22-06:16:45.889024 69 	kZSTDNotFinalCompression supported: 1
2024/02/22-06:16:45.889026 69 	kZSTD supported: 1
2024/02/22-06:16:45.889029 69 	kXpressCompression supported: 0
2024/02/22-06:16:45.889031 69 	kLZ4HCCompression supported: 0
2024/02/22-06:16:45.889033 69 	kLZ4Compression supported: 0
2024/02/22-06:16:45.889035 69 	kBZip2Compression supported: 0
2024/02/22-06:16:45.889037 69 	kZlibCompression supported: 0
2024/02/22-06:16:45.889039 69 	kSnappyCompression supported: 0
2024/02/22-06:16:45.889042 69 Fast CRC32 supported: Not supported on x86
2024/02/22-06:16:45.889194 69 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000074
2024/02/22-06:16:45.889510 69 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-06:16:45.889514 69               Options.comparator: leveldb.BytewiseComparator
2024/02/22-06:16:45.889516 69           Options.merge_operator: None
2024/02/22-06:16:45.889518 69        Options.compaction_filter: None
2024/02/22-06:16:45.889520 69        Options.compaction_filter_factory: None
2024/02/22-06:16:45.889523 69  Options.sst_partitioner_factory: None
2024/02/22-06:16:45.889525 69         Options.memtable_factory: SkipListFactory
2024/02/22-06:16:45.889527 69            Options.table_factory: BlockBasedTable
2024/02/22-06:16:45.889555 69            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1f28e00f00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f1f28e07010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-06:16:45.889560 69        Options.write_buffer_size: 67108864
2024/02/22-06:16:45.889562 69  Options.max_write_buffer_number: 2
2024/02/22-06:16:45.889564 69        Options.compression[0]: NoCompression
2024/02/22-06:16:45.889567 69        Options.compression[1]: NoCompression
2024/02/22-06:16:45.889569 69        Options.compression[2]: ZSTD
2024/02/22-06:16:45.889571 69        Options.compression[3]: ZSTD
2024/02/22-06:16:45.889573 69        Options.compression[4]: ZSTD
2024/02/22-06:16:45.889575 69                  Options.bottommost_compression: Disabled
2024/02/22-06:16:45.889577 69       Options.prefix_extractor: nullptr
2024/02/22-06:16:45.889580 69   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-06:16:45.889582 69             Options.num_levels: 5
2024/02/22-06:16:45.889584 69        Options.min_write_buffer_number_to_merge: 1
2024/02/22-06:16:45.889586 69     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-06:16:45.889588 69     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-06:16:45.889590 69            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-06:16:45.889592 69                  Options.bottommost_compression_opts.level: 32767
2024/02/22-06:16:45.889594 69               Options.bottommost_compression_opts.strategy: 0
2024/02/22-06:16:45.889596 69         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-06:16:45.889598 69         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:16:45.889600 69         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-06:16:45.889602 69                  Options.bottommost_compression_opts.enabled: false
2024/02/22-06:16:45.889604 69         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:16:45.889606 69            Options.compression_opts.window_bits: -14
2024/02/22-06:16:45.889608 69                  Options.compression_opts.level: 32767
2024/02/22-06:16:45.889610 69               Options.compression_opts.strategy: 0
2024/02/22-06:16:45.889612 69         Options.compression_opts.max_dict_bytes: 0
2024/02/22-06:16:45.889614 69         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:16:45.889616 69         Options.compression_opts.parallel_threads: 1
2024/02/22-06:16:45.889641 69                  Options.compression_opts.enabled: false
2024/02/22-06:16:45.889644 69         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:16:45.889646 69      Options.level0_file_num_compaction_trigger: 4
2024/02/22-06:16:45.889648 69          Options.level0_slowdown_writes_trigger: 20
2024/02/22-06:16:45.889650 69              Options.level0_stop_writes_trigger: 36
2024/02/22-06:16:45.889652 69                   Options.target_file_size_base: 67108864
2024/02/22-06:16:45.889654 69             Options.target_file_size_multiplier: 2
2024/02/22-06:16:45.889656 69                Options.max_bytes_for_level_base: 268435456
2024/02/22-06:16:45.889658 69 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-06:16:45.889660 69          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-06:16:45.889664 69 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-06:16:45.889667 69 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-06:16:45.889669 69 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-06:16:45.889671 69 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-06:16:45.889673 69 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-06:16:45.889675 69 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-06:16:45.889676 69 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-06:16:45.889678 69       Options.max_sequential_skip_in_iterations: 8
2024/02/22-06:16:45.889681 69                    Options.max_compaction_bytes: 1677721600
2024/02/22-06:16:45.889683 69                        Options.arena_block_size: 1048576
2024/02/22-06:16:45.889685 69   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-06:16:45.889687 69   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-06:16:45.889689 69       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-06:16:45.889691 69                Options.disable_auto_compactions: 0
2024/02/22-06:16:45.889693 69                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-06:16:45.889696 69                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-06:16:45.889698 69 Options.compaction_options_universal.size_ratio: 1
2024/02/22-06:16:45.889700 69 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-06:16:45.889702 69 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-06:16:45.889704 69 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-06:16:45.889706 69 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-06:16:45.889708 69 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-06:16:45.889710 69 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-06:16:45.889712 69 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-06:16:45.889720 69                   Options.table_properties_collectors: 
2024/02/22-06:16:45.889722 69                   Options.inplace_update_support: 0
2024/02/22-06:16:45.889724 69                 Options.inplace_update_num_locks: 10000
2024/02/22-06:16:45.889726 69               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-06:16:45.889729 69               Options.memtable_whole_key_filtering: 0
2024/02/22-06:16:45.889731 69   Options.memtable_huge_page_size: 0
2024/02/22-06:16:45.889733 69                           Options.bloom_locality: 0
2024/02/22-06:16:45.889735 69                    Options.max_successive_merges: 0
2024/02/22-06:16:45.889737 69                Options.optimize_filters_for_hits: 0
2024/02/22-06:16:45.889739 69                Options.paranoid_file_checks: 0
2024/02/22-06:16:45.889741 69                Options.force_consistency_checks: 1
2024/02/22-06:16:45.889743 69                Options.report_bg_io_stats: 0
2024/02/22-06:16:45.889745 69                               Options.ttl: 2592000
2024/02/22-06:16:45.889747 69          Options.periodic_compaction_seconds: 0
2024/02/22-06:16:45.889770 69                       Options.enable_blob_files: false
2024/02/22-06:16:45.889773 69                           Options.min_blob_size: 0
2024/02/22-06:16:45.889775 69                          Options.blob_file_size: 268435456
2024/02/22-06:16:45.889777 69                   Options.blob_compression_type: NoCompression
2024/02/22-06:16:45.889779 69          Options.enable_blob_garbage_collection: false
2024/02/22-06:16:45.889781 69      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-06:16:45.889784 69 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-06:16:45.889786 69          Options.blob_compaction_readahead_size: 0
2024/02/22-06:16:45.889911 69 [db/column_family.cc:605] --------------- Options for column family [properties]:
2024/02/22-06:16:45.889914 69               Options.comparator: leveldb.BytewiseComparator
2024/02/22-06:16:45.889916 69           Options.merge_operator: None
2024/02/22-06:16:45.889918 69        Options.compaction_filter: None
2024/02/22-06:16:45.889920 69        Options.compaction_filter_factory: None
2024/02/22-06:16:45.889922 69  Options.sst_partitioner_factory: None
2024/02/22-06:16:45.889924 69         Options.memtable_factory: SkipListFactory
2024/02/22-06:16:45.889927 69            Options.table_factory: BlockBasedTable
2024/02/22-06:16:45.889950 69            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1f28e00f00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f1f28e07010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-06:16:45.889959 69        Options.write_buffer_size: 67108864
2024/02/22-06:16:45.889961 69  Options.max_write_buffer_number: 2
2024/02/22-06:16:45.889963 69        Options.compression[0]: NoCompression
2024/02/22-06:16:45.889966 69        Options.compression[1]: NoCompression
2024/02/22-06:16:45.889968 69        Options.compression[2]: ZSTD
2024/02/22-06:16:45.889971 69        Options.compression[3]: ZSTD
2024/02/22-06:16:45.889973 69        Options.compression[4]: ZSTD
2024/02/22-06:16:45.889975 69                  Options.bottommost_compression: Disabled
2024/02/22-06:16:45.889977 69       Options.prefix_extractor: nullptr
2024/02/22-06:16:45.889979 69   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-06:16:45.889981 69             Options.num_levels: 5
2024/02/22-06:16:45.889983 69        Options.min_write_buffer_number_to_merge: 1
2024/02/22-06:16:45.889985 69     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-06:16:45.889987 69     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-06:16:45.889989 69            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-06:16:45.889991 69                  Options.bottommost_compression_opts.level: 32767
2024/02/22-06:16:45.889993 69               Options.bottommost_compression_opts.strategy: 0
2024/02/22-06:16:45.889995 69         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-06:16:45.889997 69         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:16:45.890020 69         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-06:16:45.890023 69                  Options.bottommost_compression_opts.enabled: false
2024/02/22-06:16:45.890025 69         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:16:45.890027 69            Options.compression_opts.window_bits: -14
2024/02/22-06:16:45.890029 69                  Options.compression_opts.level: 32767
2024/02/22-06:16:45.890031 69               Options.compression_opts.strategy: 0
2024/02/22-06:16:45.890033 69         Options.compression_opts.max_dict_bytes: 0
2024/02/22-06:16:45.890035 69         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:16:45.890037 69         Options.compression_opts.parallel_threads: 1
2024/02/22-06:16:45.890039 69                  Options.compression_opts.enabled: false
2024/02/22-06:16:45.890041 69         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:16:45.890043 69      Options.level0_file_num_compaction_trigger: 4
2024/02/22-06:16:45.890045 69          Options.level0_slowdown_writes_trigger: 20
2024/02/22-06:16:45.890047 69              Options.level0_stop_writes_trigger: 36
2024/02/22-06:16:45.890049 69                   Options.target_file_size_base: 67108864
2024/02/22-06:16:45.890051 69             Options.target_file_size_multiplier: 2
2024/02/22-06:16:45.890053 69                Options.max_bytes_for_level_base: 268435456
2024/02/22-06:16:45.890055 69 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-06:16:45.890057 69          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-06:16:45.890060 69 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-06:16:45.890062 69 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-06:16:45.890064 69 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-06:16:45.890066 69 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-06:16:45.890068 69 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-06:16:45.890070 69 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-06:16:45.890072 69 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-06:16:45.890074 69       Options.max_sequential_skip_in_iterations: 8
2024/02/22-06:16:45.890076 69                    Options.max_compaction_bytes: 1677721600
2024/02/22-06:16:45.890078 69                        Options.arena_block_size: 1048576
2024/02/22-06:16:45.890080 69   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-06:16:45.890082 69   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-06:16:45.890084 69       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-06:16:45.890086 69                Options.disable_auto_compactions: 0
2024/02/22-06:16:45.890088 69                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-06:16:45.890091 69                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-06:16:45.890093 69 Options.compaction_options_universal.size_ratio: 1
2024/02/22-06:16:45.890095 69 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-06:16:45.890097 69 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-06:16:45.890099 69 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-06:16:45.890101 69 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-06:16:45.890103 69 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-06:16:45.890105 69 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-06:16:45.890107 69 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-06:16:45.890111 69                   Options.table_properties_collectors: 
2024/02/22-06:16:45.890113 69                   Options.inplace_update_support: 0
2024/02/22-06:16:45.890115 69                 Options.inplace_update_num_locks: 10000
2024/02/22-06:16:45.890117 69               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-06:16:45.890141 69               Options.memtable_whole_key_filtering: 0
2024/02/22-06:16:45.890145 69   Options.memtable_huge_page_size: 0
2024/02/22-06:16:45.890148 69                           Options.bloom_locality: 0
2024/02/22-06:16:45.890150 69                    Options.max_successive_merges: 0
2024/02/22-06:16:45.890152 69                Options.optimize_filters_for_hits: 0
2024/02/22-06:16:45.890154 69                Options.paranoid_file_checks: 0
2024/02/22-06:16:45.890156 69                Options.force_consistency_checks: 1
2024/02/22-06:16:45.890158 69                Options.report_bg_io_stats: 0
2024/02/22-06:16:45.890160 69                               Options.ttl: 2592000
2024/02/22-06:16:45.890162 69          Options.periodic_compaction_seconds: 0
2024/02/22-06:16:45.890163 69                       Options.enable_blob_files: false
2024/02/22-06:16:45.890166 69                           Options.min_blob_size: 0
2024/02/22-06:16:45.890168 69                          Options.blob_file_size: 268435456
2024/02/22-06:16:45.890170 69                   Options.blob_compression_type: NoCompression
2024/02/22-06:16:45.890172 69          Options.enable_blob_garbage_collection: false
2024/02/22-06:16:45.890174 69      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-06:16:45.890176 69 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-06:16:45.890179 69          Options.blob_compaction_readahead_size: 0
2024/02/22-06:16:45.896644 69 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000074 succeeded,manifest_file_number is 74, next_file_number is 76, last_sequence is 426300, log_number is 66,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 22
2024/02/22-06:16:45.896655 69 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 66
2024/02/22-06:16:45.896659 69 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 66
2024/02/22-06:16:45.896831 69 [db/version_set.cc:4409] Creating manifest 78
2024/02/22-06:16:45.898391 69 EVENT_LOG_v1 {"time_micros": 1708582605898384, "job": 1, "event": "recovery_started", "wal_files": [75]}
2024/02/22-06:16:45.898399 69 [db/db_impl/db_impl_open.cc:888] Recovering log #75 mode 2
2024/02/22-06:16:45.899748 69 EVENT_LOG_v1 {"time_micros": 1708582605899715, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 79, "file_size": 9641, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 8663, "index_size": 59, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 13230, "raw_average_key_size": 49, "raw_value_size": 4590, "raw_average_value_size": 17, "num_data_blocks": 1, "num_entries": 270, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708582605, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "UGRLOJ85HPN78EC2Q1X6", "orig_file_number": 79}}
2024/02/22-06:16:45.899820 69 [db/version_set.cc:4409] Creating manifest 80
2024/02/22-06:16:45.901205 69 EVENT_LOG_v1 {"time_micros": 1708582605901201, "job": 1, "event": "recovery_finished"}
2024/02/22-06:16:45.905423 69 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000075.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:16:45.905481 69 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f1f28e55700
2024/02/22-06:16:45.905566 69 DB pointer 0x7f1f28e3cc00
2024/02/22-06:16:45.905644 81 (Original Log Time 2024/02/22-06:16:45.905608) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/22-06:16:48.906012 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-06:16:48.906041 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 26 writes, 26 keys, 24 commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 26 writes, 0 syncs, 26.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 26 writes, 26 keys, 24 commit groups, 1.1 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 26 writes, 0 syncs, 26.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   18.54 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      9.3      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      2/0   113.36 MB   0.4      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0   113.37 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      9.3      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      9.3      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      9.3      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f1f28e07010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 0.000105 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f1f28e07010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 0.000105 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
