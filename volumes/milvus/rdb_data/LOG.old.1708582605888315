2024/02/22-06:15:15.721208 65 RocksDB version: 6.29.5
2024/02/22-06:15:15.721309 65 Git sha 0
2024/02/22-06:15:15.721313 65 Compile date 2023-11-07 23:58:42
2024/02/22-06:15:15.721316 65 DB SUMMARY
2024/02/22-06:15:15.721318 65 DB Session ID:  JVQXPWUKC85GPMLYUJYF
2024/02/22-06:15:15.721412 65 CURRENT file:  CURRENT
2024/02/22-06:15:15.721415 65 IDENTITY file:  IDENTITY
2024/02/22-06:15:15.721429 65 MANIFEST file:  MANIFEST-000064 size: 1280 Bytes
2024/02/22-06:15:15.721433 65 SST files in /var/lib/milvus/rdb_data dir, Total Num: 2, files: 000068.sst 000069.sst 
2024/02/22-06:15:15.721436 65 Write Ahead Log file in /var/lib/milvus/rdb_data: 000065.log size: 20458 ; 
2024/02/22-06:15:15.721439 65                         Options.error_if_exists: 0
2024/02/22-06:15:15.721441 65                       Options.create_if_missing: 1
2024/02/22-06:15:15.721444 65                         Options.paranoid_checks: 1
2024/02/22-06:15:15.721446 65             Options.flush_verify_memtable_count: 1
2024/02/22-06:15:15.721448 65                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-06:15:15.721450 65                                     Options.env: 0x7fc799adb2c0
2024/02/22-06:15:15.721453 65                                      Options.fs: PosixFileSystem
2024/02/22-06:15:15.721455 65                                Options.info_log: 0x7fc5d2050140
2024/02/22-06:15:15.721457 65                Options.max_file_opening_threads: 16
2024/02/22-06:15:15.721459 65                              Options.statistics: (nil)
2024/02/22-06:15:15.721462 65                               Options.use_fsync: 0
2024/02/22-06:15:15.721464 65                       Options.max_log_file_size: 0
2024/02/22-06:15:15.721466 65                  Options.max_manifest_file_size: 1073741824
2024/02/22-06:15:15.721468 65                   Options.log_file_time_to_roll: 0
2024/02/22-06:15:15.721471 65                       Options.keep_log_file_num: 1000
2024/02/22-06:15:15.721473 65                    Options.recycle_log_file_num: 0
2024/02/22-06:15:15.721475 65                         Options.allow_fallocate: 1
2024/02/22-06:15:15.721477 65                        Options.allow_mmap_reads: 0
2024/02/22-06:15:15.721479 65                       Options.allow_mmap_writes: 0
2024/02/22-06:15:15.721481 65                        Options.use_direct_reads: 0
2024/02/22-06:15:15.721483 65                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-06:15:15.721485 65          Options.create_missing_column_families: 1
2024/02/22-06:15:15.721487 65                              Options.db_log_dir: 
2024/02/22-06:15:15.721489 65                                 Options.wal_dir: 
2024/02/22-06:15:15.721491 65                Options.table_cache_numshardbits: 6
2024/02/22-06:15:15.721493 65                         Options.WAL_ttl_seconds: 0
2024/02/22-06:15:15.721495 65                       Options.WAL_size_limit_MB: 0
2024/02/22-06:15:15.721497 65                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-06:15:15.721499 65             Options.manifest_preallocation_size: 4194304
2024/02/22-06:15:15.721501 65                     Options.is_fd_close_on_exec: 1
2024/02/22-06:15:15.721503 65                   Options.advise_random_on_open: 1
2024/02/22-06:15:15.721505 65                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-06:15:15.721509 65                    Options.db_write_buffer_size: 0
2024/02/22-06:15:15.721512 65                    Options.write_buffer_manager: 0x7fc5d2007280
2024/02/22-06:15:15.721514 65         Options.access_hint_on_compaction_start: 1
2024/02/22-06:15:15.721516 65  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-06:15:15.721518 65           Options.random_access_max_buffer_size: 1048576
2024/02/22-06:15:15.721520 65                      Options.use_adaptive_mutex: 0
2024/02/22-06:15:15.721522 65                            Options.rate_limiter: (nil)
2024/02/22-06:15:15.721524 65     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-06:15:15.721526 65                       Options.wal_recovery_mode: 2
2024/02/22-06:15:15.721555 65                  Options.enable_thread_tracking: 0
2024/02/22-06:15:15.721557 65                  Options.enable_pipelined_write: 0
2024/02/22-06:15:15.721559 65                  Options.unordered_write: 0
2024/02/22-06:15:15.721561 65         Options.allow_concurrent_memtable_write: 1
2024/02/22-06:15:15.721563 65      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-06:15:15.721565 65             Options.write_thread_max_yield_usec: 100
2024/02/22-06:15:15.721567 65            Options.write_thread_slow_yield_usec: 3
2024/02/22-06:15:15.721569 65                               Options.row_cache: None
2024/02/22-06:15:15.721572 65                              Options.wal_filter: None
2024/02/22-06:15:15.721574 65             Options.avoid_flush_during_recovery: 0
2024/02/22-06:15:15.721577 65             Options.allow_ingest_behind: 0
2024/02/22-06:15:15.721579 65             Options.preserve_deletes: 0
2024/02/22-06:15:15.721581 65             Options.two_write_queues: 0
2024/02/22-06:15:15.721583 65             Options.manual_wal_flush: 0
2024/02/22-06:15:15.721585 65             Options.atomic_flush: 0
2024/02/22-06:15:15.721587 65             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-06:15:15.721590 65                 Options.persist_stats_to_disk: 0
2024/02/22-06:15:15.721592 65                 Options.write_dbid_to_manifest: 0
2024/02/22-06:15:15.721594 65                 Options.log_readahead_size: 0
2024/02/22-06:15:15.721596 65                 Options.file_checksum_gen_factory: Unknown
2024/02/22-06:15:15.721598 65                 Options.best_efforts_recovery: 0
2024/02/22-06:15:15.721601 65                Options.max_bgerror_resume_count: 2147483647
2024/02/22-06:15:15.721603 65            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-06:15:15.721605 65             Options.allow_data_in_errors: 0
2024/02/22-06:15:15.721607 65             Options.db_host_id: __hostname__
2024/02/22-06:15:15.721609 65             Options.max_background_jobs: 4
2024/02/22-06:15:15.721612 65             Options.max_background_compactions: -1
2024/02/22-06:15:15.721614 65             Options.max_subcompactions: 1
2024/02/22-06:15:15.721616 65             Options.avoid_flush_during_shutdown: 0
2024/02/22-06:15:15.721618 65           Options.writable_file_max_buffer_size: 1048576
2024/02/22-06:15:15.721620 65             Options.delayed_write_rate : 16777216
2024/02/22-06:15:15.721623 65             Options.max_total_wal_size: 0
2024/02/22-06:15:15.721625 65             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-06:15:15.721627 65                   Options.stats_dump_period_sec: 600
2024/02/22-06:15:15.721629 65                 Options.stats_persist_period_sec: 600
2024/02/22-06:15:15.721631 65                 Options.stats_history_buffer_size: 1048576
2024/02/22-06:15:15.721634 65                          Options.max_open_files: -1
2024/02/22-06:15:15.721636 65                          Options.bytes_per_sync: 0
2024/02/22-06:15:15.721638 65                      Options.wal_bytes_per_sync: 0
2024/02/22-06:15:15.721640 65                   Options.strict_bytes_per_sync: 0
2024/02/22-06:15:15.721643 65       Options.compaction_readahead_size: 0
2024/02/22-06:15:15.721645 65                  Options.max_background_flushes: 1
2024/02/22-06:15:15.721647 65 Compression algorithms supported:
2024/02/22-06:15:15.721649 65 	kZSTDNotFinalCompression supported: 1
2024/02/22-06:15:15.721651 65 	kZSTD supported: 1
2024/02/22-06:15:15.721654 65 	kXpressCompression supported: 0
2024/02/22-06:15:15.721656 65 	kLZ4HCCompression supported: 0
2024/02/22-06:15:15.721658 65 	kLZ4Compression supported: 0
2024/02/22-06:15:15.721660 65 	kBZip2Compression supported: 0
2024/02/22-06:15:15.721662 65 	kZlibCompression supported: 0
2024/02/22-06:15:15.721664 65 	kSnappyCompression supported: 0
2024/02/22-06:15:15.721667 65 Fast CRC32 supported: Not supported on x86
2024/02/22-06:15:15.721835 65 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000064
2024/02/22-06:15:15.722165 65 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-06:15:15.722169 65               Options.comparator: leveldb.BytewiseComparator
2024/02/22-06:15:15.722171 65           Options.merge_operator: None
2024/02/22-06:15:15.722173 65        Options.compaction_filter: None
2024/02/22-06:15:15.722175 65        Options.compaction_filter_factory: None
2024/02/22-06:15:15.722177 65  Options.sst_partitioner_factory: None
2024/02/22-06:15:15.722179 65         Options.memtable_factory: SkipListFactory
2024/02/22-06:15:15.722181 65            Options.table_factory: BlockBasedTable
2024/02/22-06:15:15.722210 65            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fc5d209e740)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fc5d2007010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-06:15:15.722214 65        Options.write_buffer_size: 67108864
2024/02/22-06:15:15.722217 65  Options.max_write_buffer_number: 2
2024/02/22-06:15:15.722219 65        Options.compression[0]: NoCompression
2024/02/22-06:15:15.722222 65        Options.compression[1]: NoCompression
2024/02/22-06:15:15.722224 65        Options.compression[2]: ZSTD
2024/02/22-06:15:15.722226 65        Options.compression[3]: ZSTD
2024/02/22-06:15:15.722228 65        Options.compression[4]: ZSTD
2024/02/22-06:15:15.722230 65                  Options.bottommost_compression: Disabled
2024/02/22-06:15:15.722232 65       Options.prefix_extractor: nullptr
2024/02/22-06:15:15.722234 65   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-06:15:15.722236 65             Options.num_levels: 5
2024/02/22-06:15:15.722238 65        Options.min_write_buffer_number_to_merge: 1
2024/02/22-06:15:15.722240 65     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-06:15:15.722242 65     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-06:15:15.722244 65            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-06:15:15.722246 65                  Options.bottommost_compression_opts.level: 32767
2024/02/22-06:15:15.722248 65               Options.bottommost_compression_opts.strategy: 0
2024/02/22-06:15:15.722250 65         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-06:15:15.722253 65         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:15:15.722255 65         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-06:15:15.722257 65                  Options.bottommost_compression_opts.enabled: false
2024/02/22-06:15:15.722259 65         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:15:15.722261 65            Options.compression_opts.window_bits: -14
2024/02/22-06:15:15.722263 65                  Options.compression_opts.level: 32767
2024/02/22-06:15:15.722265 65               Options.compression_opts.strategy: 0
2024/02/22-06:15:15.722267 65         Options.compression_opts.max_dict_bytes: 0
2024/02/22-06:15:15.722269 65         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:15:15.722271 65         Options.compression_opts.parallel_threads: 1
2024/02/22-06:15:15.722302 65                  Options.compression_opts.enabled: false
2024/02/22-06:15:15.722304 65         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:15:15.722307 65      Options.level0_file_num_compaction_trigger: 4
2024/02/22-06:15:15.722309 65          Options.level0_slowdown_writes_trigger: 20
2024/02/22-06:15:15.722311 65              Options.level0_stop_writes_trigger: 36
2024/02/22-06:15:15.722313 65                   Options.target_file_size_base: 67108864
2024/02/22-06:15:15.722315 65             Options.target_file_size_multiplier: 2
2024/02/22-06:15:15.722317 65                Options.max_bytes_for_level_base: 268435456
2024/02/22-06:15:15.722319 65 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-06:15:15.722322 65          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-06:15:15.722326 65 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-06:15:15.722328 65 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-06:15:15.722330 65 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-06:15:15.722332 65 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-06:15:15.722334 65 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-06:15:15.722336 65 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-06:15:15.722338 65 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-06:15:15.722340 65       Options.max_sequential_skip_in_iterations: 8
2024/02/22-06:15:15.722342 65                    Options.max_compaction_bytes: 1677721600
2024/02/22-06:15:15.722344 65                        Options.arena_block_size: 1048576
2024/02/22-06:15:15.722346 65   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-06:15:15.722348 65   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-06:15:15.722350 65       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-06:15:15.722353 65                Options.disable_auto_compactions: 0
2024/02/22-06:15:15.722355 65                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-06:15:15.722358 65                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-06:15:15.722360 65 Options.compaction_options_universal.size_ratio: 1
2024/02/22-06:15:15.722362 65 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-06:15:15.722364 65 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-06:15:15.722366 65 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-06:15:15.722368 65 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-06:15:15.722370 65 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-06:15:15.722373 65 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-06:15:15.722375 65 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-06:15:15.722381 65                   Options.table_properties_collectors: 
2024/02/22-06:15:15.722383 65                   Options.inplace_update_support: 0
2024/02/22-06:15:15.722385 65                 Options.inplace_update_num_locks: 10000
2024/02/22-06:15:15.722387 65               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-06:15:15.722390 65               Options.memtable_whole_key_filtering: 0
2024/02/22-06:15:15.722392 65   Options.memtable_huge_page_size: 0
2024/02/22-06:15:15.722394 65                           Options.bloom_locality: 0
2024/02/22-06:15:15.722396 65                    Options.max_successive_merges: 0
2024/02/22-06:15:15.722398 65                Options.optimize_filters_for_hits: 0
2024/02/22-06:15:15.722400 65                Options.paranoid_file_checks: 0
2024/02/22-06:15:15.722402 65                Options.force_consistency_checks: 1
2024/02/22-06:15:15.722405 65                Options.report_bg_io_stats: 0
2024/02/22-06:15:15.722407 65                               Options.ttl: 2592000
2024/02/22-06:15:15.722409 65          Options.periodic_compaction_seconds: 0
2024/02/22-06:15:15.722438 65                       Options.enable_blob_files: false
2024/02/22-06:15:15.722441 65                           Options.min_blob_size: 0
2024/02/22-06:15:15.722443 65                          Options.blob_file_size: 268435456
2024/02/22-06:15:15.722445 65                   Options.blob_compression_type: NoCompression
2024/02/22-06:15:15.722447 65          Options.enable_blob_garbage_collection: false
2024/02/22-06:15:15.722449 65      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-06:15:15.722452 65 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-06:15:15.722454 65          Options.blob_compaction_readahead_size: 0
2024/02/22-06:15:15.722598 65 [db/column_family.cc:605] --------------- Options for column family [properties]:
2024/02/22-06:15:15.722602 65               Options.comparator: leveldb.BytewiseComparator
2024/02/22-06:15:15.722604 65           Options.merge_operator: None
2024/02/22-06:15:15.722606 65        Options.compaction_filter: None
2024/02/22-06:15:15.722608 65        Options.compaction_filter_factory: None
2024/02/22-06:15:15.722610 65  Options.sst_partitioner_factory: None
2024/02/22-06:15:15.722612 65         Options.memtable_factory: SkipListFactory
2024/02/22-06:15:15.722615 65            Options.table_factory: BlockBasedTable
2024/02/22-06:15:15.722638 65            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fc5d209e740)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fc5d2007010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-06:15:15.722650 65        Options.write_buffer_size: 67108864
2024/02/22-06:15:15.722653 65  Options.max_write_buffer_number: 2
2024/02/22-06:15:15.722655 65        Options.compression[0]: NoCompression
2024/02/22-06:15:15.722657 65        Options.compression[1]: NoCompression
2024/02/22-06:15:15.722660 65        Options.compression[2]: ZSTD
2024/02/22-06:15:15.722662 65        Options.compression[3]: ZSTD
2024/02/22-06:15:15.722664 65        Options.compression[4]: ZSTD
2024/02/22-06:15:15.722666 65                  Options.bottommost_compression: Disabled
2024/02/22-06:15:15.722668 65       Options.prefix_extractor: nullptr
2024/02/22-06:15:15.722671 65   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-06:15:15.722673 65             Options.num_levels: 5
2024/02/22-06:15:15.722675 65        Options.min_write_buffer_number_to_merge: 1
2024/02/22-06:15:15.722677 65     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-06:15:15.722679 65     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-06:15:15.722681 65            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-06:15:15.722683 65                  Options.bottommost_compression_opts.level: 32767
2024/02/22-06:15:15.722685 65               Options.bottommost_compression_opts.strategy: 0
2024/02/22-06:15:15.722687 65         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-06:15:15.722689 65         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:15:15.722716 65         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-06:15:15.722719 65                  Options.bottommost_compression_opts.enabled: false
2024/02/22-06:15:15.722721 65         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:15:15.722723 65            Options.compression_opts.window_bits: -14
2024/02/22-06:15:15.722725 65                  Options.compression_opts.level: 32767
2024/02/22-06:15:15.722727 65               Options.compression_opts.strategy: 0
2024/02/22-06:15:15.722729 65         Options.compression_opts.max_dict_bytes: 0
2024/02/22-06:15:15.722731 65         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:15:15.722733 65         Options.compression_opts.parallel_threads: 1
2024/02/22-06:15:15.722735 65                  Options.compression_opts.enabled: false
2024/02/22-06:15:15.722737 65         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:15:15.722739 65      Options.level0_file_num_compaction_trigger: 4
2024/02/22-06:15:15.722741 65          Options.level0_slowdown_writes_trigger: 20
2024/02/22-06:15:15.722743 65              Options.level0_stop_writes_trigger: 36
2024/02/22-06:15:15.722745 65                   Options.target_file_size_base: 67108864
2024/02/22-06:15:15.722747 65             Options.target_file_size_multiplier: 2
2024/02/22-06:15:15.722749 65                Options.max_bytes_for_level_base: 268435456
2024/02/22-06:15:15.722751 65 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-06:15:15.722753 65          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-06:15:15.722756 65 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-06:15:15.722758 65 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-06:15:15.722760 65 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-06:15:15.722762 65 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-06:15:15.722764 65 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-06:15:15.722766 65 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-06:15:15.722768 65 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-06:15:15.722770 65       Options.max_sequential_skip_in_iterations: 8
2024/02/22-06:15:15.722772 65                    Options.max_compaction_bytes: 1677721600
2024/02/22-06:15:15.722774 65                        Options.arena_block_size: 1048576
2024/02/22-06:15:15.722776 65   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-06:15:15.722778 65   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-06:15:15.722780 65       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-06:15:15.722782 65                Options.disable_auto_compactions: 0
2024/02/22-06:15:15.722785 65                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-06:15:15.722787 65                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-06:15:15.722789 65 Options.compaction_options_universal.size_ratio: 1
2024/02/22-06:15:15.722791 65 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-06:15:15.722793 65 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-06:15:15.722795 65 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-06:15:15.722797 65 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-06:15:15.722799 65 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-06:15:15.722801 65 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-06:15:15.722803 65 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-06:15:15.722807 65                   Options.table_properties_collectors: 
2024/02/22-06:15:15.722809 65                   Options.inplace_update_support: 0
2024/02/22-06:15:15.722811 65                 Options.inplace_update_num_locks: 10000
2024/02/22-06:15:15.722813 65               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-06:15:15.722841 65               Options.memtable_whole_key_filtering: 0
2024/02/22-06:15:15.722843 65   Options.memtable_huge_page_size: 0
2024/02/22-06:15:15.722845 65                           Options.bloom_locality: 0
2024/02/22-06:15:15.722847 65                    Options.max_successive_merges: 0
2024/02/22-06:15:15.722849 65                Options.optimize_filters_for_hits: 0
2024/02/22-06:15:15.722851 65                Options.paranoid_file_checks: 0
2024/02/22-06:15:15.722853 65                Options.force_consistency_checks: 1
2024/02/22-06:15:15.722855 65                Options.report_bg_io_stats: 0
2024/02/22-06:15:15.722857 65                               Options.ttl: 2592000
2024/02/22-06:15:15.722859 65          Options.periodic_compaction_seconds: 0
2024/02/22-06:15:15.722861 65                       Options.enable_blob_files: false
2024/02/22-06:15:15.722863 65                           Options.min_blob_size: 0
2024/02/22-06:15:15.722865 65                          Options.blob_file_size: 268435456
2024/02/22-06:15:15.722868 65                   Options.blob_compression_type: NoCompression
2024/02/22-06:15:15.722870 65          Options.enable_blob_garbage_collection: false
2024/02/22-06:15:15.722872 65      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-06:15:15.722874 65 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-06:15:15.722877 65          Options.blob_compaction_readahead_size: 0
2024/02/22-06:15:15.729037 65 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000064 succeeded,manifest_file_number is 64, next_file_number is 71, last_sequence is 426040, log_number is 60,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 22
2024/02/22-06:15:15.729046 65 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 60
2024/02/22-06:15:15.729049 65 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 60
2024/02/22-06:15:15.729208 65 [db/version_set.cc:4409] Creating manifest 72
2024/02/22-06:15:15.730859 65 EVENT_LOG_v1 {"time_micros": 1708582515730853, "job": 1, "event": "recovery_started", "wal_files": [65]}
2024/02/22-06:15:15.730867 65 [db/db_impl/db_impl_open.cc:888] Recovering log #65 mode 2
2024/02/22-06:15:15.732205 65 EVENT_LOG_v1 {"time_micros": 1708582515732174, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 73, "file_size": 9349, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 8371, "index_size": 59, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 12740, "raw_average_key_size": 49, "raw_value_size": 4420, "raw_average_value_size": 17, "num_data_blocks": 1, "num_entries": 260, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708582515, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "JVQXPWUKC85GPMLYUJYF", "orig_file_number": 73}}
2024/02/22-06:15:15.732269 65 [db/version_set.cc:4409] Creating manifest 74
2024/02/22-06:15:15.733691 65 EVENT_LOG_v1 {"time_micros": 1708582515733685, "job": 1, "event": "recovery_finished"}
2024/02/22-06:15:15.739963 65 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000065.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:15:15.740028 65 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fc5d2055700
2024/02/22-06:15:15.740103 82 (Original Log Time 2024/02/22-06:15:15.740095) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/22-06:15:15.740110 65 DB pointer 0x7fc5d203cc00
2024/02/22-06:15:18.740574 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-06:15:18.740620 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 28 writes, 28 keys, 27 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 28 writes, 0 syncs, 28.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 28 writes, 28 keys, 27 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 28 writes, 0 syncs, 28.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    9.13 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      8.9      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      2/0   113.36 MB   0.4      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0   113.36 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      8.9      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      8.9      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      8.9      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fc5d2007010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 0.000109 secs_since: 3
Block cache entry stats(count,size,portion): Misc(2,0.19 KB,4.47035e-06%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fc5d2007010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 0.000109 secs_since: 3
Block cache entry stats(count,size,portion): Misc(2,0.19 KB,4.47035e-06%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
