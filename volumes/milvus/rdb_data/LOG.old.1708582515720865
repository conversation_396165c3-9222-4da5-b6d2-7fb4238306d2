2024/02/22-06:10:58.614704 73 RocksDB version: 6.29.5
2024/02/22-06:10:58.614790 73 Git sha 0
2024/02/22-06:10:58.614793 73 Compile date 2023-11-07 23:58:42
2024/02/22-06:10:58.614796 73 DB SUMMARY
2024/02/22-06:10:58.614798 73 DB Session ID:  4X2Y90AXFQ7RZCQG7GE9
2024/02/22-06:10:58.614896 73 CURRENT file:  CURRENT
2024/02/22-06:10:58.614900 73 IDENTITY file:  IDENTITY
2024/02/22-06:10:58.614914 73 MANIFEST file:  MANIFEST-000058 size: 846 Bytes
2024/02/22-06:10:58.614917 73 SST files in /var/lib/milvus/rdb_data dir, Total Num: 5, files: 000040.sst 000041.sst 000045.sst 000051.sst 000057.sst 
2024/02/22-06:10:58.614920 73 Write Ahead Log file in /var/lib/milvus/rdb_data: 000059.log size: 19422 ; 
2024/02/22-06:10:58.614923 73                         Options.error_if_exists: 0
2024/02/22-06:10:58.614926 73                       Options.create_if_missing: 1
2024/02/22-06:10:58.614928 73                         Options.paranoid_checks: 1
2024/02/22-06:10:58.614930 73             Options.flush_verify_memtable_count: 1
2024/02/22-06:10:58.614932 73                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-06:10:58.614934 73                                     Options.env: 0x7f2c5c1ac2c0
2024/02/22-06:10:58.614937 73                                      Options.fs: PosixFileSystem
2024/02/22-06:10:58.614939 73                                Options.info_log: 0x7f2a90650140
2024/02/22-06:10:58.614942 73                Options.max_file_opening_threads: 16
2024/02/22-06:10:58.614944 73                              Options.statistics: (nil)
2024/02/22-06:10:58.614946 73                               Options.use_fsync: 0
2024/02/22-06:10:58.614948 73                       Options.max_log_file_size: 0
2024/02/22-06:10:58.614951 73                  Options.max_manifest_file_size: 1073741824
2024/02/22-06:10:58.614953 73                   Options.log_file_time_to_roll: 0
2024/02/22-06:10:58.614959 73                       Options.keep_log_file_num: 1000
2024/02/22-06:10:58.614961 73                    Options.recycle_log_file_num: 0
2024/02/22-06:10:58.614963 73                         Options.allow_fallocate: 1
2024/02/22-06:10:58.614965 73                        Options.allow_mmap_reads: 0
2024/02/22-06:10:58.614967 73                       Options.allow_mmap_writes: 0
2024/02/22-06:10:58.614969 73                        Options.use_direct_reads: 0
2024/02/22-06:10:58.614971 73                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-06:10:58.614973 73          Options.create_missing_column_families: 1
2024/02/22-06:10:58.614975 73                              Options.db_log_dir: 
2024/02/22-06:10:58.614977 73                                 Options.wal_dir: 
2024/02/22-06:10:58.614979 73                Options.table_cache_numshardbits: 6
2024/02/22-06:10:58.614981 73                         Options.WAL_ttl_seconds: 0
2024/02/22-06:10:58.614983 73                       Options.WAL_size_limit_MB: 0
2024/02/22-06:10:58.614985 73                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-06:10:58.614988 73             Options.manifest_preallocation_size: 4194304
2024/02/22-06:10:58.614990 73                     Options.is_fd_close_on_exec: 1
2024/02/22-06:10:58.614992 73                   Options.advise_random_on_open: 1
2024/02/22-06:10:58.614994 73                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-06:10:58.614998 73                    Options.db_write_buffer_size: 0
2024/02/22-06:10:58.615000 73                    Options.write_buffer_manager: 0x7f2a90607280
2024/02/22-06:10:58.615002 73         Options.access_hint_on_compaction_start: 1
2024/02/22-06:10:58.615004 73  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-06:10:58.615006 73           Options.random_access_max_buffer_size: 1048576
2024/02/22-06:10:58.615008 73                      Options.use_adaptive_mutex: 0
2024/02/22-06:10:58.615010 73                            Options.rate_limiter: (nil)
2024/02/22-06:10:58.615012 73     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-06:10:58.615014 73                       Options.wal_recovery_mode: 2
2024/02/22-06:10:58.615041 73                  Options.enable_thread_tracking: 0
2024/02/22-06:10:58.615043 73                  Options.enable_pipelined_write: 0
2024/02/22-06:10:58.615045 73                  Options.unordered_write: 0
2024/02/22-06:10:58.615047 73         Options.allow_concurrent_memtable_write: 1
2024/02/22-06:10:58.615049 73      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-06:10:58.615051 73             Options.write_thread_max_yield_usec: 100
2024/02/22-06:10:58.615053 73            Options.write_thread_slow_yield_usec: 3
2024/02/22-06:10:58.615055 73                               Options.row_cache: None
2024/02/22-06:10:58.615057 73                              Options.wal_filter: None
2024/02/22-06:10:58.615059 73             Options.avoid_flush_during_recovery: 0
2024/02/22-06:10:58.615061 73             Options.allow_ingest_behind: 0
2024/02/22-06:10:58.615063 73             Options.preserve_deletes: 0
2024/02/22-06:10:58.615065 73             Options.two_write_queues: 0
2024/02/22-06:10:58.615067 73             Options.manual_wal_flush: 0
2024/02/22-06:10:58.615069 73             Options.atomic_flush: 0
2024/02/22-06:10:58.615071 73             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-06:10:58.615073 73                 Options.persist_stats_to_disk: 0
2024/02/22-06:10:58.615075 73                 Options.write_dbid_to_manifest: 0
2024/02/22-06:10:58.615077 73                 Options.log_readahead_size: 0
2024/02/22-06:10:58.615079 73                 Options.file_checksum_gen_factory: Unknown
2024/02/22-06:10:58.615081 73                 Options.best_efforts_recovery: 0
2024/02/22-06:10:58.615083 73                Options.max_bgerror_resume_count: 2147483647
2024/02/22-06:10:58.615085 73            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-06:10:58.615088 73             Options.allow_data_in_errors: 0
2024/02/22-06:10:58.615089 73             Options.db_host_id: __hostname__
2024/02/22-06:10:58.615092 73             Options.max_background_jobs: 4
2024/02/22-06:10:58.615094 73             Options.max_background_compactions: -1
2024/02/22-06:10:58.615096 73             Options.max_subcompactions: 1
2024/02/22-06:10:58.615098 73             Options.avoid_flush_during_shutdown: 0
2024/02/22-06:10:58.615100 73           Options.writable_file_max_buffer_size: 1048576
2024/02/22-06:10:58.615102 73             Options.delayed_write_rate : 16777216
2024/02/22-06:10:58.615104 73             Options.max_total_wal_size: 0
2024/02/22-06:10:58.615106 73             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-06:10:58.615108 73                   Options.stats_dump_period_sec: 600
2024/02/22-06:10:58.615111 73                 Options.stats_persist_period_sec: 600
2024/02/22-06:10:58.615113 73                 Options.stats_history_buffer_size: 1048576
2024/02/22-06:10:58.615114 73                          Options.max_open_files: -1
2024/02/22-06:10:58.615117 73                          Options.bytes_per_sync: 0
2024/02/22-06:10:58.615119 73                      Options.wal_bytes_per_sync: 0
2024/02/22-06:10:58.615121 73                   Options.strict_bytes_per_sync: 0
2024/02/22-06:10:58.615123 73       Options.compaction_readahead_size: 0
2024/02/22-06:10:58.615125 73                  Options.max_background_flushes: 1
2024/02/22-06:10:58.615127 73 Compression algorithms supported:
2024/02/22-06:10:58.615129 73 	kZSTDNotFinalCompression supported: 1
2024/02/22-06:10:58.615131 73 	kZSTD supported: 1
2024/02/22-06:10:58.615133 73 	kXpressCompression supported: 0
2024/02/22-06:10:58.615135 73 	kLZ4HCCompression supported: 0
2024/02/22-06:10:58.615137 73 	kLZ4Compression supported: 0
2024/02/22-06:10:58.615140 73 	kBZip2Compression supported: 0
2024/02/22-06:10:58.615142 73 	kZlibCompression supported: 0
2024/02/22-06:10:58.615144 73 	kSnappyCompression supported: 0
2024/02/22-06:10:58.615147 73 Fast CRC32 supported: Not supported on x86
2024/02/22-06:10:58.615304 73 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000058
2024/02/22-06:10:58.615662 73 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-06:10:58.615667 73               Options.comparator: leveldb.BytewiseComparator
2024/02/22-06:10:58.615669 73           Options.merge_operator: None
2024/02/22-06:10:58.615671 73        Options.compaction_filter: None
2024/02/22-06:10:58.615673 73        Options.compaction_filter_factory: None
2024/02/22-06:10:58.615675 73  Options.sst_partitioner_factory: None
2024/02/22-06:10:58.615677 73         Options.memtable_factory: SkipListFactory
2024/02/22-06:10:58.615679 73            Options.table_factory: BlockBasedTable
2024/02/22-06:10:58.615710 73            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f2a9069e7c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f2a90607010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-06:10:58.615715 73        Options.write_buffer_size: 67108864
2024/02/22-06:10:58.615717 73  Options.max_write_buffer_number: 2
2024/02/22-06:10:58.615720 73        Options.compression[0]: NoCompression
2024/02/22-06:10:58.615722 73        Options.compression[1]: NoCompression
2024/02/22-06:10:58.615724 73        Options.compression[2]: ZSTD
2024/02/22-06:10:58.615726 73        Options.compression[3]: ZSTD
2024/02/22-06:10:58.615728 73        Options.compression[4]: ZSTD
2024/02/22-06:10:58.615730 73                  Options.bottommost_compression: Disabled
2024/02/22-06:10:58.615733 73       Options.prefix_extractor: nullptr
2024/02/22-06:10:58.615735 73   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-06:10:58.615737 73             Options.num_levels: 5
2024/02/22-06:10:58.615739 73        Options.min_write_buffer_number_to_merge: 1
2024/02/22-06:10:58.615741 73     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-06:10:58.615743 73     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-06:10:58.615745 73            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-06:10:58.615747 73                  Options.bottommost_compression_opts.level: 32767
2024/02/22-06:10:58.615749 73               Options.bottommost_compression_opts.strategy: 0
2024/02/22-06:10:58.615751 73         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-06:10:58.615753 73         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:10:58.615755 73         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-06:10:58.615757 73                  Options.bottommost_compression_opts.enabled: false
2024/02/22-06:10:58.615759 73         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:10:58.615761 73            Options.compression_opts.window_bits: -14
2024/02/22-06:10:58.615763 73                  Options.compression_opts.level: 32767
2024/02/22-06:10:58.615765 73               Options.compression_opts.strategy: 0
2024/02/22-06:10:58.615767 73         Options.compression_opts.max_dict_bytes: 0
2024/02/22-06:10:58.615769 73         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:10:58.615799 73         Options.compression_opts.parallel_threads: 1
2024/02/22-06:10:58.615801 73                  Options.compression_opts.enabled: false
2024/02/22-06:10:58.615803 73         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:10:58.615805 73      Options.level0_file_num_compaction_trigger: 4
2024/02/22-06:10:58.615807 73          Options.level0_slowdown_writes_trigger: 20
2024/02/22-06:10:58.615809 73              Options.level0_stop_writes_trigger: 36
2024/02/22-06:10:58.615811 73                   Options.target_file_size_base: 67108864
2024/02/22-06:10:58.615813 73             Options.target_file_size_multiplier: 2
2024/02/22-06:10:58.615815 73                Options.max_bytes_for_level_base: 268435456
2024/02/22-06:10:58.615817 73 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-06:10:58.615819 73          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-06:10:58.615823 73 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-06:10:58.615825 73 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-06:10:58.615828 73 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-06:10:58.615830 73 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-06:10:58.615831 73 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-06:10:58.615833 73 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-06:10:58.615835 73 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-06:10:58.615838 73       Options.max_sequential_skip_in_iterations: 8
2024/02/22-06:10:58.615840 73                    Options.max_compaction_bytes: 1677721600
2024/02/22-06:10:58.615842 73                        Options.arena_block_size: 1048576
2024/02/22-06:10:58.615844 73   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-06:10:58.615846 73   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-06:10:58.615848 73       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-06:10:58.615850 73                Options.disable_auto_compactions: 0
2024/02/22-06:10:58.615853 73                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-06:10:58.615855 73                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-06:10:58.615857 73 Options.compaction_options_universal.size_ratio: 1
2024/02/22-06:10:58.615859 73 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-06:10:58.615861 73 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-06:10:58.615863 73 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-06:10:58.615865 73 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-06:10:58.615868 73 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-06:10:58.615870 73 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-06:10:58.615872 73 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-06:10:58.615880 73                   Options.table_properties_collectors: 
2024/02/22-06:10:58.615882 73                   Options.inplace_update_support: 0
2024/02/22-06:10:58.615884 73                 Options.inplace_update_num_locks: 10000
2024/02/22-06:10:58.615887 73               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-06:10:58.615889 73               Options.memtable_whole_key_filtering: 0
2024/02/22-06:10:58.615891 73   Options.memtable_huge_page_size: 0
2024/02/22-06:10:58.615893 73                           Options.bloom_locality: 0
2024/02/22-06:10:58.615895 73                    Options.max_successive_merges: 0
2024/02/22-06:10:58.615897 73                Options.optimize_filters_for_hits: 0
2024/02/22-06:10:58.615899 73                Options.paranoid_file_checks: 0
2024/02/22-06:10:58.615901 73                Options.force_consistency_checks: 1
2024/02/22-06:10:58.615903 73                Options.report_bg_io_stats: 0
2024/02/22-06:10:58.615905 73                               Options.ttl: 2592000
2024/02/22-06:10:58.615928 73          Options.periodic_compaction_seconds: 0
2024/02/22-06:10:58.615931 73                       Options.enable_blob_files: false
2024/02/22-06:10:58.615933 73                           Options.min_blob_size: 0
2024/02/22-06:10:58.615935 73                          Options.blob_file_size: 268435456
2024/02/22-06:10:58.615937 73                   Options.blob_compression_type: NoCompression
2024/02/22-06:10:58.615939 73          Options.enable_blob_garbage_collection: false
2024/02/22-06:10:58.615941 73      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-06:10:58.615944 73 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-06:10:58.615946 73          Options.blob_compaction_readahead_size: 0
2024/02/22-06:10:58.616089 73 [db/column_family.cc:605] --------------- Options for column family [properties]:
2024/02/22-06:10:58.616092 73               Options.comparator: leveldb.BytewiseComparator
2024/02/22-06:10:58.616095 73           Options.merge_operator: None
2024/02/22-06:10:58.616097 73        Options.compaction_filter: None
2024/02/22-06:10:58.616099 73        Options.compaction_filter_factory: None
2024/02/22-06:10:58.616100 73  Options.sst_partitioner_factory: None
2024/02/22-06:10:58.616102 73         Options.memtable_factory: SkipListFactory
2024/02/22-06:10:58.616104 73            Options.table_factory: BlockBasedTable
2024/02/22-06:10:58.616127 73            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f2a9069e7c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f2a90607010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-06:10:58.616135 73        Options.write_buffer_size: 67108864
2024/02/22-06:10:58.616137 73  Options.max_write_buffer_number: 2
2024/02/22-06:10:58.616139 73        Options.compression[0]: NoCompression
2024/02/22-06:10:58.616142 73        Options.compression[1]: NoCompression
2024/02/22-06:10:58.616144 73        Options.compression[2]: ZSTD
2024/02/22-06:10:58.616146 73        Options.compression[3]: ZSTD
2024/02/22-06:10:58.616148 73        Options.compression[4]: ZSTD
2024/02/22-06:10:58.616150 73                  Options.bottommost_compression: Disabled
2024/02/22-06:10:58.616152 73       Options.prefix_extractor: nullptr
2024/02/22-06:10:58.616154 73   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-06:10:58.616156 73             Options.num_levels: 5
2024/02/22-06:10:58.616158 73        Options.min_write_buffer_number_to_merge: 1
2024/02/22-06:10:58.616160 73     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-06:10:58.616162 73     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-06:10:58.616164 73            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-06:10:58.616166 73                  Options.bottommost_compression_opts.level: 32767
2024/02/22-06:10:58.616168 73               Options.bottommost_compression_opts.strategy: 0
2024/02/22-06:10:58.616170 73         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-06:10:58.616172 73         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:10:58.616202 73         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-06:10:58.616204 73                  Options.bottommost_compression_opts.enabled: false
2024/02/22-06:10:58.616206 73         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:10:58.616208 73            Options.compression_opts.window_bits: -14
2024/02/22-06:10:58.616211 73                  Options.compression_opts.level: 32767
2024/02/22-06:10:58.616213 73               Options.compression_opts.strategy: 0
2024/02/22-06:10:58.616214 73         Options.compression_opts.max_dict_bytes: 0
2024/02/22-06:10:58.616216 73         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-06:10:58.616218 73         Options.compression_opts.parallel_threads: 1
2024/02/22-06:10:58.616220 73                  Options.compression_opts.enabled: false
2024/02/22-06:10:58.616222 73         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-06:10:58.616224 73      Options.level0_file_num_compaction_trigger: 4
2024/02/22-06:10:58.616226 73          Options.level0_slowdown_writes_trigger: 20
2024/02/22-06:10:58.616228 73              Options.level0_stop_writes_trigger: 36
2024/02/22-06:10:58.616230 73                   Options.target_file_size_base: 67108864
2024/02/22-06:10:58.616232 73             Options.target_file_size_multiplier: 2
2024/02/22-06:10:58.616234 73                Options.max_bytes_for_level_base: 268435456
2024/02/22-06:10:58.616236 73 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-06:10:58.616238 73          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-06:10:58.616241 73 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-06:10:58.616243 73 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-06:10:58.616245 73 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-06:10:58.616247 73 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-06:10:58.616249 73 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-06:10:58.616251 73 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-06:10:58.616253 73 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-06:10:58.616255 73       Options.max_sequential_skip_in_iterations: 8
2024/02/22-06:10:58.616257 73                    Options.max_compaction_bytes: 1677721600
2024/02/22-06:10:58.616259 73                        Options.arena_block_size: 1048576
2024/02/22-06:10:58.616261 73   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-06:10:58.616263 73   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-06:10:58.616265 73       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-06:10:58.616267 73                Options.disable_auto_compactions: 0
2024/02/22-06:10:58.616270 73                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-06:10:58.616272 73                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-06:10:58.616274 73 Options.compaction_options_universal.size_ratio: 1
2024/02/22-06:10:58.616276 73 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-06:10:58.616278 73 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-06:10:58.616280 73 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-06:10:58.616282 73 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-06:10:58.616284 73 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-06:10:58.616286 73 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-06:10:58.616288 73 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-06:10:58.616292 73                   Options.table_properties_collectors: 
2024/02/22-06:10:58.616294 73                   Options.inplace_update_support: 0
2024/02/22-06:10:58.616296 73                 Options.inplace_update_num_locks: 10000
2024/02/22-06:10:58.616298 73               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-06:10:58.616322 73               Options.memtable_whole_key_filtering: 0
2024/02/22-06:10:58.616324 73   Options.memtable_huge_page_size: 0
2024/02/22-06:10:58.616326 73                           Options.bloom_locality: 0
2024/02/22-06:10:58.616328 73                    Options.max_successive_merges: 0
2024/02/22-06:10:58.616330 73                Options.optimize_filters_for_hits: 0
2024/02/22-06:10:58.616332 73                Options.paranoid_file_checks: 0
2024/02/22-06:10:58.616334 73                Options.force_consistency_checks: 1
2024/02/22-06:10:58.616336 73                Options.report_bg_io_stats: 0
2024/02/22-06:10:58.616338 73                               Options.ttl: 2592000
2024/02/22-06:10:58.616340 73          Options.periodic_compaction_seconds: 0
2024/02/22-06:10:58.616342 73                       Options.enable_blob_files: false
2024/02/22-06:10:58.616344 73                           Options.min_blob_size: 0
2024/02/22-06:10:58.616346 73                          Options.blob_file_size: 268435456
2024/02/22-06:10:58.616348 73                   Options.blob_compression_type: NoCompression
2024/02/22-06:10:58.616350 73          Options.enable_blob_garbage_collection: false
2024/02/22-06:10:58.616352 73      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-06:10:58.616355 73 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-06:10:58.616357 73          Options.blob_compaction_readahead_size: 0
2024/02/22-06:10:58.622654 73 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000058 succeeded,manifest_file_number is 58, next_file_number is 60, last_sequence is 425792, log_number is 54,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 22
2024/02/22-06:10:58.622665 73 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 54
2024/02/22-06:10:58.622668 73 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 54
2024/02/22-06:10:58.622847 73 [db/version_set.cc:4409] Creating manifest 62
2024/02/22-06:10:58.624753 73 EVENT_LOG_v1 {"time_micros": 1708582258624745, "job": 1, "event": "recovery_started", "wal_files": [59]}
2024/02/22-06:10:58.624760 73 [db/db_impl/db_impl_open.cc:888] Recovering log #59 mode 2
2024/02/22-06:10:58.626167 73 EVENT_LOG_v1 {"time_micros": 1708582258626134, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 63, "file_size": 8950, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 7972, "index_size": 59, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 12152, "raw_average_key_size": 49, "raw_value_size": 4216, "raw_average_value_size": 17, "num_data_blocks": 1, "num_entries": 248, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708582258, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "4X2Y90AXFQ7RZCQG7GE9", "orig_file_number": 63}}
2024/02/22-06:10:58.626244 73 [db/version_set.cc:4409] Creating manifest 64
2024/02/22-06:10:58.627981 73 EVENT_LOG_v1 {"time_micros": 1708582258627977, "job": 1, "event": "recovery_finished"}
2024/02/22-06:10:58.633268 73 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000059.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:10:58.633330 73 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f2a90655700
2024/02/22-06:10:58.633420 73 DB pointer 0x7f2a9063cc00
2024/02/22-06:10:58.633668 84 [db/compaction/compaction_job.cc:2331] [default] [JOB 3] Compacting 4@0 + 2@1 files to L1, score 1.00
2024/02/22-06:10:58.633686 84 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 5 Base level 0, inputs: [63(8950B) 57(8954B) 51(8560B) 45(602KB)], [40(64MB) 41(48MB)]
2024/02/22-06:10:58.633740 84 EVENT_LOG_v1 {"time_micros": 1708582258633715, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [63, 57, 51, 45], "files_L1": [40, 41], "score": 1, "input_data_size": 118865953}
2024/02/22-06:10:59.372099 84 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #68: 300864 keys, 67206439 bytes
2024/02/22-06:10:59.372169 84 EVENT_LOG_v1 {"time_micros": 1708582259372138, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 68, "file_size": 67206439, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67194949, "index_size": 10537, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 14742102, "raw_average_key_size": 48, "raw_value_size": 62663719, "raw_average_value_size": 208, "num_data_blocks": 196, "num_entries": 300864, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1703207242, "oldest_key_time": 0, "file_creation_time": 1708582258, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "4X2Y90AXFQ7RZCQG7GE9", "orig_file_number": 68}}
2024/02/22-06:10:59.902143 84 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #69: 125176 keys, 51655520 bytes
2024/02/22-06:10:59.902214 84 EVENT_LOG_v1 {"time_micros": 1708582259902182, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 69, "file_size": 51655520, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 51650157, "index_size": 4411, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 6133624, "raw_average_key_size": 49, "raw_value_size": 49764908, "raw_average_value_size": 397, "num_data_blocks": 82, "num_entries": 125176, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1703207242, "oldest_key_time": 0, "file_creation_time": 1708582259, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "4X2Y90AXFQ7RZCQG7GE9", "orig_file_number": 69}}
2024/02/22-06:10:59.902835 84 [db/compaction/compaction_job.cc:1998] [default] [JOB 3] Compacted 4@0 + 2@1 files to L1 => 118861959 bytes
2024/02/22-06:10:59.903354 84 (Original Log Time 2024/02/22-06:10:59.903221) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 2 0 0 0] max score 0.44, MB/sec: 93.7 rd, 93.7 wr, level 1, files in(4, 2) out(2 +0 blob) MB in(0.6, 112.7 +0.0 blob) out(113.4 +0.0 blob), read-write-amplify(369.6) write-amplify(184.8) OK, records in: 426040, records dropped: 0 output_compression: NoCompression
2024/02/22-06:10:59.903361 84 (Original Log Time 2024/02/22-06:10:59.903251) EVENT_LOG_v1 {"time_micros": 1708582259903235, "job": 3, "event": "compaction_finished", "compaction_time_micros": 1268522, "compaction_time_cpu_micros": 1081208, "output_level": 1, "num_output_files": 2, "total_output_size": 118861959, "num_input_records": 426040, "num_output_records": 426040, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 2, 0, 0, 0]}
2024/02/22-06:10:59.903516 84 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000063.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:10:59.903529 84 EVENT_LOG_v1 {"time_micros": 1708582259903526, "job": 3, "event": "table_file_deletion", "file_number": 63}
2024/02/22-06:10:59.903597 84 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000057.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:10:59.903602 84 EVENT_LOG_v1 {"time_micros": 1708582259903600, "job": 3, "event": "table_file_deletion", "file_number": 57}
2024/02/22-06:10:59.903659 84 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000051.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:10:59.903663 84 EVENT_LOG_v1 {"time_micros": 1708582259903661, "job": 3, "event": "table_file_deletion", "file_number": 51}
2024/02/22-06:10:59.903812 84 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000045.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:10:59.903820 84 EVENT_LOG_v1 {"time_micros": 1708582259903818, "job": 3, "event": "table_file_deletion", "file_number": 45}
2024/02/22-06:10:59.911187 84 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000041.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:10:59.911200 84 EVENT_LOG_v1 {"time_micros": 1708582259911197, "job": 3, "event": "table_file_deletion", "file_number": 41}
2024/02/22-06:10:59.920332 84 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000040.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-06:10:59.920339 84 EVENT_LOG_v1 {"time_micros": 1708582259920337, "job": 3, "event": "table_file_deletion", "file_number": 40}
2024/02/22-06:10:59.920403 84 (Original Log Time 2024/02/22-06:10:59.920398) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/22-06:11:01.634172 103 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-06:11:01.634206 103 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 28 writes, 28 keys, 25 commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 28 writes, 0 syncs, 28.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 28 writes, 28 keys, 25 commit groups, 1.1 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 28 writes, 0 syncs, 28.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      7.8      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      2/0   113.36 MB   0.4      0.1     0.0      0.1       0.1      0.0       0.0 184.8     89.4     89.4      1.27              1.08         1    1.269    426K      0       0.0       0.0
 Sum      2/0   113.36 MB   0.0      0.1     0.0      0.1       0.1      0.0       0.0 13281.7     89.3     89.3      1.27              1.08         2    0.635    426K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.1     0.0      0.1       0.1      0.0       0.0 13280.2     89.3     89.3      1.27              1.08         2    0.635    426K      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.1     0.0      0.1       0.1      0.0       0.0   0.0     89.4     89.4      1.27              1.08         1    1.269    426K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      7.8      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.11 GB write, 37.56 MB/s write, 0.11 GB read, 37.56 MB/s read, 1.3 seconds
Interval compaction: 0.11 GB write, 37.56 MB/s write, 0.11 GB read, 37.56 MB/s read, 1.3 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f2a90607010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 0.000154 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f2a90607010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 0.000154 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
