2024/02/22-04:05:09.693598 68 RocksDB version: 6.29.5
2024/02/22-04:05:09.693689 68 Git sha 0
2024/02/22-04:05:09.693693 68 Compile date 2023-11-07 23:58:42
2024/02/22-04:05:09.693696 68 DB SUMMARY
2024/02/22-04:05:09.693698 68 DB Session ID:  00DPFF01V9F7JEPQLH49
2024/02/22-04:05:09.693785 68 CURRENT file:  CURRENT
2024/02/22-04:05:09.693788 68 IDENTITY file:  IDENTITY
2024/02/22-04:05:09.693802 68 MANIFEST file:  MANIFEST-000036 size: 1007 Bytes
2024/02/22-04:05:09.693806 68 SST files in /var/lib/milvus/rdb_data dir, Total Num: 2, files: 000040.sst 000041.sst 
2024/02/22-04:05:09.693809 68 Write Ahead Log file in /var/lib/milvus/rdb_data: 000037.log size: 709327 ; 
2024/02/22-04:05:09.693812 68                         Options.error_if_exists: 0
2024/02/22-04:05:09.693814 68                       Options.create_if_missing: 1
2024/02/22-04:05:09.693817 68                         Options.paranoid_checks: 1
2024/02/22-04:05:09.693819 68             Options.flush_verify_memtable_count: 1
2024/02/22-04:05:09.693821 68                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-04:05:09.693823 68                                     Options.env: 0x7f97a328a2c0
2024/02/22-04:05:09.693826 68                                      Options.fs: PosixFileSystem
2024/02/22-04:05:09.693828 68                                Options.info_log: 0x7f95dc050140
2024/02/22-04:05:09.693830 68                Options.max_file_opening_threads: 16
2024/02/22-04:05:09.693833 68                              Options.statistics: (nil)
2024/02/22-04:05:09.693837 68                               Options.use_fsync: 0
2024/02/22-04:05:09.693839 68                       Options.max_log_file_size: 0
2024/02/22-04:05:09.693841 68                  Options.max_manifest_file_size: 1073741824
2024/02/22-04:05:09.693844 68                   Options.log_file_time_to_roll: 0
2024/02/22-04:05:09.693846 68                       Options.keep_log_file_num: 1000
2024/02/22-04:05:09.693848 68                    Options.recycle_log_file_num: 0
2024/02/22-04:05:09.693850 68                         Options.allow_fallocate: 1
2024/02/22-04:05:09.693852 68                        Options.allow_mmap_reads: 0
2024/02/22-04:05:09.693854 68                       Options.allow_mmap_writes: 0
2024/02/22-04:05:09.693856 68                        Options.use_direct_reads: 0
2024/02/22-04:05:09.693858 68                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-04:05:09.693860 68          Options.create_missing_column_families: 1
2024/02/22-04:05:09.693862 68                              Options.db_log_dir: 
2024/02/22-04:05:09.693865 68                                 Options.wal_dir: 
2024/02/22-04:05:09.693867 68                Options.table_cache_numshardbits: 6
2024/02/22-04:05:09.693869 68                         Options.WAL_ttl_seconds: 0
2024/02/22-04:05:09.693871 68                       Options.WAL_size_limit_MB: 0
2024/02/22-04:05:09.693873 68                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-04:05:09.693875 68             Options.manifest_preallocation_size: 4194304
2024/02/22-04:05:09.693877 68                     Options.is_fd_close_on_exec: 1
2024/02/22-04:05:09.693879 68                   Options.advise_random_on_open: 1
2024/02/22-04:05:09.693881 68                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-04:05:09.693886 68                    Options.db_write_buffer_size: 0
2024/02/22-04:05:09.693888 68                    Options.write_buffer_manager: 0x7f95dc007280
2024/02/22-04:05:09.693890 68         Options.access_hint_on_compaction_start: 1
2024/02/22-04:05:09.693892 68  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-04:05:09.693894 68           Options.random_access_max_buffer_size: 1048576
2024/02/22-04:05:09.693896 68                      Options.use_adaptive_mutex: 0
2024/02/22-04:05:09.693898 68                            Options.rate_limiter: (nil)
2024/02/22-04:05:09.693901 68     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-04:05:09.693903 68                       Options.wal_recovery_mode: 2
2024/02/22-04:05:09.693932 68                  Options.enable_thread_tracking: 0
2024/02/22-04:05:09.693934 68                  Options.enable_pipelined_write: 0
2024/02/22-04:05:09.693936 68                  Options.unordered_write: 0
2024/02/22-04:05:09.693938 68         Options.allow_concurrent_memtable_write: 1
2024/02/22-04:05:09.693940 68      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-04:05:09.693942 68             Options.write_thread_max_yield_usec: 100
2024/02/22-04:05:09.693944 68            Options.write_thread_slow_yield_usec: 3
2024/02/22-04:05:09.693946 68                               Options.row_cache: None
2024/02/22-04:05:09.693948 68                              Options.wal_filter: None
2024/02/22-04:05:09.693951 68             Options.avoid_flush_during_recovery: 0
2024/02/22-04:05:09.693953 68             Options.allow_ingest_behind: 0
2024/02/22-04:05:09.693955 68             Options.preserve_deletes: 0
2024/02/22-04:05:09.693957 68             Options.two_write_queues: 0
2024/02/22-04:05:09.693959 68             Options.manual_wal_flush: 0
2024/02/22-04:05:09.693961 68             Options.atomic_flush: 0
2024/02/22-04:05:09.693963 68             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-04:05:09.693965 68                 Options.persist_stats_to_disk: 0
2024/02/22-04:05:09.693967 68                 Options.write_dbid_to_manifest: 0
2024/02/22-04:05:09.693969 68                 Options.log_readahead_size: 0
2024/02/22-04:05:09.693971 68                 Options.file_checksum_gen_factory: Unknown
2024/02/22-04:05:09.693973 68                 Options.best_efforts_recovery: 0
2024/02/22-04:05:09.693975 68                Options.max_bgerror_resume_count: 2147483647
2024/02/22-04:05:09.693977 68            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-04:05:09.693979 68             Options.allow_data_in_errors: 0
2024/02/22-04:05:09.693981 68             Options.db_host_id: __hostname__
2024/02/22-04:05:09.693983 68             Options.max_background_jobs: 4
2024/02/22-04:05:09.693985 68             Options.max_background_compactions: -1
2024/02/22-04:05:09.693988 68             Options.max_subcompactions: 1
2024/02/22-04:05:09.693990 68             Options.avoid_flush_during_shutdown: 0
2024/02/22-04:05:09.693992 68           Options.writable_file_max_buffer_size: 1048576
2024/02/22-04:05:09.693994 68             Options.delayed_write_rate : 16777216
2024/02/22-04:05:09.693996 68             Options.max_total_wal_size: 0
2024/02/22-04:05:09.693998 68             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-04:05:09.694000 68                   Options.stats_dump_period_sec: 600
2024/02/22-04:05:09.694002 68                 Options.stats_persist_period_sec: 600
2024/02/22-04:05:09.694004 68                 Options.stats_history_buffer_size: 1048576
2024/02/22-04:05:09.694007 68                          Options.max_open_files: -1
2024/02/22-04:05:09.694009 68                          Options.bytes_per_sync: 0
2024/02/22-04:05:09.694011 68                      Options.wal_bytes_per_sync: 0
2024/02/22-04:05:09.694013 68                   Options.strict_bytes_per_sync: 0
2024/02/22-04:05:09.694016 68       Options.compaction_readahead_size: 0
2024/02/22-04:05:09.694018 68                  Options.max_background_flushes: 1
2024/02/22-04:05:09.694020 68 Compression algorithms supported:
2024/02/22-04:05:09.694022 68 	kZSTDNotFinalCompression supported: 1
2024/02/22-04:05:09.694024 68 	kZSTD supported: 1
2024/02/22-04:05:09.694026 68 	kXpressCompression supported: 0
2024/02/22-04:05:09.694029 68 	kLZ4HCCompression supported: 0
2024/02/22-04:05:09.694031 68 	kLZ4Compression supported: 0
2024/02/22-04:05:09.694033 68 	kBZip2Compression supported: 0
2024/02/22-04:05:09.694035 68 	kZlibCompression supported: 0
2024/02/22-04:05:09.694037 68 	kSnappyCompression supported: 0
2024/02/22-04:05:09.694040 68 Fast CRC32 supported: Not supported on x86
2024/02/22-04:05:09.694207 68 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000036
2024/02/22-04:05:09.694599 68 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-04:05:09.694603 68               Options.comparator: leveldb.BytewiseComparator
2024/02/22-04:05:09.694606 68           Options.merge_operator: None
2024/02/22-04:05:09.694608 68        Options.compaction_filter: None
2024/02/22-04:05:09.694610 68        Options.compaction_filter_factory: None
2024/02/22-04:05:09.694612 68  Options.sst_partitioner_factory: None
2024/02/22-04:05:09.694614 68         Options.memtable_factory: SkipListFactory
2024/02/22-04:05:09.694616 68            Options.table_factory: BlockBasedTable
2024/02/22-04:05:09.694648 68            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f95dc0005a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f95dc007010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-04:05:09.694652 68        Options.write_buffer_size: 67108864
2024/02/22-04:05:09.694655 68  Options.max_write_buffer_number: 2
2024/02/22-04:05:09.694658 68        Options.compression[0]: NoCompression
2024/02/22-04:05:09.694660 68        Options.compression[1]: NoCompression
2024/02/22-04:05:09.694662 68        Options.compression[2]: ZSTD
2024/02/22-04:05:09.694664 68        Options.compression[3]: ZSTD
2024/02/22-04:05:09.694666 68        Options.compression[4]: ZSTD
2024/02/22-04:05:09.694668 68                  Options.bottommost_compression: Disabled
2024/02/22-04:05:09.694671 68       Options.prefix_extractor: nullptr
2024/02/22-04:05:09.694673 68   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-04:05:09.694675 68             Options.num_levels: 5
2024/02/22-04:05:09.694677 68        Options.min_write_buffer_number_to_merge: 1
2024/02/22-04:05:09.694679 68     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-04:05:09.694681 68     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-04:05:09.694683 68            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-04:05:09.694685 68                  Options.bottommost_compression_opts.level: 32767
2024/02/22-04:05:09.694687 68               Options.bottommost_compression_opts.strategy: 0
2024/02/22-04:05:09.694689 68         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-04:05:09.694691 68         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-04:05:09.694693 68         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-04:05:09.694695 68                  Options.bottommost_compression_opts.enabled: false
2024/02/22-04:05:09.694698 68         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-04:05:09.694700 68            Options.compression_opts.window_bits: -14
2024/02/22-04:05:09.694702 68                  Options.compression_opts.level: 32767
2024/02/22-04:05:09.694704 68               Options.compression_opts.strategy: 0
2024/02/22-04:05:09.694706 68         Options.compression_opts.max_dict_bytes: 0
2024/02/22-04:05:09.694708 68         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-04:05:09.694710 68         Options.compression_opts.parallel_threads: 1
2024/02/22-04:05:09.694777 68                  Options.compression_opts.enabled: false
2024/02/22-04:05:09.694781 68         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-04:05:09.694783 68      Options.level0_file_num_compaction_trigger: 4
2024/02/22-04:05:09.694785 68          Options.level0_slowdown_writes_trigger: 20
2024/02/22-04:05:09.694787 68              Options.level0_stop_writes_trigger: 36
2024/02/22-04:05:09.694789 68                   Options.target_file_size_base: 67108864
2024/02/22-04:05:09.694792 68             Options.target_file_size_multiplier: 2
2024/02/22-04:05:09.694794 68                Options.max_bytes_for_level_base: 268435456
2024/02/22-04:05:09.694796 68 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-04:05:09.694798 68          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-04:05:09.694802 68 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-04:05:09.694804 68 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-04:05:09.694806 68 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-04:05:09.694808 68 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-04:05:09.694810 68 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-04:05:09.694812 68 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-04:05:09.694814 68 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-04:05:09.694816 68       Options.max_sequential_skip_in_iterations: 8
2024/02/22-04:05:09.694819 68                    Options.max_compaction_bytes: 1677721600
2024/02/22-04:05:09.694821 68                        Options.arena_block_size: 1048576
2024/02/22-04:05:09.694823 68   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-04:05:09.694825 68   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-04:05:09.694827 68       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-04:05:09.694829 68                Options.disable_auto_compactions: 0
2024/02/22-04:05:09.694832 68                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-04:05:09.694834 68                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-04:05:09.694836 68 Options.compaction_options_universal.size_ratio: 1
2024/02/22-04:05:09.694838 68 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-04:05:09.694840 68 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-04:05:09.694842 68 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-04:05:09.694844 68 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-04:05:09.694847 68 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-04:05:09.694849 68 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-04:05:09.694851 68 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-04:05:09.694857 68                   Options.table_properties_collectors: 
2024/02/22-04:05:09.694859 68                   Options.inplace_update_support: 0
2024/02/22-04:05:09.694861 68                 Options.inplace_update_num_locks: 10000
2024/02/22-04:05:09.694863 68               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-04:05:09.694866 68               Options.memtable_whole_key_filtering: 0
2024/02/22-04:05:09.694868 68   Options.memtable_huge_page_size: 0
2024/02/22-04:05:09.694870 68                           Options.bloom_locality: 0
2024/02/22-04:05:09.694872 68                    Options.max_successive_merges: 0
2024/02/22-04:05:09.694874 68                Options.optimize_filters_for_hits: 0
2024/02/22-04:05:09.694876 68                Options.paranoid_file_checks: 0
2024/02/22-04:05:09.694878 68                Options.force_consistency_checks: 1
2024/02/22-04:05:09.694880 68                Options.report_bg_io_stats: 0
2024/02/22-04:05:09.694882 68                               Options.ttl: 2592000
2024/02/22-04:05:09.694884 68          Options.periodic_compaction_seconds: 0
2024/02/22-04:05:09.694918 68                       Options.enable_blob_files: false
2024/02/22-04:05:09.694920 68                           Options.min_blob_size: 0
2024/02/22-04:05:09.694923 68                          Options.blob_file_size: 268435456
2024/02/22-04:05:09.694925 68                   Options.blob_compression_type: NoCompression
2024/02/22-04:05:09.694927 68          Options.enable_blob_garbage_collection: false
2024/02/22-04:05:09.694929 68      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-04:05:09.694932 68 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-04:05:09.694934 68          Options.blob_compaction_readahead_size: 0
2024/02/22-04:05:09.695069 68 [db/column_family.cc:605] --------------- Options for column family [properties]:
2024/02/22-04:05:09.695073 68               Options.comparator: leveldb.BytewiseComparator
2024/02/22-04:05:09.695076 68           Options.merge_operator: None
2024/02/22-04:05:09.695078 68        Options.compaction_filter: None
2024/02/22-04:05:09.695080 68        Options.compaction_filter_factory: None
2024/02/22-04:05:09.695082 68  Options.sst_partitioner_factory: None
2024/02/22-04:05:09.695084 68         Options.memtable_factory: SkipListFactory
2024/02/22-04:05:09.695086 68            Options.table_factory: BlockBasedTable
2024/02/22-04:05:09.695107 68            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f95dc0005a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f95dc007010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-04:05:09.695110 68        Options.write_buffer_size: 67108864
2024/02/22-04:05:09.695113 68  Options.max_write_buffer_number: 2
2024/02/22-04:05:09.695115 68        Options.compression[0]: NoCompression
2024/02/22-04:05:09.695117 68        Options.compression[1]: NoCompression
2024/02/22-04:05:09.695119 68        Options.compression[2]: ZSTD
2024/02/22-04:05:09.695122 68        Options.compression[3]: ZSTD
2024/02/22-04:05:09.695124 68        Options.compression[4]: ZSTD
2024/02/22-04:05:09.695126 68                  Options.bottommost_compression: Disabled
2024/02/22-04:05:09.695128 68       Options.prefix_extractor: nullptr
2024/02/22-04:05:09.695130 68   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-04:05:09.695132 68             Options.num_levels: 5
2024/02/22-04:05:09.695134 68        Options.min_write_buffer_number_to_merge: 1
2024/02/22-04:05:09.695136 68     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-04:05:09.695138 68     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-04:05:09.695140 68            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-04:05:09.695142 68                  Options.bottommost_compression_opts.level: 32767
2024/02/22-04:05:09.695144 68               Options.bottommost_compression_opts.strategy: 0
2024/02/22-04:05:09.695146 68         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-04:05:09.695148 68         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-04:05:09.695217 68         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-04:05:09.695220 68                  Options.bottommost_compression_opts.enabled: false
2024/02/22-04:05:09.695222 68         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-04:05:09.695224 68            Options.compression_opts.window_bits: -14
2024/02/22-04:05:09.695227 68                  Options.compression_opts.level: 32767
2024/02/22-04:05:09.695229 68               Options.compression_opts.strategy: 0
2024/02/22-04:05:09.695231 68         Options.compression_opts.max_dict_bytes: 0
2024/02/22-04:05:09.695233 68         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-04:05:09.695234 68         Options.compression_opts.parallel_threads: 1
2024/02/22-04:05:09.695236 68                  Options.compression_opts.enabled: false
2024/02/22-04:05:09.695239 68         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-04:05:09.695241 68      Options.level0_file_num_compaction_trigger: 4
2024/02/22-04:05:09.695243 68          Options.level0_slowdown_writes_trigger: 20
2024/02/22-04:05:09.695245 68              Options.level0_stop_writes_trigger: 36
2024/02/22-04:05:09.695247 68                   Options.target_file_size_base: 67108864
2024/02/22-04:05:09.695249 68             Options.target_file_size_multiplier: 2
2024/02/22-04:05:09.695251 68                Options.max_bytes_for_level_base: 268435456
2024/02/22-04:05:09.695253 68 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-04:05:09.695255 68          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-04:05:09.695258 68 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-04:05:09.695261 68 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-04:05:09.695264 68 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-04:05:09.695267 68 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-04:05:09.695270 68 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-04:05:09.695273 68 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-04:05:09.695276 68 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-04:05:09.695279 68       Options.max_sequential_skip_in_iterations: 8
2024/02/22-04:05:09.695282 68                    Options.max_compaction_bytes: 1677721600
2024/02/22-04:05:09.695285 68                        Options.arena_block_size: 1048576
2024/02/22-04:05:09.695288 68   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-04:05:09.695291 68   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-04:05:09.695294 68       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-04:05:09.695297 68                Options.disable_auto_compactions: 0
2024/02/22-04:05:09.695300 68                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-04:05:09.695303 68                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-04:05:09.695307 68 Options.compaction_options_universal.size_ratio: 1
2024/02/22-04:05:09.695310 68 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-04:05:09.695313 68 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-04:05:09.695316 68 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-04:05:09.695319 68 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-04:05:09.695322 68 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-04:05:09.695325 68 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-04:05:09.695328 68 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-04:05:09.695333 68                   Options.table_properties_collectors: 
2024/02/22-04:05:09.695336 68                   Options.inplace_update_support: 0
2024/02/22-04:05:09.695339 68                 Options.inplace_update_num_locks: 10000
2024/02/22-04:05:09.695342 68               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-04:05:09.695377 68               Options.memtable_whole_key_filtering: 0
2024/02/22-04:05:09.695381 68   Options.memtable_huge_page_size: 0
2024/02/22-04:05:09.695385 68                           Options.bloom_locality: 0
2024/02/22-04:05:09.695388 68                    Options.max_successive_merges: 0
2024/02/22-04:05:09.695391 68                Options.optimize_filters_for_hits: 0
2024/02/22-04:05:09.695394 68                Options.paranoid_file_checks: 0
2024/02/22-04:05:09.695397 68                Options.force_consistency_checks: 1
2024/02/22-04:05:09.695400 68                Options.report_bg_io_stats: 0
2024/02/22-04:05:09.695403 68                               Options.ttl: 2592000
2024/02/22-04:05:09.695407 68          Options.periodic_compaction_seconds: 0
2024/02/22-04:05:09.695414 68                       Options.enable_blob_files: false
2024/02/22-04:05:09.695417 68                           Options.min_blob_size: 0
2024/02/22-04:05:09.695419 68                          Options.blob_file_size: 268435456
2024/02/22-04:05:09.695422 68                   Options.blob_compression_type: NoCompression
2024/02/22-04:05:09.695424 68          Options.enable_blob_garbage_collection: false
2024/02/22-04:05:09.695426 68      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-04:05:09.695429 68 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-04:05:09.695431 68          Options.blob_compaction_readahead_size: 0
2024/02/22-04:05:09.701898 68 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000036 succeeded,manifest_file_number is 36, next_file_number is 43, last_sequence is 423358, log_number is 30,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 22
2024/02/22-04:05:09.701909 68 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 30
2024/02/22-04:05:09.701912 68 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 30
2024/02/22-04:05:09.702085 68 [db/version_set.cc:4409] Creating manifest 44
2024/02/22-04:05:09.703910 68 EVENT_LOG_v1 {"time_micros": 1708574709703902, "job": 1, "event": "recovery_started", "wal_files": [37]}
2024/02/22-04:05:09.703918 68 [db/db_impl/db_impl_open.cc:888] Recovering log #37 mode 2
2024/02/22-04:05:09.718614 68 EVENT_LOG_v1 {"time_micros": 1708574709718585, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 45, "file_size": 616773, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 615737, "index_size": 113, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 95542, "raw_average_key_size": 48, "raw_value_size": 586657, "raw_average_value_size": 300, "num_data_blocks": 2, "num_entries": 1950, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708574709, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "00DPFF01V9F7JEPQLH49", "orig_file_number": 45}}
2024/02/22-04:05:09.718677 68 [db/version_set.cc:4409] Creating manifest 46
2024/02/22-04:05:09.720202 68 EVENT_LOG_v1 {"time_micros": 1708574709720199, "job": 1, "event": "recovery_finished"}
2024/02/22-04:05:09.722471 68 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000037.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-04:05:09.722514 68 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f95dc055700
2024/02/22-04:05:09.722560 68 DB pointer 0x7f95dc03cc00
2024/02/22-04:05:09.722670 87 (Original Log Time 2024/02/22-04:05:09.722643) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/22-04:05:12.723010 105 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-04:05:12.723056 105 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 26 writes, 26 keys, 24 commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 26 writes, 0 syncs, 26.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 26 writes, 26 keys, 24 commit groups, 1.1 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 26 writes, 0 syncs, 26.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   602.32 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     68.6      0.01              0.00         1    0.009       0      0       0.0       0.0
  L1      2/0   112.75 MB   0.4      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0   113.33 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     68.6      0.01              0.00         1    0.009       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     68.6      0.01              0.00         1    0.009       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     68.6      0.01              0.00         1    0.009       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.001, interval 0.001
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.19 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.19 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f95dc007010#9 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 0.000132 secs_since: 3
Block cache entry stats(count,size,portion): Misc(4,0.86 KB,2.04891e-05%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f95dc007010#9 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 0.000132 secs_since: 3
Block cache entry stats(count,size,portion): Misc(4,0.86 KB,2.04891e-05%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
