2023/12/22-02:30:46.496499 71 RocksDB version: 6.29.5
2023/12/22-02:30:46.496552 71 Git sha 0
2023/12/22-02:30:46.496554 71 Compile date 2023-11-07 23:58:42
2023/12/22-02:30:46.496555 71 DB SUMMARY
2023/12/22-02:30:46.496557 71 DB Session ID:  K8YDJ786ULEL7N4QAPAZ
2023/12/22-02:30:46.496599 71 CURRENT file:  CURRENT
2023/12/22-02:30:46.496600 71 IDENTITY file:  IDENTITY
2023/12/22-02:30:46.496608 71 MANIFEST file:  MANIFEST-000012 size: 293 Bytes
2023/12/22-02:30:46.496610 71 SST files in /var/lib/milvus/rdb_data dir, Total Num: 1, files: 000011.sst 
2023/12/22-02:30:46.496612 71 Write Ahead Log file in /var/lib/milvus/rdb_data: 000013.log size: 20025024 ; 
2023/12/22-02:30:46.496614 71                         Options.error_if_exists: 0
2023/12/22-02:30:46.496616 71                       Options.create_if_missing: 1
2023/12/22-02:30:46.496617 71                         Options.paranoid_checks: 1
2023/12/22-02:30:46.496618 71             Options.flush_verify_memtable_count: 1
2023/12/22-02:30:46.496619 71                               Options.track_and_verify_wals_in_manifest: 0
2023/12/22-02:30:46.496621 71                                     Options.env: 0x7efe50c232c0
2023/12/22-02:30:46.496623 71                                      Options.fs: PosixFileSystem
2023/12/22-02:30:46.496625 71                                Options.info_log: 0x7efc80450140
2023/12/22-02:30:46.496626 71                Options.max_file_opening_threads: 16
2023/12/22-02:30:46.496627 71                              Options.statistics: (nil)
2023/12/22-02:30:46.496629 71                               Options.use_fsync: 0
2023/12/22-02:30:46.496630 71                       Options.max_log_file_size: 0
2023/12/22-02:30:46.496632 71                  Options.max_manifest_file_size: 1073741824
2023/12/22-02:30:46.496633 71                   Options.log_file_time_to_roll: 0
2023/12/22-02:30:46.496635 71                       Options.keep_log_file_num: 1000
2023/12/22-02:30:46.496636 71                    Options.recycle_log_file_num: 0
2023/12/22-02:30:46.496637 71                         Options.allow_fallocate: 1
2023/12/22-02:30:46.496639 71                        Options.allow_mmap_reads: 0
2023/12/22-02:30:46.496640 71                       Options.allow_mmap_writes: 0
2023/12/22-02:30:46.496642 71                        Options.use_direct_reads: 0
2023/12/22-02:30:46.496643 71                        Options.use_direct_io_for_flush_and_compaction: 0
2023/12/22-02:30:46.496644 71          Options.create_missing_column_families: 1
2023/12/22-02:30:46.496645 71                              Options.db_log_dir: 
2023/12/22-02:30:46.496647 71                                 Options.wal_dir: 
2023/12/22-02:30:46.496648 71                Options.table_cache_numshardbits: 6
2023/12/22-02:30:46.496649 71                         Options.WAL_ttl_seconds: 0
2023/12/22-02:30:46.496650 71                       Options.WAL_size_limit_MB: 0
2023/12/22-02:30:46.496652 71                        Options.max_write_batch_group_size_bytes: 1048576
2023/12/22-02:30:46.496653 71             Options.manifest_preallocation_size: 4194304
2023/12/22-02:30:46.496659 71                     Options.is_fd_close_on_exec: 1
2023/12/22-02:30:46.496660 71                   Options.advise_random_on_open: 1
2023/12/22-02:30:46.496661 71                   Options.experimental_mempurge_threshold: 0.000000
2023/12/22-02:30:46.496665 71                    Options.db_write_buffer_size: 0
2023/12/22-02:30:46.496667 71                    Options.write_buffer_manager: 0x7efc80404280
2023/12/22-02:30:46.496668 71         Options.access_hint_on_compaction_start: 1
2023/12/22-02:30:46.496669 71  Options.new_table_reader_for_compaction_inputs: 0
2023/12/22-02:30:46.496670 71           Options.random_access_max_buffer_size: 1048576
2023/12/22-02:30:46.496671 71                      Options.use_adaptive_mutex: 0
2023/12/22-02:30:46.496672 71                            Options.rate_limiter: (nil)
2023/12/22-02:30:46.496674 71     Options.sst_file_manager.rate_bytes_per_sec: 0
2023/12/22-02:30:46.496675 71                       Options.wal_recovery_mode: 2
2023/12/22-02:30:46.496690 71                  Options.enable_thread_tracking: 0
2023/12/22-02:30:46.496692 71                  Options.enable_pipelined_write: 0
2023/12/22-02:30:46.496693 71                  Options.unordered_write: 0
2023/12/22-02:30:46.496694 71         Options.allow_concurrent_memtable_write: 1
2023/12/22-02:30:46.496695 71      Options.enable_write_thread_adaptive_yield: 1
2023/12/22-02:30:46.496697 71             Options.write_thread_max_yield_usec: 100
2023/12/22-02:30:46.496698 71            Options.write_thread_slow_yield_usec: 3
2023/12/22-02:30:46.496699 71                               Options.row_cache: None
2023/12/22-02:30:46.496701 71                              Options.wal_filter: None
2023/12/22-02:30:46.496702 71             Options.avoid_flush_during_recovery: 0
2023/12/22-02:30:46.496703 71             Options.allow_ingest_behind: 0
2023/12/22-02:30:46.496705 71             Options.preserve_deletes: 0
2023/12/22-02:30:46.496706 71             Options.two_write_queues: 0
2023/12/22-02:30:46.496708 71             Options.manual_wal_flush: 0
2023/12/22-02:30:46.496709 71             Options.atomic_flush: 0
2023/12/22-02:30:46.496710 71             Options.avoid_unnecessary_blocking_io: 0
2023/12/22-02:30:46.496712 71                 Options.persist_stats_to_disk: 0
2023/12/22-02:30:46.496713 71                 Options.write_dbid_to_manifest: 0
2023/12/22-02:30:46.496715 71                 Options.log_readahead_size: 0
2023/12/22-02:30:46.496716 71                 Options.file_checksum_gen_factory: Unknown
2023/12/22-02:30:46.496717 71                 Options.best_efforts_recovery: 0
2023/12/22-02:30:46.496719 71                Options.max_bgerror_resume_count: 2147483647
2023/12/22-02:30:46.496720 71            Options.bgerror_resume_retry_interval: 1000000
2023/12/22-02:30:46.496722 71             Options.allow_data_in_errors: 0
2023/12/22-02:30:46.496723 71             Options.db_host_id: __hostname__
2023/12/22-02:30:46.496724 71             Options.max_background_jobs: 4
2023/12/22-02:30:46.496726 71             Options.max_background_compactions: -1
2023/12/22-02:30:46.496727 71             Options.max_subcompactions: 1
2023/12/22-02:30:46.496729 71             Options.avoid_flush_during_shutdown: 0
2023/12/22-02:30:46.496730 71           Options.writable_file_max_buffer_size: 1048576
2023/12/22-02:30:46.496732 71             Options.delayed_write_rate : 16777216
2023/12/22-02:30:46.496733 71             Options.max_total_wal_size: 0
2023/12/22-02:30:46.496734 71             Options.delete_obsolete_files_period_micros: 21600000000
2023/12/22-02:30:46.496736 71                   Options.stats_dump_period_sec: 600
2023/12/22-02:30:46.496737 71                 Options.stats_persist_period_sec: 600
2023/12/22-02:30:46.496739 71                 Options.stats_history_buffer_size: 1048576
2023/12/22-02:30:46.496740 71                          Options.max_open_files: -1
2023/12/22-02:30:46.496742 71                          Options.bytes_per_sync: 0
2023/12/22-02:30:46.496743 71                      Options.wal_bytes_per_sync: 0
2023/12/22-02:30:46.496744 71                   Options.strict_bytes_per_sync: 0
2023/12/22-02:30:46.496745 71       Options.compaction_readahead_size: 0
2023/12/22-02:30:46.496746 71                  Options.max_background_flushes: 1
2023/12/22-02:30:46.496747 71 Compression algorithms supported:
2023/12/22-02:30:46.496749 71 	kZSTDNotFinalCompression supported: 1
2023/12/22-02:30:46.496751 71 	kZSTD supported: 1
2023/12/22-02:30:46.496752 71 	kXpressCompression supported: 0
2023/12/22-02:30:46.496753 71 	kLZ4HCCompression supported: 0
2023/12/22-02:30:46.496755 71 	kLZ4Compression supported: 0
2023/12/22-02:30:46.496756 71 	kBZip2Compression supported: 0
2023/12/22-02:30:46.496757 71 	kZlibCompression supported: 0
2023/12/22-02:30:46.496758 71 	kSnappyCompression supported: 0
2023/12/22-02:30:46.496760 71 Fast CRC32 supported: Not supported on x86
2023/12/22-02:30:46.496865 71 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000012
2023/12/22-02:30:46.497088 71 [db/column_family.cc:605] --------------- Options for column family [default]:
2023/12/22-02:30:46.497091 71               Options.comparator: leveldb.BytewiseComparator
2023/12/22-02:30:46.497092 71           Options.merge_operator: None
2023/12/22-02:30:46.497093 71        Options.compaction_filter: None
2023/12/22-02:30:46.497095 71        Options.compaction_filter_factory: None
2023/12/22-02:30:46.497096 71  Options.sst_partitioner_factory: None
2023/12/22-02:30:46.497098 71         Options.memtable_factory: SkipListFactory
2023/12/22-02:30:46.497099 71            Options.table_factory: BlockBasedTable
2023/12/22-02:30:46.497118 71            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7efc804166c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7efc80404010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2023/12/22-02:30:46.497121 71        Options.write_buffer_size: 67108864
2023/12/22-02:30:46.497122 71  Options.max_write_buffer_number: 2
2023/12/22-02:30:46.497124 71        Options.compression[0]: NoCompression
2023/12/22-02:30:46.497125 71        Options.compression[1]: NoCompression
2023/12/22-02:30:46.497126 71        Options.compression[2]: ZSTD
2023/12/22-02:30:46.497128 71        Options.compression[3]: ZSTD
2023/12/22-02:30:46.497129 71        Options.compression[4]: ZSTD
2023/12/22-02:30:46.497130 71                  Options.bottommost_compression: Disabled
2023/12/22-02:30:46.497131 71       Options.prefix_extractor: nullptr
2023/12/22-02:30:46.497133 71   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2023/12/22-02:30:46.497134 71             Options.num_levels: 5
2023/12/22-02:30:46.497135 71        Options.min_write_buffer_number_to_merge: 1
2023/12/22-02:30:46.497136 71     Options.max_write_buffer_number_to_maintain: 0
2023/12/22-02:30:46.497138 71     Options.max_write_buffer_size_to_maintain: 0
2023/12/22-02:30:46.497139 71            Options.bottommost_compression_opts.window_bits: -14
2023/12/22-02:30:46.497140 71                  Options.bottommost_compression_opts.level: 32767
2023/12/22-02:30:46.497142 71               Options.bottommost_compression_opts.strategy: 0
2023/12/22-02:30:46.497143 71         Options.bottommost_compression_opts.max_dict_bytes: 0
2023/12/22-02:30:46.497145 71         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2023/12/22-02:30:46.497146 71         Options.bottommost_compression_opts.parallel_threads: 1
2023/12/22-02:30:46.497147 71                  Options.bottommost_compression_opts.enabled: false
2023/12/22-02:30:46.497149 71         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2023/12/22-02:30:46.497150 71            Options.compression_opts.window_bits: -14
2023/12/22-02:30:46.497151 71                  Options.compression_opts.level: 32767
2023/12/22-02:30:46.497153 71               Options.compression_opts.strategy: 0
2023/12/22-02:30:46.497154 71         Options.compression_opts.max_dict_bytes: 0
2023/12/22-02:30:46.497155 71         Options.compression_opts.zstd_max_train_bytes: 0
2023/12/22-02:30:46.497157 71         Options.compression_opts.parallel_threads: 1
2023/12/22-02:30:46.497172 71                  Options.compression_opts.enabled: false
2023/12/22-02:30:46.497174 71         Options.compression_opts.max_dict_buffer_bytes: 0
2023/12/22-02:30:46.497175 71      Options.level0_file_num_compaction_trigger: 4
2023/12/22-02:30:46.497176 71          Options.level0_slowdown_writes_trigger: 20
2023/12/22-02:30:46.497177 71              Options.level0_stop_writes_trigger: 36
2023/12/22-02:30:46.497178 71                   Options.target_file_size_base: 67108864
2023/12/22-02:30:46.497180 71             Options.target_file_size_multiplier: 2
2023/12/22-02:30:46.497181 71                Options.max_bytes_for_level_base: 268435456
2023/12/22-02:30:46.497182 71 Options.level_compaction_dynamic_level_bytes: 0
2023/12/22-02:30:46.497183 71          Options.max_bytes_for_level_multiplier: 10.000000
2023/12/22-02:30:46.497185 71 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2023/12/22-02:30:46.497187 71 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2023/12/22-02:30:46.497188 71 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2023/12/22-02:30:46.497189 71 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2023/12/22-02:30:46.497190 71 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2023/12/22-02:30:46.497191 71 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2023/12/22-02:30:46.497192 71 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2023/12/22-02:30:46.497193 71       Options.max_sequential_skip_in_iterations: 8
2023/12/22-02:30:46.497195 71                    Options.max_compaction_bytes: 1677721600
2023/12/22-02:30:46.497196 71                        Options.arena_block_size: 1048576
2023/12/22-02:30:46.497197 71   Options.soft_pending_compaction_bytes_limit: 68719476736
2023/12/22-02:30:46.497199 71   Options.hard_pending_compaction_bytes_limit: 274877906944
2023/12/22-02:30:46.497200 71       Options.rate_limit_delay_max_milliseconds: 100
2023/12/22-02:30:46.497201 71                Options.disable_auto_compactions: 0
2023/12/22-02:30:46.497203 71                        Options.compaction_style: kCompactionStyleLevel
2023/12/22-02:30:46.497204 71                          Options.compaction_pri: kMinOverlappingRatio
2023/12/22-02:30:46.497206 71 Options.compaction_options_universal.size_ratio: 1
2023/12/22-02:30:46.497207 71 Options.compaction_options_universal.min_merge_width: 2
2023/12/22-02:30:46.497208 71 Options.compaction_options_universal.max_merge_width: 4294967295
2023/12/22-02:30:46.497209 71 Options.compaction_options_universal.max_size_amplification_percent: 200
2023/12/22-02:30:46.497210 71 Options.compaction_options_universal.compression_size_percent: -1
2023/12/22-02:30:46.497212 71 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2023/12/22-02:30:46.497213 71 Options.compaction_options_fifo.max_table_files_size: 1073741824
2023/12/22-02:30:46.497214 71 Options.compaction_options_fifo.allow_compaction: 0
2023/12/22-02:30:46.497223 71                   Options.table_properties_collectors: 
2023/12/22-02:30:46.497224 71                   Options.inplace_update_support: 0
2023/12/22-02:30:46.497225 71                 Options.inplace_update_num_locks: 10000
2023/12/22-02:30:46.497226 71               Options.memtable_prefix_bloom_size_ratio: 0.000000
2023/12/22-02:30:46.497228 71               Options.memtable_whole_key_filtering: 0
2023/12/22-02:30:46.497229 71   Options.memtable_huge_page_size: 0
2023/12/22-02:30:46.497230 71                           Options.bloom_locality: 0
2023/12/22-02:30:46.497232 71                    Options.max_successive_merges: 0
2023/12/22-02:30:46.497233 71                Options.optimize_filters_for_hits: 0
2023/12/22-02:30:46.497234 71                Options.paranoid_file_checks: 0
2023/12/22-02:30:46.497235 71                Options.force_consistency_checks: 1
2023/12/22-02:30:46.497236 71                Options.report_bg_io_stats: 0
2023/12/22-02:30:46.497237 71                               Options.ttl: 2592000
2023/12/22-02:30:46.497239 71          Options.periodic_compaction_seconds: 0
2023/12/22-02:30:46.497252 71                       Options.enable_blob_files: false
2023/12/22-02:30:46.497254 71                           Options.min_blob_size: 0
2023/12/22-02:30:46.497255 71                          Options.blob_file_size: 268435456
2023/12/22-02:30:46.497256 71                   Options.blob_compression_type: NoCompression
2023/12/22-02:30:46.497257 71          Options.enable_blob_garbage_collection: false
2023/12/22-02:30:46.497258 71      Options.blob_garbage_collection_age_cutoff: 0.250000
2023/12/22-02:30:46.497260 71 Options.blob_garbage_collection_force_threshold: 1.000000
2023/12/22-02:30:46.497261 71          Options.blob_compaction_readahead_size: 0
2023/12/22-02:30:46.497333 71 [db/column_family.cc:605] --------------- Options for column family [properties]:
2023/12/22-02:30:46.497335 71               Options.comparator: leveldb.BytewiseComparator
2023/12/22-02:30:46.497337 71           Options.merge_operator: None
2023/12/22-02:30:46.497338 71        Options.compaction_filter: None
2023/12/22-02:30:46.497339 71        Options.compaction_filter_factory: None
2023/12/22-02:30:46.497340 71  Options.sst_partitioner_factory: None
2023/12/22-02:30:46.497341 71         Options.memtable_factory: SkipListFactory
2023/12/22-02:30:46.497342 71            Options.table_factory: BlockBasedTable
2023/12/22-02:30:46.497355 71            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7efc804166c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7efc80404010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2023/12/22-02:30:46.497357 71        Options.write_buffer_size: 67108864
2023/12/22-02:30:46.497359 71  Options.max_write_buffer_number: 2
2023/12/22-02:30:46.497360 71        Options.compression[0]: NoCompression
2023/12/22-02:30:46.497361 71        Options.compression[1]: NoCompression
2023/12/22-02:30:46.497363 71        Options.compression[2]: ZSTD
2023/12/22-02:30:46.497364 71        Options.compression[3]: ZSTD
2023/12/22-02:30:46.497365 71        Options.compression[4]: ZSTD
2023/12/22-02:30:46.497366 71                  Options.bottommost_compression: Disabled
2023/12/22-02:30:46.497367 71       Options.prefix_extractor: nullptr
2023/12/22-02:30:46.497368 71   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2023/12/22-02:30:46.497370 71             Options.num_levels: 5
2023/12/22-02:30:46.497371 71        Options.min_write_buffer_number_to_merge: 1
2023/12/22-02:30:46.497372 71     Options.max_write_buffer_number_to_maintain: 0
2023/12/22-02:30:46.497373 71     Options.max_write_buffer_size_to_maintain: 0
2023/12/22-02:30:46.497374 71            Options.bottommost_compression_opts.window_bits: -14
2023/12/22-02:30:46.497375 71                  Options.bottommost_compression_opts.level: 32767
2023/12/22-02:30:46.497377 71               Options.bottommost_compression_opts.strategy: 0
2023/12/22-02:30:46.497378 71         Options.bottommost_compression_opts.max_dict_bytes: 0
2023/12/22-02:30:46.497379 71         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2023/12/22-02:30:46.497380 71         Options.bottommost_compression_opts.parallel_threads: 1
2023/12/22-02:30:46.497394 71                  Options.bottommost_compression_opts.enabled: false
2023/12/22-02:30:46.497396 71         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2023/12/22-02:30:46.497397 71            Options.compression_opts.window_bits: -14
2023/12/22-02:30:46.497398 71                  Options.compression_opts.level: 32767
2023/12/22-02:30:46.497399 71               Options.compression_opts.strategy: 0
2023/12/22-02:30:46.497401 71         Options.compression_opts.max_dict_bytes: 0
2023/12/22-02:30:46.497402 71         Options.compression_opts.zstd_max_train_bytes: 0
2023/12/22-02:30:46.497403 71         Options.compression_opts.parallel_threads: 1
2023/12/22-02:30:46.497404 71                  Options.compression_opts.enabled: false
2023/12/22-02:30:46.497405 71         Options.compression_opts.max_dict_buffer_bytes: 0
2023/12/22-02:30:46.497406 71      Options.level0_file_num_compaction_trigger: 4
2023/12/22-02:30:46.497407 71          Options.level0_slowdown_writes_trigger: 20
2023/12/22-02:30:46.497408 71              Options.level0_stop_writes_trigger: 36
2023/12/22-02:30:46.497410 71                   Options.target_file_size_base: 67108864
2023/12/22-02:30:46.497411 71             Options.target_file_size_multiplier: 2
2023/12/22-02:30:46.497412 71                Options.max_bytes_for_level_base: 268435456
2023/12/22-02:30:46.497413 71 Options.level_compaction_dynamic_level_bytes: 0
2023/12/22-02:30:46.497414 71          Options.max_bytes_for_level_multiplier: 10.000000
2023/12/22-02:30:46.497416 71 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2023/12/22-02:30:46.497418 71 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2023/12/22-02:30:46.497419 71 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2023/12/22-02:30:46.497420 71 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2023/12/22-02:30:46.497421 71 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2023/12/22-02:30:46.497422 71 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2023/12/22-02:30:46.497423 71 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2023/12/22-02:30:46.497424 71       Options.max_sequential_skip_in_iterations: 8
2023/12/22-02:30:46.497426 71                    Options.max_compaction_bytes: 1677721600
2023/12/22-02:30:46.497427 71                        Options.arena_block_size: 1048576
2023/12/22-02:30:46.497428 71   Options.soft_pending_compaction_bytes_limit: 68719476736
2023/12/22-02:30:46.497429 71   Options.hard_pending_compaction_bytes_limit: 274877906944
2023/12/22-02:30:46.497430 71       Options.rate_limit_delay_max_milliseconds: 100
2023/12/22-02:30:46.497431 71                Options.disable_auto_compactions: 0
2023/12/22-02:30:46.497433 71                        Options.compaction_style: kCompactionStyleLevel
2023/12/22-02:30:46.497434 71                          Options.compaction_pri: kMinOverlappingRatio
2023/12/22-02:30:46.497435 71 Options.compaction_options_universal.size_ratio: 1
2023/12/22-02:30:46.497436 71 Options.compaction_options_universal.min_merge_width: 2
2023/12/22-02:30:46.497437 71 Options.compaction_options_universal.max_merge_width: 4294967295
2023/12/22-02:30:46.497439 71 Options.compaction_options_universal.max_size_amplification_percent: 200
2023/12/22-02:30:46.497440 71 Options.compaction_options_universal.compression_size_percent: -1
2023/12/22-02:30:46.497441 71 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2023/12/22-02:30:46.497442 71 Options.compaction_options_fifo.max_table_files_size: 1073741824
2023/12/22-02:30:46.497443 71 Options.compaction_options_fifo.allow_compaction: 0
2023/12/22-02:30:46.497445 71                   Options.table_properties_collectors: 
2023/12/22-02:30:46.497446 71                   Options.inplace_update_support: 0
2023/12/22-02:30:46.497447 71                 Options.inplace_update_num_locks: 10000
2023/12/22-02:30:46.497449 71               Options.memtable_prefix_bloom_size_ratio: 0.000000
2023/12/22-02:30:46.497450 71               Options.memtable_whole_key_filtering: 0
2023/12/22-02:30:46.497463 71   Options.memtable_huge_page_size: 0
2023/12/22-02:30:46.497465 71                           Options.bloom_locality: 0
2023/12/22-02:30:46.497466 71                    Options.max_successive_merges: 0
2023/12/22-02:30:46.497467 71                Options.optimize_filters_for_hits: 0
2023/12/22-02:30:46.497468 71                Options.paranoid_file_checks: 0
2023/12/22-02:30:46.497469 71                Options.force_consistency_checks: 1
2023/12/22-02:30:46.497470 71                Options.report_bg_io_stats: 0
2023/12/22-02:30:46.497472 71                               Options.ttl: 2592000
2023/12/22-02:30:46.497473 71          Options.periodic_compaction_seconds: 0
2023/12/22-02:30:46.497474 71                       Options.enable_blob_files: false
2023/12/22-02:30:46.497475 71                           Options.min_blob_size: 0
2023/12/22-02:30:46.497476 71                          Options.blob_file_size: 268435456
2023/12/22-02:30:46.497477 71                   Options.blob_compression_type: NoCompression
2023/12/22-02:30:46.497479 71          Options.enable_blob_garbage_collection: false
2023/12/22-02:30:46.497480 71      Options.blob_garbage_collection_age_cutoff: 0.250000
2023/12/22-02:30:46.497482 71 Options.blob_garbage_collection_force_threshold: 1.000000
2023/12/22-02:30:46.497483 71          Options.blob_compaction_readahead_size: 0
2023/12/22-02:30:46.501893 71 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000012 succeeded,manifest_file_number is 12, next_file_number is 14, last_sequence is 12828, log_number is 6,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 0
2023/12/22-02:30:46.501903 71 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 6
2023/12/22-02:30:46.501905 71 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 6
2023/12/22-02:30:46.502006 71 [db/version_set.cc:4409] Creating manifest 16
2023/12/22-02:30:46.503379 71 EVENT_LOG_v1 {"time_micros": 1703212246503374, "job": 1, "event": "recovery_started", "wal_files": [13]}
2023/12/22-02:30:46.503383 71 [db/db_impl/db_impl_open.cc:888] Recovering log #13 mode 2
2023/12/22-02:30:46.668930 71 EVENT_LOG_v1 {"time_micros": 1703212246668881, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 17, "file_size": 18722637, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 18720312, "index_size": 1397, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 1402201, "raw_average_key_size": 48, "raw_value_size": 18291210, "raw_average_value_size": 639, "num_data_blocks": 26, "num_entries": 28617, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1703212246, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "K8YDJ786ULEL7N4QAPAZ", "orig_file_number": 17}}
2023/12/22-02:30:46.670042 71 [db/version_set.cc:4409] Creating manifest 18
2023/12/22-02:30:46.671699 71 EVENT_LOG_v1 {"time_micros": 1703212246671695, "job": 1, "event": "recovery_finished"}
2023/12/22-02:30:46.678670 71 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000013.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2023/12/22-02:30:46.678725 71 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7efc80455700
2023/12/22-02:30:46.678790 71 DB pointer 0x7efc8043cc00
2023/12/22-02:30:49.679312 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-02:30:49.679362 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.2 total, 3.2 interval
Cumulative writes: 39 writes, 39 keys, 29 commit groups, 1.3 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 39 writes, 0 syncs, 39.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 39 writes, 39 keys, 29 commit groups, 1.3 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 39 writes, 0 syncs, 39.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   35.64 MB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Sum      2/0   35.64 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.2 total, 3.2 interval
Flush(GB): cumulative 0.017, interval 0.017
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 5.61 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.02 GB write, 5.61 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 0.000128 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.2 total, 3.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 0.000128 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-02:40:49.679909 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-02:40:49.680046 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 603.2 total, 600.0 interval
Cumulative writes: 9045 writes, 9045 keys, 6150 commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 9045 writes, 0 syncs, 9045.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9006 writes, 9006 keys, 6121 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9006 writes, 0 syncs, 9006.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   35.64 MB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Sum      2/0   35.64 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 603.2 total, 600.0 interval
Flush(GB): cumulative 0.017, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.03 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 2 last_copies: 2 last_secs: 0.000123 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 603.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 2 last_copies: 2 last_secs: 0.000123 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-02:50:49.680579 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-02:50:49.680730 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1203.2 total, 600.0 interval
Cumulative writes: 17K writes, 17K keys, 12K commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 17K writes, 0 syncs, 17267.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8222 writes, 8222 keys, 6036 commit groups, 1.4 writes per commit group, ingest: 2.96 MB, 0.00 MB/s
Interval WAL: 8222 writes, 0 syncs, 8222.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   35.64 MB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Sum      2/0   35.64 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1203.2 total, 600.0 interval
Flush(GB): cumulative 0.017, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 3 last_copies: 2 last_secs: 0.0003 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 3 last_copies: 2 last_secs: 0.0003 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-03:00:49.681272 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-03:00:49.681431 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1803.2 total, 600.0 interval
Cumulative writes: 26K writes, 26K keys, 18K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.01 MB/s
Cumulative WAL: 26K writes, 0 syncs, 26283.00 writes per sync, written: 0.02 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9016 writes, 9016 keys, 6083 commit groups, 1.5 writes per commit group, ingest: 15.73 MB, 0.03 MB/s
Interval WAL: 9016 writes, 0 syncs, 9016.00 writes per sync, written: 0.02 GB, 0.03 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   35.64 MB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Sum      2/0   35.64 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1803.2 total, 600.0 interval
Flush(GB): cumulative 0.017, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 4 last_copies: 2 last_secs: 0.000175 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 4 last_copies: 2 last_secs: 0.000175 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-03:10:49.681960 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-03:10:49.682108 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2403.2 total, 600.0 interval
Cumulative writes: 35K writes, 35K keys, 24K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.01 MB/s
Cumulative WAL: 35K writes, 0 syncs, 35295.00 writes per sync, written: 0.02 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9012 writes, 9012 keys, 6175 commit groups, 1.5 writes per commit group, ingest: 2.66 MB, 0.00 MB/s
Interval WAL: 9012 writes, 0 syncs, 9012.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   35.64 MB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Sum      2/0   35.64 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2403.2 total, 600.0 interval
Flush(GB): cumulative 0.017, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 5 last_copies: 2 last_secs: 0.000167 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2403.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 5 last_copies: 2 last_secs: 0.000167 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-03:20:49.682630 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-03:20:49.682791 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3003.2 total, 600.0 interval
Cumulative writes: 44K writes, 44K keys, 30K commit groups, 1.5 writes per commit group, ingest: 0.04 GB, 0.01 MB/s
Cumulative WAL: 44K writes, 0 syncs, 44319.00 writes per sync, written: 0.04 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9024 writes, 9024 keys, 6006 commit groups, 1.5 writes per commit group, ingest: 15.81 MB, 0.03 MB/s
Interval WAL: 9024 writes, 0 syncs, 9024.00 writes per sync, written: 0.02 GB, 0.03 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   35.64 MB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Sum      2/0   35.64 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3003.2 total, 600.0 interval
Flush(GB): cumulative 0.017, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 6 last_copies: 2 last_secs: 0.000166 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3003.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 6 last_copies: 2 last_secs: 0.000166 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-03:30:49.683290 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-03:30:49.683450 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3603.2 total, 600.0 interval
Cumulative writes: 53K writes, 53K keys, 36K commit groups, 1.5 writes per commit group, ingest: 0.04 GB, 0.01 MB/s
Cumulative WAL: 53K writes, 0 syncs, 53327.00 writes per sync, written: 0.04 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9008 writes, 9008 keys, 6180 commit groups, 1.5 writes per commit group, ingest: 8.16 MB, 0.01 MB/s
Interval WAL: 9008 writes, 0 syncs, 9008.00 writes per sync, written: 0.01 GB, 0.01 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   35.64 MB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Sum      2/0   35.64 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3603.2 total, 600.0 interval
Flush(GB): cumulative 0.017, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 7 last_copies: 2 last_secs: 0.000113 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3603.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 7 last_copies: 2 last_secs: 0.000113 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-03:40:49.684003 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-03:40:49.684164 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4203.2 total, 600.0 interval
Cumulative writes: 62K writes, 62K keys, 42K commit groups, 1.5 writes per commit group, ingest: 0.05 GB, 0.01 MB/s
Cumulative WAL: 62K writes, 0 syncs, 62327.00 writes per sync, written: 0.05 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6018 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   35.64 MB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Sum      2/0   35.64 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4203.2 total, 600.0 interval
Flush(GB): cumulative 0.017, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 8 last_copies: 2 last_secs: 0.000142 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 8 last_copies: 2 last_secs: 0.000142 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-03:45:06.182352 3489 [db/db_impl/db_impl_write.cc:1868] [default] New memtable created with log file: #22. Immutable memtables: 0.
2023/12/22-03:45:06.182887 84 [db/db_impl/db_impl_compaction_flush.cc:109] [JOB 3] Syncing log #19
2023/12/22-03:45:06.190144 84 (Original Log Time 2023/12/22-03:45:06.182807) [db/db_impl/db_impl_compaction_flush.cc:2779] Calling FlushMemTableToOutputFile with column family [default], flush slots available 1, compaction slots available 1, flush slots scheduled 1, compaction slots scheduled 0
2023/12/22-03:45:06.190150 84 [db/flush_job.cc:816] [default] [JOB 3] Flushing memtable with next log file: 22
2023/12/22-03:45:06.190203 84 EVENT_LOG_v1 {"time_micros": 1703216706190185, "job": 3, "event": "flush_started", "num_memtables": 1, "num_entries": 66175, "num_deletes": 0, "total_data_size": 50948847, "memory_usage": 54989136, "flush_reason": "Write Buffer Full"}
2023/12/22-03:45:06.190209 84 [db/flush_job.cc:845] [default] [JOB 3] Level-0 flush table #23: started
2023/12/22-03:45:06.455340 84 EVENT_LOG_v1 {"time_micros": 1703216706455301, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 23, "file_size": 48576904, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 48572528, "index_size": 3420, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 3242491, "raw_average_key_size": 48, "raw_value_size": 47573928, "raw_average_value_size": 718, "num_data_blocks": 64, "num_entries": 66175, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1703212247, "oldest_key_time": 1703212247, "file_creation_time": 1703216706, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "K8YDJ786ULEL7N4QAPAZ", "orig_file_number": 23}}
2023/12/22-03:45:06.455386 84 [db/flush_job.cc:930] [default] [JOB 3] Level-0 flush table #23: 48576904 bytes OK
2023/12/22-03:45:06.455554 84 [db/flush_job.cc:983] [default] [JOB 3] Flush lasted 265417 microseconds, and 258741 cpu microseconds.
2023/12/22-03:45:06.455990 84 (Original Log Time 2023/12/22-03:45:06.455565) [db/memtable_list.cc:469] [default] Level-0 commit table #23 started
2023/12/22-03:45:06.455993 84 (Original Log Time 2023/12/22-03:45:06.455885) [db/memtable_list.cc:675] [default] Level-0 commit table #23: memtable #1 done
2023/12/22-03:45:06.455995 84 (Original Log Time 2023/12/22-03:45:06.455914) EVENT_LOG_v1 {"time_micros": 1703216706455899, "job": 3, "event": "flush_finished", "output_compression": "NoCompression", "lsm_state": [3, 0, 0, 0, 0], "immutable_memtables": 0}
2023/12/22-03:45:06.455997 84 (Original Log Time 2023/12/22-03:45:06.455948) [db/db_impl/db_impl_compaction_flush.cc:288] [default] Level summary: files[3 0 0 0 0] max score 0.75
2023/12/22-03:45:06.456006 84 [db/db_impl/db_impl_files.cc:430] [JOB 3] Try to delete WAL files size 51029102, prev total WAL file size 51029504, number of live WAL files 2.
2023/12/22-03:45:06.469562 84 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000019.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2023/12/22-03:50:49.684759 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-03:50:49.684915 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4803.2 total, 600.0 interval
Cumulative writes: 71K writes, 71K keys, 48K commit groups, 1.5 writes per commit group, ingest: 0.05 GB, 0.01 MB/s
Cumulative WAL: 71K writes, 0 syncs, 71333.00 writes per sync, written: 0.05 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9006 writes, 9006 keys, 6278 commit groups, 1.4 writes per commit group, ingest: 6.27 MB, 0.01 MB/s
Interval WAL: 9006 writes, 0 syncs, 9006.00 writes per sync, written: 0.01 GB, 0.01 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4803.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.045
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.05 GB write, 0.08 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.3 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 9 last_copies: 2 last_secs: 0.000112 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 9 last_copies: 2 last_secs: 0.000112 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-04:00:49.685425 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-04:00:49.685582 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5403.2 total, 600.0 interval
Cumulative writes: 80K writes, 80K keys, 55K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 80K writes, 0 syncs, 80337.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9004 writes, 9004 keys, 6262 commit groups, 1.4 writes per commit group, ingest: 4.38 MB, 0.01 MB/s
Interval WAL: 9004 writes, 0 syncs, 9004.00 writes per sync, written: 0.00 GB, 0.01 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5403.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 10 last_copies: 2 last_secs: 0.000138 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5403.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 10 last_copies: 2 last_secs: 0.000138 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-04:10:49.686166 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-04:10:49.686323 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6003.2 total, 600.0 interval
Cumulative writes: 89K writes, 89K keys, 61K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 89K writes, 0 syncs, 89337.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6216 commit groups, 1.4 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6003.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 11 last_copies: 2 last_secs: 0.000158 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6003.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 11 last_copies: 2 last_secs: 0.000158 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-04:20:49.686783 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-04:20:49.686938 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6603.2 total, 600.0 interval
Cumulative writes: 98K writes, 98K keys, 67K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 98K writes, 0 syncs, 98338.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9001 writes, 9001 keys, 6182 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9001 writes, 0 syncs, 9001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6603.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 12 last_copies: 2 last_secs: 0.000112 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6603.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 12 last_copies: 2 last_secs: 0.000112 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-04:30:49.687430 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-04:30:49.687578 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7203.2 total, 600.0 interval
Cumulative writes: 107K writes, 107K keys, 73K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 107K writes, 0 syncs, 107338.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6161 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7203.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 13 last_copies: 2 last_secs: 0.000103 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 13 last_copies: 2 last_secs: 0.000103 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-04:40:49.688096 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-04:40:49.688254 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7803.2 total, 600.0 interval
Cumulative writes: 116K writes, 116K keys, 80K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 116K writes, 0 syncs, 116339.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9001 writes, 9001 keys, 6273 commit groups, 1.4 writes per commit group, ingest: 0.60 MB, 0.00 MB/s
Interval WAL: 9001 writes, 0 syncs, 9001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7803.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 14 last_copies: 2 last_secs: 0.000114 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 14 last_copies: 2 last_secs: 0.000114 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-04:50:49.688731 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-04:50:49.688883 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 8403.2 total, 600.0 interval
Cumulative writes: 125K writes, 125K keys, 86K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 125K writes, 0 syncs, 125339.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6189 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8403.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 15 last_copies: 2 last_secs: 0.000113 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8403.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 15 last_copies: 2 last_secs: 0.000113 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-05:00:49.689407 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-05:00:49.689578 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9003.2 total, 600.0 interval
Cumulative writes: 134K writes, 134K keys, 92K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 134K writes, 0 syncs, 134339.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6109 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9003.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 16 last_copies: 2 last_secs: 0.000115 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9003.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 16 last_copies: 2 last_secs: 0.000115 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-05:10:49.690096 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-05:10:49.690252 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9603.2 total, 600.0 interval
Cumulative writes: 143K writes, 143K keys, 98K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 143K writes, 0 syncs, 143339.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6206 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9603.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 17 last_copies: 2 last_secs: 0.000112 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9603.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 17 last_copies: 2 last_secs: 0.000112 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-05:20:49.690742 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-05:20:49.690899 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10203.2 total, 600.0 interval
Cumulative writes: 152K writes, 152K keys, 104K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 152K writes, 0 syncs, 152339.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6404 commit groups, 1.4 writes per commit group, ingest: 0.60 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10203.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 18 last_copies: 2 last_secs: 0.000111 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 18 last_copies: 2 last_secs: 0.000111 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-05:30:49.691467 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-05:30:49.691618 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10803.2 total, 600.0 interval
Cumulative writes: 161K writes, 161K keys, 111K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 161K writes, 0 syncs, 161339.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6182 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10803.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 19 last_copies: 2 last_secs: 0.000113 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 19 last_copies: 2 last_secs: 0.000113 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-05:40:49.692131 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-05:40:49.692283 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 11403.2 total, 600.0 interval
Cumulative writes: 170K writes, 170K keys, 117K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 170K writes, 0 syncs, 170339.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6175 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11403.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 20 last_copies: 2 last_secs: 0.000114 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11403.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 20 last_copies: 2 last_secs: 0.000114 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-05:50:49.692838 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-05:50:49.693002 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12003.2 total, 600.0 interval
Cumulative writes: 179K writes, 179K keys, 123K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 179K writes, 0 syncs, 179339.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6150 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12003.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 21 last_copies: 2 last_secs: 0.000149 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12003.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 21 last_copies: 2 last_secs: 0.000149 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-06:00:49.693500 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-06:00:49.693672 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12603.2 total, 600.0 interval
Cumulative writes: 188K writes, 188K keys, 129K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.01 MB/s
Cumulative WAL: 188K writes, 0 syncs, 188339.00 writes per sync, written: 0.06 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6187 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12603.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 22 last_copies: 2 last_secs: 0.000151 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12603.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 22 last_copies: 2 last_secs: 0.000151 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-06:10:49.694179 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-06:10:49.694336 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13203.2 total, 600.0 interval
Cumulative writes: 197K writes, 197K keys, 135K commit groups, 1.5 writes per commit group, ingest: 0.06 GB, 0.00 MB/s
Cumulative WAL: 197K writes, 0 syncs, 197339.00 writes per sync, written: 0.06 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6222 commit groups, 1.4 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13203.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 23 last_copies: 2 last_secs: 0.000114 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 23 last_copies: 2 last_secs: 0.000114 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-06:20:49.694799 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-06:20:49.694940 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13803.2 total, 600.0 interval
Cumulative writes: 206K writes, 206K keys, 142K commit groups, 1.5 writes per commit group, ingest: 0.07 GB, 0.00 MB/s
Cumulative WAL: 206K writes, 0 syncs, 206341.00 writes per sync, written: 0.07 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9002 writes, 9002 keys, 6288 commit groups, 1.4 writes per commit group, ingest: 3.72 MB, 0.01 MB/s
Interval WAL: 9002 writes, 0 syncs, 9002.00 writes per sync, written: 0.00 GB, 0.01 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13803.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 24 last_copies: 2 last_secs: 0.000104 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 24 last_copies: 2 last_secs: 0.000104 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-06:30:49.695469 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-06:30:49.695631 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 14403.2 total, 600.0 interval
Cumulative writes: 215K writes, 215K keys, 148K commit groups, 1.5 writes per commit group, ingest: 0.08 GB, 0.01 MB/s
Cumulative WAL: 215K writes, 0 syncs, 215347.00 writes per sync, written: 0.08 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9006 writes, 9006 keys, 6218 commit groups, 1.4 writes per commit group, ingest: 9.97 MB, 0.02 MB/s
Interval WAL: 9006 writes, 0 syncs, 9006.00 writes per sync, written: 0.01 GB, 0.02 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 14403.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 25 last_copies: 2 last_secs: 0.000118 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 14403.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 25 last_copies: 2 last_secs: 0.000118 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-06:40:49.696137 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-06:40:49.696259 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15003.2 total, 600.0 interval
Cumulative writes: 224K writes, 224K keys, 154K commit groups, 1.5 writes per commit group, ingest: 0.08 GB, 0.01 MB/s
Cumulative WAL: 224K writes, 0 syncs, 224347.00 writes per sync, written: 0.08 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6142 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15003.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 26 last_copies: 2 last_secs: 0.000145 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15003.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 26 last_copies: 2 last_secs: 0.000145 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-06:50:49.696742 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-06:50:49.696876 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15603.2 total, 600.0 interval
Cumulative writes: 233K writes, 233K keys, 160K commit groups, 1.5 writes per commit group, ingest: 0.08 GB, 0.01 MB/s
Cumulative WAL: 233K writes, 0 syncs, 233347.00 writes per sync, written: 0.08 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6296 commit groups, 1.4 writes per commit group, ingest: 0.60 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15603.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 27 last_copies: 2 last_secs: 0.000117 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15603.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 27 last_copies: 2 last_secs: 0.000117 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-07:00:49.697369 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-07:00:49.697499 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16203.2 total, 600.0 interval
Cumulative writes: 242K writes, 242K keys, 167K commit groups, 1.5 writes per commit group, ingest: 0.08 GB, 0.00 MB/s
Cumulative WAL: 242K writes, 0 syncs, 242347.00 writes per sync, written: 0.08 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6212 commit groups, 1.4 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16203.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 28 last_copies: 2 last_secs: 0.00017 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 28 last_copies: 2 last_secs: 0.00017 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-07:10:49.697996 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-07:10:49.698127 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16803.2 total, 600.0 interval
Cumulative writes: 251K writes, 251K keys, 173K commit groups, 1.4 writes per commit group, ingest: 0.08 GB, 0.00 MB/s
Cumulative WAL: 251K writes, 0 syncs, 251347.00 writes per sync, written: 0.08 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6392 commit groups, 1.4 writes per commit group, ingest: 0.60 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16803.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 29 last_copies: 2 last_secs: 0.000112 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 29 last_copies: 2 last_secs: 0.000112 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-07:20:49.698672 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-07:20:49.698802 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 17403.2 total, 600.0 interval
Cumulative writes: 260K writes, 260K keys, 179K commit groups, 1.4 writes per commit group, ingest: 0.08 GB, 0.00 MB/s
Cumulative WAL: 260K writes, 0 syncs, 260347.00 writes per sync, written: 0.08 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6375 commit groups, 1.4 writes per commit group, ingest: 0.60 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 17403.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 30 last_copies: 2 last_secs: 0.000111 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 17403.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 30 last_copies: 2 last_secs: 0.000111 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-07:30:49.699236 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-07:30:49.699368 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18003.2 total, 600.0 interval
Cumulative writes: 269K writes, 269K keys, 186K commit groups, 1.4 writes per commit group, ingest: 0.08 GB, 0.00 MB/s
Cumulative WAL: 269K writes, 0 syncs, 269347.00 writes per sync, written: 0.08 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6379 commit groups, 1.4 writes per commit group, ingest: 0.60 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18003.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 31 last_copies: 2 last_secs: 0.000114 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18003.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 31 last_copies: 2 last_secs: 0.000114 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-07:40:49.699898 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-07:40:49.700035 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18603.2 total, 600.0 interval
Cumulative writes: 278K writes, 278K keys, 192K commit groups, 1.4 writes per commit group, ingest: 0.08 GB, 0.00 MB/s
Cumulative WAL: 278K writes, 0 syncs, 278347.00 writes per sync, written: 0.08 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6120 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18603.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 32 last_copies: 2 last_secs: 0.000163 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18603.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 32 last_copies: 2 last_secs: 0.000163 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-07:50:49.700487 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-07:50:49.700624 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19203.2 total, 600.0 interval
Cumulative writes: 287K writes, 287K keys, 198K commit groups, 1.4 writes per commit group, ingest: 0.08 GB, 0.00 MB/s
Cumulative WAL: 287K writes, 0 syncs, 287347.00 writes per sync, written: 0.08 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6286 commit groups, 1.4 writes per commit group, ingest: 0.60 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19203.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 33 last_copies: 2 last_secs: 0.000117 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 33 last_copies: 2 last_secs: 0.000117 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-08:00:49.701142 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-08:00:49.701276 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19803.2 total, 600.0 interval
Cumulative writes: 296K writes, 296K keys, 204K commit groups, 1.4 writes per commit group, ingest: 0.08 GB, 0.00 MB/s
Cumulative WAL: 296K writes, 0 syncs, 296347.00 writes per sync, written: 0.08 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6068 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19803.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 34 last_copies: 2 last_secs: 0.000144 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 34 last_copies: 2 last_secs: 0.000144 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/22-08:10:49.701856 100 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/22-08:10:49.702022 100 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 20403.2 total, 600.0 interval
Cumulative writes: 305K writes, 305K keys, 210K commit groups, 1.4 writes per commit group, ingest: 0.08 GB, 0.00 MB/s
Cumulative WAL: 305K writes, 0 syncs, 305347.00 writes per sync, written: 0.08 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6183 commit groups, 1.5 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   81.96 MB   0.8      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Sum      3/0   81.96 MB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0    178.5      0.36              0.26         2    0.180       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    174.5      0.27              0.26         1    0.265       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    189.5      0.09              0.00         1    0.094       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20403.2 total, 600.0 interval
Flush(GB): cumulative 0.063, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.06 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 35 last_copies: 2 last_secs: 0.000169 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20403.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7efc80404010#8 capacity: 4.00 GB collections: 35 last_copies: 2 last_secs: 0.000169 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(7,8.05 MB,0.196495%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
