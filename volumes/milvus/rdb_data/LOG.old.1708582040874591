2024/02/22-04:06:43.109612 73 RocksDB version: 6.29.5
2024/02/22-04:06:43.109721 73 Git sha 0
2024/02/22-04:06:43.109724 73 Compile date 2023-11-07 23:58:42
2024/02/22-04:06:43.109727 73 DB SUMMARY
2024/02/22-04:06:43.109729 73 DB Session ID:  JLSV6FBC77UESSINXVME
2024/02/22-04:06:43.109822 73 CURRENT file:  CURRENT
2024/02/22-04:06:43.109824 73 IDENTITY file:  IDENTITY
2024/02/22-04:06:43.109838 73 MANIFEST file:  MANIFEST-000046 size: 580 Bytes
2024/02/22-04:06:43.109842 73 SST files in /var/lib/milvus/rdb_data dir, Total Num: 3, files: 000040.sst 000041.sst 000045.sst 
2024/02/22-04:06:43.109845 73 Write Ahead Log file in /var/lib/milvus/rdb_data: 000047.log size: 18158 ; 
2024/02/22-04:06:43.109848 73                         Options.error_if_exists: 0
2024/02/22-04:06:43.109850 73                       Options.create_if_missing: 1
2024/02/22-04:06:43.109853 73                         Options.paranoid_checks: 1
2024/02/22-04:06:43.109855 73             Options.flush_verify_memtable_count: 1
2024/02/22-04:06:43.109857 73                               Options.track_and_verify_wals_in_manifest: 0
2024/02/22-04:06:43.109859 73                                     Options.env: 0x7f1a3b1792c0
2024/02/22-04:06:43.109862 73                                      Options.fs: PosixFileSystem
2024/02/22-04:06:43.109864 73                                Options.info_log: 0x7f1869a50140
2024/02/22-04:06:43.109866 73                Options.max_file_opening_threads: 16
2024/02/22-04:06:43.109868 73                              Options.statistics: (nil)
2024/02/22-04:06:43.109871 73                               Options.use_fsync: 0
2024/02/22-04:06:43.109873 73                       Options.max_log_file_size: 0
2024/02/22-04:06:43.109875 73                  Options.max_manifest_file_size: 1073741824
2024/02/22-04:06:43.109877 73                   Options.log_file_time_to_roll: 0
2024/02/22-04:06:43.109879 73                       Options.keep_log_file_num: 1000
2024/02/22-04:06:43.109881 73                    Options.recycle_log_file_num: 0
2024/02/22-04:06:43.109883 73                         Options.allow_fallocate: 1
2024/02/22-04:06:43.109885 73                        Options.allow_mmap_reads: 0
2024/02/22-04:06:43.109887 73                       Options.allow_mmap_writes: 0
2024/02/22-04:06:43.109889 73                        Options.use_direct_reads: 0
2024/02/22-04:06:43.109891 73                        Options.use_direct_io_for_flush_and_compaction: 0
2024/02/22-04:06:43.109893 73          Options.create_missing_column_families: 1
2024/02/22-04:06:43.109895 73                              Options.db_log_dir: 
2024/02/22-04:06:43.109898 73                                 Options.wal_dir: 
2024/02/22-04:06:43.109900 73                Options.table_cache_numshardbits: 6
2024/02/22-04:06:43.109902 73                         Options.WAL_ttl_seconds: 0
2024/02/22-04:06:43.109904 73                       Options.WAL_size_limit_MB: 0
2024/02/22-04:06:43.109906 73                        Options.max_write_batch_group_size_bytes: 1048576
2024/02/22-04:06:43.109908 73             Options.manifest_preallocation_size: 4194304
2024/02/22-04:06:43.109910 73                     Options.is_fd_close_on_exec: 1
2024/02/22-04:06:43.109912 73                   Options.advise_random_on_open: 1
2024/02/22-04:06:43.109914 73                   Options.experimental_mempurge_threshold: 0.000000
2024/02/22-04:06:43.109918 73                    Options.db_write_buffer_size: 0
2024/02/22-04:06:43.109920 73                    Options.write_buffer_manager: 0x7f1869a04280
2024/02/22-04:06:43.109922 73         Options.access_hint_on_compaction_start: 1
2024/02/22-04:06:43.109924 73  Options.new_table_reader_for_compaction_inputs: 0
2024/02/22-04:06:43.109926 73           Options.random_access_max_buffer_size: 1048576
2024/02/22-04:06:43.109928 73                      Options.use_adaptive_mutex: 0
2024/02/22-04:06:43.109930 73                            Options.rate_limiter: (nil)
2024/02/22-04:06:43.109933 73     Options.sst_file_manager.rate_bytes_per_sec: 0
2024/02/22-04:06:43.109935 73                       Options.wal_recovery_mode: 2
2024/02/22-04:06:43.109961 73                  Options.enable_thread_tracking: 0
2024/02/22-04:06:43.109964 73                  Options.enable_pipelined_write: 0
2024/02/22-04:06:43.109966 73                  Options.unordered_write: 0
2024/02/22-04:06:43.109968 73         Options.allow_concurrent_memtable_write: 1
2024/02/22-04:06:43.109970 73      Options.enable_write_thread_adaptive_yield: 1
2024/02/22-04:06:43.109972 73             Options.write_thread_max_yield_usec: 100
2024/02/22-04:06:43.109974 73            Options.write_thread_slow_yield_usec: 3
2024/02/22-04:06:43.109976 73                               Options.row_cache: None
2024/02/22-04:06:43.109978 73                              Options.wal_filter: None
2024/02/22-04:06:43.109980 73             Options.avoid_flush_during_recovery: 0
2024/02/22-04:06:43.109982 73             Options.allow_ingest_behind: 0
2024/02/22-04:06:43.109984 73             Options.preserve_deletes: 0
2024/02/22-04:06:43.109986 73             Options.two_write_queues: 0
2024/02/22-04:06:43.109988 73             Options.manual_wal_flush: 0
2024/02/22-04:06:43.109990 73             Options.atomic_flush: 0
2024/02/22-04:06:43.109992 73             Options.avoid_unnecessary_blocking_io: 0
2024/02/22-04:06:43.109994 73                 Options.persist_stats_to_disk: 0
2024/02/22-04:06:43.109996 73                 Options.write_dbid_to_manifest: 0
2024/02/22-04:06:43.109998 73                 Options.log_readahead_size: 0
2024/02/22-04:06:43.110000 73                 Options.file_checksum_gen_factory: Unknown
2024/02/22-04:06:43.110002 73                 Options.best_efforts_recovery: 0
2024/02/22-04:06:43.110004 73                Options.max_bgerror_resume_count: 2147483647
2024/02/22-04:06:43.110006 73            Options.bgerror_resume_retry_interval: 1000000
2024/02/22-04:06:43.110009 73             Options.allow_data_in_errors: 0
2024/02/22-04:06:43.110011 73             Options.db_host_id: __hostname__
2024/02/22-04:06:43.110013 73             Options.max_background_jobs: 4
2024/02/22-04:06:43.110015 73             Options.max_background_compactions: -1
2024/02/22-04:06:43.110017 73             Options.max_subcompactions: 1
2024/02/22-04:06:43.110019 73             Options.avoid_flush_during_shutdown: 0
2024/02/22-04:06:43.110021 73           Options.writable_file_max_buffer_size: 1048576
2024/02/22-04:06:43.110023 73             Options.delayed_write_rate : 16777216
2024/02/22-04:06:43.110025 73             Options.max_total_wal_size: 0
2024/02/22-04:06:43.110028 73             Options.delete_obsolete_files_period_micros: 21600000000
2024/02/22-04:06:43.110030 73                   Options.stats_dump_period_sec: 600
2024/02/22-04:06:43.110032 73                 Options.stats_persist_period_sec: 600
2024/02/22-04:06:43.110034 73                 Options.stats_history_buffer_size: 1048576
2024/02/22-04:06:43.110036 73                          Options.max_open_files: -1
2024/02/22-04:06:43.110038 73                          Options.bytes_per_sync: 0
2024/02/22-04:06:43.110040 73                      Options.wal_bytes_per_sync: 0
2024/02/22-04:06:43.110042 73                   Options.strict_bytes_per_sync: 0
2024/02/22-04:06:43.110044 73       Options.compaction_readahead_size: 0
2024/02/22-04:06:43.110046 73                  Options.max_background_flushes: 1
2024/02/22-04:06:43.110048 73 Compression algorithms supported:
2024/02/22-04:06:43.110050 73 	kZSTDNotFinalCompression supported: 1
2024/02/22-04:06:43.110053 73 	kZSTD supported: 1
2024/02/22-04:06:43.110055 73 	kXpressCompression supported: 0
2024/02/22-04:06:43.110057 73 	kLZ4HCCompression supported: 0
2024/02/22-04:06:43.110059 73 	kLZ4Compression supported: 0
2024/02/22-04:06:43.110061 73 	kBZip2Compression supported: 0
2024/02/22-04:06:43.110063 73 	kZlibCompression supported: 0
2024/02/22-04:06:43.110065 73 	kSnappyCompression supported: 0
2024/02/22-04:06:43.110068 73 Fast CRC32 supported: Not supported on x86
2024/02/22-04:06:43.110228 73 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000046
2024/02/22-04:06:43.110574 73 [db/column_family.cc:605] --------------- Options for column family [default]:
2024/02/22-04:06:43.110578 73               Options.comparator: leveldb.BytewiseComparator
2024/02/22-04:06:43.110581 73           Options.merge_operator: None
2024/02/22-04:06:43.110583 73        Options.compaction_filter: None
2024/02/22-04:06:43.110585 73        Options.compaction_filter_factory: None
2024/02/22-04:06:43.110587 73  Options.sst_partitioner_factory: None
2024/02/22-04:06:43.110589 73         Options.memtable_factory: SkipListFactory
2024/02/22-04:06:43.110591 73            Options.table_factory: BlockBasedTable
2024/02/22-04:06:43.110620 73            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1869a16480)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f1869a04010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-04:06:43.110630 73        Options.write_buffer_size: 67108864
2024/02/22-04:06:43.110632 73  Options.max_write_buffer_number: 2
2024/02/22-04:06:43.110635 73        Options.compression[0]: NoCompression
2024/02/22-04:06:43.110637 73        Options.compression[1]: NoCompression
2024/02/22-04:06:43.110639 73        Options.compression[2]: ZSTD
2024/02/22-04:06:43.110642 73        Options.compression[3]: ZSTD
2024/02/22-04:06:43.110644 73        Options.compression[4]: ZSTD
2024/02/22-04:06:43.110646 73                  Options.bottommost_compression: Disabled
2024/02/22-04:06:43.110648 73       Options.prefix_extractor: nullptr
2024/02/22-04:06:43.110650 73   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-04:06:43.110652 73             Options.num_levels: 5
2024/02/22-04:06:43.110654 73        Options.min_write_buffer_number_to_merge: 1
2024/02/22-04:06:43.110656 73     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-04:06:43.110658 73     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-04:06:43.110660 73            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-04:06:43.110662 73                  Options.bottommost_compression_opts.level: 32767
2024/02/22-04:06:43.110664 73               Options.bottommost_compression_opts.strategy: 0
2024/02/22-04:06:43.110666 73         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-04:06:43.110668 73         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-04:06:43.110670 73         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-04:06:43.110672 73                  Options.bottommost_compression_opts.enabled: false
2024/02/22-04:06:43.110674 73         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-04:06:43.110676 73            Options.compression_opts.window_bits: -14
2024/02/22-04:06:43.110679 73                  Options.compression_opts.level: 32767
2024/02/22-04:06:43.110681 73               Options.compression_opts.strategy: 0
2024/02/22-04:06:43.110683 73         Options.compression_opts.max_dict_bytes: 0
2024/02/22-04:06:43.110685 73         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-04:06:43.110687 73         Options.compression_opts.parallel_threads: 1
2024/02/22-04:06:43.110718 73                  Options.compression_opts.enabled: false
2024/02/22-04:06:43.110721 73         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-04:06:43.110723 73      Options.level0_file_num_compaction_trigger: 4
2024/02/22-04:06:43.110725 73          Options.level0_slowdown_writes_trigger: 20
2024/02/22-04:06:43.110727 73              Options.level0_stop_writes_trigger: 36
2024/02/22-04:06:43.110729 73                   Options.target_file_size_base: 67108864
2024/02/22-04:06:43.110731 73             Options.target_file_size_multiplier: 2
2024/02/22-04:06:43.110733 73                Options.max_bytes_for_level_base: 268435456
2024/02/22-04:06:43.110735 73 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-04:06:43.110737 73          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-04:06:43.110741 73 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-04:06:43.110743 73 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-04:06:43.110745 73 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-04:06:43.110747 73 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-04:06:43.110749 73 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-04:06:43.110751 73 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-04:06:43.110753 73 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-04:06:43.110755 73       Options.max_sequential_skip_in_iterations: 8
2024/02/22-04:06:43.110757 73                    Options.max_compaction_bytes: 1677721600
2024/02/22-04:06:43.110759 73                        Options.arena_block_size: 1048576
2024/02/22-04:06:43.110761 73   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-04:06:43.110763 73   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-04:06:43.110766 73       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-04:06:43.110768 73                Options.disable_auto_compactions: 0
2024/02/22-04:06:43.110770 73                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-04:06:43.110772 73                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-04:06:43.110775 73 Options.compaction_options_universal.size_ratio: 1
2024/02/22-04:06:43.110777 73 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-04:06:43.110779 73 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-04:06:43.110781 73 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-04:06:43.110783 73 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-04:06:43.110785 73 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-04:06:43.110787 73 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-04:06:43.110789 73 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-04:06:43.110796 73                   Options.table_properties_collectors: 
2024/02/22-04:06:43.110798 73                   Options.inplace_update_support: 0
2024/02/22-04:06:43.110800 73                 Options.inplace_update_num_locks: 10000
2024/02/22-04:06:43.110802 73               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-04:06:43.110805 73               Options.memtable_whole_key_filtering: 0
2024/02/22-04:06:43.110807 73   Options.memtable_huge_page_size: 0
2024/02/22-04:06:43.110809 73                           Options.bloom_locality: 0
2024/02/22-04:06:43.110811 73                    Options.max_successive_merges: 0
2024/02/22-04:06:43.110813 73                Options.optimize_filters_for_hits: 0
2024/02/22-04:06:43.110815 73                Options.paranoid_file_checks: 0
2024/02/22-04:06:43.110817 73                Options.force_consistency_checks: 1
2024/02/22-04:06:43.110819 73                Options.report_bg_io_stats: 0
2024/02/22-04:06:43.110821 73                               Options.ttl: 2592000
2024/02/22-04:06:43.110823 73          Options.periodic_compaction_seconds: 0
2024/02/22-04:06:43.110847 73                       Options.enable_blob_files: false
2024/02/22-04:06:43.110850 73                           Options.min_blob_size: 0
2024/02/22-04:06:43.110852 73                          Options.blob_file_size: 268435456
2024/02/22-04:06:43.110854 73                   Options.blob_compression_type: NoCompression
2024/02/22-04:06:43.110856 73          Options.enable_blob_garbage_collection: false
2024/02/22-04:06:43.110858 73      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-04:06:43.110861 73 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-04:06:43.110863 73          Options.blob_compaction_readahead_size: 0
2024/02/22-04:06:43.110992 73 [db/column_family.cc:605] --------------- Options for column family [properties]:
2024/02/22-04:06:43.110996 73               Options.comparator: leveldb.BytewiseComparator
2024/02/22-04:06:43.110998 73           Options.merge_operator: None
2024/02/22-04:06:43.111000 73        Options.compaction_filter: None
2024/02/22-04:06:43.111003 73        Options.compaction_filter_factory: None
2024/02/22-04:06:43.111004 73  Options.sst_partitioner_factory: None
2024/02/22-04:06:43.111006 73         Options.memtable_factory: SkipListFactory
2024/02/22-04:06:43.111008 73            Options.table_factory: BlockBasedTable
2024/02/22-04:06:43.111029 73            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1869a16480)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f1869a04010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2024/02/22-04:06:43.111033 73        Options.write_buffer_size: 67108864
2024/02/22-04:06:43.111035 73  Options.max_write_buffer_number: 2
2024/02/22-04:06:43.111037 73        Options.compression[0]: NoCompression
2024/02/22-04:06:43.111039 73        Options.compression[1]: NoCompression
2024/02/22-04:06:43.111042 73        Options.compression[2]: ZSTD
2024/02/22-04:06:43.111044 73        Options.compression[3]: ZSTD
2024/02/22-04:06:43.111046 73        Options.compression[4]: ZSTD
2024/02/22-04:06:43.111048 73                  Options.bottommost_compression: Disabled
2024/02/22-04:06:43.111050 73       Options.prefix_extractor: nullptr
2024/02/22-04:06:43.111052 73   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2024/02/22-04:06:43.111054 73             Options.num_levels: 5
2024/02/22-04:06:43.111056 73        Options.min_write_buffer_number_to_merge: 1
2024/02/22-04:06:43.111058 73     Options.max_write_buffer_number_to_maintain: 0
2024/02/22-04:06:43.111060 73     Options.max_write_buffer_size_to_maintain: 0
2024/02/22-04:06:43.111062 73            Options.bottommost_compression_opts.window_bits: -14
2024/02/22-04:06:43.111064 73                  Options.bottommost_compression_opts.level: 32767
2024/02/22-04:06:43.111066 73               Options.bottommost_compression_opts.strategy: 0
2024/02/22-04:06:43.111068 73         Options.bottommost_compression_opts.max_dict_bytes: 0
2024/02/22-04:06:43.111070 73         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2024/02/22-04:06:43.111094 73         Options.bottommost_compression_opts.parallel_threads: 1
2024/02/22-04:06:43.111097 73                  Options.bottommost_compression_opts.enabled: false
2024/02/22-04:06:43.111099 73         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2024/02/22-04:06:43.111101 73            Options.compression_opts.window_bits: -14
2024/02/22-04:06:43.111103 73                  Options.compression_opts.level: 32767
2024/02/22-04:06:43.111105 73               Options.compression_opts.strategy: 0
2024/02/22-04:06:43.111107 73         Options.compression_opts.max_dict_bytes: 0
2024/02/22-04:06:43.111109 73         Options.compression_opts.zstd_max_train_bytes: 0
2024/02/22-04:06:43.111111 73         Options.compression_opts.parallel_threads: 1
2024/02/22-04:06:43.111113 73                  Options.compression_opts.enabled: false
2024/02/22-04:06:43.111115 73         Options.compression_opts.max_dict_buffer_bytes: 0
2024/02/22-04:06:43.111117 73      Options.level0_file_num_compaction_trigger: 4
2024/02/22-04:06:43.111119 73          Options.level0_slowdown_writes_trigger: 20
2024/02/22-04:06:43.111121 73              Options.level0_stop_writes_trigger: 36
2024/02/22-04:06:43.111123 73                   Options.target_file_size_base: 67108864
2024/02/22-04:06:43.111125 73             Options.target_file_size_multiplier: 2
2024/02/22-04:06:43.111127 73                Options.max_bytes_for_level_base: 268435456
2024/02/22-04:06:43.111129 73 Options.level_compaction_dynamic_level_bytes: 0
2024/02/22-04:06:43.111131 73          Options.max_bytes_for_level_multiplier: 10.000000
2024/02/22-04:06:43.111134 73 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2024/02/22-04:06:43.111136 73 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2024/02/22-04:06:43.111139 73 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2024/02/22-04:06:43.111141 73 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2024/02/22-04:06:43.111143 73 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2024/02/22-04:06:43.111145 73 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2024/02/22-04:06:43.111147 73 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2024/02/22-04:06:43.111149 73       Options.max_sequential_skip_in_iterations: 8
2024/02/22-04:06:43.111151 73                    Options.max_compaction_bytes: 1677721600
2024/02/22-04:06:43.111153 73                        Options.arena_block_size: 1048576
2024/02/22-04:06:43.111155 73   Options.soft_pending_compaction_bytes_limit: 68719476736
2024/02/22-04:06:43.111157 73   Options.hard_pending_compaction_bytes_limit: 274877906944
2024/02/22-04:06:43.111159 73       Options.rate_limit_delay_max_milliseconds: 100
2024/02/22-04:06:43.111161 73                Options.disable_auto_compactions: 0
2024/02/22-04:06:43.111163 73                        Options.compaction_style: kCompactionStyleLevel
2024/02/22-04:06:43.111165 73                          Options.compaction_pri: kMinOverlappingRatio
2024/02/22-04:06:43.111167 73 Options.compaction_options_universal.size_ratio: 1
2024/02/22-04:06:43.111169 73 Options.compaction_options_universal.min_merge_width: 2
2024/02/22-04:06:43.111171 73 Options.compaction_options_universal.max_merge_width: 4294967295
2024/02/22-04:06:43.111173 73 Options.compaction_options_universal.max_size_amplification_percent: 200
2024/02/22-04:06:43.111175 73 Options.compaction_options_universal.compression_size_percent: -1
2024/02/22-04:06:43.111178 73 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2024/02/22-04:06:43.111180 73 Options.compaction_options_fifo.max_table_files_size: 1073741824
2024/02/22-04:06:43.111182 73 Options.compaction_options_fifo.allow_compaction: 0
2024/02/22-04:06:43.111185 73                   Options.table_properties_collectors: 
2024/02/22-04:06:43.111187 73                   Options.inplace_update_support: 0
2024/02/22-04:06:43.111189 73                 Options.inplace_update_num_locks: 10000
2024/02/22-04:06:43.111191 73               Options.memtable_prefix_bloom_size_ratio: 0.000000
2024/02/22-04:06:43.111214 73               Options.memtable_whole_key_filtering: 0
2024/02/22-04:06:43.111217 73   Options.memtable_huge_page_size: 0
2024/02/22-04:06:43.111219 73                           Options.bloom_locality: 0
2024/02/22-04:06:43.111221 73                    Options.max_successive_merges: 0
2024/02/22-04:06:43.111223 73                Options.optimize_filters_for_hits: 0
2024/02/22-04:06:43.111225 73                Options.paranoid_file_checks: 0
2024/02/22-04:06:43.111227 73                Options.force_consistency_checks: 1
2024/02/22-04:06:43.111229 73                Options.report_bg_io_stats: 0
2024/02/22-04:06:43.111231 73                               Options.ttl: 2592000
2024/02/22-04:06:43.111233 73          Options.periodic_compaction_seconds: 0
2024/02/22-04:06:43.111235 73                       Options.enable_blob_files: false
2024/02/22-04:06:43.111237 73                           Options.min_blob_size: 0
2024/02/22-04:06:43.111239 73                          Options.blob_file_size: 268435456
2024/02/22-04:06:43.111241 73                   Options.blob_compression_type: NoCompression
2024/02/22-04:06:43.111243 73          Options.enable_blob_garbage_collection: false
2024/02/22-04:06:43.111245 73      Options.blob_garbage_collection_age_cutoff: 0.250000
2024/02/22-04:06:43.111248 73 Options.blob_garbage_collection_force_threshold: 1.000000
2024/02/22-04:06:43.111250 73          Options.blob_compaction_readahead_size: 0
2024/02/22-04:06:43.117606 73 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000046 succeeded,manifest_file_number is 46, next_file_number is 48, last_sequence is 425308, log_number is 38,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 22
2024/02/22-04:06:43.117615 73 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 38
2024/02/22-04:06:43.117618 73 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 38
2024/02/22-04:06:43.117781 73 [db/version_set.cc:4409] Creating manifest 50
2024/02/22-04:06:43.119377 73 EVENT_LOG_v1 {"time_micros": 1708574803119371, "job": 1, "event": "recovery_started", "wal_files": [47]}
2024/02/22-04:06:43.119385 73 [db/db_impl/db_impl_open.cc:888] Recovering log #47 mode 2
2024/02/22-04:06:43.120611 73 EVENT_LOG_v1 {"time_micros": 1708574803120579, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 51, "file_size": 8560, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 7582, "index_size": 59, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 11564, "raw_average_key_size": 49, "raw_value_size": 4012, "raw_average_value_size": 17, "num_data_blocks": 1, "num_entries": 236, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1708574803, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "4604bef7-f227-4260-bd81-5e50a0c304f5", "db_session_id": "JLSV6FBC77UESSINXVME", "orig_file_number": 51}}
2024/02/22-04:06:43.120689 73 [db/version_set.cc:4409] Creating manifest 52
2024/02/22-04:06:43.122130 73 EVENT_LOG_v1 {"time_micros": 1708574803122125, "job": 1, "event": "recovery_finished"}
2024/02/22-04:06:43.125714 73 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000047.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2024/02/22-04:06:43.125774 73 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f1869a55700
2024/02/22-04:06:43.125862 73 DB pointer 0x7f1869a3cc00
2024/02/22-04:06:43.125948 83 (Original Log Time 2024/02/22-04:06:43.125885) [db/db_impl/db_impl_compaction_flush.cc:3225] Compaction nothing to do
2024/02/22-04:06:46.126230 102 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2024/02/22-04:06:46.126258 102 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 26 writes, 26 keys, 25 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 26 writes, 0 syncs, 26.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 26 writes, 26 keys, 25 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 26 writes, 0 syncs, 26.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   610.68 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      9.1      0.00              0.00         1    0.001       0      0       0.0       0.0
  L1      2/0   112.75 MB   0.4      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0   113.34 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      9.1      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      9.1      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      9.1      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f1869a04010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 8.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f1869a04010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 8.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
