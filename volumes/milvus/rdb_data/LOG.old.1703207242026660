2023/12/21-08:45:04.971261 19 RocksDB version: 6.29.5
2023/12/21-08:45:04.971284 19 Git sha 0
2023/12/21-08:45:04.971285 19 Compile date 2023-11-07 23:58:42
2023/12/21-08:45:04.971286 19 DB SUMMARY
2023/12/21-08:45:04.971287 19 DB Session ID:  6GC3LBFAJNN3L0VL1VTB
2023/12/21-08:45:04.971303 19 SST files in /var/lib/milvus/rdb_data dir, Total Num: 0, files: 
2023/12/21-08:45:04.971304 19 Write Ahead Log file in /var/lib/milvus/rdb_data: 
2023/12/21-08:45:04.971305 19                         Options.error_if_exists: 0
2023/12/21-08:45:04.971306 19                       Options.create_if_missing: 1
2023/12/21-08:45:04.971306 19                         Options.paranoid_checks: 1
2023/12/21-08:45:04.971307 19             Options.flush_verify_memtable_count: 1
2023/12/21-08:45:04.971308 19                               Options.track_and_verify_wals_in_manifest: 0
2023/12/21-08:45:04.971308 19                                     Options.env: 0x7fdd98e692c0
2023/12/21-08:45:04.971309 19                                      Options.fs: PosixFileSystem
2023/12/21-08:45:04.971310 19                                Options.info_log: 0x7fdd57a50140
2023/12/21-08:45:04.971311 19                Options.max_file_opening_threads: 16
2023/12/21-08:45:04.971311 19                              Options.statistics: (nil)
2023/12/21-08:45:04.971312 19                               Options.use_fsync: 0
2023/12/21-08:45:04.971313 19                       Options.max_log_file_size: 0
2023/12/21-08:45:04.971313 19                  Options.max_manifest_file_size: 1073741824
2023/12/21-08:45:04.971314 19                   Options.log_file_time_to_roll: 0
2023/12/21-08:45:04.971315 19                       Options.keep_log_file_num: 1000
2023/12/21-08:45:04.971315 19                    Options.recycle_log_file_num: 0
2023/12/21-08:45:04.971316 19                         Options.allow_fallocate: 1
2023/12/21-08:45:04.971317 19                        Options.allow_mmap_reads: 0
2023/12/21-08:45:04.971317 19                       Options.allow_mmap_writes: 0
2023/12/21-08:45:04.971318 19                        Options.use_direct_reads: 0
2023/12/21-08:45:04.971318 19                        Options.use_direct_io_for_flush_and_compaction: 0
2023/12/21-08:45:04.971319 19          Options.create_missing_column_families: 1
2023/12/21-08:45:04.971320 19                              Options.db_log_dir: 
2023/12/21-08:45:04.971320 19                                 Options.wal_dir: 
2023/12/21-08:45:04.971321 19                Options.table_cache_numshardbits: 6
2023/12/21-08:45:04.971321 19                         Options.WAL_ttl_seconds: 0
2023/12/21-08:45:04.971322 19                       Options.WAL_size_limit_MB: 0
2023/12/21-08:45:04.971323 19                        Options.max_write_batch_group_size_bytes: 1048576
2023/12/21-08:45:04.971323 19             Options.manifest_preallocation_size: 4194304
2023/12/21-08:45:04.971324 19                     Options.is_fd_close_on_exec: 1
2023/12/21-08:45:04.971324 19                   Options.advise_random_on_open: 1
2023/12/21-08:45:04.971325 19                   Options.experimental_mempurge_threshold: 0.000000
2023/12/21-08:45:04.971326 19                    Options.db_write_buffer_size: 0
2023/12/21-08:45:04.971327 19                    Options.write_buffer_manager: 0x7fdd57a04280
2023/12/21-08:45:04.971327 19         Options.access_hint_on_compaction_start: 1
2023/12/21-08:45:04.971328 19  Options.new_table_reader_for_compaction_inputs: 0
2023/12/21-08:45:04.971329 19           Options.random_access_max_buffer_size: 1048576
2023/12/21-08:45:04.971329 19                      Options.use_adaptive_mutex: 0
2023/12/21-08:45:04.971330 19                            Options.rate_limiter: (nil)
2023/12/21-08:45:04.971330 19     Options.sst_file_manager.rate_bytes_per_sec: 0
2023/12/21-08:45:04.971331 19                       Options.wal_recovery_mode: 2
2023/12/21-08:45:04.971332 19                  Options.enable_thread_tracking: 0
2023/12/21-08:45:04.971332 19                  Options.enable_pipelined_write: 0
2023/12/21-08:45:04.971333 19                  Options.unordered_write: 0
2023/12/21-08:45:04.971341 19         Options.allow_concurrent_memtable_write: 1
2023/12/21-08:45:04.971341 19      Options.enable_write_thread_adaptive_yield: 1
2023/12/21-08:45:04.971342 19             Options.write_thread_max_yield_usec: 100
2023/12/21-08:45:04.971342 19            Options.write_thread_slow_yield_usec: 3
2023/12/21-08:45:04.971343 19                               Options.row_cache: None
2023/12/21-08:45:04.971343 19                              Options.wal_filter: None
2023/12/21-08:45:04.971344 19             Options.avoid_flush_during_recovery: 0
2023/12/21-08:45:04.971345 19             Options.allow_ingest_behind: 0
2023/12/21-08:45:04.971345 19             Options.preserve_deletes: 0
2023/12/21-08:45:04.971346 19             Options.two_write_queues: 0
2023/12/21-08:45:04.971346 19             Options.manual_wal_flush: 0
2023/12/21-08:45:04.971347 19             Options.atomic_flush: 0
2023/12/21-08:45:04.971347 19             Options.avoid_unnecessary_blocking_io: 0
2023/12/21-08:45:04.971348 19                 Options.persist_stats_to_disk: 0
2023/12/21-08:45:04.971348 19                 Options.write_dbid_to_manifest: 0
2023/12/21-08:45:04.971349 19                 Options.log_readahead_size: 0
2023/12/21-08:45:04.971349 19                 Options.file_checksum_gen_factory: Unknown
2023/12/21-08:45:04.971350 19                 Options.best_efforts_recovery: 0
2023/12/21-08:45:04.971351 19                Options.max_bgerror_resume_count: 2147483647
2023/12/21-08:45:04.971351 19            Options.bgerror_resume_retry_interval: 1000000
2023/12/21-08:45:04.971352 19             Options.allow_data_in_errors: 0
2023/12/21-08:45:04.971353 19             Options.db_host_id: __hostname__
2023/12/21-08:45:04.971353 19             Options.max_background_jobs: 1
2023/12/21-08:45:04.971354 19             Options.max_background_compactions: -1
2023/12/21-08:45:04.971354 19             Options.max_subcompactions: 1
2023/12/21-08:45:04.971355 19             Options.avoid_flush_during_shutdown: 0
2023/12/21-08:45:04.971356 19           Options.writable_file_max_buffer_size: 1048576
2023/12/21-08:45:04.971356 19             Options.delayed_write_rate : 16777216
2023/12/21-08:45:04.971357 19             Options.max_total_wal_size: 0
2023/12/21-08:45:04.971357 19             Options.delete_obsolete_files_period_micros: 21600000000
2023/12/21-08:45:04.971358 19                   Options.stats_dump_period_sec: 600
2023/12/21-08:45:04.971359 19                 Options.stats_persist_period_sec: 600
2023/12/21-08:45:04.971359 19                 Options.stats_history_buffer_size: 1048576
2023/12/21-08:45:04.971360 19                          Options.max_open_files: -1
2023/12/21-08:45:04.971360 19                          Options.bytes_per_sync: 0
2023/12/21-08:45:04.971361 19                      Options.wal_bytes_per_sync: 0
2023/12/21-08:45:04.971361 19                   Options.strict_bytes_per_sync: 0
2023/12/21-08:45:04.971362 19       Options.compaction_readahead_size: 0
2023/12/21-08:45:04.971362 19                  Options.max_background_flushes: 1
2023/12/21-08:45:04.971363 19 Compression algorithms supported:
2023/12/21-08:45:04.971364 19 	kZSTDNotFinalCompression supported: 1
2023/12/21-08:45:04.971364 19 	kZSTD supported: 1
2023/12/21-08:45:04.971365 19 	kXpressCompression supported: 0
2023/12/21-08:45:04.971365 19 	kLZ4HCCompression supported: 0
2023/12/21-08:45:04.971366 19 	kLZ4Compression supported: 0
2023/12/21-08:45:04.971367 19 	kBZip2Compression supported: 0
2023/12/21-08:45:04.971367 19 	kZlibCompression supported: 0
2023/12/21-08:45:04.971368 19 	kSnappyCompression supported: 0
2023/12/21-08:45:04.971369 19 Fast CRC32 supported: Not supported on x86
2023/12/21-08:45:04.972283 19 [db/db_impl/db_impl_open.cc:307] Creating manifest 1 
2023/12/21-08:45:04.973771 19 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000001
2023/12/21-08:45:04.973870 19 [db/column_family.cc:605] --------------- Options for column family [default]:
2023/12/21-08:45:04.973871 19               Options.comparator: leveldb.BytewiseComparator
2023/12/21-08:45:04.973884 19           Options.merge_operator: None
2023/12/21-08:45:04.973884 19        Options.compaction_filter: None
2023/12/21-08:45:04.973885 19        Options.compaction_filter_factory: None
2023/12/21-08:45:04.973886 19  Options.sst_partitioner_factory: None
2023/12/21-08:45:04.973886 19         Options.memtable_factory: SkipListFactory
2023/12/21-08:45:04.973887 19            Options.table_factory: BlockBasedTable
2023/12/21-08:45:04.973896 19            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fdd57bda1a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fdd57a04010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1074680217
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2023/12/21-08:45:04.973900 19        Options.write_buffer_size: 67108864
2023/12/21-08:45:04.973901 19  Options.max_write_buffer_number: 2
2023/12/21-08:45:04.973901 19        Options.compression[0]: NoCompression
2023/12/21-08:45:04.973902 19        Options.compression[1]: NoCompression
2023/12/21-08:45:04.973903 19        Options.compression[2]: ZSTD
2023/12/21-08:45:04.973903 19        Options.compression[3]: ZSTD
2023/12/21-08:45:04.973904 19        Options.compression[4]: ZSTD
2023/12/21-08:45:04.973904 19                  Options.bottommost_compression: Disabled
2023/12/21-08:45:04.973905 19       Options.prefix_extractor: nullptr
2023/12/21-08:45:04.973905 19   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2023/12/21-08:45:04.973906 19             Options.num_levels: 5
2023/12/21-08:45:04.973906 19        Options.min_write_buffer_number_to_merge: 1
2023/12/21-08:45:04.973907 19     Options.max_write_buffer_number_to_maintain: 0
2023/12/21-08:45:04.973908 19     Options.max_write_buffer_size_to_maintain: 0
2023/12/21-08:45:04.973908 19            Options.bottommost_compression_opts.window_bits: -14
2023/12/21-08:45:04.973909 19                  Options.bottommost_compression_opts.level: 32767
2023/12/21-08:45:04.973909 19               Options.bottommost_compression_opts.strategy: 0
2023/12/21-08:45:04.973910 19         Options.bottommost_compression_opts.max_dict_bytes: 0
2023/12/21-08:45:04.973911 19         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2023/12/21-08:45:04.973911 19         Options.bottommost_compression_opts.parallel_threads: 1
2023/12/21-08:45:04.973912 19                  Options.bottommost_compression_opts.enabled: false
2023/12/21-08:45:04.973912 19         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2023/12/21-08:45:04.973913 19            Options.compression_opts.window_bits: -14
2023/12/21-08:45:04.973913 19                  Options.compression_opts.level: 32767
2023/12/21-08:45:04.973914 19               Options.compression_opts.strategy: 0
2023/12/21-08:45:04.973914 19         Options.compression_opts.max_dict_bytes: 0
2023/12/21-08:45:04.973915 19         Options.compression_opts.zstd_max_train_bytes: 0
2023/12/21-08:45:04.973915 19         Options.compression_opts.parallel_threads: 1
2023/12/21-08:45:04.973916 19                  Options.compression_opts.enabled: false
2023/12/21-08:45:04.973917 19         Options.compression_opts.max_dict_buffer_bytes: 0
2023/12/21-08:45:04.973923 19      Options.level0_file_num_compaction_trigger: 4
2023/12/21-08:45:04.973924 19          Options.level0_slowdown_writes_trigger: 20
2023/12/21-08:45:04.973925 19              Options.level0_stop_writes_trigger: 36
2023/12/21-08:45:04.973925 19                   Options.target_file_size_base: 67108864
2023/12/21-08:45:04.973926 19             Options.target_file_size_multiplier: 2
2023/12/21-08:45:04.973926 19                Options.max_bytes_for_level_base: 268435456
2023/12/21-08:45:04.973927 19 Options.level_compaction_dynamic_level_bytes: 0
2023/12/21-08:45:04.973927 19          Options.max_bytes_for_level_multiplier: 10.000000
2023/12/21-08:45:04.973928 19 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2023/12/21-08:45:04.973929 19 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2023/12/21-08:45:04.973930 19 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2023/12/21-08:45:04.973930 19 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2023/12/21-08:45:04.973931 19 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2023/12/21-08:45:04.973931 19 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2023/12/21-08:45:04.973932 19 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2023/12/21-08:45:04.973932 19       Options.max_sequential_skip_in_iterations: 8
2023/12/21-08:45:04.973933 19                    Options.max_compaction_bytes: 1677721600
2023/12/21-08:45:04.973933 19                        Options.arena_block_size: 1048576
2023/12/21-08:45:04.973934 19   Options.soft_pending_compaction_bytes_limit: 68719476736
2023/12/21-08:45:04.973935 19   Options.hard_pending_compaction_bytes_limit: 274877906944
2023/12/21-08:45:04.973935 19       Options.rate_limit_delay_max_milliseconds: 100
2023/12/21-08:45:04.973936 19                Options.disable_auto_compactions: 0
2023/12/21-08:45:04.973937 19                        Options.compaction_style: kCompactionStyleLevel
2023/12/21-08:45:04.973937 19                          Options.compaction_pri: kMinOverlappingRatio
2023/12/21-08:45:04.973938 19 Options.compaction_options_universal.size_ratio: 1
2023/12/21-08:45:04.973939 19 Options.compaction_options_universal.min_merge_width: 2
2023/12/21-08:45:04.973939 19 Options.compaction_options_universal.max_merge_width: 4294967295
2023/12/21-08:45:04.973940 19 Options.compaction_options_universal.max_size_amplification_percent: 200
2023/12/21-08:45:04.973940 19 Options.compaction_options_universal.compression_size_percent: -1
2023/12/21-08:45:04.973941 19 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2023/12/21-08:45:04.973941 19 Options.compaction_options_fifo.max_table_files_size: 1073741824
2023/12/21-08:45:04.973942 19 Options.compaction_options_fifo.allow_compaction: 0
2023/12/21-08:45:04.973944 19                   Options.table_properties_collectors: 
2023/12/21-08:45:04.973945 19                   Options.inplace_update_support: 0
2023/12/21-08:45:04.973945 19                 Options.inplace_update_num_locks: 10000
2023/12/21-08:45:04.973946 19               Options.memtable_prefix_bloom_size_ratio: 0.000000
2023/12/21-08:45:04.973946 19               Options.memtable_whole_key_filtering: 0
2023/12/21-08:45:04.973947 19   Options.memtable_huge_page_size: 0
2023/12/21-08:45:04.973948 19                           Options.bloom_locality: 0
2023/12/21-08:45:04.973948 19                    Options.max_successive_merges: 0
2023/12/21-08:45:04.973949 19                Options.optimize_filters_for_hits: 0
2023/12/21-08:45:04.973949 19                Options.paranoid_file_checks: 0
2023/12/21-08:45:04.973950 19                Options.force_consistency_checks: 1
2023/12/21-08:45:04.973950 19                Options.report_bg_io_stats: 0
2023/12/21-08:45:04.973951 19                               Options.ttl: 2592000
2023/12/21-08:45:04.973951 19          Options.periodic_compaction_seconds: 0
2023/12/21-08:45:04.973952 19                       Options.enable_blob_files: false
2023/12/21-08:45:04.973952 19                           Options.min_blob_size: 0
2023/12/21-08:45:04.973959 19                          Options.blob_file_size: 268435456
2023/12/21-08:45:04.973959 19                   Options.blob_compression_type: NoCompression
2023/12/21-08:45:04.973960 19          Options.enable_blob_garbage_collection: false
2023/12/21-08:45:04.973960 19      Options.blob_garbage_collection_age_cutoff: 0.250000
2023/12/21-08:45:04.973961 19 Options.blob_garbage_collection_force_threshold: 1.000000
2023/12/21-08:45:04.973962 19          Options.blob_compaction_readahead_size: 0
2023/12/21-08:45:04.976919 19 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2023/12/21-08:45:04.976922 19 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2023/12/21-08:45:04.976972 19 [db/version_set.cc:4409] Creating manifest 4
2023/12/21-08:45:04.978893 19 [db/column_family.cc:605] --------------- Options for column family [properties]:
2023/12/21-08:45:04.978895 19               Options.comparator: leveldb.BytewiseComparator
2023/12/21-08:45:04.978896 19           Options.merge_operator: None
2023/12/21-08:45:04.978897 19        Options.compaction_filter: None
2023/12/21-08:45:04.978898 19        Options.compaction_filter_factory: None
2023/12/21-08:45:04.978899 19  Options.sst_partitioner_factory: None
2023/12/21-08:45:04.978899 19         Options.memtable_factory: SkipListFactory
2023/12/21-08:45:04.978900 19            Options.table_factory: BlockBasedTable
2023/12/21-08:45:04.978909 19            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fdd57bda1a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fdd57a04010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 1074680217
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2023/12/21-08:45:04.978913 19        Options.write_buffer_size: 67108864
2023/12/21-08:45:04.978913 19  Options.max_write_buffer_number: 2
2023/12/21-08:45:04.978914 19        Options.compression[0]: NoCompression
2023/12/21-08:45:04.978915 19        Options.compression[1]: NoCompression
2023/12/21-08:45:04.978915 19        Options.compression[2]: ZSTD
2023/12/21-08:45:04.978916 19        Options.compression[3]: ZSTD
2023/12/21-08:45:04.978916 19        Options.compression[4]: ZSTD
2023/12/21-08:45:04.978917 19                  Options.bottommost_compression: Disabled
2023/12/21-08:45:04.978918 19       Options.prefix_extractor: nullptr
2023/12/21-08:45:04.978918 19   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2023/12/21-08:45:04.978919 19             Options.num_levels: 5
2023/12/21-08:45:04.978920 19        Options.min_write_buffer_number_to_merge: 1
2023/12/21-08:45:04.978920 19     Options.max_write_buffer_number_to_maintain: 0
2023/12/21-08:45:04.978921 19     Options.max_write_buffer_size_to_maintain: 0
2023/12/21-08:45:04.978922 19            Options.bottommost_compression_opts.window_bits: -14
2023/12/21-08:45:04.978922 19                  Options.bottommost_compression_opts.level: 32767
2023/12/21-08:45:04.978923 19               Options.bottommost_compression_opts.strategy: 0
2023/12/21-08:45:04.978923 19         Options.bottommost_compression_opts.max_dict_bytes: 0
2023/12/21-08:45:04.978924 19         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2023/12/21-08:45:04.978925 19         Options.bottommost_compression_opts.parallel_threads: 1
2023/12/21-08:45:04.978925 19                  Options.bottommost_compression_opts.enabled: false
2023/12/21-08:45:04.978926 19         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2023/12/21-08:45:04.978926 19            Options.compression_opts.window_bits: -14
2023/12/21-08:45:04.978927 19                  Options.compression_opts.level: 32767
2023/12/21-08:45:04.978927 19               Options.compression_opts.strategy: 0
2023/12/21-08:45:04.978928 19         Options.compression_opts.max_dict_bytes: 0
2023/12/21-08:45:04.978928 19         Options.compression_opts.zstd_max_train_bytes: 0
2023/12/21-08:45:04.978929 19         Options.compression_opts.parallel_threads: 1
2023/12/21-08:45:04.978941 19                  Options.compression_opts.enabled: false
2023/12/21-08:45:04.978941 19         Options.compression_opts.max_dict_buffer_bytes: 0
2023/12/21-08:45:04.978942 19      Options.level0_file_num_compaction_trigger: 4
2023/12/21-08:45:04.978943 19          Options.level0_slowdown_writes_trigger: 20
2023/12/21-08:45:04.978943 19              Options.level0_stop_writes_trigger: 36
2023/12/21-08:45:04.978944 19                   Options.target_file_size_base: 67108864
2023/12/21-08:45:04.978944 19             Options.target_file_size_multiplier: 2
2023/12/21-08:45:04.978945 19                Options.max_bytes_for_level_base: 268435456
2023/12/21-08:45:04.978945 19 Options.level_compaction_dynamic_level_bytes: 0
2023/12/21-08:45:04.978946 19          Options.max_bytes_for_level_multiplier: 10.000000
2023/12/21-08:45:04.978947 19 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2023/12/21-08:45:04.978948 19 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2023/12/21-08:45:04.978948 19 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2023/12/21-08:45:04.978949 19 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2023/12/21-08:45:04.978949 19 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2023/12/21-08:45:04.978950 19 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2023/12/21-08:45:04.978950 19 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2023/12/21-08:45:04.978951 19       Options.max_sequential_skip_in_iterations: 8
2023/12/21-08:45:04.978952 19                    Options.max_compaction_bytes: 1677721600
2023/12/21-08:45:04.978952 19                        Options.arena_block_size: 1048576
2023/12/21-08:45:04.978953 19   Options.soft_pending_compaction_bytes_limit: 68719476736
2023/12/21-08:45:04.978953 19   Options.hard_pending_compaction_bytes_limit: 274877906944
2023/12/21-08:45:04.978954 19       Options.rate_limit_delay_max_milliseconds: 100
2023/12/21-08:45:04.978954 19                Options.disable_auto_compactions: 0
2023/12/21-08:45:04.978955 19                        Options.compaction_style: kCompactionStyleLevel
2023/12/21-08:45:04.978956 19                          Options.compaction_pri: kMinOverlappingRatio
2023/12/21-08:45:04.978956 19 Options.compaction_options_universal.size_ratio: 1
2023/12/21-08:45:04.978957 19 Options.compaction_options_universal.min_merge_width: 2
2023/12/21-08:45:04.978957 19 Options.compaction_options_universal.max_merge_width: 4294967295
2023/12/21-08:45:04.978958 19 Options.compaction_options_universal.max_size_amplification_percent: 200
2023/12/21-08:45:04.978959 19 Options.compaction_options_universal.compression_size_percent: -1
2023/12/21-08:45:04.978959 19 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2023/12/21-08:45:04.978960 19 Options.compaction_options_fifo.max_table_files_size: 1073741824
2023/12/21-08:45:04.978960 19 Options.compaction_options_fifo.allow_compaction: 0
2023/12/21-08:45:04.978962 19                   Options.table_properties_collectors: 
2023/12/21-08:45:04.978963 19                   Options.inplace_update_support: 0
2023/12/21-08:45:04.978963 19                 Options.inplace_update_num_locks: 10000
2023/12/21-08:45:04.978964 19               Options.memtable_prefix_bloom_size_ratio: 0.000000
2023/12/21-08:45:04.978965 19               Options.memtable_whole_key_filtering: 0
2023/12/21-08:45:04.978965 19   Options.memtable_huge_page_size: 0
2023/12/21-08:45:04.978966 19                           Options.bloom_locality: 0
2023/12/21-08:45:04.978966 19                    Options.max_successive_merges: 0
2023/12/21-08:45:04.978967 19                Options.optimize_filters_for_hits: 0
2023/12/21-08:45:04.978968 19                Options.paranoid_file_checks: 0
2023/12/21-08:45:04.978968 19                Options.force_consistency_checks: 1
2023/12/21-08:45:04.978969 19                Options.report_bg_io_stats: 0
2023/12/21-08:45:04.978969 19                               Options.ttl: 2592000
2023/12/21-08:45:04.978970 19          Options.periodic_compaction_seconds: 0
2023/12/21-08:45:04.978976 19                       Options.enable_blob_files: false
2023/12/21-08:45:04.978977 19                           Options.min_blob_size: 0
2023/12/21-08:45:04.978977 19                          Options.blob_file_size: 268435456
2023/12/21-08:45:04.978978 19                   Options.blob_compression_type: NoCompression
2023/12/21-08:45:04.978978 19          Options.enable_blob_garbage_collection: false
2023/12/21-08:45:04.978979 19      Options.blob_garbage_collection_age_cutoff: 0.250000
2023/12/21-08:45:04.978980 19 Options.blob_garbage_collection_force_threshold: 1.000000
2023/12/21-08:45:04.978980 19          Options.blob_compaction_readahead_size: 0
2023/12/21-08:45:04.979008 19 [db/db_impl/db_impl.cc:2780] Created column family [properties] (ID 1)
2023/12/21-08:45:04.981958 19 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fdd57a55700
2023/12/21-08:45:04.981980 19 DB pointer 0x7fdd57a3cc00
2023/12/21-08:45:07.982376 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-08:45:07.982394 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 1 last_copies: 2 last_secs: 7.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 1 last_copies: 2 last_secs: 7.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/21-08:55:07.982723 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-08:55:07.982768 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 603.0 total, 600.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 603.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 2 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 603.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 2 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/21-09:05:07.983050 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-09:05:07.983096 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1203.0 total, 600.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1203.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 3 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1203.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 3 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/21-09:15:07.983400 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-09:15:07.983467 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1803.0 total, 600.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1803.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 4 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1803.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 4 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/21-09:25:07.984264 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-09:25:07.984541 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2403.0 total, 600.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2403.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 5 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2403.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 5 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/21-09:35:07.985277 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-09:35:07.985331 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3003.0 total, 600.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3003.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 6 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3003.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 6 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/21-09:45:07.986336 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-09:45:07.986390 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3603.0 total, 600.0 interval
Cumulative writes: 511 writes, 511 keys, 511 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 511 writes, 0 syncs, 511.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 511 writes, 511 keys, 511 commit groups, 1.0 writes per commit group, ingest: 2.33 MB, 0.00 MB/s
Interval WAL: 511 writes, 0 syncs, 511.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3603.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 7 last_copies: 2 last_secs: 3.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3603.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 7 last_copies: 2 last_secs: 3.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/21-09:55:07.986698 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-09:55:07.986739 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4203.0 total, 600.0 interval
Cumulative writes: 3527 writes, 3527 keys, 3527 commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 3527 writes, 0 syncs, 3527.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3016 writes, 3016 keys, 3016 commit groups, 1.0 writes per commit group, ingest: 7.51 MB, 0.01 MB/s
Interval WAL: 3016 writes, 0 syncs, 3016.00 writes per sync, written: 0.01 GB, 0.01 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4203.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 8 last_copies: 2 last_secs: 3.7e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4203.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 8 last_copies: 2 last_secs: 3.7e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/21-10:05:07.987055 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-10:05:07.987093 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4803.0 total, 600.0 interval
Cumulative writes: 6527 writes, 6527 keys, 6527 commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 6527 writes, 0 syncs, 6527.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4803.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 9 last_copies: 2 last_secs: 7.2e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4803.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 9 last_copies: 2 last_secs: 7.2e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/21-10:15:07.988187 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-10:15:07.988242 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5403.0 total, 600.0 interval
Cumulative writes: 9527 writes, 9527 keys, 9527 commit groups, 1.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 9527 writes, 0 syncs, 9527.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3000 writes, 3000 keys, 3000 commit groups, 1.0 writes per commit group, ingest: 0.21 MB, 0.00 MB/s
Interval WAL: 3000 writes, 0 syncs, 3000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5403.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 10 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5403.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 10 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2023/12/21-10:25:07.988897 39 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2023/12/21-10:25:07.988954 39 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6003.0 total, 600.0 interval
Cumulative writes: 11K writes, 11K keys, 11K commit groups, 1.0 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 11K writes, 0 syncs, 11688.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2161 writes, 2161 keys, 2161 commit groups, 1.0 writes per commit group, ingest: 5.29 MB, 0.01 MB/s
Interval WAL: 2161 writes, 0 syncs, 2161.00 writes per sync, written: 0.01 GB, 0.01 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6003.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 11 last_copies: 2 last_secs: 4e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6003.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdd57a04010#7 capacity: 1.00 GB collections: 11 last_copies: 2 last_secs: 4e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
