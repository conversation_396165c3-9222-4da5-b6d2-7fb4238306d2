"""

    Module Name :           mixtral_module
    Last Modified Date :    3 Jan 2024

"""

# Import Open-source Libraries
from langchain import HuggingFacePipeline
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_core.messages import HumanMessage
import transformers
from transformers import AutoTokenizer
import torch
from huggingface_hub import hf_hub_download
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain.llms import LlamaCpp
from langchain import PromptTemplate, LLMChain
from datetime import datetime

# Import Self-defined Modules
import preprocessing

# init mixtral pipeline
def init_mixtral_piepeline(model_para):
    pipeline = transformers.pipeline(
        model_para["task"],
        model=model_para["model"],
        device_map=model_para["device_map"],
        model_kwargs={"torch_dtype": eval(model_para["torch_dtype"])},
        max_new_tokens=model_para["max_new_tokens"],
        repetition_penalty=model_para["repetition_penalty"],
        return_full_text=eval(model_para["return_full_text"])
    )
    return pipeline

def init_mixtral_piepeline_2():
    import transformers
    from transformers import (
        AutoTokenizer,
        AutoModelForCausalLM,
        BitsAndBytesConfig,
        pipeline
    )
    model_name = 'mistralai/Mixtral-8x7B-Instruct-v0.1'

    model_config = transformers.AutoConfig.from_pretrained(
        model_name,
    )

    tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
    tokenizer.pad_token = tokenizer.eos_token
    tokenizer.padding_side = "right"

    #################################################################
    # bitsandbytes parameters
    #################################################################

    # Activate 4-bit precision base model loading
    use_4bit = True

    # Compute dtype for 4-bit base models
    bnb_4bit_compute_dtype = "float16"

    # Quantization type (fp4 or nf4)
    bnb_4bit_quant_type = "nf4"

    # Activate nested quantization for 4-bit base models (double quantization)
    use_nested_quant = False

    #################################################################
    # Set up quantization config
    #################################################################
    compute_dtype = getattr(torch, bnb_4bit_compute_dtype)

    bnb_config = BitsAndBytesConfig(
        load_in_4bit=use_4bit,
        bnb_4bit_quant_type=bnb_4bit_quant_type,
        bnb_4bit_compute_dtype=compute_dtype,
        bnb_4bit_use_double_quant=use_nested_quant,
    )

    # Check GPU compatibility with bfloat16
    if compute_dtype == torch.float16 and use_4bit:
        major, _ = torch.cuda.get_device_capability()
        if major >= 8:
            print("=" * 80)
            print("Your GPU supports bfloat16: accelerate training with bf16=True")
            print("=" * 80)

    #################################################################
    # Load pre-trained config
    #################################################################
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=bnb_config,
        device_map="auto"
    )

    from langchain.text_splitter import CharacterTextSplitter
    from langchain.document_loaders import AsyncChromiumLoader
    from langchain.document_transformers import Html2TextTransformer
    from langchain.vectorstores import FAISS
    from langchain.embeddings.huggingface import HuggingFaceEmbeddings
    import nest_asyncio

    from langchain.llms import HuggingFacePipeline
    from langchain.prompts import PromptTemplate
    from langchain.embeddings.huggingface import HuggingFaceEmbeddings
    from langchain.chains import LLMChain

    text_generation_pipeline = transformers.pipeline(
        model=model,
        tokenizer=tokenizer,
        task="text-generation",
        repetition_penalty=1.1,
        return_full_text=True,
        max_new_tokens=300,
        pad_token_id=2,
        eos_token_id=2

    )

    return text_generation_pipeline


# mixtral Module
def mixtral_response(question,
                    system_prompt,
                    condense_system_prompt,
                    prompt_template,
                    model_para,
                    pipeline,
                    retriever,
                    chat_history=None):
    
    print("... Generating AI Response")
    ai_msg_content = ''

    # Without Vector Store
    if not retriever:
        messages = [{"role": "user", "content": question}]
        prompt = pipeline.tokenizer.apply_chat_template(
            messages, 
            tokenize=False, 
            add_generation_prompt=True
        )

        outputs = pipeline(
            prompt, 
            max_new_tokens=10000,
            do_sample=True, 
            temperature=0,
            top_k=50, 
            top_p=0.95)
        
        ai_msg_content = outputs[0]["generated_text"].replace('<s>[INST] who is Alice? [/INST]', '')

    # With Vector Store
    # else:
    #     callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
    #     aimodel_starttime = datetime.now()
    #     llm = HuggingFacePipeline(pipeline=pipeline, callback_manager=callback_manager)
    #     aimodel_endtime = datetime.now()
    #     print(f"AI Model Loading Time = {aimodel_endtime - aimodel_starttime}")
    #
    #     custom_rag_prompt = PromptTemplate.from_template(prompt_template)
    #     rag_chain = (
    #             {"context": retriever | preprocessing.format_docs, "question": RunnablePassthrough()}
    #             | custom_rag_prompt
    #             | llm
    #             | StrOutputParser()
    #     )
    #
    #     airespone_starttime = datetime.now()
    #     ai_msg_content = rag_chain.invoke(question)
    #     airesponse_endtime = datetime.now()
    #     print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")
    #     # print(ai_msg_content)

    #Try to use another way to generate results
    else:

        aimodel_starttime = datetime.now()
        mixtral_llm = HuggingFacePipeline(pipeline=pipeline)
        aimodel_endtime = datetime.now()
        print(f"AI Model Loading Time = {aimodel_endtime - aimodel_starttime}")
        prompt_template = """
        ### [INST]
        Instruction: Answer the question based on your
        cricket knowledge. Here is context to help:

        {context}

        ### QUESTION:
        {question}

        [/INST]
        """

        # Create prompt from prompt template
        prompt = PromptTemplate(
            input_variables=["context", "question"],
            template=prompt_template,
        )

        # Create llm chain
        llm_chain = LLMChain(llm=mixtral_llm, prompt=prompt)
        rag_chain = (
                {"context": retriever, "question": RunnablePassthrough()}
                | llm_chain
        )
        airespone_starttime = datetime.now()
        ai_msg_content = rag_chain.invoke(question)
        # print(response)
        airesponse_endtime = datetime.now()
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")

    print('>>> Generated AI Response')

    return ai_msg_content # Return only the content of the AI's response
