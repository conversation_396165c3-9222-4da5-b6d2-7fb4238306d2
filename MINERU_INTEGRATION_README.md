# MinerU集成技术方案

## 概述

本文档描述了将MinerU直接集成到文件上传模块的技术方案，替代之前手动提取layoutjson然后导入的方式。

## 技术方案对比

### 当前方案（手动）
```
PDF文件 → MinerU客户端 → layoutjson文件 → 手动导入uploads文件夹 → 处理
```

### 新方案1：Python API直接集成（推荐）
```
PDF文件 → MinerU Python API → 实时处理 → 直接生成chunks → 存储到向量数据库
```

### 新方案2：API调用方式（备选）
```
PDF文件 → HTTP API调用 → MinerU服务 → 返回布局数据 → 生成chunks
```

## 方案1：Python API直接集成

### 优点
- ✅ 无需外部服务依赖
- ✅ 处理流程完全可控
- ✅ 可以实时处理，无需预计算
- ✅ 错误处理更好
- ✅ 性能更高

### 实现文件
1. `mineru_integration.py` - 核心集成模块
2. `mineru_config.py` - 配置管理
3. `install_mineru.py` - 自动安装脚本

### 安装步骤

#### 1. 安装MinerU依赖
```bash
# 方法1：使用自动安装脚本
python install_mineru.py

# 方法2：手动安装
pip install magic-pdf[full]
```

#### 2. 配置MinerU
编辑 `mineru_user_config.json` 文件：
```json
{
  "enabled": true,
  "parse_mode": "auto",
  "lang": "auto",
  "layout_config": {
    "model": "doclayout_yolo",
    "confidence_threshold": 0.5
  },
  "formula_config": {
    "enable": true,
    "mfd_model": "yolo_v8_mfd",
    "mfr_model": "unimernet_small"
  },
  "table_config": {
    "enable": true,
    "model": "rapid_table"
  }
}
```

#### 3. 重启应用
重启你的Flask应用，MinerU集成将自动生效。

### 处理流程

1. **文件上传检测**：当用户上传PDF文件时，系统首先检查是否启用MinerU
2. **预计算检查**：检查是否存在预计算的布局文件
3. **MinerU处理**：如果没有预计算文件且MinerU可用，则实时处理PDF
4. **结果转换**：将MinerU结果转换为与现有系统兼容的chunks格式
5. **回退机制**：如果MinerU处理失败，自动回退到原始处理方式

### 代码集成点

在 `chatbot_newui_new_version_v5.py` 的 `upload_file()` 函数中：

```python
# 【新增】尝试使用MinerU实时处理PDF
if filename.lower().endswith(".pdf") and MINERU_AVAILABLE and not os.path.exists(json_path):
    print(f"🔄 尝试使用MinerU实时处理PDF: {filename}")
    try:
        mineru_processor = MinerUProcessor()
        layout_data, markdown_content = mineru_processor.process_pdf(filename)
        
        if layout_data:
            chunks_with_metadata = mineru_processor.convert_to_chunks(layout_data, file.filename)
            if chunks_with_metadata and len(chunks_with_metadata) > 0:
                print(f"✅ MinerU成功生成 {len(chunks_with_metadata)} 个文本块")
                element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}
    except Exception as mineru_e:
        print(f"❌ MinerU处理出错: {str(mineru_e)}")
        print("回退到其他处理方式")
```

## 方案2：API调用方式

### 优点
- ✅ 服务解耦，可以独立部署MinerU服务
- ✅ 支持分布式处理
- ✅ 可以处理大文件
- ✅ 支持异步处理

### 缺点
- ❌ 需要额外的服务部署
- ❌ 网络延迟
- ❌ 需要处理网络错误

### 实现文件
1. `mineru_api_client.py` - API客户端

### 使用方式
```python
# 初始化API客户端
processor = MinerUAPIProcessor("http://localhost:8080")

# 检查服务可用性
if processor.is_available():
    # 处理PDF
    layout_data, _ = processor.process_pdf(pdf_path)
    if layout_data:
        chunks = processor.convert_to_chunks(layout_data, filename)
```

## 配置选项

### 基础配置
- `enabled`: 是否启用MinerU
- `parse_mode`: 解析模式（auto, ocr, txt）
- `lang`: 语言设置（auto, zh, en）

### 模型配置
- `layout_config`: 布局检测配置
- `formula_config`: 公式识别配置
- `table_config`: 表格识别配置
- `ocr_config`: OCR配置

### 性能配置
- `batch_size`: 批处理大小
- `max_workers`: 最大工作线程数
- `gpu_enabled`: 是否使用GPU
- `memory_limit`: 内存限制

## 错误处理

### 回退机制
1. MinerU处理失败 → 使用预计算布局文件
2. 预计算布局文件不存在/失败 → 使用Markdown文件
3. Markdown文件不存在/失败 → 使用原始PDF处理方式

### 日志记录
- ✅ 成功处理：记录处理时间和生成的文本块数量
- ⚠️  警告：记录回退原因
- ❌ 错误：记录详细错误信息

## 性能优化

### 缓存机制
- 自动保存MinerU处理结果为JSON文件
- 下次处理相同文件时直接使用缓存

### 并行处理
- 支持多线程处理
- GPU加速（如果可用）

### 内存管理
- 大文件分块处理
- 及时释放内存

## 测试验证

### 功能测试
```bash
# 测试MinerU集成
python mineru_integration.py

# 测试API客户端
python mineru_api_client.py
```

### 性能测试
- 处理时间对比
- 内存使用对比
- 准确性对比

## 部署建议

### 生产环境
1. 使用方案1（Python API直接集成）
2. 启用GPU加速（如果有GPU）
3. 配置适当的内存限制
4. 启用结果缓存

### 开发环境
1. 可以使用方案2（API调用）进行测试
2. 使用CPU版本即可
3. 启用详细日志

## 故障排除

### 常见问题
1. **MinerU安装失败**：检查Python版本，使用install_mineru.py脚本
2. **处理速度慢**：启用GPU加速，调整batch_size
3. **内存不足**：降低memory_limit，减少max_workers
4. **准确性不高**：调整confidence_threshold，更换模型

### 调试方法
1. 检查日志输出
2. 验证配置文件
3. 测试单个PDF文件
4. 检查模型文件是否正确下载

## 总结

推荐使用**方案1（Python API直接集成）**，因为：
- 集成简单，无需额外服务
- 性能更好，延迟更低
- 错误处理更完善
- 维护成本更低

通过这个集成方案，你可以：
- 🚀 实时处理PDF文件，无需手动预处理
- 📈 提高处理准确性和效率
- 🔄 保持与现有系统的兼容性
- 🛡️ 提供完善的错误处理和回退机制
