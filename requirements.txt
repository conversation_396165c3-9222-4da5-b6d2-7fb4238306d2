accelerate==0.27.2
aiohttp==3.9.3
aiosignal==1.3.1
annotated-types==0.6.0
antlr4-python3-runtime==4.9.3
anyio==4.3.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
asttokens==2.4.1
async-timeout==4.0.3
attrs==23.2.0
backcall==0.2.0
backoff==2.2.1
beautifulsoup4==4.12.3
bleach==6.1.0
blinker==1.7.0
certifi==2024.2.2
cffi==1.16.0
chardet==5.2.0
charset-normalizer==3.3.2
click==8.1.7
coloredlogs==15.0.1
contourpy==1.2.0
cryptography==42.0.2
cycler==0.12.1
dataclasses-json==0.6.4
dataclasses-json-speakeasy==0.5.11
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.14
distro==1.9.0
docopt==0.6.2
effdet==0.4.1
emoji==2.10.1
environs==9.5.0
exceptiongroup==1.2.0
executing==2.0.1
fastjsonschema==2.19.1
filelock==3.13.1
filetype==1.2.0
Flask==3.0.2
flatbuffers==23.5.26
fonttools==4.49.0
frozenlist==1.4.1
fsspec==2024.2.0
greenlet==3.0.3
grpcio==1.60.0
h11==0.14.0
httpcore==1.0.4
httpx==0.25.2
huggingface==0.0.1
huggingface-hub==0.20.3
humanfriendly==10.0
idna==3.6
importlib-resources==6.1.1
InstructorEmbedding==1.0.1
iopath==0.1.10
ipython==8.12.3
itsdangerous==2.1.2
jedi==0.19.1
Jinja2==3.1.3
joblib==1.3.2
jsonpatch==1.33
jsonpath-python==1.0.6
jsonpointer==2.4
jsonschema==4.21.1
jsonschema-specifications==2023.12.1
jupyter_client==8.6.1
jupyter_core==5.7.2
jupyterlab_pygments==0.3.0
kiwisolver==1.4.5
langchain==0.1.11
langchain-community==0.0.27
langchain-core==0.1.30
langchain-openai==0.0.8
langchain-text-splitters==0.0.1
langdetect==1.0.9
langsmith==0.1.23
layoutparser==0.3.4
lxml==5.1.0
MarkupSafe==2.1.5
marshmallow==3.20.2
matplotlib==3.7.2
matplotlib-inline==0.1.6
minio==7.2.5
mistune==3.0.2
mpmath==1.3.0
multidict==6.0.5
mypy-extensions==1.0.0
nbclient==0.10.0
nbconvert==7.16.2
nbformat==5.10.3
networkx==3.2.1
nltk==3.8.1
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu12==2.19.3
nvidia-nvjitlink-cu12==12.4.99
nvidia-nvtx-cu12==12.1.105
ollama==0.1.7
omegaconf==2.3.0
onnx==1.15.0
onnxruntime==1.15.1
openai==1.13.3
opencv-python==********
orjson==3.9.15
packaging==23.2
pandas==2.2.0
pandocfilters==1.5.1
parso==0.8.3
pdf2image==1.17.0
pdfminer.six==20221105
pdfplumber==0.10.4
pexpect==4.9.0
pickleshare==0.7.5
pikepdf==8.11.0
pillow==10.2.0
pillow_heif==0.15.0
pipreqs==0.5.0
platformdirs==4.2.0
portalocker==2.8.2
prompt-toolkit==3.0.43
protobuf==4.23.4
psutil==5.9.8
psycopg2-binary==2.9.9
ptyprocess==0.7.0
pure-eval==0.2.2
pyarrow==15.0.1
pycocotools==2.0.7
pycparser==2.21
pycryptodome==3.20.0
pydantic==2.6.3
pydantic_core==2.16.3
Pygments==2.17.2
pymilvus==2.3.6
pyparsing==3.0.9
pypdf==4.0.1
pypdfium2==4.27.0
pytesseract==0.3.10
python-dateutil==2.8.2
python-dotenv==1.0.1
python-iso639==2024.2.7
python-magic==0.4.27
python-multipart==0.0.9
pytz==2024.1
PyYAML==6.0.1
pyzmq==25.1.2
rapidfuzz==3.6.1
referencing==0.34.0
regex==2023.12.25
requests==2.31.0
rpds-py==0.18.0
safetensors==0.4.2
scikit-learn==1.4.1.post1
scipy==1.10.1
sentence-transformers==2.2.2/3.3.1
sentencepiece==0.2.0
six==1.16.0
sniffio==1.3.1
soupsieve==2.5
SQLAlchemy==2.0.28
stack-data==0.6.3
sympy==1.12
tabulate==0.9.0
tenacity==8.2.3
threadpoolctl==3.3.0
tiktoken==0.6.0
timm==0.9.12

tinycss2==1.2.1
tokenizers==0.15.2
torch==2.2.0
torchvision==0.17.0
tornado==6.4
tqdm==4.66.2
traitlets==5.14.2
transformers==4.38.1
triton==2.2.0
typing-inspect==0.9.0
typing_extensions==4.9.0
tzdata==2024.1
ujson==5.9.0
unstructured==0.12.6
unstructured-client==0.18.0
unstructured-inference==0.7.23
unstructured.pytesseract==0.3.12
urllib3==1.26.18
wcwidth==0.2.13
webencodings==0.5.1
Werkzeug==3.0.1
wrapt==1.16.0
yarg==0.1.9
yarl==1.9.4
zipp==3.17.0
openpyxl