<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG-CHATBOT</title>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://kit.fontawesome.com/8e9c71f3b7.js" crossorigin="anonymous"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href='https://fonts.googleapis.com/css?family=Inter' rel='stylesheet'>

<!--&lt;!&ndash;    W3 CSS library&ndash;&gt;-->
<!--    <link rel="stylesheet" href="https://www.w3schools.com/w3css/4/w3.css">-->
<!--&lt;!&ndash;    end&ndash;&gt;-->

<style>

@font-face {
    font-family: Inter;
}
    .toggle-chat-history-btn {
        position: fixed; /* ?????? */
        align-content: center;
        margin-top: 35px ;
        transform: translateY(-50%); /* ???? */
        background-color: transparent; /* ???? */
        border: none; /* ??? */
    }
    .toggle-chat-history-btn:hover {
        color: grey; /* ?????????? */
    }

    .toggle-chat-history-btn .fas {
        transition: transform 0.3s; /* ?????? */
    }

    /*.toggle-chat-history-btn.collapsed .fas {*/
    /*    transform: rotate(-180deg); !* Chat History ??????? *!*/
    /*}*/

    /* Button & Icon style*/
    .custom-icon-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 120px; /* 增加图标容器的高度 */
        animation: icon-animation 5s ease-in-out infinite;
    }

    .icon-btn {
        background: transparent;
        border: none;
        cursor: pointer;
        font-size: 20px;
    }

    /*.icon-btn i {*/
    /*font-size: 20px;*/
    /*}*/

    .send-icon {
      border: none;
      width: 36px;
      height: 36px;
      cursor: pointer;
    }

    .database-icon {
        left: 10px;
    }

    /*==============================================*/

    /*chat boxes style*/
    /*.chat-form, .file-form {*/
    /*    display: flex;*/
    /*    flex-wrap: wrap;*/
    /*    gap: 10px;*/
    /*    font-family: Inter;*/
    /*}*/
    /*.chat-form > *, .file-form > * {*/
    /*    flex: 1;*/
    /*    min-width: 120px;*/
    /*}*/
    .chat-form {
        display: flex;
        flex-direction: column;
        align-items: stretch;
    }

    /*chat history style*/
    /* auto gen chat dialog format*/
    .chat-history-entry {
        width: 100%;
        height: 100%;
        border-radius: 22px;
        margin-left: 5px;
        margin-right:5px;
        margin-bottom: 15px;
        padding: 10px;
        /*background-color: #f0f0f0;*/
        background: #DDEFE8;
        font-family: Inter;
        overflow: hidden;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center; gap: 9px;
        display: inline-flex;
        cursor: pointer; /* ?????????????????????? */
        transition: background-color 0.3s ease; /* ?????????????? */
    }

    /*.chat-history-entry :hover {*/
    /*    border: 2px;*/
    /*    border-color: #4a4a4a;*/
    /*    overflow-y: auto;*/
    /*}*/

    .loader {
        border: 4px solid #f3f3f3; /* Light grey */
        border-top: 4px solid #3498db; /* Blue */
        border-radius: 50%;
        width: 20px;
        height: 20px;
        animation: spin 2s linear infinite;
    }

    .docu-loader {
        border: 4px solid #f3f3f3; /* Light grey */
        border-top: 4px solid #3498db; /* Blue */
        border-radius: 50%;
        width: 35px;
        height: 35px;
        animation: spin 2s linear infinite;
        padding-top: 3px
    }

    @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

    @keyframes icon-animation {
    0% { transform: translateY(0); }
    50% { transform: translateY(20px); } /* 增加图标容器的垂直移动距离 */
    100% { transform: translateY(0); }
}

@keyframes circle-group-rotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes circle-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 22px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
    border-radius: 22px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(18px);
  -ms-transform: translateX(18px);
  transform: translateX(18px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

    /*==================================================================================== Window Frame START ===============================================================================================*/

    .sideM{
        position: fixed;
        width: 100px;
        height: 100%;
        background-color: rgba(48, 48, 48, 0);
        padding-top: 10px;
        padding-left: 10px;
        padding-right: 5px;
        padding-bottom: 10px;
    }

    .main-win{
        position: fixed;
        width: calc(100% - 100px) ;
        height: 100%;
        margin-left: 100px;
        padding-top: 10px;
        padding-bottom: 10px;
        padding-left: 5px;
        padding-right: 10px;
        background-color: #ffffff;

    }

    .exp-sideM{
        position:fixed!important;
        width:300px;
        height:100%;
        background-color: #4a4a4a;
        z-index:1;
        overflow:auto ;
        padding-top: 10px;
        padding-bottom: 10px;
        padding-left: 5px;
        padding-right: 10px;
    }

    /*================================================== Sub - Main Window Frame START ================================================================*/
    .top-menu{
        height: 50px ;

        background-color: rgba(255, 255, 255, 0);
        margin-top: 8px ;
    }

    .chat-window{
        height: calc(100% - 50px - 185px );
        margin-top: 10px ;
        background-color: rgba(248, 248, 248, 0);
        /*overflow-y: scroll;*/
        scroll-behavior: auto;
        font-family: Inter;
    }
    .chat-box {
            height: 100%;
            overflow: auto;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border: none;
             align-items: center;
        }

    .input-area{
        margin-top: 15px;
        height: 185px ;
        align-content: center;
        /*margin-top: 20px !important;*/
        /*border: 2px #aee2b1 solid;*/
    }

    /*.input-container{*/
    /*    font-family: Inter;*/
    /*    align-content: center;*/
    /*    resize: none;*/
    /*}*/
    .input-container {
        position: relative;
        display: flex;
        align-items: center;
        width: 100%;
    }

    #user_input {
        border-radius: 5px;
        border: 0px;
        background: #ffffff;
        outline: none;
    }
    /*.user-input-t-area{*/
    /*    height: 80%;*/
    /*    resize: none;*/
    /*    overflow-y: scroll;*/
    /*    color: #0097A9;*/
    /*    background-color: rgba(21, 119, 234, 0);*/
    /*    margin-left: 20px ;*/
    /*    font-size: 22px;*/
    /*    font-weight: 400;*/
    /*    overflow-wrap: break-word;*/
    /*    flex-grow: 1;*/
    /*    align-self: center;*/
    /*    border: none;*/
    /*    outline: none;*/
    /*    font-family: Inter;*/
    /*}*/


    /*================================================== Sub - Expanded Side Menu Frame START ================================================================*/
    .com-logo{
        height: 50px ;
        background-color: rgba(114, 211, 112, 0);
        margin-top: 10px ;
        margin-left: 12px;
    }

    .history-window{
        height: calc(100% - 50px - 100px );
        margin-top: 10px ;
        padding: 10px;
        background-color: rgba(248, 248, 248, 0);
        /*overflow-y: scroll;*/
        scroll-behavior: auto;
        overflow: auto;
        display: flex;
        align-content: center;
        justify-content: center;

    }



    .func-menu{
        height: 100px ;
        margin-top: 10px ;
        margin-bottom: 10px;
        background-color: rgba(174, 226, 177, 0);
    }

    .single-history-entry{

    }
    /*===================================================================================================================================================================================*/

</style>


</head>

<body>

<!-- Expanded Side Manu -->
<div id="offcanvasChatHistory"
     class="offcanvas offcanvas-start"
     tabindex="-1"  aria-labelledby="offcanvasChatHistoryLabel">

    <section class="com-logo">

        <img src="{{ url_for('static', filename='images/deloitte-1 1.png') }}" style="width: 150px; display: block; margin-top: 10px; margin-left: 10px;">

    </section>

    <section class="history-window">

        <div class="chat-history-container">
            <div id="chatHistoryBox" ></div>
        </div>

    </section>

    <section class="func-menu">

        <div class="icon-container" style="display: flex; align-self:center; justify-content: center;  bottom: 0; width: 100%; padding-top: 20px; border: none;">
            <button type="button" class="icon-btn database-icon" data-bs-toggle="modal" data-bs-target="#databaseModal">
                <img style="margin-left : 40px; margin-right: 40px" src="{{ url_for('static', filename='images/Doc Mgmt Button.png') }}" alt="Settings">

            </button>
            <button type="button" class="icon-btn settings-icon" data-bs-toggle="modal" data-bs-target="#settingsModal" style="border: none;">
                <img style="margin-left : 40px; margin-right: 40px" src="{{ url_for('static', filename='images/Setting Button.png') }}" alt="Settings">
            </button>
        </div>

    </section>
</div>

<!--Side Menu-->
<!--class="offcanvas offcanvas-start" tabindex="-1" id="offcanvasChatHistory" aria-labelledby="offcanvasChatHistoryLabel"-->
<div class="sideM">
    <button class="toggle-chat-history-btn"
            type="button"
            style="border: none;"
            data-bs-toggle="offcanvas" data-bs-target="#offcanvasChatHistory" aria-controls="offcanvasChatHistory">
        <img style="" src="{{ url_for('static', filename='images/Ribbon Button.png') }}" alt="Toggle">
    </button>
</div>

<!--Main window-->
<main class="main-win">
    <section class="top-menu">
        <button id="addChatHistoryButton"
                type="button"
                style="width: 170px;height: 40px; padding-left: 18px; padding-right: 18px; margin-top: 7px; background: #0097A9; color: white; border: none; border-radius: 28px; justify-content: center; align-items: center; gap: 13px; display: inline-flex;">
            New Chat
        </button>
    </section>

    <section class="chat-window">
        <div id="chatBox" class="chat-box">
        </div>
    </section>

    <section class="input-area">
        <form id="chatForm" class="chat-form" enctype="multipart/form-data">
<!--            <div class="input-container">-->
<!--                <div style="min-height: 1em ; max-height: 6em; width: 100%; position: relative; background: white; border-radius: 28px; border: 2px #0097A9 solid; display: flex; align-content: center;">-->

<!--                    <textarea type="text" class="user-input-t-area" name="user_input" id="user_input" placeholder="Type your message..." required></textarea>-->

<!--                    <div style="padding-right: 10px;  margin-right: 14px ;align-content: center;">-->
<!--                        <button type="submit" class="icon-btn send-icon">-->
<!--                            <img src="{{ url_for('static', filename='images/Input Button.png') }}">-->
<!--                        </button>-->
<!--                    </div>-->

<!--                </div>-->

<!--            </div>-->
            <div class="input-container">
                    <div style="width: 100%; position: relative; background: white; border-radius: 28px; border: 2px #0097A9 solid; display: flex; justify-content: center; align-items: center;">
                        <textarea type="text" name="user_input" id="user_input" placeholder="Type your message..." required style="resize: none;width: 100%; color: #0097A9; margin-left: 20px ;font-size: 22px; font-weight: 400; overflow-wrap: break-word; flex-grow: 1; align-self: center; border: none; outline: none;"></textarea>
                        <div style=" margin-top:5px; margin-right: 15px; flex-direction: column; justify-content: center; align-content: center">
                            <button style="height: 50px; align-self: center " type="submit" class="icon-btn send-icon">
                                <img src="{{ url_for('static', filename='images/Input Button.png') }}">
                            </button>
                        </div>
                    </div>

            </div>
        </form>
    </section>

</main>

<!--    setting window-->
<div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true" style=" justify-content: center; align-content: center">
        <div class="modal-dialog " style="justify-content: center; align-content: center">
            <div class="modal-content" style="border-radius: 1.5rem ; ">
                <div class="modal-header" style="border:none; padding: 0.5rem 1.2rem">


                    <b><h2 style="color: #004F59; font-weight: bold; margin-left:5px">Settings</h2></b>




                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="align-self: center;width: 91%;border-radius: 15px;background: #DDEFE8;">
                    <select name="mode" id="mode" onchange="updateModelOptions()" style="visibility: hidden;color:#DDEFE8; border-radius: 10px">
                        <option value="online">Online</option>
                        <option value="offline" selected>Offline</option>
                    </select>
                    <p><b><label style="color:#0097A9; width: 200px">Model:</label></b>
                        <select name="model" id="model" style="justify-content: flex-end;color:#0097A9; margin-left: 20px ; border-color:#DDEFE8; border-radius: 10px">
                            <option value="gpt-3.5">GPT-3.5</option>
<!--                            <option value="llama" style="display:none;">LLaMA3-8B</option>-->
                            <option value="mixtral" style="display:none;" selected>Mistral-7B</option>
<!--                            <option value="instructmixtral-1" style="display:none;">instructmixtral-1</option>-->
                        </select></P>
                    <p><b><label style="color:#0097A9; width: 200px">Rag:</label></b>
                    <select name="rag" id="rag" style="margin-left: 20px; color:#0097A9 ; border-color:#DDEFE8; border-radius: 10px">
                        <option value="off">Off</option>
                        <option value="on" selected>On</option>
                    </select></P>
    <!--                <select id="instruct-selector"></select>-->
                    <div>
                        <p><b><label style="color:#0097A9;  width: 200px" for="max_history_no">Max History Items:</label></b>
                        <select name="max_history_no" id="max_history_no" style=" margin-left: 20px; color:#0097A9; border-color:#DDEFE8; border-radius: 10px">
    <!--                        <option value="0">0</option>-->
                            <option value="3">3</option>
                            <option value="5">5</option>
                            <option value="7">7</option>
                            <option value="9">9</option>
                        </select></p>
                    </div>
                    <div class="checkbox-container" style="visibility: hidden">
                        <input type="checkbox" id="include_history" name="include_history" checked>
                        <label for="include_history">Include Chat History</label>
                    </div>
                </div>
                <div class="modal-footer"  style="border:none">
<!--                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>-->
<!--                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Save changes</button>-->
                </div>
            </div>
        </div>
    </div>

<!--    docu window-->
<div class="modal fade" id="databaseModal" tabindex="-1" aria-labelledby="databaseModalLabel" aria-hidden="true" style=" justify-content: center; align-content: center">
        <div class="modal-dialog" style="max-width: 1400px ; justify-content: center; align-content: center">
            <div class="modal-content" style="align-items: center; flex-direction: column; border-radius: 1.5rem;">
                <div class="modal-header dataset-modal-header" style="width: 70%; display: flex; flex-direction: column; align-items: flex-start; border: none; margin-right: auto; margin-left: 7px">

                  <b><h2 style="color: #004F59; font-weight: bold; margin-left:10px">Document Management</h2></b>


                  <div class="controls" style="width: 100%; display: flex; flex-wrap: wrap; align-items: start; justify-content: flex-start ; vertical-align: middle;" >
                    <button type="button" class="btn" id="backToDatabaseList" style="padding: 0 0 0 0;">
                      <img src="{{ url_for('static', filename='images/Directory Up Button.png') }}" style="width: 36px; height: 36px; margin-left: 10px">
                    </button>


                    <input type="text" class="form-control me-2" style="width: 40%; background: #DDEFE8; border-radius: 2.25rem; margin-left: 4px" placeholder="Search">


                    <button class="btn" id="createFolderButton" style="background: #004F59; color: white; border-radius: 2.25rem;">
                      <span class="fas fa-folder-plus"></span>
                      Create New Dataset
                    </button>


                    <button class="btn" id="deleteButton" style="background: grey; margin-left: 5px; color: lightgrey; border-radius: 1.3rem">
                      <span class="fas fa-trash"></span>
                    </button>


                    <button id="uploadLoading" style="visibility: hidden;display: flex ;align-content: center; margin-left: 5px; border: none; background-color: rgba(21,119,234,0);">
                        <div class="docu-loader" ></div>
                        <div style="font-family: Inter; font-size: 16px; color: #004F59 ; margin-left: 10px; padding-top:6px"> File Uploading</div>
                    </button>


                  </div>
                </div>

                <div class="modal-body dataset-modal-body" style="position: relative; height: 800px; width: 96%; overflow-y: auto; background: #DDEFE8; border:none; border-radius: 1rem; margin-bottom: 5px">
                  <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" style="background: #DDEFE8; border: 0px;border-bottom: 2px solid rgba(0, 0, 0, .125);">
        <!--                            <div class="me-2 d-flex align-items-center">-->
        <!--                                <input class="form-check-input me-1" type="checkbox" value="" aria-label="..." style="margin-top: 0rem">-->
        <!--                            </div>-->
                                    <span class="flex-grow-1 dataset-name" style="cursor:pointer; color: #0097A9;font-family: Inter; font-weight: 600">Name</span>
        <!--                            <label class="switch ms-auto">-->
        <!--                                <input type="checkbox">-->
        <!--                                <span class="slider round"></span>-->
        <!--                            </label>-->
                                </a>

                    <div id="databaseList" class="list-group"></div>
                  <div class="list-group" style="display: none;" id="fileList"></div>
                    <input type="file" id="file-upload" style="display: none;" multiple>
                    <button id="uploadFileButton" class="btn btn-primary rounded-circle" style="position: absolute; right: 10px; bottom: 10px; background-color: #004F59; color: white; width: 70px; height: 70px">
                    <span class="fas fa-plus"></span>
                    </button>
                </div>

                <div class="modal-footer" style="border:none">
                    <b><div style="font-family: Inter; color: #004F59; justify-content: start;align-content: start;margin-left: 10px">Note: only files in the same folders can be activated.</div></b>
                </div>
            </div>
        </div>
</div>

<!--    pop-up window-->
<div class="modal fade" id="createFolderModal" tabindex="-1" aria-labelledby="createFolderModalLabel" aria-hidden="true" style=" justify-content: center; align-content: center">
          <div class="modal-dialog" style="top: 5%" >
            <div class="modal-content" style="border-radius: 1.5rem ; background-color: white ; border: none">

              <div class="modal-header" style="border: none;">
                <b><h2 style="color: #004F59; font-weight: bold; margin-left:5px; border: none;">Create New Folder</h2></b>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="border: none;"></button>
              </div>

              <div class="modal-body" style="border: none;">
                <input type="text" class="form-control" id="newFolderName" placeholder="Folder Name" >
              </div>

              <div class="modal-footer" style="border: none;">
                <button type="button" class="btn btn-primary" id="confirmCreateFolder" style="background: #004F59 ">Confirm</button>
              </div>

            </div>
          </div>
        </div>

<div class="modal fade" id="warningWin" tabindex="-1" aria-labelledby="createFolderModalLabel" aria-hidden="true" style="justify-content: center; align-content: center">
          <div class="modal-dialog" style="position: absolute; top: 25%; left: 50%; transform: translate(-50%, -50%);width: 400px; height: 180px; padding-top: 13px;border:none; padding-bottom: 13px; background: white; border-radius: 30px; overflow: hidden; flex-direction: column; justify-content: center; align-items: center; gap: 17px; display: inline-flex">
            <div class="modal-content" style="align-content: center ;border:none;">


              <div class="modal-header" style="justify-content: center; align-content: center ;border:none;">
                <div style="color: #0097A9; font-size: 30px;  font-weight: bold; word-wrap: break-word">Warning</div>
              </div>


              <div class="modal-body" style="justify-content: center; align-content: center; border:none;">
                <div style="color: #004F59; font-size: 16px;  font-weight: 500; word-wrap: break-word; justify-content: center; align-content: center;">No document is currently selected. RAG is off now.</div>
              </div>


              <div class="modal-footer" style="align-content: center;border:none;">
              </div>


            </div>
          </div>
        </div>

<div style="visibility: hidden; display: none; z-index: -1; width: 1px; height: 1px;">
  <img src="{{ url_for('static', filename='images/robot.png') }}" alt="Image Description">
  <img src="{{ url_for('static', filename='images/user.png') }}" alt="Image Description">
</div>

<script>



// ================================ Side Menu Expanded

function loadAllUsersChatHistory() {
    $.ajax({
        url: '/get-all-users-data',
        type: 'GET',
        success: function(data) {
            const chatHistoryBox = $('#chatHistoryBox');
            chatHistoryBox.empty();
            Object.keys(data).forEach(userId => {
                const firstQuestion = data[userId]['q_msg'];
                const firstQuestionTime = data[userId]['q_time']
                console.log(data)
                const entryHtml = `
                    <div class="chat-history-entry" data-userId="${userId}">
                        <div style="width: 100%; height: 100%;  background: #DDEFE8; border-radius: 22px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: center; gap: 9px; display: inline-flex">

                            <div style=" height:30%; padding-left: 10px;padding-bottom:3px;  align-items: center; ">
                                <div style="width:250px; justify-content: flex-start; align-items: flex-start; gap: 10px; display: inline-flex;">
                                    <div style="color: #6FC2B4; font-size: 15px; font-weight: 700; word-wrap: break-word">${firstQuestionTime}</div>
                                </div>
                                <button style="background:#DDEFE8; border: none; margin-top: 5px">
                                    <img src="{{ url_for('static', filename='images/Cross button Frame.svg') }}"  alt="Image Description" style="vertical-align: middle; padding-bottom: 5px">
                                </button>
                            </div>


                            <div style="width: 290px; height: 0px; border: 1px #6FC2B4 solid"></div>


                            <div style="width: 284px; height: 40%; padding-top:10px; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
                                <div style="color: #004F59; font-size: 15px; font-weight: 700; word-wrap: break-word">Title:&nbsp;</div>
                                <div style="width: 246px; max-height: 50px ; color: #004F59; font-size: 15px;font-weight: 700; word-wrap: break-word; overflow-y: hidden">${firstQuestion}</div>
                            </div>
                        </div>
                   </div>
                `;
                chatHistoryBox.prepend(entryHtml);
            });
        },
        error: function(xhr, status, error) {
            console.error("Failed to load all users' chat history:", error);
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    loadAllUsersChatHistory()
    setTimeout(function() {
        var chatHistoryBox = document.getElementById("chatHistoryBox");
        if (!chatHistoryBox) {
            console.log("Chat history box not found.");
            return;
        }

        var entries = chatHistoryBox.getElementsByClassName("chat-history-entry");
        console.log("Entries found: " + entries.length);

        Array.from(entries).forEach(function(entry) {
            var titleDiv = entry.querySelector("div[style*='width: 246px;']");
            if (!titleDiv) {
                console.log("Title div not found in an entry.");
                return;
            }
            console.log(titleDiv)
            var titleText = titleDiv.textContent;
            console.log("Checking entry: " + titleText);

            if (titleText === "No question available") {
                var deleteButton = entry.querySelector("button[style*='background:#DDEFE8;']");
                if (deleteButton) {
                    console.log("Deleting entry: " + titleText);
                    deleteButton.click();
                } else {
                    console.log("Delete button not found.");
                }
            }
        });

        var addChatHistoryButton = document.getElementById("addChatHistoryButton");
        if (addChatHistoryButton) {
            console.log("Adding new chat history entry.");
            addChatHistoryButton.click();
        } else {
            console.log("Add chat history button not found.");
        }
    }, 1000);
});



$(document).ready(function(){

    const textArea = document.getElementById('user_input');
    const inputContainer = document.querySelector('.input-container');
    const outerContainer = document.querySelector('.input-container > div > div');
    const submitButton = document.querySelector('.send-icon');
    var currentSelectedDataset;
    var selectedDatasets = []; // This will hold the names of all selected datasets
    var selectedFiles = [];

    // initialize function @every refresh
    updateButtonVisibility()
    adjustTextAreaHeight();
    newLoadInitialDatasets();
    // loadAllUsersChatHistory();
    updateDeleteButtonStatus();
    loadInstructOptions();
    // updateDatasetCount();
    // updateFileCount();
    // updateModelOptions();
    updateFormValues();
    // $('#mode').change(updateModelOptions);
    //loadInitialDatasets();
    var selectedFilesMap = {};

//================================================================================================================================================
//================================================================================================================================================


//====================== Main Window

//Main Window: update chat window content according to selected chat history
function loadUserChatHistory(userId) {
    $.ajax({
url: '/get-user-data/' + userId,
type: 'GET',
success: function(data) {
const chatBox = $('#chatBox');
chatBox.empty();
Object.values(data).forEach(entry => {
    const userMessageHtml = `
        <div style="width: 100%; padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; flex-direction: column; justify-content: flex-start; align-items: flex-end; gap: 6px; display: inline-flex">
          <div style="padding-left: 2px; padding-right: 2px; padding-top: 1px; padding-bottom: 1px; justify-content: flex-end; align-items: center; gap: 10px; display: inline-flex">
            <div style="text-align: right; color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 20px">User
                      <img src="{{ url_for('static', filename='images/user.png') }}"  alt="Image Description" style="vertical-align: middle;">
</div>
          </div>
          <div style="padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; border-radius: 18px; overflow: hidden; border: 2px #0097A9 solid; justify-content: center; align-items: center; gap: 19px; display: inline-flex">
            <div style="color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 18px">${entry.q_msg}</div>
          </div>
        </div>
      `;
    const botMessageHtml = `
        <div>
<div style="width: 100%; height: 100%; padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex">
<div style="transform: rotate(180deg); transform-origin: 0 0; justify-content: flex-start; align-items: flex-end; gap: 5px; display: inline-flex">
<div style="transform: rotate(180deg); transform-origin: 0 0; text-align: right; color: #004F59; font-size: 22px;font-weight: 700; word-wrap: break-word; font-size: 20px">
<img src="{{ url_for('static', filename='images/robot.png') }}"  alt="Image Description" style="vertical-align: middle;">
Assistant
</div>
<div style="width: 34px; height: 34px; position: relative; transform: rotate(180deg); transform-origin: 0 0">
<div style="width: 34px; height: 34px; left: 0px; top: 0px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: rgba(255, 255, 255, 0.01)"></div>
<div style="width: 21.38px; height: 17.10px; left: -6.41px; top: 12.83px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: rgba(255, 255, 255, 0.01); border: 2px #004F59 solid"></div>
<div style="width: 2.85px; height: 2.85px; left: -10.69px; top: 17.10px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: #69746D; border: 2px #004F59 solid"></div>
<div style="width: 2.85px; height: 2.85px; left: -20.66px; top: 17.10px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: #69746D; border: 2px #004F59 solid"></div>
<div style="width: 8.55px; height: 2.85px; left: -12.83px; top: 22.80px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: #69746D; border: 2px #004F59 solid"></div>
<div style="width: 28.50px; height: 19.95px; left: -2.85px; top: 4.28px; position: absolute; transform: rotate(180deg); transform-origin: 0 0">
<div style="width: 0px; height: 5.70px; left: -14.25px; top: 2.85px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
<div style="width: 0px; height: 5.70px; left: -0px; top: 14.25px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
<div style="width: 0px; height: 5.70px; left: -28.50px; top: 14.25px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
<div style="width: 2.85px; height: 2.85px; left: -12.83px; top: 0px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
</div>
</div>
</div>
<div style="padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; background: #004F59; border-radius: 18px; overflow: hidden; justify-content: center; align-items: center; gap: 19px; display: inline-flex">
<div style="text-align: justify; color: white; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 18px">${entry.a_msg}</div>
</div>
</div>
</div>`;
    chatBox.append(userMessageHtml + botMessageHtml);
    loadAllUsersChatHistory()
});
$('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
// updateChatEmptyState();
},
error: function(xhr, status, error) {
console.error("Failed to load user's chat history:", error);
}
});
}

//Main Window: append msg & continue chat
function appendMessage(message, className, isLoader = false) {
    var processedMessage = message.replace(/^\nAnswer: /, '');
    processedMessage = processedMessage.replace(/^AI:\s*/, '');
    processedMessage_2 = processedMessage.trim();

    var isCodeBlock = processedMessage_2.startsWith('```') && processedMessage_2.endsWith('```');

    var messageContent;
    if (isLoader) {
        messageContent = '<div class="loader"></div>';
    } else if (isCodeBlock) {
        var codeContent = processedMessage_2.substring(3, processedMessage_2.length - 3);
        messageContent = '<pre><code>' + codeContent + '</code></pre>';
    } else {
        // ??????
        messageContent = processedMessage_2.replace(/\n/g, '<br>');
    }

    var messageElement = $('<div>').addClass('chat-message ' + className);
    // console.log(messageContent)
    if(className.includes('user-message')) {
    //     var iconHtml = '<div><i class="fa-solid fa-user"></i><span>You: </span></div>';
    //     var textDiv = $('<div>').addClass('message-text').html(messageContent);
    //     messageElement.append(iconHtml).append(textDiv);
      messageElement.html(`
        <div style="width: 100%; height: 100%; padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; flex-direction: column; justify-content: flex-start; align-items: flex-end; gap: 6px; display: inline-flex">
          <div style="padding-left: 2px; padding-right: 2px; padding-top: 1px; padding-bottom: 1px; justify-content: flex-end; align-items: center; gap: 10px; display: inline-flex">
            <div style="text-align: right; color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 20px">User
                      <img src="{{ url_for('static', filename='images/user.png') }}"  alt="Image Description" style="vertical-align: middle;">
</div>
          </div>
          <div style="padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; border-radius: 18px; overflow: hidden; border: 2px #0097A9 solid; justify-content: center; align-items: center; gap: 19px; display: inline-flex">
            <div style="color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 18px">${messageContent}</div>
          </div>
        </div>
      `);
    }
    else {
        // var iconHtml = '<div><i class="fa-solid fa-robot"></i><span>Bot: </span></div>';
        // var textDiv = $('<div>').addClass('message-text').html(messageContent);
        // messageElement.append(iconHtml).append(textDiv);
        messageElement.html(`
        <div>
<div style="width: 100%; height: 100%; padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex">
<div style="transform: rotate(180deg); transform-origin: 0 0; justify-content: flex-start; align-items: flex-end; gap: 5px; display: inline-flex">
<div style="transform: rotate(180deg); transform-origin: 0 0; text-align: right; color: #004F59; font-size: 22px;font-weight: 700; word-wrap: break-word; font-size: 20px">
<img src="{{ url_for('static', filename='images/robot.png') }}"  alt="Image Description" style="vertical-align: middle;">
Assistant
</div>
<div style="width: 34px; height: 34px; position: relative; transform: rotate(180deg); transform-origin: 0 0">
<div style="width: 34px; height: 34px; left: 0px; top: 0px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: rgba(255, 255, 255, 0.01)"></div>
<div style="width: 21.38px; height: 17.10px; left: -6.41px; top: 12.83px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: rgba(255, 255, 255, 0.01); border: 2px #004F59 solid"></div>
<div style="width: 2.85px; height: 2.85px; left: -10.69px; top: 17.10px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: #69746D; border: 2px #004F59 solid"></div>
<div style="width: 2.85px; height: 2.85px; left: -20.66px; top: 17.10px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: #69746D; border: 2px #004F59 solid"></div>
<div style="width: 8.55px; height: 2.85px; left: -12.83px; top: 22.80px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: #69746D; border: 2px #004F59 solid"></div>
<div style="width: 28.50px; height: 19.95px; left: -2.85px; top: 4.28px; position: absolute; transform: rotate(180deg); transform-origin: 0 0">
<div style="width: 0px; height: 5.70px; left: -14.25px; top: 2.85px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
<div style="width: 0px; height: 5.70px; left: -0px; top: 14.25px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
<div style="width: 0px; height: 5.70px; left: -28.50px; top: 14.25px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
<div style="width: 2.85px; height: 2.85px; left: -12.83px; top: 0px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
</div>
</div>
</div>
<div style="padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; background: #004F59; border-radius: 18px; overflow: hidden; justify-content: center; align-items: center; gap: 19px; display: inline-flex">
<div style="text-align: justify; color: white; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 18px">${messageContent}</div>
</div>
</div>
</div>`)
    }

    $('#chatBox').append(messageElement);
    loadAllUsersChatHistory()
    $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
    // updateChatEmptyState();

    return messageElement;
}

//Main window:  Input Box

function adjustTextAreaHeight() {
    textArea.style.height = "0px";
    // textArea.style.height = Math.min(textArea.scrollHeight, 280) + "px";
    textArea.style.height = Math.min(textArea.scrollHeight, 150) + "px";


    inputContainer.style.height = "auto";
    outerContainer.style.height = "auto";

    // var newHeight = Math.min(textArea.scrollHeight + 20, 320) + "px";
    var newHeight = Math.min(textArea.scrollHeight + 20, 150) + "px";

    inputContainer.style.height = newHeight;
    outerContainer.style.height = newHeight;
}
// input box size
textArea.addEventListener('input', adjustTextAreaHeight);

// input function
textArea.addEventListener('paste', (event) => {
    const pastedText = event.clipboardData.getData('text/plain');
    event.preventDefault();
    textArea.value += pastedText;
    adjustTextAreaHeight();
});

textArea.addEventListener('keydown', (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        submitButton.click();
        textArea.value = '';
        console.log('Message sent');
    } else if (event.key === 'Enter' && event.shiftKey) {
        event.preventDefault();
        const cursorPosition = textArea.selectionStart;
        textArea.value = textArea.value.substring(0, cursorPosition) + '\n' + textArea.value.substring(cursorPosition);
        textArea.selectionStart = textArea.selectionEnd = cursorPosition + 1;
        adjustTextAreaHeight();
    }
});

adjustTextAreaHeight();

document.addEventListener('DOMContentLoaded', function() {
    submitButton.addEventListener('click', function() {
        var textArea = document.getElementById('user_input');
        textArea.value = null;
        adjustTextAreaHeight();
    });
});

$('#chatForm').on('submit', function(e){
    e.preventDefault();
    console.log("submit")
    var formData = new FormData(this);
    var ragStatus = $('#rag').val();
    var useOriginalText = $('#useOriginalText').is(':checked');
    formData.append('useOriginalText', useOriginalText); // ??useOriginalText??
    var userMessage = $('#user_input').val();
    appendMessage(userMessage, 'user-message', false);
    var loaderMessage = appendMessage('', 'bot-message', true);

    // Include the new settings in the formData
    var includeHistory = $('#include_history').is(':checked');
    var maxHistoryNo = $('#max_history_no').val();
    var model = $('#model').val();

    formData.append('include_history', includeHistory);
    formData.append('max_history_no', maxHistoryNo);
    formData.append('model', model);

    // var selectedDataset = $('#initial-dataset-selector').val();
    var selectedDataset = currentSelectedDataset;
    console.log("selectedDataset")
    console.log(model)
    if (ragStatus === 'on' && !selectedDataset) {
        // alert('No dataset selected. Using RAG Off mode.');
        var warningWin = new bootstrap.Modal(document.getElementById('warningWin'), {
          keyboard: false
        });
        warningWin.show();
        formData.append('rag', 'off');

    } else {
        formData.append('rag', ragStatus);
    }
    formData.append('selectedDataset', selectedDataset);
    textArea.value = ''
    adjustTextAreaHeight()
    $.ajax({
        url: '/chat',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        success: function(data){
            loaderMessage.remove();
            appendMessage(data.response, 'bot-message', false);
        },
        error: function(){
            loaderMessage.find('.message-text').html('Error loading response.');
        }
    });
});


// ========= TO BE COMPLETED

//Main Window: msg copying
$('#chatBox').on('click', '.copy-btn', function() {
    var message = $(this).siblings('.message-text').text();
    navigator.clipboard.writeText(message).then(() => {
        alert('Message copied!');
    });
});

// ========= TO BE COMPLETED


//================================================================================================================================================
//================================================================================================================================================


//============= Side Menu Expanded

// Button control
$('#toggle-file-container').click(function() {
    $('.container').toggleClass('collapsed-right');
    var isCollapsed = $('.container').hasClass('collapsed-right');
    $(this).find('i').toggleClass('fa-arrow-left fa-arrow-right'); // ????
});

$('#toggle-database-icon-btn').click(function() {
    $('.container').toggleClass('collapsed-right');
});

//Side Menu: open/close side menu
$('#toggle-chat-history').click(function() {
    $('.container').toggleClass('collapsed-left');
    var isCollapsed = $('.container').hasClass('collapsed-left');
    $(this).find('i').toggleClass('fa-arrow-right fa-arrow-left'); // ????
});

//Side Menu: load history
    function loadAllUsersChatHistory() {
    $.ajax({
        url: '/get-all-users-data',
        type: 'GET',
        success: function(data) {
            const chatHistoryBox = $('#chatHistoryBox');
            chatHistoryBox.empty();
            Object.keys(data).forEach(userId => {
                const firstQuestion = data[userId]['q_msg'];
                const firstQuestionTime = data[userId]['q_time']
                console.log(data)
                const entryHtml = `
                    <div class="chat-history-entry" data-userId="${userId}">
                        <div style="width: 100%; height: 100%;  background: #DDEFE8; border-radius: 22px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: center; gap: 9px; display: inline-flex">

                            <div style=" height:30%; padding-left: 10px;padding-bottom:3px;  align-items: center; ">
                                <div style="width:250px; justify-content: flex-start; align-items: flex-start; gap: 10px; display: inline-flex;">
                                    <div style="color: #6FC2B4; font-size: 15px; font-weight: 700; word-wrap: break-word">${firstQuestionTime}</div>
                                </div>
                                <button style="background:#DDEFE8; border: none; margin-top: 5px">
                                    <img src="{{ url_for('static', filename='images/Cross button Frame.svg') }}"  alt="Image Description" style="vertical-align: middle; padding-bottom: 5px">
                                </button>
                            </div>


                            <div style="width: 290px; height: 0px; border: 1px #6FC2B4 solid"></div>


                            <div style="width: 284px; height: 40%; padding-top:10px; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
                                <div style="color: #004F59; font-size: 15px; font-weight: 700; word-wrap: break-word">Title:&nbsp;</div>
                                <div style="width: 246px; max-height: 50px ; color: #004F59; font-size: 15px;font-weight: 700; word-wrap: break-word; overflow-y: hidden">${firstQuestion}</div>
                            </div>
                        </div>
                   </div>
                `;
                chatHistoryBox.prepend(entryHtml);
            });
        },
        error: function(xhr, status, error) {
            console.error("Failed to load all users' chat history:", error);
        }
    });
}

//Side Menu: open corresponding chat history
    $('#chatHistoryBox').on('click', '.chat-history-entry', function() {
        console.log("Clicked on: ", this);
        const userId = $(this).data('userid');
        console.log("test " + userId);
        if (userId) {
            console.log(userId);
            loadUserChatHistory(userId);
        } else {
            console.error("User ID is undefined.");
        }
    });

// Add event listener for click on the delete button within each chat history entry
    // Add event listener for click on the delete button within each chat history entry
    $('#chatHistoryBox').on('click', '.chat-history-entry button', function() {
        var userId = $(this).closest('.chat-history-entry').data('userid');
        console.log("Deleting chat history for user ID: " + userId); // For debugging purposes

        var that = this; // Preserve the context for use in the AJAX callback

        // AJAX request to the server to handle the deletion
        $.ajax({
            url: '/delete-chat-history', // Ensure this URL matches the appropriate endpoint on your server
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ userId: userId }),
            success: function(response) {
                console.log('Chat history deleted successfully for user ID: ' + userId);
                // Remove the chat history entry from the DOM
                $(that).closest('.chat-history-entry').remove();
                // Refresh the chat history list
                loadAllUsersChatHistory();
            },
            error: function(xhr, status, error) {
                console.error("Failed to delete chat history for user ID: " + userId, error);
            }
        });
        return false; // Prevent default action and stop propagation
    });


//================================================================================================================================================
//================================================================================================================================================



// ========================== Document Window

// Generate Single document layout
function newLoadInitialDatasets() {
    $.getJSON('/get-datasets', function (datasets) {
    $('#databaseList').empty();
    datasets.forEach(dataset => {
        const datasetItem = $('<a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" style="background: #DDEFE8; border: 0px">').html(`
            <div class="me-2 d-flex align-items-center">
                <input class="form-check-input me-1" type="checkbox" value="" aria-label="..." style="margin-top: 0rem">
                <span class="fas fa-folder" style="color: #0097A9; margin-left: 0.5rem"></span>
            </div>
            <span class="flex-grow-1 dataset-name" style="cursor:pointer; color: #0097A9; font-family: Inter; font-weight: 600">${dataset.dataset_name}</span>
<!--            <label class="switch ms-auto">-->
<!--                <input type="checkbox">-->
<!--                <span class="slider round"></span>-->
<!--            </label>-->
        `);
        datasetItem.appendTo('#databaseList');
    });
});
}

    $('#databaseList').on('change', '.list-group-item .form-check-input', function() {
    var datasetName = $(this).closest('.list-group-item').find('.dataset-name').text().trim();
    if ($(this).is(':checked')) {
        // Add the dataset name to the array if it's checked and not already included
        if (!selectedDatasets.includes(datasetName)) {
            selectedDatasets.push(datasetName);
        }
    } else {
        // Remove the dataset name from the array if it's unchecked
        selectedDatasets = selectedDatasets.filter(name => name !== datasetName);
    }
    console.log(selectedDatasets); // Logging to see the current state of selectedDatasets
});

$('#deleteButton').click(function() {
    if ($('#databaseList').is(':visible')) {
        // Delete selected datasets
                selectedDatasets.forEach(function(dataset) {
                    $.ajax({
                        url: '/delete-dataset',  // Backend endpoint for deleting datasets
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ dataset: dataset }),  // Send the dataset name to be deleted
                        success: function(response) {
                            console.log(response.message);  // Log the success message
                            // Update the UI to reflect the deletion
                            $('#databaseList .list-group-item').each(function() {
                                if (selectedDatasets.includes($(this).find('.dataset-name').text().trim())) {
                                    $(this).remove();  // Remove the dataset entry from the list
                                }
                            });
                            selectedFiles = [];  // Clear the array after successful deletion
                        },
                        error: function(xhr, status, error) {
                            console.error("Error deleting dataset:", error);
                            selectedFiles = [];  // Clear the array after successful deletion
                        }
                    });
                });
    } else if ($('#fileList').is(':visible')) {
        // Delete selected files
        $('#fileList .list-group-item .form-check-input:checked').each(function() {
            var fileItem = $(this).closest('.list-group-item');  // Store reference to the list item
            var fileName = fileItem.find('.flex-grow-1').text().trim();
            var selectedDataset = currentSelectedDataset;
            $.ajax({
                url: '/delete-file',  // Backend endpoint for deleting files
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ file_name: fileName, dataset: selectedDataset }),
                success: function(response) {
                    console.log('File deleted successfully:', response.message);
                    fileItem.remove();  // Remove the file from the list in the UI
                    selectedDataset = [];
                },
                error: function(xhr, status, error) {
                    console.error("Error deleting file:", error);
                }
            });
        });
    }

});


    // Load files for a specific dataset and reset selections from other datasets
    function loadFilesForDataset(datasetName) {
        // Immediately clear the file list to prevent displaying the previous dataset's files
    $('#fileList').empty();

    // Show the loading icon
    $('#fileList').append('<div class="d-flex justify-content-center align-items-center" style="position: fixed; top: 35%; left: 0; width: 100%; z-index: 9999;"><div class="docu-loader"></div></div>');
    $.getJSON('/get-datasets', function (datasets) {
        const dataset = datasets.find(d => d.dataset_name === datasetName);
        $('#fileList').empty();

                // Hide the loading icon
        $('#loading-icon').remove();

        // find element loading by ID, call same loading icon

        dataset.document_list.forEach(file => {
            // Check if the current file is selected
            const isSelected = selectedFilesMap[datasetName] && selectedFilesMap[datasetName].includes(file);
            const fileItem = $('<a href="#" class="list-group-item list-group-item-action d-flex align-items-center" style="background: #DDEFE8; border: 0px;">').html(`
                <div class="me-2 d-flex align-items-center">
                    <input class="form-check-input me-1" type="checkbox" value="" aria-label="..." style="margin-top: 0rem">
                    <span class="fas fa-file" style="color: #0097A9; margin-left: 0.5rem"></span>
                </div>
                <span class="flex-grow-1" style="color: #0097A9; font-family: Inter; font-weight: 600">${file}</span>
                <label class="switch ms-auto select-switch"> <!-- Added class for targeting -->
                    <input type="checkbox" ${isSelected ? 'checked' : ''}>
                    <span class="slider round"></span>
                </label>
            `);
            fileItem.appendTo('#fileList');

            // Add click event handler
            fileItem.find('.select-switch input[type="checkbox"]').on('click', function() {
                    // Reset selections from other datasets
    Object.keys(selectedFilesMap).forEach(key => {
        if (key !== datasetName) {
            selectedFilesMap[key] = [];
        }
    });

                const isChecked = $(this).is(':checked');
                const fileName = $(this).closest('.list-group-item').find('span.flex-grow-1').text();

                // Update selectedFilesMap to track selection status
                if (!selectedFilesMap[datasetName]) {
                    selectedFilesMap[datasetName] = [];
                }

                if (isChecked) {
                    if (!selectedFilesMap[datasetName].includes(fileName)) {
                        selectedFilesMap[datasetName].push(fileName);
                    }
                } else {
                    selectedFilesMap[datasetName] = selectedFilesMap[datasetName].filter(f => f !== fileName);
                }

                // Send the selected files to the server
                $.ajax({
                    url: '/selected-files',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ selectedFiles: selectedFilesMap[datasetName] }),
                    success: function(response) {
                        console.log('Selected files sent successfully');
                    },
                    error: function(xhr, status, error) {
                        console.log('Error: ' + error);
                    }
                });
            });
        });
    });
}

    $('#fileList').on('change', '.form-check-input', function() {
    const isChecked = $(this).is(':checked');
    const fileName = $(this).closest('.list-group-item').find('.flex-grow-1').text().trim(); // Adjust this selector based on your actual HTML structure.

    // Manage the array of selected files
    if (isChecked) {
        // Add the file to the selected files array if it's checked and not already included
        if (!selectedFiles.includes(fileName)) {
            selectedFiles.push(fileName);
        }
    } else {
        // Remove the file from the array if it's unchecked
        selectedFiles = selectedFiles.filter(name => name !== fileName);
    }

    // Debugging log to see the current selected files
    console.log(selectedFiles);
});

    // docu window: update folder layout
    $('#confirmCreateFolder').on('click', function() {
    var originalInput = $('#newFolderName').val().trim();
    var sanitizedInput = originalInput.replace(/[^a-zA-Z0-9_]/g, '_').replace(/^_+/, '');

    if (sanitizedInput) {
        if (originalInput !== sanitizedInput) {
            // alert("Note: Special characters in the folder name have been replaced with underscores.");
        }

        const datasetItem = $('<a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" style="background: #DDEFE8; border: 0px">')
            .html(`
                <div class="me-2 d-flex align-items-center">
                     <input class="form-check-input me-1" type="checkbox" value="" aria-label="..." style="margin-top: 0rem">
                    <span class="fas fa-folder" style="color: #0097A9; margin-left: 0.5rem"></span>
                </div>
                <span class="flex-grow-1 dataset-name" style="cursor:pointer; color: #0097A9; font-family: Inter; font-weight: 600;">${sanitizedInput}</span>
            `);
        $('#databaseList').append(datasetItem);

        $('#newFolderName').val('');
        $('#createFolderModal').modal('hide');
    } else {
        alert('Please enter a dataset name.');
    }
});

    // docu window: enter lower level
    // When clicking on a dataset, call the function to load its files
    $('#databaseList').on('click', '.dataset-name', function() {
        const dataset = $(this).text();
        currentSelectedDataset = dataset;
        loadFilesForDataset(dataset);
        $('#databaseList').hide();
        $('#fileList').show();
        updateButtonVisibility(); // Update the visibility based on the current view
    });

    //docu window: back to upper level
    $('#backToDatabaseList').click(function() {
        $('#fileList').hide();
        $('#databaseList').show();
            updateButtonVisibility(); // Update the visibility when returning to dataset view
    });

    //docu window: create folder
    $('#createFolderButton').on('click', function() {
        var createFolderModal = new bootstrap.Modal(document.getElementById('createFolderModal'), {
          keyboard: false
        });
        createFolderModal.show();
    });

    // docu window: add file button layout control
    function updateButtonVisibility() {
    if ($('#fileList').is(':visible')) {
        // File page is visible
        $('#createFolderButton').hide();
        $('#uploadFileButton').show();
    } else {
        // Dataset page is visible
        $('#createFolderButton').show();
        $('#uploadFileButton').hide();
    }
}

    //docu window: add selected dataset
    function addDatasetToLoaded(dataset) {
        if (dataset && $('#loaded-dataset-selector').find('#dataset-' + dataset).length == 0) {
            var datasetDiv = $('<div>').attr('id', 'dataset-' + dataset).addClass('loaded-dataset-item');
            datasetDiv.html('<span class="dataset-name">' + dataset + '</span><button class="delete-dataset-btn">x</button>');
            $('#loaded-dataset-selector').append(datasetDiv);
        }
    }

//docu window: update file list
function updateFileList(selectedDataset) {
       if (!selectedDataset) {
            $('#fileList').empty();
            // updateFileCount();
            return;
        }
    $.getJSON('../vectorstore/vsdb_log.json', function(data) {
        var filteredFiles = data.filter(function(file) {
            return file.dataset === selectedDataset;
        });

        $('#fileList').empty();

        filteredFiles.forEach(function(file) {
            var fileItem = $('<div>').addClass('file-item');
            fileItem.html('<input type="checkbox"><span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
            // fileItem.html('<span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
            $('#fileList').append(fileItem);
        });
        updateFileCount();
    });
}

updateFileList($('#dataset-selector').val());


//docu window: upload single file inside folder
$('#uploadFileButton').on('click', function() {
    $('#file-upload').click();
});

//docu window: upload screen
$('#file-upload').on('change', function() {
    if (this.files.length > 0) {
        var formData = new FormData();
        for (var i = 0; i < this.files.length; i++) {
            formData.append('file', this.files[i]);
        }
        var selectedDataset = currentSelectedDataset;
        formData.append('selectedDataset', selectedDataset);

        var uploadLoading = document.getElementById("uploadLoading");
        console.log("file-upload1");

        // $('#upload-progress-bar').css('width', '0%');
        // $('#upload-loader').show();
        // $('#upload-status-message').show().text('Uploading file...');

        var xhr = new XMLHttpRequest();
        xhr.open('POST', '/upload', true);

        xhr.upload.onprogress = function(e) {
            console.log("file-upload2");

            uploadLoading.style.visibility ="visible";
            if (e.lengthComputable) {
                var percentComplete = (e.loaded / e.total) * 100;
                $('#upload-progress-bar').css('width', percentComplete + '%');

                if (percentComplete === 100) {
                    $('#upload-status-message').text('Processing file...');
                }
            }
        };

        xhr.onload = function() {
            if (xhr.status === 200) {
                $('#upload-status-message').text('File uploaded successfully');
                $('#upload-loader .loader').hide();

                uploadLoading.style.visibility ="hidden";

                Array.from(this.files).forEach(file => {
                    const noDotfileName = file.name.replace(/\.(txt|doc|docx|pdf)$/, (match, p1) => p1);
                    const fileName = noDotfileName.replace(/[^a-zA-Z0-9]+/g, '');
                    const newFileItem = $('<a href="#" class="list-group-item list-group-item-action d-flex align-items-center" style="background: #DDEFE8; border: 0px">').html(`
                        <div class="me-2 d-flex align-items-center">
                            <input class="form-check-input me-1" type="checkbox" value="" aria-label="..." style="margin-top: 0rem">
                            <span class="fas fa-file" style="color: #0097A9; margin-left: 0.5rem"></span>
                        </div>
                        <span class="flex-grow-1" style="color: #0097A9 ;font-family: Inter; font-weight: 600">${fileName}</span>
                        <label class="switch ms-auto select-switch">
                            <input type="checkbox">
                            <span class="slider round"></span>
                        </label>
                    `);
                    newFileItem.appendTo('#fileList');
                    newFileItem.find('.select-switch input[type="checkbox"]').on('click', function() {
                const selectedFiles = [];
                $('#fileList .select-switch input[type="checkbox"]:checked').each(function() {
                    selectedFiles.push($(this).closest('.list-group-item').find('span.flex-grow-1').text());
                });
                $.ajax({
                    url: '/selected-files',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ selectedFiles: selectedFiles }),
                    success: function(response) {
                        console.log('Selected files sent successfully');
                    },
                    error: function(xhr, status, error) {
                        console.log('Error: ' + error);
                    }
                });
            });

                });
            } else {

                uploadLoading.style.visibility ="hidden";

                $('#upload-status-message').text('Error occurred during file upload');
                $('#upload-loader .loader').hide();
            }
            $('#file-upload').val('');
        }.bind(this);

        xhr.onerror = function() {

            uploadLoading.style.visibility ="hidden";

            $('#upload-status-message').text('Network error occurred during file upload');
            $('#upload-loader .loader').hide();
            $('#file-upload').val('');
        };

        xhr.send(formData);
    }
});


// ========= TO BE COMPLETED
// docu window: window open/close control
$('[data-bs-toggle="offcanvas"]').on('click', function() {
    $(this).toggleClass('collapsed');
});

// docu window: file delete
$('#fileList').on('click', '.delete-switch', function() {
    var fileName = $(this).siblings('.flex-grow-1').text(); // Modified to get the file name
    var selectedDataset = $('#initial-dataset-selector').val(); // Assuming you have a way to get the selected dataset

    $.ajax({
        url: '/delete-file',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ file_name: fileName, dataset: selectedDataset }),
        success: function(response) {
            console.log(response.message);
        },
        error: function(xhr, status, error) {
            console.log("Error: " + error);
        }
    });

    $(this).closest('.list-group-item').remove(); // Modified to remove the closest list group item
});

// $('#model').val('mixtral');

function updateDeleteButtonStatus() {
    var selectedDataset = $('#initial-dataset-selector').val();
    if(selectedDataset) {
        $('#delete-dataset-btn').css('background-color', '#f44336').prop('disabled', false);
    } else {
        $('#delete-dataset-btn').css('background-color', 'grey').prop('disabled', true);
    }
}

$('#delete-dataset-btn').show();

// $(document).on('click', '.delete-dataset-btn', function() {
//     $(this).closest('.loaded-dataset-item').remove();
// });

$(document).on('click', '.loaded-dataset-item', function() {
    var selectedDataset = $(this).find('.dataset-name').text();
    updateFileList(selectedDataset);
    updateFileCount();
    $('#fileList').empty();

    $.ajax({
        url: '/selected-dataset',
        type: 'POST',
        contentType: 'application/json',
        // data: { selected_dataset: selectedDataset },
        data: JSON.stringify({ selectedDataset: selectedDataset }),
        success: function(response) {
            console.log('Selected dataset updated successfully:', response.message);
        },
        error: function(xhr, status, error) {
            console.log("Error occurred while updating selected dataset:", error);
        }
    });
});

$('#fileList').on('click', '.delete-btn', function() {
    var fileName = $(this).siblings('.file-name').text();
    var selectedDataset = $('#initial-dataset-selector').val(); // ??????????

    $.ajax({
        url: '/delete-file',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ file_name: fileName, dataset: selectedDataset }), // ????????
        success: function(response) {
            console.log(response.message);
        },
        error: function(xhr, status, error) {
            console.log("Error: " + error);
        }
    });

    $(this).parent('.file-item').remove();
});

// ========= TO BE COMPLETED



//================================================================================================================================================
//================================================================================================================================================



// ========================== Setting Window


function updateModelOptions() {
    console.log("updateModelOptions")
    var modeSelect = document.getElementById("mode");
    var modelSelect = document.getElementById("model");

    console.log(modelSelect.value)
    console.log(modelSelect.value)

    if (modeSelect.value === "online") {
        modelSelect.options[0].style.display = "block"; // GPT-3.5
        modelSelect.options[1].style.display = "none"; // LLAMA
        // modelSelect.options[2].style.display = "none"; // Mixtral-8x7B-Instruct
        // modelSelect.options[3].style.display = "none";  // instructmixtral-1
        modelSelect.value = "gpt-3.5";
    } else {
        modelSelect.options[0].style.display = "none"; // GPT-3.5
        modelSelect.options[1].style.display = "block"; // LLAMA
        // modelSelect.options[2].style.display = "block";  // Mixtral-8x7B-Instruct
        // modelSelect.options[3].style.display = "block";  // instructmixtral-1
        modelSelect.value = "mixtral";
    }
}

updateModelOptions();
$('#mode').change(updateModelOptions);

function updateFormValues() {
    var mode = $('#mode').val();
    var model = $('#model').val();
    var rag = $('#rag').val();

    $('#form_mode').val(mode);
    $('#form_model').val(model);
    $('#form_rag').val(rag);

    $('#display-mode').text(mode);
    $('#display-model').text(model);
    $('#display-rag').text(rag);
}


$('#mode, #model, #rag').change(updateFormValues);

function loadInstructOptions() {
    $.ajax({
        url: '/get-prompt-templates',
        type: 'GET',
        success: function(data) {
            var promptTemplates = data.prompt_template_list;
            var instructSelector = $('#instruct-selector');
            instructSelector.empty(); // ???????

            instructSelector.append($('<option>', {
                value: 'none',
                text: 'No role selected'
            }));

            promptTemplates.forEach(function(template) {
                instructSelector.append($('<option>', {
                    value: template,
                    text: template
                }));
            });
        },
        error: function() {
            console.log('Error loading role options');
        }
    });
}



//================================================================================================================================================


$('#select-all-files').change(function() {
    var isChecked = $(this).is(':checked');
    $('#fileList .file-item input[type="checkbox"]').prop('checked', isChecked);

    if (isChecked) {
        var selectedFiles = [];
        $('#fileList .file-item input[type="checkbox"]:checked').each(function() {
            selectedFiles.push($(this).siblings('.file-name').text());
        });

        $.ajax({
            url: '/selected-files',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ selectedFiles: selectedFiles }),
            success: function(response) {
                console.log('Selected files sent successfully', response);
            },
            error: function(xhr, status, error) {
                console.error('Error: ' + error);
            }
        });
    }
});
$('#toggle-options-btn').click(function() {
    $('.options-container').slideToggle(); // ?? slideToggle ??????
});
$('#initial-dataset-selector').change(function() {
    var selectedDataset = $(this).val();
    updateFileList(selectedDataset)
    updateDeleteButtonStatus();
});

$('#delete-dataset-btn').click(function() {
    var selectedDataset = $('#initial-dataset-selector').val();
    if(selectedDataset) {
        $.ajax({
            url: '/delete-dataset',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ dataset: selectedDataset }), // ?????????
            success: function(response) {
                console.log('Dataset deleted successfully:', response);
                loadInitialDatasets();
                $('#initial-dataset-selector').val('');
                $('#fileList').empty();
                updateFileCount();
            },
            error: function(xhr, status, error) {
                console.log("Error occurred while deleting dataset:", error);
            }
        });
    } else {
        alert('Please select a dataset to delete.');
    }
});
$('#toggle-new-dataset-btn').click(function() {
    $('#new-dataset-container').slideToggle();
});

$('#create-dataset-btn').click(function() {
    var newDatasetName = $('#new-dataset-name').val().trim();
    if(newDatasetName) {
        $('#initial-dataset-selector').append('<option value="' + newDatasetName + '">' + newDatasetName + '</option>');
        $('#initial-dataset-selector').val(newDatasetName);
        $('#new-dataset-name').val('');
        $('#new-dataset-container').slideUp();
        var selectedDataset = $(this).val();
        updateFileList(selectedDataset);
        updateDeleteButtonStatus();
    } else {
        alert('Please enter a dataset name.');
    }
});
$('#addChatHistoryButton').click(function() {
    // Send an AJAX request to the server to create a new chat history
    $.ajax({
        url: '/create-new-chat-history',
        type: 'POST',
        success: function(response) {
            // Handle success response, maybe update the UI to reflect the new chat history
            console.log('New chat history added successfully:', response);
            // Optionally, reload the chat history to include the new one
            loadAllUsersChatHistory();
            // After loading the new chat history, simulate a click on the latest entry
            setTimeout(function() {
                $('#chatHistoryBox .chat-history-entry:first-child').click(); // Simulates a click on the last chat history entry
            }, 1000); // Adjust timeout as necessary to ensure the DOM has updated
        },
        error: function(xhr, status, error) {
            console.error("Error creating new chat history:", error);
        }
    });
});



                $(document).on('click', '.chat-history-entry', function() {
                    const userId = $(this).data('userid');
                    console.log("User ID: ", userId);
                    // ??????????
                    $(this).css('background-color', '#e0e0e0'); // ????????
                    setTimeout(() => {
                        $(this).css('background-color', ''); // ???????
                    }, 2000); // 2000 ??????????????
                });
                $(document).on('click', '.chat-history-entry', function() {
                    const userId = $(this).data('userid');
                    // ??????
                    $('#feedback').text('Loading user info...'); // ?????? ID ? feedback ???????????
                    // ????????? AJAX ??
                    setTimeout(() => {
                        $('#feedback').text(''); // ??????
                        // ??????????????
                    }, 2000); // ????????
                });
                $(document).on('click', '.chat-history-entry', function() {
                    const userId = $(this).data('userid');
                    $.ajax({
                        url: '/update-user-session',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ userId: userId }),
                        success: function(response) {
                            console.log('Send UserID Successfully', response);
                        },
                        error: function(xhr, status, error) {
                            console.error("Send UserID error", error);
                        }
                    });
                });





// function loadInitialDatasets() {
//     $.getJSON('../vectorstore/vsdb_log.json', function (data) {
//         let datasets = new Set(data.map(item => item.dataset));
//
//         $('#initial-dataset-selector').empty().append('<option value="">No dataset selected</option>');
//
//         datasets.forEach(dataset => {
//             $('#initial-dataset-selector').append('<option value="' + dataset + '">' + dataset + '</option>');
//         });
//     });
// }


//
//
// $('#load-dataset-btn').click(function() {
//     var selectedDataset = $('#initial-dataset-selector').val();
//     addDatasetToLoaded(selectedDataset);
// });
// $('#load-dataset-btn').click(function() {
//     var selectedDataset = $('#initial-dataset-selector').val();
//     addDatasetToLoaded(selectedDataset);
//     updateDatasetCount();
// });



// $(document).on('click', '.loaded-dataset-item', function() {
//     var selectedDataset = $(this).text();
//     updateFileList(selectedDataset);
// });




// $(document).on('click', '.loaded-dataset-item', function() {
//     var selectedDataset = $(this).find('.dataset-name').text();
//     updateFileList(selectedDataset);
// });
// function updateFileList(selectedDataset) {
//     return new Promise((resolve, reject) => {
//         if (!selectedDataset) {
//             $('#fileList').empty();
//             updateFileCount();
//             resolve();
//             return;
//         }
//         $.getJSON('../vectorstore/vsdb_log.json', function(data) {
//             var filteredFiles = data.filter(function(file) {
//                 return file.dataset === selectedDataset;
//             });
//
//             $('#fileList').empty();
//
//             filteredFiles.forEach(function(file) {
//                 var fileItem = $('<div>').addClass('file-item');
//                 fileItem.html('<input type="checkbox"><span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
//                 // fileItem.html('<span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
//                 $('#fileList').append(fileItem);
//             });
//             updateFileCount();
//             resolve();
//         });
//     });
// }


// function updateFormValues() {
//     $('#form_mode').val($('#mode').val());
//     $('#form_model').val($('#model').val());
//     $('#form_rag').val($('#rag').val());
// }








// $('#instruct-selector').change(function() {
//     var selectedOption = $(this).val();
//
//     $.ajax({
//         url: '/select-instruct',
//         type: 'POST',
//         contentType: 'application/json',
//         data: JSON.stringify({ selectedInstruct: selectedOption }),
//         success: function(response) {
//             console.log('Response from server:', response);
//         },
//         error: function(xhr, status, error) {
//             console.log('An error occurred:', error);
//         }
//     });
// });
//


// function updateDatasetCount() {
//     var count = $('#loaded-dataset-selector').children().length;
//     $('#loaded-dataset-count').text(count);
// }
//
// function updateFileCount() {
//     var count = $('#fileList').children().length;
//     $('#file-count').text(count);
// }



// $(document).on('click', '.delete-dataset-btn', function() {
//     $(this).closest('.loaded-dataset-item').remove();
//     $('#fileList').empty();
//     updateDatasetCount();
//     updateFileCount();
// });

// $('#fileList').on('click', '.delete-btn', function() {
//     $(this).parent('.file-item').remove();
//     updateFileCount();
// });
// function addDatasetToLoadedIfNotExists(dataset) {
//     if (dataset && $('#loaded-dataset-selector').find('#dataset-' + dataset).length == 0) {
//         var datasetDiv = $('<div>').attr('id', 'dataset-' + dataset).addClass('loaded-dataset-item');
//         datasetDiv.html('<span class="dataset-name">' + dataset + '</span><button class="delete-dataset-btn">x</button>');
//         $('#loaded-dataset-selector').append(datasetDiv);
//         updateDatasetCount();
//     }
// }



});


</script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
