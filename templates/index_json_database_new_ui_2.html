<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG-CHATBOT</title>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }
        .container {
            display: flex;
            justify-content: space-between;
            width: 90%;
            max-width: 1200px;
        }
        .chat-container, .file-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        }
        .chat-container {
            width: 60%;
        }
        .file-container {
            width: 35%;
            margin-left: 5%;
        }
        .file-list {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            background: #fafafa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .chat-box {
            height: 600px;
            overflow-y: auto;
            border: 1px solid #ddd;
            background: #fafafa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .chat-message, .file-item {
            padding: 8px;
            margin: 5px 0;
            border-radius: 8px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
        }
        .user-message {
            text-align: right;
        }
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .file-name {
            flex-grow: 1;
            margin-left: 10px;
        }
        .chat-form, .file-form {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .chat-form > *, .file-form > * {
            flex: 1;
            min-width: 120px;
        }
        input[type="text"], input[type="file"], select {
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        input[type="submit"], .delete-btn {
            background-color: #4caf50;
            color: white;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        input[type="submit"]:hover, .delete-btn:hover {
            background-color: #45a049;
        }
        input[type="text"] {
            flex-grow: 2;
        }
        input[type="submit"] {
            white-space: nowrap;
        }
        #directory {
            display: none;
        }
        .upload-btn {
            display: inline-block;
            background-color: #4caf50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
        }
        #file-upload-status {
            margin-left: 10px;
            font-size: 14px;
        }
        .copy-btn {
            display: none;
            cursor: pointer;
            margin-left: 10px;
            color: #4caf50;
        }
        .chat-message:hover .copy-btn {
            display: inline;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-radius: 50%;
            border-top: 4px solid #3498db;
            width: 20px;
            height: 20px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .options-container {
            margin-bottom: 20px;
        }
        .options-container select {
            margin-bottom: 10px;
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="chat-container">
            <h1>LLM-RAG</h1>
            <div id="chatBox" class="chat-box">
            </div>
            <form id="chatForm" class="chat-form" enctype="multipart/form-data">
                <input type="text" name="user_input" id="user_input" placeholder="Type your message..." required>
                <input type="submit" value="Send">
            </form>
        </div>
        <div class="file-container">
            <div class="options-container">
                <select name="mode" id="mode" onchange="updateModelOptions()">
                    <option value="online">Online</option>
                    <option value="offline">Offline</option>
                </select>
                <select name="model" id="model">
                    <option value="gpt-3.5">GPT-3.5</option>
                    <option value="llama" style="display:none;">LLaMA-2-13B</option>
                </select>
                <select name="rag" id="rag">
                    <option value="off">RAG Off</option>
                    <option value="on">RAG On</option>
                </select>
            </div>
            <div id="initialDatasetPanel">
                <h2>Available Datasets</h2>
                <select id="initial-dataset-selector">
                </select>
                <button id="load-dataset-btn" class="load-btn">Load</button>
            </div>

            <div id="loadedDatasetPanel">
                <h2>Loaded Datasets</h2>
                <div id="loaded-dataset-selector">
                </div>
            </div>
            <h2>File Manager</h2>
            <div class="file-list" id="fileList">
                <!-- Files will be listed here -->
            </div>
            <form class="file-form">
                <label for="file-upload" class="upload-btn">Upload Files</label>
                <input type="file" name="file-upload" id="file-upload" multiple style="display: none;">
            </form>
        </div>
    </div>

    <script>
        $(document).ready(function(){
            function loadInitialDatasets() {
                $.getJSON('../log/vsdb_log.json', function (data) {
                    let datasets = new Set(data.map(item => item.dataset));
                    datasets.forEach(dataset => {
                        $('#initial-dataset-selector').append('<option value="' + dataset + '">' + dataset + '</option>');
                    });
                });
            }
            loadInitialDatasets();

            $('#load-dataset-btn').click(function() {
                var selectedDataset = $('#initial-dataset-selector').val();
                addDatasetToLoaded(selectedDataset);
            });

            $(document).on('click', '.delete-dataset-btn', function() {
                $(this).parent('.loaded-dataset-item').remove();
            });

            $(document).on('click', '.loaded-dataset-item', function() {
                var selectedDataset = $(this).text();
                updateFileList(selectedDataset);
            });
            function updateModelOptions() {
                var modeSelect = document.getElementById("mode");
                var modelSelect = document.getElementById("model");

                if (modeSelect.value === "online") {
                    modelSelect.options[0].style.display = "block"; // GPT-3.5
                    modelSelect.options[1].style.display = "none"; // LLAMA
                    modelSelect.value = "gpt-3.5";
                } else {
                    modelSelect.options[0].style.display = "none"; // GPT-3.5
                    modelSelect.options[1].style.display = "block"; // LLAMA
                    modelSelect.value = "llama";
                }
            }

            updateModelOptions();
            $('#mode').change(updateModelOptions);

            $('#chatForm').on('submit', function(e){
                e.preventDefault();
                var formData = new FormData(this);
                var useOriginalText = $('#useOriginalText').is(':checked');
                formData.append('useOriginalText', useOriginalText); // ??useOriginalText??
                var userMessage = $('#user_input').val();
                appendMessage(userMessage, 'user-message', false);
                var loaderMessage = appendMessage('', 'bot-message', true);

                $.ajax({
                    url: '/chat',
                    type: 'post',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data){
                        loaderMessage.remove();
                        appendMessage(data.response, 'bot-message', false);
                    },
                    error: function(){
                        loaderMessage.find('.message-text').html('Error loading response.');
                    }
                });
            });

            function appendMessage(message, className, isLoader = false) {
                var messageContent = isLoader ? '<div class="loader"></div>' : message;

                var messageElement = $('<div>').addClass('chat-message ' + className);

                if (isLoader) {
                    messageElement.html('<span class="message-text">' + messageContent + '</span>');
                } else {
                    messageElement.html('<span class="message-text">' + messageContent + '</span><span class="copy-btn">Copy</span>');
                }

                $('#chatBox').append(messageElement);
                $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);

                return messageElement;
            }

            function updateFileList(selectedDataset) {
                $.getJSON('../log/vsdb_log.json', function(data) {
                    var filteredFiles = data.filter(function(file) {
                        return file.dataset === selectedDataset;
                    });

                    $('#fileList').empty();
                    filteredFiles.forEach(function(file) {
                        var fileItem = $('<div>').addClass('file-item');
                        fileItem.html('<input type="checkbox"><span class="file-name">' + file.file_name + '</span><button class="delete-btn">Delete</button>');
                        $('#fileList').append(fileItem);
                    });
                });
            }
            function addDatasetToLoaded(dataset) {
                if (dataset && $('#loaded-dataset-selector').find('#dataset-' + dataset).length == 0) {
                    var datasetDiv = $('<div>').attr('id', 'dataset-' + dataset).addClass('loaded-dataset-item');
                    datasetDiv.html('<span class="dataset-name">' + dataset + '</span><button class="delete-dataset-btn">x</button>');
                    $('#loaded-dataset-selector').append(datasetDiv);
                }
            }

            $(document).on('click', '.loaded-dataset-item', function() {
                var selectedDataset = $(this).find('.dataset-name').text();
                updateFileList(selectedDataset);
            });



            // Function to load files from JSON
            function loadFilesFromJson() {
                $.getJSON('../log/vsdb_log.json', function(data) {
                    var uniqueFiles = new Set();
                    data.forEach(function(file) {
                        uniqueFiles.add(file.file_name);
                    });

                    uniqueFiles.forEach(function(fileName) {
                        var fileItem = $('<div>').addClass('file-item');
                        fileItem.html('<input type="checkbox"><span class="file-name">' + fileName + '</span><button class="delete-btn">Delete</button>');
                        $('#fileList').append(fileItem);
                    });
                });
            }

            // loadFilesFromJson();

            function updateFileList(selectedDataset) {
                $.getJSON('../log/vsdb_log.json', function(data) {
                    var filteredFiles = data.filter(function(file) {
                        return file.dataset === selectedDataset;
                    });

                    $('#fileList').empty();

                    filteredFiles.forEach(function(file) {
                        var fileItem = $('<div>').addClass('file-item');
                        fileItem.html('<input type="checkbox"><span class="file-name">' + file.file_name + '</span><button class="delete-btn">Delete</button>');
                        $('#fileList').append(fileItem);
                    });
                });
            }

            updateFileList($('#dataset-selector').val());

            $('#chatBox').on('click', '.copy-btn', function() {
                var message = $(this).siblings('.message-text').text();
                navigator.clipboard.writeText(message).then(() => {
                    alert('Message copied!');
                });
            });

            $('#file-upload').on('change', function() {
                if (this.files.length > 0) {
                    var formData = new FormData();
                    for (var i = 0; i < this.files.length; i++) {
                        formData.append('file', this.files[i]);

                        // Update the UI to display the selected file
                        var fileItem = $('<div>').addClass('file-item');
                        fileItem.html('<span class="file-name">' + this.files[i].name + '</span><button class="delete-btn">Delete</button>');
                        $('#fileList').append(fileItem);
                    }

                    // Ajax call to upload the file
                    $.ajax({
                        url: '/upload',
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            console.log('File uploaded successfully');
                        },
                        error: function(xhr, status, error) {
                            console.log('Error occurred during file upload: ' + error);
                        }
                    });
                }
            });

            $('#fileList').on('click', '.delete-btn', function() {
                var fileName = $(this).siblings('.file-name').text();
                $.ajax({
                    url: '/delete-file',
                    type: 'POST',
                    data: { file_name: fileName },
                    success: function(response) {
                        console.log(response.message);
                    },
                    error: function(xhr, status, error) {
                        console.log("Error: " + error);
                    }
                });
                $(this).parent('.file-item').remove();
            });

            $('#fileList').on('change', 'input[type="checkbox"]', function() {
            var selectedFiles = [];
            $('#fileList .file-item input[type="checkbox"]:checked').each(function() {
                selectedFiles.push($(this).siblings('.file-name').text());
            });

            $.ajax({
                url: '/selected-files',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ selectedFiles: selectedFiles }),
                success: function(response) {
                    console.log('Selected files sent successfully');
                },
                error: function(xhr, status, error) {
                    console.log('Error: ' + error);
                }
            });
        });

            $(document).on('click', '.loaded-dataset-item', function() {
                var selectedDataset = $(this).find('.dataset-name').text();
                updateFileList(selectedDataset);

                $.ajax({
                    url: '/selected-dataset',
                    type: 'POST',
                    contentType: 'application/json',
                    // data: { selected_dataset: selectedDataset },
                    data: JSON.stringify({ selectedDataset: selectedDataset }),
                    success: function(response) {
                        console.log('Selected dataset updated successfully:', response.message);
                    },
                    error: function(xhr, status, error) {
                        console.log("Error occurred while updating selected dataset:", error);
                    }
                });
            });

        });
    </script>
</body>
</html>