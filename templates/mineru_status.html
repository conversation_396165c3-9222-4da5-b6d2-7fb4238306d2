<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MinerU集成状态</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .config-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .btn.danger {
            background: #dc3545;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.online {
            background: #28a745;
        }
        .status-indicator.offline {
            background: #dc3545;
        }
        .status-indicator.warning {
            background: #ffc107;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .loading {
            text-align: center;
            padding: 40px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 MinerU集成状态</h1>
            <p>实时PDF布局分析和内容提取系统</p>
        </div>

        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>正在加载MinerU状态...</p>
        </div>

        <div id="content" style="display: none;">
            <!-- 主要状态 -->
            <div class="status-card" id="main-status">
                <h3><span class="status-indicator" id="status-indicator"></span>MinerU集成状态</h3>
                <div id="status-details"></div>
            </div>

            <!-- 详细信息网格 -->
            <div class="status-grid">
                <div class="status-card">
                    <h4>📦 安装状态</h4>
                    <div id="installation-status"></div>
                </div>
                
                <div class="status-card">
                    <h4>⚙️ 配置状态</h4>
                    <div id="config-status"></div>
                </div>
                
                <div class="status-card">
                    <h4>📊 支持格式</h4>
                    <div id="supported-formats"></div>
                </div>
                
                <div class="status-card">
                    <h4>💾 文件限制</h4>
                    <div id="file-limits"></div>
                </div>
            </div>

            <!-- 配置管理 -->
            <div class="config-section">
                <h3>🔧 配置管理</h3>
                <div>
                    <button class="btn" onclick="refreshStatus()">🔄 刷新状态</button>
                    <button class="btn success" onclick="showConfig()">📋 查看配置</button>
                    <button class="btn warning" onclick="testMinerU()">🧪 测试MinerU</button>
                </div>
                
                <div id="config-details" style="display: none; margin-top: 20px;">
                    <h4>当前配置:</h4>
                    <div class="code-block" id="config-content"></div>
                </div>
            </div>

            <!-- 安装指南 -->
            <div class="config-section" id="install-guide" style="display: none;">
                <h3>📖 安装指南</h3>
                <p>如果MinerU未安装或配置有问题，请按照以下步骤操作：</p>
                
                <h4>1. 安装MinerU</h4>
                <div class="code-block">
python install_mineru.py
                </div>
                
                <h4>2. 手动安装（如果自动安装失败）</h4>
                <div class="code-block">
pip install magic-pdf[full]
                </div>
                
                <h4>3. 验证安装</h4>
                <div class="code-block">
python test_mineru_integration.py
                </div>
                
                <h4>4. 重启应用</h4>
                <p>安装完成后，请重启Flask应用以使MinerU集成生效。</p>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时获取状态
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
        });

        async function refreshStatus() {
            try {
                const response = await fetch('/mineru-status');
                const status = await response.json();
                
                updateStatusDisplay(status);
                
                // 隐藏加载动画，显示内容
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';
                
            } catch (error) {
                console.error('获取状态失败:', error);
                showError('无法获取MinerU状态: ' + error.message);
            }
        }

        function updateStatusDisplay(status) {
            const mainStatus = document.getElementById('main-status');
            const statusIndicator = document.getElementById('status-indicator');
            const statusDetails = document.getElementById('status-details');
            
            // 更新主要状态
            if (status.available && status.enabled) {
                mainStatus.className = 'status-card success';
                statusIndicator.className = 'status-indicator online';
                statusDetails.innerHTML = '<strong>✅ MinerU已启用并正常工作</strong>';
            } else if (status.available && !status.enabled) {
                mainStatus.className = 'status-card warning';
                statusIndicator.className = 'status-indicator warning';
                statusDetails.innerHTML = '<strong>⚠️ MinerU已安装但未启用</strong>';
            } else {
                mainStatus.className = 'status-card error';
                statusIndicator.className = 'status-indicator offline';
                statusDetails.innerHTML = '<strong>❌ MinerU不可用</strong>';
                document.getElementById('install-guide').style.display = 'block';
            }
            
            if (status.error_message) {
                statusDetails.innerHTML += '<br><span style="color: #dc3545;">错误: ' + status.error_message + '</span>';
            }
            
            // 更新详细信息
            document.getElementById('installation-status').innerHTML = `
                <p><strong>可用性:</strong> ${status.available ? '✅ 已安装' : '❌ 未安装'}</p>
                <p><strong>版本:</strong> ${status.version}</p>
                <p><strong>配置加载:</strong> ${status.config_loaded ? '✅ 已加载' : '❌ 未加载'}</p>
            `;
            
            document.getElementById('config-status').innerHTML = `
                <p><strong>启用状态:</strong> ${status.enabled ? '✅ 已启用' : '❌ 已禁用'}</p>
                <p><strong>配置文件:</strong> ${status.config_loaded ? '✅ 正常' : '❌ 缺失'}</p>
            `;
            
            document.getElementById('supported-formats').innerHTML = `
                <p><strong>支持格式:</strong></p>
                <ul>${status.supported_formats.map(format => `<li>${format}</li>`).join('')}</ul>
            `;
            
            document.getElementById('file-limits').innerHTML = `
                <p><strong>最大文件大小:</strong> ${status.max_file_size}</p>
            `;
        }

        async function showConfig() {
            try {
                const response = await fetch('/mineru-config');
                const result = await response.json();
                
                if (result.status === 'success') {
                    document.getElementById('config-content').textContent = JSON.stringify(result.config, null, 2);
                    document.getElementById('config-details').style.display = 'block';
                } else {
                    alert('获取配置失败: ' + result.message);
                }
            } catch (error) {
                alert('获取配置失败: ' + error.message);
            }
        }

        async function testMinerU() {
            alert('测试功能开发中...\n\n请使用以下命令进行测试:\npython test_mineru_integration.py');
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').innerHTML = `
                <div class="status-card error">
                    <h3>❌ 错误</h3>
                    <p>${message}</p>
                    <button class="btn" onclick="location.reload()">🔄 重新加载</button>
                </div>
            `;
            document.getElementById('content').style.display = 'block';
        }
    </script>
</body>
</html>
