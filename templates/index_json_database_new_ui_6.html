<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG-CHATBOT</title>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }
        .container {
            display: flex;
            justify-content: space-between;
            width: 90%;
            max-width: 1200px;
        }
        .chat-container, .file-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        }
        .chat-container {
            width: 70%;
        }
        .file-container {
            width: 30%;
            margin-left: 5%;
        }
        .file-list {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            background: #fafafa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .chat-box {
            height: 850px;
            overflow-y: auto;
            border: 1px solid #ddd;
            background: #fafafa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .chat-message, .file-item {
            padding: 8px;
            margin: 5px 0;
            border-radius: 8px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
        }
        .user-message {
            text-align: right;
        }
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .file-name {
            flex-grow: 1;
            margin-left: 10px;
        }
        .file-form {
            display: flex;
            align-items: center;
        }
        .chat-form, .file-form {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .chat-form > *, .file-form > * {
            flex: 1;
            min-width: 120px;
        }
        input[type="text"], input[type="file"], select {
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            height: 40px; /* ???? */
        }
        input[type="submit"], .delete-btn {
            background-color: #4caf50;
            color: white;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        input[type="submit"]:hover, .delete-btn:hover {
            background-color: #45a049;
        }
        input[type="text"] {
            flex-grow: 2;
        }
        input[type="submit"] {
            white-space: nowrap;
        }
        #directory {
            display: none;
        }
        .upload-btn {
            display: inline-block;
            background-color: #4caf50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
            min-width: 120px;
            height: 20px;
            line-height: 20px;
        }
        #file-upload-status {
            margin-left: 10px;
            font-size: 14px;
        }
        .copy-btn {
            display: none;
            cursor: pointer;
            margin-left: 10px;
            color: #4caf50;
        }
        .chat-message:hover .copy-btn {
            display: inline;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-radius: 50%;
            border-top: 4px solid #4caf50;
            width: 20px;
            height: 20px;
            animation: spin 2s linear infinite;
            display: inline-block;
            vertical-align: middle;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .options-container {
            margin-bottom: 20px;
        }
        .options-container select {
            margin-bottom: 10px;
            width: 20%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .loaded-dataset-item {
            background-color: #f0f0f0;
            padding: 8px;
            margin: 5px 0;
            border-radius: 8px;
            border: 1px solid #ddd;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .delete-dataset-btn {
            color: white;
            background-color: #f44336;
            border: none;
            padding: 5px 8px;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
        }

        .dataset-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            margin-top: 20px;
        }
        .dataset-container h2 {
            margin-top: 0;
        }
        .dataset-select-container {
            display: flex;
            width: 100%;
            margin-bottom: 10px;
        }
        #initial-dataset-selector {
            flex-grow: 1;
        }
        .load-button-container {
            width: 100%;
            text-align: center;
            margin-top: 10px;
        }

       .load-btn {
           display: block;
            width: 100%;
            background-color: #4caf50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
        }
       .instruct-select-container {
            width: 100%;
            margin-top: 10px;
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .instruct-select-container select {
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .chat-form {
            display: flex;
            flex-direction: column;
            align-items: stretch;
        }

        .input-container, .submit-container {
            width: 100%;
            margin-bottom: 10px;
            height: 40px;
        }

        input[type="text"] {
            width: 100%;
            box-sizing: border-box;
        }

        input[type="submit"] {
            width: auto;
            align-self: center;
        }

        .new-dataset-container input[type="text"] {
            margin-bottom: 10px;
        }

        .new-dataset-container {
            margin-bottom: 10px;
        }
        .small-title {
            font-size: 14px;
            margin-bottom: 10px;
        }

        .dataset-name {
            font-size: 12px;
        }

        .file-name {
            font-size: 12px;
        }
        #upload-status-message {
            margin-top: 10px;
            font-size: 12px;
            display: block;
            height: 20px;
            text-align: left;
            display: flex;
            align-items: center;
            margin-right: 10px;
        }
        #upload-status-container {
            display: flex;
            align-items: center;
            min-width: 120px;
            height: 40px;
        }
        /*.loader {*/
        /*    display: none;*/
        /*}*/
        #delete-dataset-btn {
            display: inline-block;
            background-color: #f44336;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
            min-width: 120px;
            height: 40px;
            line-height: 20px;
        }
        .checkbox-container {
            display: flex;
            align-items: center;
        }
        #toggle-options-btn {
            background-color: grey;
        }

    </style>
</head>
<body>
    <div class="container">
        <div class="chat-container">
<!--            <div class="status">mode: test</div> -->


            <button id="toggle-options-btn" class="load-btn">Settings</button>

            <div class="options-container"  style="display: none;">
                <select name="mode" id="mode" onchange="updateModelOptions()">
                    <option value="online">Online</option>
                    <option value="offline" selected>Offline</option>
                </select>
                <select name="model" id="model">
                    <option value="gpt-3.5">GPT-3.5</option>
                    <option value="llama" style="display:none;">LLaMA-2-13B</option>
                    <option value="mixtral" style="display:none;" selected>Mixtral-8x7B-Instruct</option>
                    <option value="instructmixtral-1" style="display:none;">instructmixtral-1</option>
                </select>
                <select name="rag" id="rag">
                    <option value="off">RAG Off</option>
                    <option value="on" selected>RAG On</option>
                </select>
                <select id="instruct-selector"></select>
<!--                <div class="instruct-select-container">-->
<!--                    <select id="instruct-selector">-->
<!--                </select>-->
<!--                </div>-->
            </div>



<!--            <h1>LLM-RAG</h1>-->
            <div id="chatBox" class="chat-box">
            </div>
            <form id="chatForm" class="chat-form" enctype="multipart/form-data">
                <div class="input-container">
                    <input type="text" name="user_input" id="user_input" placeholder="Type your message..." required>
                </div>
                <div class="submit-container">
                    <input type="submit" value="Send">
                </div>
                <input type="hidden" name="mode" id="form_mode">
                <input type="hidden" name="model" id="form_model">
                <input type="hidden" name="rag" id="form_rag">
            </form>
        </div>

        <div class="file-container">


            <div id="initialDatasetPanel">
<!--                <h2>Available Datasets</h2>-->
                <div class="new-dataset-container">
                    <input type="text" id="new-dataset-name" placeholder="Enter new dataset name">
                    <button id="create-dataset-btn" class="load-btn">Create Dataset</button>
                </div>

<!--                <div class="dataset-select-container">
                    <select id="initial-dataset-selector">
                    </select>
                </div>

                <div class="load-button-container">
                    <button id="load-dataset-btn" class="load-btn">Load Datasets</button>
                </div>
            </div>
            <h2 class="small-title">Loaded <span id="loaded-dataset-count">0</span> Datasets </h2>
-->

            <div class="dataset-container">
<!--                <h2>Loaded Datasets</h2>-->
                <div id="loaded-dataset-selector">
                    <select id="initial-dataset-selector" ></select>
                    <!-- Loaded datasets will be listed here -->
                </div>

            </div>
<!--            <h2>File Manager</h2>-->
            <h2 class="small-title">
                Filelist: <span id="file-count">0</span> files
            </h2>
<!--            <h2 class="small-title">-->
<!--                <input type="checkbox" id="select-all-files"> Select All-->
<!--            </h2>-->
            <div class="file-list" id="fileList">
                <!-- Files will be listed here -->
            </div>

            <div class="file-form">
                    <!-- Status Message -->
<!--                    <p id="upload-status-message"></p>-->
<!--                    <div id="upload-loader" style="display: none;">-->
<!--                        <div class="loader"></div>-->
<!--                    </div>-->
                    <div id="upload-status-container">
                        <p id="upload-status-message" style="display: none;"></p>
                        <div id="upload-loader" style="display: none;">
                            <div class="loader"></div>
                        </div>
                    </div>
                    <!-- Progress Bar -->
<!--                    <div id="upload-progress-container" style="display:none;">-->
<!--                        <div id="upload-progress-bar" style="width: 0%; height: 20px; background-color: #4caf50;"></div>-->
<!--                    </div>-->
                </div>
            <div>
                <label for="file-upload" class="upload-btn">Upload Files</label>
                <input type="file" name="file-upload" id="file-upload" multiple style="display: none;">
                <button id="delete-dataset-btn" class="upload-btn" style="background-color: #f44336; display: none;">Delete Dataset</button>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function(){
            function loadInitialDatasets() {
                $.getJSON('../vectorstore/vsdb_log.json', function (data) {
                    let datasets = new Set(data.map(item => item.dataset));

                    $('#initial-dataset-selector').empty().append('<option value="">No dataset selected</option>');

                    datasets.forEach(dataset => {
                        $('#initial-dataset-selector').append('<option value="' + dataset + '">' + dataset + '</option>');
                    });
                });
            }
            $('#model').val('mixtral');
            loadInitialDatasets();

            loadInstructOptions();

            updateDatasetCount();

            updateFileCount();

            $('#delete-dataset-btn').show();

            $('#load-dataset-btn').click(function() {
                var selectedDataset = $('#initial-dataset-selector').val();
                addDatasetToLoaded(selectedDataset);
            });

            $(document).on('click', '.delete-dataset-btn', function() {
                $(this).closest('.loaded-dataset-item').remove();
            });

            $(document).on('click', '.loaded-dataset-item', function() {
                var selectedDataset = $(this).text();
                updateFileList(selectedDataset);
            });
            function updateModelOptions() {
                var modeSelect = document.getElementById("mode");
                var modelSelect = document.getElementById("model");

                if (modeSelect.value === "online") {
                    modelSelect.options[0].style.display = "block"; // GPT-3.5
                    modelSelect.options[1].style.display = "none"; // LLAMA
                    modelSelect.options[2].style.display = "none"; // Mixtral-8x7B-Instruct
                    modelSelect.options[3].style.display = "none";  // instructmixtral-1
                    modelSelect.value = "gpt-3.5";
                } else {
                    modelSelect.options[0].style.display = "none"; // GPT-3.5
                    modelSelect.options[1].style.display = "block"; // LLAMA
                    modelSelect.options[2].style.display = "block";  // Mixtral-8x7B-Instruct
                    modelSelect.options[3].style.display = "block";  // instructmixtral-1
                    modelSelect.value = "mixtral";
                }
            }

            updateModelOptions();
            $('#mode').change(updateModelOptions);

            $('#chatForm').on('submit', function(e){
                e.preventDefault();
                var formData = new FormData(this);
                var useOriginalText = $('#useOriginalText').is(':checked');
                formData.append('useOriginalText', useOriginalText); // ??useOriginalText??
                var userMessage = $('#user_input').val();
                appendMessage(userMessage, 'user-message', false);
                var loaderMessage = appendMessage('', 'bot-message', true);

                $.ajax({
                    url: '/chat',
                    type: 'post',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data){
                        loaderMessage.remove();
                        appendMessage(data.response, 'bot-message', false);
                    },
                    error: function(){
                        loaderMessage.find('.message-text').html('Error loading response.');
                    }
                });
            });

            function appendMessage(message, className, isLoader = false) {
                var messageContent = isLoader ? '<div class="loader"></div>' : message;

                messageContent = messageContent.replace(/\n/g, '<br>');

                var messageElement = $('<div>').addClass('chat-message ' + className);

                if (isLoader) {
                    messageElement.html('<span class="message-text">' + messageContent + '</span>');
                } else {
                    messageElement.html('<span class="message-text">' + messageContent + '</span><span class="copy-btn">Copy</span>');
                }

                $('#chatBox').append(messageElement);
                $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);

                return messageElement;
            }

            function updateFileList(selectedDataset) {
                return new Promise((resolve, reject) => {
                    if (!selectedDataset) {
                        $('#fileList').empty();
                        updateFileCount();
                        resolve();
                        return;
                    }
                    $.getJSON('../vectorstore/vsdb_log.json', function(data) {
                        var filteredFiles = data.filter(function(file) {
                            return file.dataset === selectedDataset;
                        });

                        $('#fileList').empty();

                        filteredFiles.forEach(function(file) {
                            var fileItem = $('<div>').addClass('file-item');
                            // fileItem.html('<input type="checkbox"><span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                            fileItem.html('<span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                            $('#fileList').append(fileItem);
                        });
                        updateFileCount();
                        resolve();
                    });
                });
            }
            function addDatasetToLoaded(dataset) {
                if (dataset && $('#loaded-dataset-selector').find('#dataset-' + dataset).length == 0) {
                    var datasetDiv = $('<div>').attr('id', 'dataset-' + dataset).addClass('loaded-dataset-item');
                    datasetDiv.html('<span class="dataset-name">' + dataset + '</span><button class="delete-dataset-btn">x</button>');
                    $('#loaded-dataset-selector').append(datasetDiv);
                }
            }

            $(document).on('click', '.loaded-dataset-item', function() {
                var selectedDataset = $(this).find('.dataset-name').text();
                updateFileList(selectedDataset);
            });



            // Function to load files from JSON
            function loadFilesFromJson() {
                $.getJSON('../vectorstore/vsdb_log.json', function(data) {
                    var uniqueFiles = new Set();
                    data.forEach(function(file) {
                        uniqueFiles.add(file.filename);
                    });

                    uniqueFiles.forEach(function(fileName) {
                        var fileItem = $('<div>').addClass('file-item');
                        // fileItem.html('<input type="checkbox"><span class="file-name">' + fileName + '</span><button class="delete-btn">Delete</button>');
                        fileItem.html('<span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                        $('#fileList').append(fileItem);
                    });
                });
            }

            // loadFilesFromJson();

            function updateFileList(selectedDataset) {
                   if (!selectedDataset) {
                        $('#fileList').empty();
                        updateFileCount();
                        return;
                    }
                $.getJSON('../vectorstore/vsdb_log.json', function(data) {
                    var filteredFiles = data.filter(function(file) {
                        return file.dataset === selectedDataset;
                    });

                    $('#fileList').empty();

                    filteredFiles.forEach(function(file) {
                        var fileItem = $('<div>').addClass('file-item');
                        // fileItem.html('<input type="checkbox"><span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                        fileItem.html('<span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                        $('#fileList').append(fileItem);
                    });
                    updateFileCount();
                });
            }

            updateFileList($('#dataset-selector').val());

            $('#chatBox').on('click', '.copy-btn', function() {
                var message = $(this).siblings('.message-text').text();
                navigator.clipboard.writeText(message).then(() => {
                    alert('Message copied!');
                });
            });

            $('#file-upload').on('change', function() {
                if (this.files.length > 0) {
                    var formData = new FormData();
                    for (var i = 0; i < this.files.length; i++) {
                        formData.append('file', this.files[i]);
                    }

                    var selectedDataset = $('#initial-dataset-selector').val();
                    formData.append('selectedDataset', selectedDataset);

                    // $('#delete-dataset-btn').hide();
                    $('#upload-progress-bar').css('width', '0%');
                    $('#upload-loader').show();
                    $('#upload-status-message').show().text('Uploading file...');

                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', '/upload', true);

                    xhr.upload.onprogress = function(e) {
                        if (e.lengthComputable) {
                            var percentComplete = (e.loaded / e.total) * 100;
                            $('#upload-progress-bar').css('width', percentComplete + '%');

                            if (percentComplete === 100) {
                                $('#upload-status-message').text('Uploading file...');
                                $('#upload-loader .loader').show();
                                $('#upload-progress-bar').css('background-color', '#FFD700');
                            }
                        }
                    };

                    xhr.onload = function() {
                        if (xhr.status == 200) {
                            $('#upload-status-message').text('File uploaded successfully');
                            $('#upload-loader .loader').hide();

                            updateFileList(selectedDataset).then(() => {
                                setTimeout(function() {
                                    $('#delete-dataset-btn').show();
                                }, 3000);
                            });

                            addDatasetToLoadedIfNotExists(selectedDataset);
                        } else {
                            $('#upload-status-message').text('Error occurred during file upload');
                            $('#upload-loader .loader').hide();
                        }
                    };

                    xhr.onerror = function() {
                        $('#upload-status-message').text('Error occurred during file upload');
                        $('#upload-progress-container').hide();
                    };
                    updateFileCount();

                    xhr.send(formData);
                }
            });


            $('#fileList').on('click', '.delete-btn', function() {
                var fileName = $(this).siblings('.file-name').text();
                $.ajax({
                    url: '/delete-file',
                    type: 'POST',
                    data: { file_name: fileName },
                    success: function(response) {
                        console.log(response.message);
                    },
                    error: function(xhr, status, error) {
                        console.log("Error: " + error);
                    }
                });
                $(this).parent('.file-item').remove();
            });

            $('#fileList').on('change', 'input[type="checkbox"]', function() {
            var selectedFiles = [];
            $('#fileList .file-item input[type="checkbox"]:checked').each(function() {
                selectedFiles.push($(this).siblings('.file-name').text());
            });

            $.ajax({
                url: '/selected-files',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ selectedFiles: selectedFiles }),
                success: function(response) {
                    console.log('Selected files sent successfully');
                },
                error: function(xhr, status, error) {
                    console.log('Error: ' + error);
                }
            });
        });

            $(document).on('click', '.loaded-dataset-item', function() {
                var selectedDataset = $(this).find('.dataset-name').text();
                updateFileList(selectedDataset);
                updateFileCount();
                $('#fileList').empty();

                $.ajax({
                    url: '/selected-dataset',
                    type: 'POST',
                    contentType: 'application/json',
                    // data: { selected_dataset: selectedDataset },
                    data: JSON.stringify({ selectedDataset: selectedDataset }),
                    success: function(response) {
                        console.log('Selected dataset updated successfully:', response.message);
                    },
                    error: function(xhr, status, error) {
                        console.log("Error occurred while updating selected dataset:", error);
                    }
                });
            });
            function updateFormValues() {
                $('#form_mode').val($('#mode').val());
                $('#form_model').val($('#model').val());
                $('#form_rag').val($('#rag').val());
            }

            $('#mode, #model, #rag').change(updateFormValues);

            updateFormValues();

            $('#instruct-selector').change(function() {
                var selectedOption = $(this).val();

                $.ajax({
                    url: '/select-instruct',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ selectedInstruct: selectedOption }),
                    success: function(response) {
                        console.log('Response from server:', response);
                    },
                    error: function(xhr, status, error) {
                        console.log('An error occurred:', error);
                    }
                });
            });

            function loadInstructOptions() {
                $.ajax({
                    url: '/get-prompt-templates',
                    type: 'GET',
                    success: function(data) {
                        var promptTemplates = data.prompt_template_list;
                        var instructSelector = $('#instruct-selector');
                        instructSelector.empty(); // ???????

                        instructSelector.append($('<option>', {
                            value: 'none',
                            text: 'No instruction selected'
                        }));

                        promptTemplates.forEach(function(template) {
                            instructSelector.append($('<option>', {
                                value: template,
                                text: template
                            }));
                        });
                    },
                    error: function() {
                        console.log('Error loading instruct options');
                    }
                });
            }

            $('#create-dataset-btn').click(function() {
                var newDatasetName = $('#new-dataset-name').val().trim();
                if(newDatasetName) {
                    // Add the new dataset to the selector
                    $('#initial-dataset-selector').append('<option value="' + newDatasetName + '">' + newDatasetName + '</option>');
                    // Select the new dataset
                    $('#initial-dataset-selector').val(newDatasetName);
                } else {
                    alert('Please enter a dataset name.');
                }
            });

            function updateDatasetCount() {
                var count = $('#loaded-dataset-selector').children().length;
                $('#loaded-dataset-count').text(count);
            }

            function updateFileCount() {
                var count = $('#fileList').children().length;
                $('#file-count').text(count);
            }

            $('#load-dataset-btn').click(function() {
                var selectedDataset = $('#initial-dataset-selector').val();
                addDatasetToLoaded(selectedDataset);
                updateDatasetCount();
            });

            $(document).on('click', '.delete-dataset-btn', function() {
                $(this).closest('.loaded-dataset-item').remove();
                $('#fileList').empty();
                updateDatasetCount();
                updateFileCount();
            });

            $('#fileList').on('click', '.delete-btn', function() {
                $(this).parent('.file-item').remove();
                updateFileCount();
            });
            function addDatasetToLoadedIfNotExists(dataset) {
                if (dataset && $('#loaded-dataset-selector').find('#dataset-' + dataset).length == 0) {
                    var datasetDiv = $('<div>').attr('id', 'dataset-' + dataset).addClass('loaded-dataset-item');
                    datasetDiv.html('<span class="dataset-name">' + dataset + '</span><button class="delete-dataset-btn">x</button>');
                    $('#loaded-dataset-selector').append(datasetDiv);
                    updateDatasetCount();
                }
            }

            $('#select-all-files').change(function() {
                var isChecked = $(this).is(':checked');
                $('#fileList .file-item input[type="checkbox"]').prop('checked', isChecked);

                if (isChecked) {
                    var selectedFiles = [];
                    $('#fileList .file-item input[type="checkbox"]:checked').each(function() {
                        selectedFiles.push($(this).siblings('.file-name').text());
                    });

                    $.ajax({
                        url: '/selected-files',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ selectedFiles: selectedFiles }),
                        success: function(response) {
                            console.log('Selected files sent successfully', response);
                        },
                        error: function(xhr, status, error) {
                            console.error('Error: ' + error);
                        }
                    });
                }
            });
            $('#toggle-options-btn').click(function() {
            $('.options-container').slideToggle(); // ?? slideToggle ??????
    });
        });


    </script>
</body>
</html>