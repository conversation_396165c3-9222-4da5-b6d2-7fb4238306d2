<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG-CHATBOT</title>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://kit.fontawesome.com/8e9c71f3b7.js" crossorigin="anonymous"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://alcdn.msauth.net/browser/2.19.0/js/msal-browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <style>

        .thinking-part {
            background-color: #333;
            color: #ccc;
            font-size: 14px;
            padding: 10px;
            border-radius: 20px;
            margin-bottom: 10px;
        }

        .headline-container {
            display: flex;
            justify-content: space-between;

        }

        .filename {
            margin-left: 15px;
            margin-top: 15px;
            display: flex;
        }

        .page-button {
            background: #004F59;
            border: none;
            margin-left: 10px;
            color: white;
            height: 30px;
            border-radius: 20px;
            font-weight: bold;
            margin-right: 15px;
        }

        .pdf-button-group {
            display: flex;
            align-items: center;
            justify-content: end;
        }

        #my-pdf {
            height: calc(100% - 110px)
        }

        .cancel-button {

            display: flex;
            justify-content: end;
            align-items: center;
            margin-right: 10px;
            border: none;
            background: transparent;

            &:hover,
            &:focus {
                color: white;
                background: transparent;
                border: none;
            }

            &:active {
                top: .08em;
                border: none;
            }
        }

        .toggle-chat-history-btn {
            position: fixed;

            align-content: center;
            margin-top: 35px;
            transform: translateY(-50%);

            background-color: transparent;

            border: none;

        }

        .toggle-chat-history-btn:hover {
            color: grey;

        }

        .toggle-chat-history-btn .fas {
            transition: transform 0.3s;

        }

        .custom-icon-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 120px;

            animation: icon-animation 5s ease-in-out infinite;
        }

        .icon-btn {
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 20px;
        }

        .send-icon {
            border: none;
            width: 36px;
            height: 36px;
            cursor: pointer;
        }

        .database-icon {
            left: 10px;
        }

        .chat-form {
            display: flex;
            flex-direction: column;
            align-items: stretch;
        }

        .chat-history-entry {
            width: 100%;
            height: 100%;
            border-radius: 22px;
            margin-left: 5px;
            margin-right: 5px;
            margin-bottom: 15px;
            padding: 10px;

            background: #DDEFE8;
            font-family: 'Inter', sans-serif;
            overflow: hidden;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            gap: 9px;
            display: inline-flex;
            cursor: pointer;

            transition: background-color 0.3s ease;

        }

        .loader {
            border: 4px solid #f3f3f3;

            border-top: 4px solid #3498db;

            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 2s linear infinite;
        }

        .docu-loader {
            border: 4px solid #f3f3f3;

            border-top: 4px solid #3498db;

            border-radius: 50%;
            width: 35px;
            height: 35px;
            animation: spin 2s linear infinite;
            padding-top: 3px
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        @keyframes icon-animation {
            0% {
                transform: translateY(0);
            }

            50% {
                transform: translateY(20px);
            }

            100% {
                transform: translateY(0);
            }
        }

        @keyframes circle-group-rotate {
            0% {
                transform: translate(-50%, -50%) rotate(0deg);
            }

            100% {
                transform: translate(-50%, -50%) rotate(360deg);
            }
        }

        @keyframes circle-pulse {

            0%,
            100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.2);
            }
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 22px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            -webkit-transition: .4s;
            transition: .4s;
            border-radius: 22px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked+.slider {
            background-color: #2196F3;
        }

        input:focus+.slider {
            box-shadow: 0 0 1px #2196F3;
        }

        input:checked+.slider:before {
            -webkit-transform: translateX(18px);
            -ms-transform: translateX(18px);
            transform: translateX(18px);
        }

        .slider.round {
            border-radius: 34px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        .sideM {
            position: fixed;
            width: 100px;
            height: 100%;
            background-color: rgba(48, 48, 48, 0);
            padding-top: 10px;
            padding-left: 10px;
            padding-right: 5px;
            padding-bottom: 10px;
        }

        .main-win {
            position: fixed;
            width: calc(100% - 100px);
            height: 100%;
            margin-left: 100px;

            padding-left: 5px;

            background-color: #ffffff;

        }

        .exp-sideM {
            position: fixed !important;
            width: 300px;
            height: 100%;
            background-color: #4a4a4a;
            z-index: 1;
            overflow: auto;
            padding-top: 10px;
            padding-bottom: 10px;
            padding-left: 5px;
            padding-right: 10px;
        }

        .top-menu {
            height: 50px;
            padding-top: 10px;

            background-color: rgba(255, 255, 255, 0);
            margin-top: 8px;
        }

        .chat-window {
            height: calc(100% - 50px - 185px);
            margin-top: 10px;

            background-color: rgba(248, 248, 248, 0);

            scroll-behavior: auto;
            font-family: 'Inter', sans-serif;
        }

        .chat-box {
            height: 100%;
            overflow: auto;
            border: 1px solid #ddd;
            padding-right: 25px;

            border-radius: 8px;
            margin-bottom: 10px;

            border: none;
            align-items: center;
        }

        .input-area {

            padding-right: 25px;
            padding-bottom: 10px;

            height: 185px;
            align-content: center;

        }

        .input-container {
            width: 100%;
            position: relative;
            background: white;
            border-radius: 28px;
            border: 2px #0097A9 solid;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            left: 0;
            bottom: 120px;

            z-index: 1;
        }

        .dropdown-content button {
            color: #0097A9;
            padding: 8px 12px;
            text-decoration: none;
            display: flex;
            align-items: start;
            align-content: center;

            border: #e0f7f9;
            border-radius: 20px;
            margin: 20px 0;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            width: 300px;
            height: 40px;

        }

        .dropdown-content button:hover {
            background-color: #ccecf2;
        }

        .dropdown:hover .dropdown-content {
            display: block;
        }

        textarea {
            resize: none;
            width: 100%;
            height: 50px;

            max-height: 100px;

            color: #0097A9;
            margin-left: 20px;
            font-size: 22px;
            font-weight: 400;
            overflow-wrap: break-word;
            flex-grow: 1;
            align-self: center;
            border: none;
            outline: none;
        }

        .icon-btn {
            background: none;
            border: none;
            cursor: pointer;
        }

        #user_input {
            border-radius: 5px;
            border: 0px;
            background: #ffffff;
            outline: none;
        }

        .com-logo {
            height: 50px;
            background-color: rgba(114, 211, 112, 0);
            margin-top: 10px;
            margin-left: 12px;
        }

        .history-window {
            height: calc(100% - 50px - 100px);
            margin-top: 10px;
            padding: 10px;
            background-color: rgba(248, 248, 248, 0);

            scroll-behavior: auto;
            overflow: auto;
            display: flex;
            align-content: center;
            justify-content: center;

        }

        .func-menu {
            height: 100px;
            margin-top: 10px;
            margin-bottom: 10px;
            background-color: rgba(174, 226, 177, 0);
        }

        .frame {
            display: flex;
            flex-wrap: wrap;
            width: 550px;

            align-items: center;
            align-self: center;
            justify-content: center;
            gap: 22px 160px;
            position: relative;
        }

        .frame .deloitte {
            position: relative;
            width: 250px;
            height: 54.3px;
        }

        .frame .ID-input {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 13px 15px;
            position: relative;
            flex: 0 0 auto;
            border-radius: 36px;
            overflow: hidden;
            border: 1.5px solid;
            border-color: var(--backup);
        }

        .frame .text-wrapper {
            position: relative;
            width: 515px;
            height: 21px;
            margin-top: -1.5px;
            font-family: "Inter-Bold", Helvetica;
            font-weight: 700;
            color: var(--backup);
            font-size: 17px;
            letter-spacing: 0;
            line-height: normal;
        }

        .frame .overlap-group-wrapper {
            position: relative;
            width: 550px;
            height: 48px;
        }

        .frame .overlap-group {
            position: relative;
            height: 48px;
            border-radius: 36px;
        }

        .frame .div {
            position: absolute;
            width: 311px;
            top: 13px;
            left: 15px;
            font-family: "Inter-Bold", Helvetica;
            font-weight: 700;
            color: var(--backup);
            font-size: 17px;
            letter-spacing: 0;
            line-height: normal;
        }

        .frame .rectangle {
            position: absolute;
            width: 550px;
            height: 48px;
            top: 0;
            left: 0;
            border-radius: 36px;
            border: 1.5px solid;
            border-color: var(--backup);
        }

        .frame .login-enter-button {
            position: absolute;
            width: 36px;
            height: 36px;
            top: 7px;
            left: 505px;
        }

        .frame .div-2 {
            display: inline-flex;
            align-items: center;
            gap: 11px;
            position: relative;
            flex: 0 0 auto;
        }

        .frame .line-stroke {
            position: relative;
            width: 255px;
            height: 2px;
        }

        .frame .text-wrapper-2 {
            position: relative;
            width: fit-content;
            margin-top: -1px;
            font-family: "Inter-Bold", Helvetica;
            font-weight: 700;
            color: var(--main-dark);
            font-size: 15px;
            text-align: right;
            letter-spacing: 0;
            line-height: normal;
            white-space: nowrap;
        }

        .frame .SSO-button {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            position: relative;
            flex: 0 0 auto;
        }

        .frame .ellipse {
            position: relative;
            width: 60px;
            height: 60px;
        }

        .frame .text-wrapper-3 {
            position: relative;
            width: fit-content;
            font-family: "Inter-Bold", Helvetica;
            font-weight: 700;
            color: var(--backup);
            font-size: 12px;
            letter-spacing: 0;
            line-height: normal;
        }

        .frame .key-svgrepo-com {
            position: absolute;
            width: 38px;
            height: 38px;
            top: 11px;
            left: 11px;
        }

        .frame .registration-button {
            all: unset;
            box-sizing: border-box;
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            position: relative;
            flex: 0 0 auto;
        }

        .frame .ellipse-2 {
            position: relative;
            width: 60px;
            height: 60px;
            border-radius: 30px;
            border: 1.8px solid;
            border-color: var(--main-light);
        }

        .frame .vector {
            position: absolute;
            width: 34px;
            height: 35px;
            top: 14px;
            left: 16px;

            .file-button {
                display: inline-flex;
                height: 35px;
                align-items: center;
                gap: 7px;
                padding: 4px 6px;
                position: relative;
                background-color: var(--main-light);
                border-radius: 18px;
                overflow: hidden;
                border: 2px solid;
                border-color: var(--main-dark);
            }

            .file-button .document-svgrepo-com {
                position: relative;
                width: 26px;
                height: 26px;
            }

            .file-button .text-wrapper {
                position: relative;
                width: fit-content;
                margin-top: -2px;
                font-family: "Inter-Bold", Helvetica;
                font-weight: 700;
                color: var(--main-dark);
                font-size: 22px;
                text-align: center;
                letter-spacing: 0;
                line-height: normal;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }

            .file-button .demo-folder {
                position: relative;
                width: 179px;
                margin-top: -2px;
                font-family: "Inter-Bold", Helvetica;
                font-weight: 700;
                color: var(--main-dark);
                font-size: 22px;
                letter-spacing: 0;
                line-height: normal;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }

        }

        #pdf-container {

            height: 100%;
            float: right;
            border-left: solid;
            background: #ffffff;
            transform: translateX(100%);
            -webkit-transform: translateX(100%);
        }

        .pdf-slide-in {
            animation: pdf-slide-in 0.5s forwards;
            -webkit-animation: pdf-slide-in 0.5s forwards;
            width: 40%;
            min-width: 250px;
        }

        .pdf-slide-out {
            animation: pdf-slide-out 0.5s forwards;
            -webkit-animation: pdf-slide-out 0.5s forwards;
        }

        @keyframes pdf-slide-in {
            100% {
                transform: translateX(0%);
            }
        }

        @-webkit-keyframes pdf-slide-in {
            100% {
                -webkit-transform: translateX(0%);
            }
        }

        @keyframes pdf-slide-out {
            0% {
                transform: translateX(0%);
                width: 40%;
                min-width: 250px;
            }

            100% {
                width: 0%;
                min-width: 0px;
                transform: translateX(100%);
            }
        }

        @-webkit-keyframes pdf-slide-out {
            0% {
                -webkit-transform: translateX(0%);
            }

            100% {
                -webkit-transform: translateX(100%);
            }
        }

        #chatBox h1, #chatBox h2, #chatBox h3, #chatBox h4, #chatBox h5, #chatBox h6 {
    color: white;
    font-weight: 700;
    margin-top: 10px;
    margin-bottom: 10px;
}

#chatBox h1 {
    font-size: 24px;
}

#chatBox h2 {
    font-size: 22px;
}

#chatBox h3 {
    font-size: 20px;
}

#chatBox p {
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 700;
}

#chatBox ul, #chatBox ol {
    margin-left: 20px;
    margin-bottom: 10px;
    color: white;
    font-size: 18px;
    font-weight: 700;
}

#chatBox li {
    margin-bottom: 5px;
}

#chatBox strong {
    font-weight: 900; 
}
    </style>

</head>

<body>

    <div id="offcanvasChatHistory" class="offcanvas offcanvas-start" tabindex="-1"
        aria-labelledby="offcanvasChatHistoryLabel">

        <section class="com-logo">

            <img src="{{ url_for('static', filename='images/Deloitte.svg') }}"
                style="width: 200px; display: block; margin-top: 10px; margin-left: 10px;">

        </section>

        <section class="history-window">

            <div class="chat-history-container">
                <div id="chatHistoryBox"></div>
            </div>

        </section>

        <section class="func-menu">

            <div class="icon-container"
                style="display: flex; align-self:center; justify-content: center;  bottom: 0; width: 100%; padding-top: 20px; border: none;">
                <button type="button" class="icon-btn database-icon" data-bs-toggle="modal"
                    data-bs-target="#databaseModal">
                    <i style="margin-left : 40px; margin-right: 40px; color:#0097A9"
                        class="fa-regular fa-file-lines fa-xl"></i>
                </button>
                <button type="button" class="icon-btn settings-icon" data-bs-toggle="modal"
                    data-bs-target="#settingsModal" style="border: none;">
                    <i style="margin-left : 40px; margin-right: 40px; color:#0097A9"
                        class="fa-solid fa-gears fa-xl"></i>
                </button>

            </div>

        </section>
    </div>

    <div class="sideM">
        <button class="toggle-chat-history-btn" type="button" style="border: none;" data-bs-toggle="offcanvas"
            data-bs-target="#offcanvasChatHistory" aria-controls="offcanvasChatHistory">
            <i class="fa-solid fa-bars fa-2xl" style="color: #0097A9; margin-left: 10px"></i>
        </button>
    </div>

    <main class="main-win">
        <aside id="pdf-reference" style="display:inline;">

            <div id="pdf-container">

            </div>

        </aside>
        <section class="top-menu">
            <button id="addChatHistoryButton" type="button"
                style="width: 170px;height: 40px; padding-left: 18px; padding-right: 18px; margin-top: 7px; background: #0097A9; color: white; border: none; border-radius: 28px; justify-content: center; align-items: center; gap: 13px; display: inline-flex;">
                New Chat
            </button>
        </section>

        <section class="chat-window">
            <div id="chatBox" class="chat-box">
            </div>
        </section>

        <section class="input-area">
            <form id="chatForm" class="chat-form" enctype="multipart/form-data">
                <div class="input-container">
                    <div class="dropdown" onmouseover="showDropdown()" onmouseout="hideDropdown()">
                        <button class="icon-btn" type="button" style="margin-left: 10px"><i
                                class="fa-solid fa-list fa-xl" style="color: #0097a9;"></i></i></button>
                        <div class="dropdown-content" id="drpCon" style="display: none ">
                            <button type="button" style="width: 250px; background-color: #DDEFE8; border: #0097A9;"><i
                                    style=" margin-top: 12px; margin-right: 22px; color:#0097A9"
                                    class="fa-regular fa-file-lines fa-xl"></i> Active: Demo_folder_1...</button>
                            <button type="button" style="width: 250px; background-color: #DDEFE8; border: #0097A9;"><i
                                    style=" margin-top: 12px; margin-right: 10px; color:#0097A9"
                                    class="fa-solid fa-gears fa-xl"></i> Model: Mixtral 8x7b</button>
                        </div>
                    </div>
                    <textarea type="text" name="user_input" id="user_input" placeholder="Type your message..."
                        required></textarea>
                    <div
                        style="margin-top:0px; margin-right: 15px; flex-direction: column; justify-content: center; align-content: center">
                        <button style="height: 50px; align-self: center" type="submit" class="icon-btn send-icon">
                            <i class="fa-solid fa-circle-up fa-2xl" style="color: #0097a9;"></i>
                        </button>
                    </div>
                </div>
            </form>
        </section>

    </main>

    <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true"
        style=" justify-content: center; align-content: center">
        <div class="modal-dialog " style="justify-content: center; align-content: center">
            <div class="modal-content" style="border-radius: 1.5rem ; ">
                <div class="modal-header" style="border:none; padding: 0.5rem 1.2rem">

                    <b>
                        <h2 style="color: #004F59; font-weight: bold; margin-left:5px">Settings</h2>
                    </b>

                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="align-self: center;width: 91%;border-radius: 15px;background: #DDEFE8;">
                    <select name="mode" id="mode" onchange="updateModelOptions()"
                        style="visibility: hidden;color:#DDEFE8; border-radius: 10px">
                        <option value="online">Online</option>
                        <option value="offline" selected>Offline</option>
                    </select>
                    <p><b><label style="color:#0097A9; width: 200px">Model:</label></b>
                        <select name="model" id="model"
                            style="justify-content: flex-end;color:#0097A9; margin-left: 20px ; border-color:#DDEFE8; border-radius: 10px">
                            <option value="gpt-3.5">GPT-3.5</option>

                            <option value="mixtral" style="display:none;" selected>Mistral-7B</option>

                        </select>
                    </P>
                    <p><b><label style="color:#0097A9; width: 200px">Rag:</label></b>
                        <select name="rag" id="rag"
                            style="margin-left: 20px; color:#0097A9 ; border-color:#DDEFE8; border-radius: 10px">
                            <option value="off">Off</option>
                            <option value="on" selected>On</option>
                        </select>
                    </P>

                    <div>
                        <p><b><label style="color:#0097A9;  width: 200px" for="max_history_no">Max History
                                    Items:</label></b>
                            <select name="max_history_no" id="max_history_no"
                                style=" margin-left: 20px; color:#0097A9; border-color:#DDEFE8; border-radius: 10px">
                                <option value="3">3</option>
                                <option value="5">5</option>
                                <option value="7">7</option>
                                <option value="9">9</option>
                            </select>
                        </p>
                    </div>
                    <div class="checkbox-container" style="visibility: hidden">
                        <input type="checkbox" id="include_history" name="include_history" checked>
                        <label for="include_history">Include Chat History</label>
                    </div>
                </div>
                <div class="modal-footer" style="border:none">

                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="databaseModal" tabindex="-1" aria-labelledby="databaseModalLabel" aria-hidden="true"
        style=" justify-content: center; align-content: center">
        <div class="modal-dialog" style="max-width: 1400px ; justify-content: center; align-content: center">
            <div class="modal-content" style="align-items: center; flex-direction: column; border-radius: 1.5rem;">
                <div class="modal-header dataset-modal-header"
                    style="width: 70%; display: flex; flex-direction: column; align-items: flex-start; border: none; margin-right: auto; margin-left: 7px">

                    <b>
                        <h2 style="color: #004F59; font-weight: bold; margin-left:10px">Document Management</h2>
                    </b>

                    <div class="controls"
                        style="width: 100%; display: flex; flex-wrap: wrap; align-items: start; justify-content: flex-start ; vertical-align: middle;">
                        <button type="button" class="btn" id="backToDatabaseList" style="padding: 0 0 0 0;">
                            <i class="fa-solid fa-circle-arrow-left fa-2xl"
                                style="color: #0097a9; width: 36px; margin-left: 7px; margin-top: 18px"></i>
                        </button>

                        <input type="text" class="form-control me-2"
                            style="width: 40%; background: #DDEFE8; border-radius: 2.25rem; margin-left: 4px"
                            placeholder="Search">

                        <button class="btn" id="createFolderButton"
                            style="background: #004F59; color: white; border-radius: 2.25rem;">
                            <span class="fas fa-folder-plus"></span>
                            Create New Dataset
                        </button>

                        <button class="btn" id="deleteButton"
                            style="background: grey; margin-left: 5px; color: lightgrey; border-radius: 1.3rem">
                            <span class="fas fa-trash"></span>
                        </button>

                        <button id="uploadLoading"
                            style="visibility: hidden;display: flex ;align-content: center; margin-left: 5px; border: none; background-color: rgba(21,119,234,0);">
                            <div class="docu-loader"></div>
                            <div
                                style=" font-family: 'Inter' ,sans-serif; font-size: 16px; color: #004F59 ; margin-left: 10px; padding-top:6px">
                                File Uploading</div>
                        </button>
                    </div>
                </div>

                <div class="modal-body dataset-modal-body"
                    style="position: relative; height: 800px; width: 96%; overflow-y: auto; background: #DDEFE8; border:none; border-radius: 1rem; margin-bottom: 5px">
                    <a href="#"
                        class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                        style="background: #DDEFE8; border: 0px;border-bottom: 2px solid rgba(0, 0, 0, .125);">
                        <span class="flex-grow-1 dataset-name"
                            style="cursor:pointer; color: #0097A9;font-family: 'Inter', sans-serif; font-weight: 600">Name</span>
                        <span class="active-text"
                            style="margin-right: 5px; color: #0097A9; font-family: 'Inter', sans-serif; font-weight: 600">Active</span>
                    </a>

                    <div id="databaseList" class="list-group"></div>
                    <div class="list-group" style="display: none;" id="fileList"></div>
                    <input type="file" id="file-upload" style="display: none;" multiple>
                    <button id="uploadFileButton" class="btn btn-primary rounded-circle"
                        style="position: absolute; right: 10px; bottom: 10px; background-color: #004F59; color: white; width: 70px; height: 70px">
                        <span class="fas fa-plus"></span>
                    </button>
                </div>

                <div class="modal-footer" style="border:none">
                    <b>
                        <div
                            style=" font-family: 'Inter' ,sans-serif; color: #004F59; justify-content: start;align-content: start;margin-left: 10px">
                            Note: only files in the same folders can be activated.</div>
                    </b>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="createFolderModal" tabindex="-1" aria-labelledby="createFolderModalLabel"
        aria-hidden="true" style=" justify-content: center; align-content: center">
        <div class="modal-dialog">
            <div class="modal-content" style="border-radius: 1.5rem ; background-color: white ; border: none">

                <div class="modal-header" style="border: none;">
                    <b>
                        <h2 style="color: #004F59; font-weight: bold; margin-left:5px; border: none;">Create New Folder
                        </h2>
                    </b>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                        style="border: none;"></button>
                </div>

                <div class="modal-body" style="border: none;">
                    <input type="text" class="form-control" id="newFolderName" placeholder="Folder Name">
                </div>

                <div class="modal-footer" style="border: none;">
                    <button type="button" class="btn btn-primary" id="confirmCreateFolder"
                        style="background: #004F59 ">Confirm</button>
                </div>

            </div>
        </div>
    </div>

    <div class="modal fade" id="warningWin" tabindex="-1" aria-labelledby="createFolderModalLabel" aria-hidden="true"
        style="justify-content: center; align-content: center">
        <div class="modal-dialog"
            style="position: absolute; top: 25%; left: 50%; transform: translate(-50%, -50%);width: 400px; height: 180px; padding-top: 13px;border:none; padding-bottom: 13px; background: white; border-radius: 30px; overflow: hidden; flex-direction: column; justify-content: center; align-items: center; gap: 17px; display: inline-flex">
            <div class="modal-content" style="align-content: center ;border:none;">

                <div class="modal-header" style="justify-content: center; align-content: center ;border:none;">
                    <div style="color: #0097A9; font-size: 30px;  font-weight: bold; word-wrap: break-word">Warning
                    </div>
                </div>

                <div class="modal-body" style="justify-content: center; align-content: center; border:none;">
                    <div
                        style="color: #004F59; font-size: 16px;  font-weight: 500; word-wrap: break-word; justify-content: center; align-content: center;">
                        No document is currently selected. RAG is off now.</div>
                </div>

                <div class="modal-footer" style="align-content: center;border:none;">
                </div>

            </div>
        </div>
    </div>
    <div style="visibility: hidden; display: none; z-index: -1; width: 1px; height: 1px;">
        <i class="fa-solid fa-robot fa-xl" style="color: #0097a9;" alt="Image Description"></i>
        <i class="fa-regular fa-user fa-xl" style="color: #0097a9;" alt="Image Description"></i>
    </div>

    <script src="https://unpkg.com/pdfobject"></script>
    <script>
        let dropdownTimeout;

        function showDropdown() {
            clearTimeout(dropdownTimeout);
            document.querySelector('.dropdown-content').style.display = 'block';
        }

        function hideDropdown() {
            dropdownTimeout = setTimeout(() => {
                document.querySelector('.dropdown-content').style.display = 'none';
            }, 800);
        }

        function showPDFChunkReference(pages_data, file_name) {
            pages_data = JSON.parse(pages_data.replaceAll("~>~<~", '"'));
            pdfContainer = document.getElementById('pdf-container');
            var isOpen = pdfContainer.classList.contains('pdf-slide-in');
            console.log("show pdf");
            let pdf_path;
            $.ajax({
                url: '/draw_file',
                type: 'post',
                data: JSON.stringify({ pages_data: pages_data, filename: file_name }),
                processData: false,
                contentType: 'application/json',
                success: function (response) {
                    console.log("Response received:", response.response);
                    pdf_path = response.processed_file_path
                    console.log("pdfpath:", pdf_path)

                    PDFObject.embed(pdf_path, "#my-pdf", {
                        pdfOpenParams: { page: 2 },
                        callback: function(pdfObj) {
                            if (pdfObj) {
                                console.log("PDF embedded successfully");

                            } else {
                                console.log("PDF embedding failed");
                            }
                        }
                    });

                    setTimeout(function() {
                        var iframe = document.querySelector("#my-pdf iframe");
                        if (iframe) {
                            iframe.src = pdf_path + "#page=2";
                        }
                    }, 1000);  

                    document.getElementById('my-pdf').addEventListener('load', function() {
                        var iframe = this.querySelector('iframe');
                        if (iframe) {
                            iframe.contentWindow.PDFViewerApplication.page = 2;
                        }
                    });
                },
                error: function (error) {
                    console.error("Error:", error);
                }
            });
            pdfContainer.innerHTML = `
                <div class="headline-container">
                    <div class="filename">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right:15px">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4C7.34315 4 6 5.34315 6 7V17C6 18.6569 7.34315 20 9 20H15C16.6569 20 18 18.6569 18 17V10H15C13.3431 10 12 8.65685 12 7V4H9ZM14 5.41421L16.5858 8H15C14.4477 8 14 7.55228 14 7V5.41421ZM4 7C4 4.23858 6.23858 2 9 2H13C13.2652 2 13.5196 2.10536 13.7071 2.29289L19.7071 8.29289C19.8946 8.48043 20 8.73478 20 9V17C20 19.7614 17.7614 22 15 22H9C6.23858 22 4 19.7614 4 17V7ZM15.2071 12.2929C15.5976 12.6834 15.5976 13.3166 15.2071 13.7071L12.2071 16.7071C11.8166 17.0976 11.1834 17.0976 10.7929 16.7071L9.29289 15.2071C8.90237 14.8166 8.90237 14.1834 9.29289 13.7929C9.68342 13.4024 10.3166 13.4024 10.7071 13.7929L11.5 14.5858L13.7929 12.2929C14.1834 11.9024 14.8166 11.9024 15.2071 12.2929Z" fill="#F90505"/>
                        </svg>
                        <p>${file_name}</p>
                    </div>

                    <div onclick="closePDFChunkReference()" class="cancel-button">
                        <svg width="25" height="25" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 25C0 11.1929 11.1929 0 25 0V0C38.8071 0 50 11.1929 50 25V25C50 38.8071 38.8071 50 25 50V50C11.1929 50 0 38.8071 0 25V25Z" fill="#FF0000"/>
                        <path d="M12.1669 34.2064L21.0198 25.3536L24.6464 28.9802L15.7936 37.8331C14.7921 38.8346 13.1684 38.8346 12.1669 37.8331C11.1654 36.8316 11.1654 35.2079 12.1669 34.2064ZM25 28.6267L21.3733 25L25 21.3733L28.6267 25L25 28.6267ZM21.0198 24.6465L12.1669 15.7936C11.1654 14.7921 11.1654 13.1684 12.1669 12.1669C13.1684 11.1654 14.7921 11.1654 15.7936 12.1669L24.6464 21.0198L21.0198 24.6465ZM28.9802 24.6465L25.3536 21.0198L34.2064 12.1669C35.2079 11.1654 36.8316 11.1654 37.8331 12.1669C38.8346 13.1684 38.8346 14.7921 37.8331 15.7936L28.9802 24.6465ZM25.3536 28.9802L28.9802 25.3536L37.8331 34.2064C38.8346 35.2079 38.8346 36.8316 37.8331 37.8331C36.8316 38.8346 35.2079 38.8346 34.2064 37.8331L25.3536 28.9802Z" fill="white" stroke="#FF0000" stroke-width="0.5"/>
                        </svg>
                    </div>
                </div>

                <div id="my-pdf"></div> `;

            //     <svg width="40" height="40" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">

            //     <svg width="40" height="40" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">

            //         <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">

            pdfContainer.setAttribute('class', 'pdf-slide-in');
        }

        function closePDFChunkReference() {

            document.getElementById('pdf-container').setAttribute('class', 'pdf-slide-out');
        }

        const msalConfig = {
            auth: {
                clientId: "802fe47f-0185-4956-8278-91f1c9e2a60b",
                authority: "https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732",
                redirectUri: "http://localhost:8008/chat"
            },
            cache: {
                cacheLocation: "localStorage",
                storeAuthStateInCookie: true
            }
        };

        const msalInstance = new msal.PublicClientApplication(msalConfig);

        //         fetch('http://localhost:8000/api/auth/url', {

        //                         fetch('http://localhost:8000/api/auth/token', {

        //                 const response = await fetch('http://localhost:8340/api/admin/user/create', {

        //             fetch(`http://localhost:8000/api/validate_token?token=${token.access_token}`)

        function signOut() {
            fetch('/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(response => {
                if (response.ok) {
                    localStorage.removeItem('user');
                    return msalInstance.logoutPopup();
                } else {
                    return Promise.reject('Failed to logout from server');
                }
            }).then(() => {
                window.location.href = '/login_page';
            }).catch(error => {
                console.error(error);
            });
        }

        function loadAllUsersChatHistory() {
            $.ajax({
                url: '/get-all-users-data',
                type: 'GET',
                success: function (data) {
                    const chatHistoryBox = $('#chatHistoryBox');
                    chatHistoryBox.empty();
                    Object.keys(data).forEach(userId => {
                        const firstQuestion = data[userId]['q_msg'];
                        const firstQuestionTime = data[userId]['q_time']
                        console.log(data)
                        const entryHtml = `
                    <div class="chat-history-entry" data-userId="${userId}" style="        font-family: 'Inter' ,sans-serif;">
                        <div style="width: 100%; height: 100%;  background: #DDEFE8; border-radius: 22px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: center; gap: 9px; display: inline-flex">

                            <div style=" height:30%; padding-left: 10px;padding-bottom:3px;  align-items: center; ">
                                <div style="width:250px; justify-content: flex-start; align-items: flex-start; gap: 10px; display: inline-flex;">
                                    <div style="color: #6FC2B4; font-size: 15px; font-weight: 700; word-wrap: break-word">${firstQuestionTime}</div>
                                </div>
                                <button style="background:#DDEFE8; border: none; margin-top: 5px">
                                    <img src="{{ url_for('static', filename='images/Cross button Frame.svg') }}"  alt="Image Description" style="vertical-align: middle; padding-bottom: 5px">
                                </button>
                            </div>

                            <div style="width: 290px; height: 0px; border: 1px #6FC2B4 solid"></div>

                            <div style="width: 284px; height: 40%; padding-top:10px; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
                                <div style="color: #004F59; font-size: 15px; font-weight: 700; word-wrap: break-word">Title:&nbsp;</div>
                                <div style="width: 246px; max-height: 50px ; color: #004F59; font-size: 15px;font-weight: 700; word-wrap: break-word; overflow-y: hidden">${firstQuestion}</div>
                            </div>
                        </div>
                   </div>
                `;
                        chatHistoryBox.prepend(entryHtml);
                    });
                },
                error: function (xhr, status, error) {
                    console.error("Failed to load all users' chat history:", error);
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function () {
            loadAllUsersChatHistory()
            setTimeout(function () {
                var chatHistoryBox = document.getElementById("chatHistoryBox");
                if (!chatHistoryBox) {
                    console.log("Chat history box not found.");
                    return;
                }

                var entries = chatHistoryBox.getElementsByClassName("chat-history-entry");
                console.log("Entries found: " + entries.length);

                Array.from(entries).forEach(function (entry) {
                    var titleDiv = entry.querySelector("div[style*='width: 246px;']");
                    if (!titleDiv) {
                        console.log("Title div not found in an entry.");
                        return;
                    }
                    console.log(titleDiv)
                    var titleText = titleDiv.textContent;
                    console.log("Checking entry: " + titleText);

                    if (titleText === "No question available") {
                        var deleteButton = entry.querySelector("button[style*='background:#DDEFE8;']");
                        if (deleteButton) {
                            console.log("Deleting entry: " + titleText);
                            deleteButton.click();
                        } else {
                            console.log("Delete button not found.");
                        }
                    }
                });

                var addChatHistoryButton = document.getElementById("addChatHistoryButton");
                if (addChatHistoryButton) {
                    console.log("Adding new chat history entry.");
                    addChatHistoryButton.click();
                } else {
                    console.log("Add chat history button not found.");
                }
            }, 1000);
        });

        $(document).ready(function () {

            const textArea = document.getElementById('user_input');
            const inputContainer = document.querySelector('.input-container');
            const outerContainer = document.querySelector('.input-container > div > div');
            const submitButton = document.querySelector('.send-icon');

            var currentSelectedDataset;
            var selectedDatasets = []; 
            var selectedFiles = [];

            updateButtonVisibility()
            adjustTextAreaHeight();
            newLoadInitialDatasets();

            updateDeleteButtonStatus();
            loadInstructOptions();

            updateFormValues();

            var selectedFilesMap = {};

            document.getElementById('backToDatabaseList').style.display = 'none';

            function loadUserChatHistory(userId) {
                $.ajax({
                    url: '/get-user-data/' + userId,
                    type: 'GET',
                    success: function (data) {
                        const chatBox = $('#chatBox');
                        chatBox.empty();
                        Object.entries(data).forEach(([key, entry]) => {
                            const uniqueId = `buttons-container-${key}`;
                            const userMessageHtml = `
                    <div style="width: 100%; padding-top: 20px; flex-direction: column; justify-content: flex-start; align-items: flex-end; gap: 6px; display: inline-flex">
                        <div style="padding-left: 2px; padding-right: 2px; padding-top: 1px; padding-bottom: 1px; justify-content: flex-end; align-items: center; gap: 10px; display: inline-flex">
                            <div style="text-align: right; color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 20px">You
                            <i class="fa-regular fa-circle-user fa-lg" style="color: #0097a9; vertical-align: middle; position: relative; top: -3px;"></i>
                            </div>
                        </div>
                        <div style="padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px; border-radius: 30px; overflow: hidden; border: 2px #0097A9 solid; justify-content: center; align-items: center; gap: 19px; display: inline-flex">
                            <div style="color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 18px">${entry.q_msg}</div>
                        </div>
                    </div>
                `;
                            const botMessageHtml = `
                    <div>
                        <div style="width: 100%; height: 100%; padding-top: 20px;flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex">
                            <div style="transform: rotate(180deg); transform-origin: 0 0; justify-content: flex-start; align-items: flex-end; gap: 5px; display: inline-flex">
                                <div style="transform: rotate(180deg); transform-origin: 0 0; text-align: right; color: #004F59; font-size: 22px; font-weight: 700; word-wrap: break-word; font-size: 20px">
                                    <img src="{{ url_for('static', filename='images/chat.png') }}" style="width: 1.6em; height: 1.6em; vertical-align: middle; position: relative; top: -3px;">
                                    Sapitor
                                </div>
                                <div style="width: 34px; height: 34px; position: relative; transform: rotate(180deg); transform-origin: 0 0">

                                </div>
                            </div>
                            <div style="padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px; background: #004F59; border-radius: 30px; overflow: hidden; justify-content: center; align-items: center; gap: 19px; display: inline-flex">
                                <div style="display: flex; flex-direction: column;">
                                    <div style="text-align: justify; color: white; font-size: 18px; font-weight: 700; word-wrap: break-word;">
                                        ${entry.a_msg}
                                    </div>
                                    ${entry.chunks_list && Object.keys(entry.chunks_list).length > 0 ? `
                                        <div>
                                            <div><br></div>
                                            <div style="text-align: justify; color: white; font-size: 18px; font-weight: 700; word-wrap: break-word;">
                                                Citations:
                                            </div>
                                            <div id="${uniqueId}"></div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                            chatBox.append(userMessageHtml + botMessageHtml);

                            if (entry.chunks_list) {
                                let buttonsHtml = '';
                                Object.keys(entry.chunks_list).forEach(function (filename) {
                                    buttonsHtml += `
                            <button id="reference-button" style="text-align: center; color: black; background: white; font-weight: 700; word-wrap: break-word; font-size: 15px; border-radius:10px; height:40px; margin-right:10px" onclick='showPDFChunkReference("${JSON.stringify(entry.chunks_list[filename]).replaceAll('"', "~>~<~")}", "${filename}")'>
                                <div style="display:flex; align-items:center">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right:10px">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4C7.34315 4 6 5.34315 6 7V17C6 18.6569 7.34315 20 9 20H15C16.6569 20 18 18.6569 18 17V10H15C13.3431 10 12 8.65685 12 7V4H9ZM14 5.41421L16.5858 8H15C14.4477 8 14 7.55228 14 7V5.41421ZM4 7C4 4.23858 6.23858 2 9 2H13C13.2652 2 13.5196 2.10536 13.7071 2.29289L19.7071 8.29289C19.8946 8.48043 20 8.73478 20 9V17C20 19.7614 17.7614 22 15 22H9C6.23858 22 4 19.7614 4 17V7ZM15.2071 12.2929C15.5976 12.6834 15.5976 13.3166 15.2071 13.7071L12.2071 16.7071C11.8166 17.0976 11.1834 17.0976 10.7929 16.7071L9.29289 15.2071C8.90237 14.8166 8.90237 14.1834 9.29289 13.7929C9.68342 13.4024 10.3166 13.4024 10.7071 13.7929L11.5 14.5858L13.7929 12.2929C14.1834 11.9024 14.8166 11.9024 15.2071 12.2929Z" fill="#F90505"/>
                                    </svg>
                                    <div style="margin-right:10px">${filename}</div>
                                </div>
                            </button>
                        `;
                                });
                                document.getElementById(uniqueId).innerHTML = buttonsHtml;
                            }
                        });

                        $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
                    },
                    error: function (xhr, status, error) {
                        console.error("Failed to load user's chat history:", error);
                    }
                });
            }

            //                             <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right:10px">

            function parseMessage(message) {
                console.log('Original message:', message);
                const thinkStart = message.indexOf('<think>');
                const thinkEnd = message.indexOf('</think>');
                let thinking = '';
                let finalAnswer = message.trim();

                if (thinkStart !== -1 && thinkEnd !== -1) {
                    thinking = message.substring(thinkStart + 7, thinkEnd).trim();
                    finalAnswer = message.substring(thinkEnd + 8).trim();
                }

                console.log('Thinking:', thinking);
                console.log('Final Answer:', finalAnswer);
                return { thinking, finalAnswer };
            }

            function appendMessage(message, className, isLoader = false, list_of_chunks = null) {
                var processedMessage = message.replace(/^\nAnswer: /, '').replace(/^AI:\s*/, '').trim();
                const { thinking, finalAnswer } = parseMessage(processedMessage);

                var isCodeBlock = finalAnswer.startsWith('```') && finalAnswer.endsWith('```');
                var messageContent;
                let filename_list;
                let buttonsHtml = '';
                if (isLoader) {
                    messageContent = '<div class="loader"></div>';
                } else if (isCodeBlock) {
                    var codeContent = finalAnswer.substring(3, finalAnswer.length - 3);
                    messageContent = '<pre><code>' + codeContent + '</code></pre>';
                } else {
                    messageContent = finalAnswer.replace(/\n/g, '<br>');
                    messageContent = marked.parse(messageContent);
                    if (className.includes('bot-message') && (list_of_chunks != null)) {
                        console.log(list_of_chunks)
                        filename_list = Object.keys(list_of_chunks)
                        console.log("data_extracted:", filename_list)
                        filename_list.forEach(function (filename) {
                            buttonsHtml += `
                                <button id="reference-button" style="text-align: center; color: black; background: white; font-weight: 700; word-wrap: break-word; font-size: 15px; border-radius:10px; height:40px; margin-right:10px" onclick='showPDFChunkReference("${JSON.stringify(list_of_chunks[filename]).replaceAll('"', "~>~<~")}", "${filename}")'>
                                    <div style="display:flex; align-items:center">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right:10px">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4C7.34315 4 6 5.34315 6 7V17C6 18.6569 7.34315 20 9 20H15C16.6569 20 18 18.6569 18 17V10H15C13.3431 10 12 8.65685 12 7V4H9ZM14 5.41421L16.5858 8H15C14.4477 8 14 7.55228 14 7V5.41421ZM4 7C4 4.23858 6.23858 2 9 2H13C13.2652 2 13.5196 2.10536 13.7071 2.29289L19.7071 8.29289C19.8946 8.48043 20 8.73478 20 9V17C20 19.7614 17.7614 22 15 22H9C6.23858 22 4 19.7614 4 17V7ZM15.2071 12.2929C15.5976 12.6834 15.5976 13.3166 15.2071 13.7071L12.2071 16.7071C11.8166 17.0976 11.1834 17.0976 10.7929 16.7071L9.29289 15.2071C8.90237 14.8166 8.90237 14.1834 9.29289 13.7929C9.68342 13.4024 10.3166 13.4024 10.7071 13.7929L11.5 14.5858L13.7929 12.2929C14.1834 11.9024 14.8166 11.9024 15.2071 12.2929Z" fill="#F90505"/>
                                        </svg>
                                        <div style="margin-right:10px">${filename}</div>
                                    </div>
                                </button>
                                `;
                        });
                        messageContent += `
                            <div><br></div>
                            <div style="text-align: justify; color: white; font-size: 30px; font-weight: 700; word-wrap: break-word; font-size: 18px">Citations: </div>
                            <div>${buttonsHtml}</div>`;
                    }

                }

                var messageElement = $('<div>').addClass('chat-message ' + className);

                if (className.includes('bot-message')) {
                    messageElement.html(`
                        <div style="width: 100%; padding-top: 20px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex">
                            <div style="justify-content: flex-start; align-items: center; gap: 5px; display: inline-flex">
                                <div style="text-align: left; color: #004F59; font-size: 20px; font-weight: 700; word-wrap: break-word;">
                                    <img src="{{ url_for('static', filename='images/chat.png') }}" style="width: 1.6em; height: 1.6em; vertical-align: middle; position: relative; top: -3px;">
                                    Sapitor
                                </div>
                            </div>
                            <div style="padding: 15px 20px; background: #004F59; border-radius: 30px; display: flex; flex-direction: column; gap: 10px;">
                                ${thinking ? `
                                    <div class="thinking-part" style="background: #333; color: #ccc; font-size: 14px; padding: 10px; border-radius: 10px;">
                                        ${thinking.replace(/\n/g, '<br>')}
                                    </div>
                                ` : ''}
                                <div style="text-align: justify; color: white; font-size: 18px; font-weight: 700; word-wrap: break-word;">
                                    ${messageContent}
                                </div>
                            </div>
                        </div>
                    `);
                } else {
                    messageElement.html(`
                        <div style="width: 100%; padding-top: 20px; flex-direction: column; justify-content: flex-end; align-items: flex-end; gap: 6px; display: inline-flex">
                            <div style="text-align: right; color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word;">
                                You <i class="fa-regular fa-circle-user fa-lg" style="color: #0097a9; vertical-align: middle;"></i>
                            </div>
                            <div style="padding: 15px 20px; border-radius: 30px; border: 2px #0097A9 solid;">
                                <div style="color: #0097A9; font-size: 18px; font-weight: 700; word-wrap: break-word;">${messageContent}</div>
                            </div>
                        </div>
                    `);
                }

                $('#chatBox').append(messageElement);
                loadAllUsersChatHistory()
                $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
                return [messageElement, null];
            }
            function adjustTextAreaHeight() {
                textArea.style.height = "0px";

                textArea.style.height = Math.min(textArea.scrollHeight, 150) + "px";

                inputContainer.style.height = "auto";
                outerContainer.style.height = "auto";

                var newHeight = Math.min(textArea.scrollHeight + 20, 150) + "px";

                inputContainer.style.height = newHeight;
                outerContainer.style.height = newHeight;
            }

            textArea.addEventListener('input', adjustTextAreaHeight);

            textArea.addEventListener('paste', (event) => {
                const pastedText = event.clipboardData.getData('text/plain');
                event.preventDefault();
                textArea.value += pastedText;
                adjustTextAreaHeight();
            });

            textArea.addEventListener('keydown', (event) => {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    submitButton.click();
                    textArea.value = '';
                    console.log('Message sent');
                } else if (event.key === 'Enter' && event.shiftKey) {
                    event.preventDefault();
                    const cursorPosition = textArea.selectionStart;
                    textArea.value = textArea.value.substring(0, cursorPosition) + '\n' + textArea.value.substring(cursorPosition);
                    textArea.selectionStart = textArea.selectionEnd = cursorPosition + 1;
                    adjustTextAreaHeight();
                }
            });

            adjustTextAreaHeight();

            document.addEventListener('DOMContentLoaded', function () {
                submitButton.addEventListener('click', function () {
                    var textArea = document.getElementById('user_input');
                    textArea.value = null;
                    adjustTextAreaHeight();
                });
            });

            $('#chatForm').on('submit', function (e) {
                e.preventDefault();
                console.log("submit")
                var formData = new FormData(this);
                var ragStatus = $('#rag').val();
                var useOriginalText = $('#useOriginalText').is(':checked');
                formData.append('useOriginalText', useOriginalText); 
                var userMessage = $('#user_input').val();
                appendMessage(userMessage, 'user-message', false);
                var [loaderMessage, filename_list] = appendMessage('', 'bot-message', true);
                console.log("filename_list", filename_list)
                formData.append('filename_list', filename_list)

                var includeHistory = $('#include_history').is(':checked');
                var maxHistoryNo = $('#max_history_no').val();
                var model = $('#model').val();

                formData.append('include_history', includeHistory);
                formData.append('max_history_no', maxHistoryNo);
                formData.append('model', model);

                var selectedDataset = currentSelectedDataset;
                console.log("selectedDataset")
                console.log(model)
                if (ragStatus === 'on' && !selectedDataset) {

                    var warningWin = new bootstrap.Modal(document.getElementById('warningWin'), {
                        keyboard: false
                    });
                    warningWin.show();
                    formData.append('rag', 'off');

                } else {
                    formData.append('rag', ragStatus);
                }
                formData.append('selectedDataset', selectedDataset);
                textArea.value = ''
                adjustTextAreaHeight()
                $.ajax({
                    url: '/chat',
                    type: 'post',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (data) {
                        loaderMessage.remove();
                        appendMessage(data.response, 'bot-message', false, data.chunk_list);
                    },
                    error: function () {
                        loaderMessage.find('.message-text').html('Error loading response.');
                    }
                });
            });

            $('#chatBox').on('click', '.copy-btn', function () {
                var message = $(this).siblings('.message-text').text();
                navigator.clipboard.writeText(message).then(() => {
                    alert('Message copied!');
                });
            });

            $('#toggle-file-container').click(function () {
                $('.container').toggleClass('collapsed-right');
                var isCollapsed = $('.container').hasClass('collapsed-right');
                $(this).find('i').toggleClass('fa-arrow-left fa-arrow-right'); 
            });

            $('#toggle-database-icon-btn').click(function () {
                $('.container').toggleClass('collapsed-right');
            });

            $('#toggle-chat-history').click(function () {
                $('.container').toggleClass('collapsed-left');
                var isCollapsed = $('.container').hasClass('collapsed-left');
                $(this).find('i').toggleClass('fa-arrow-right fa-arrow-left'); 
            });

        function loadUserChatHistory(userId) {
            $.ajax({
                url: '/get-user-data/' + userId,
                type: 'GET',
                success: function (data) {
                    const chatBox = $('#chatBox');
                    chatBox.empty();
                    Object.entries(data).forEach(([key, entry]) => {
                        const uniqueId = `buttons-container-${key}`;
                        const userMessageHtml = `
                            <div style="width: 100%; padding-top: 20px; flex-direction: column; justify-content: flex-start; align-items: flex-end; gap: 6px; display: inline-flex">
                                <div style="padding-left: 2px; padding-right: 2px; padding-top: 1px; padding-bottom: 1px; justify-content: flex-end; align-items: center; gap: 10px; display: inline-flex">
                                    <div style="text-align: right; color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 20px">You
                                    <i class="fa-regular fa-circle-user fa-lg" style="color: #0097a9; vertical-align: middle; position: relative; top: -3px;"></i>
                                    </div>
                                </div>
                                <div style="padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px; border-radius: 30px; overflow: hidden; border: 2px #0097A9 solid; justify-content: center; align-items: center; gap: 19px; display: inline-flex">
                                    <div style="color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 18px">${entry.q_msg}</div>
                                </div>
                            </div>
                        `;

                        const { thinking, finalAnswer } = parseMessage(entry.a_msg);

                        const botMessageHtml = `
                            <div>
                                <div style="width: 100%; padding-top: 20px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex">
                                    <div style="justify-content: flex-start; align-items: center; gap: 5px; display: inline-flex">
                                        <div style="text-align: left; color: #004F59; font-size: 20px; font-weight: 700; word-wrap: break-word;">
                                            <img src="{{ url_for('static', filename='images/chat.png') }}" style="width: 1.6em; height: 1.6em; vertical-align: middle; position: relative; top: -3px;">
                                            Sapitor
                                        </div>
                                    </div>
                                    <div style="padding: 15px 20px; background: #004F59; border-radius: 30px; display: flex; flex-direction: column; gap: 10px;">
                                        ${thinking ? `
                                            <div class="thinking-part" style="background: #333; color: #ccc; font-size: 14px; padding: 10px; border-radius: 10px;">
                                                ${thinking.replace(/\n/g, '<br>')}
                                            </div>
                                        ` : ''}
                                        <div class="final-answer" style="text-align: justify; color: white; font-size: 18px; font-weight: 700; word-wrap: break-word;">
                                            ${marked.parse(finalAnswer)} 
                                        </div>
                                        ${entry.chunks_list && Object.keys(entry.chunks_list).length > 0 ? `
                                            <div>
                                                <div><br></div>
                                                <div style="text-align: justify; color: white; font-size: 18px; font-weight: 700; word-wrap: break-word;">
                                                    Citations:
                                                </div>
                                                <div id="${uniqueId}"></div>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        `;

                        chatBox.append(userMessageHtml + botMessageHtml);

                        if (entry.chunks_list && Object.keys(entry.chunks_list).length > 0) {
                            let buttonsHtml = '';
                            Object.keys(entry.chunks_list).forEach(function (filename) {
                                buttonsHtml += `
                                    <button id="reference-button" style="text-align: center; color: black; background: white; font-weight: 700; word-wrap: break-word; font-size: 15px; border-radius:10px; height:40px; margin-right:10px" onclick='showPDFChunkReference("${JSON.stringify(entry.chunks_list[filename]).replaceAll('"', "~>~<~")}", "${filename}")'>
                                        <div style="display:flex; align-items:center">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right:10px">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4C7.34315 4 6 5.34315 6 7V17C6 18.6569 7.34315 20 9 20H15C16.6569 20 18 18.6569 18 17V10H15C13.3431 10 12 8.65685 12 7V4H9ZM14 5.41421L16.5858 8H15C14.4477 8 14 7.55228 14 7V5.41421ZM4 7C4 4.23858 6.23858 2 9 2H13C13.2652 2 13.5196 2.10536 13.7071 2.29289L19.7071 8.29289C19.8946 8.48043 20 8.73478 20 9V17C20 19.7614 17.7614 22 15 22H9C6.23858 22 4 19.7614 4 17V7ZM15.2071 12.2929C15.5976 12.6834 15.5976 13.3166 15.2071 13.7071L12.2071 16.7071C11.8166 17.0976 11.1834 17.0976 10.7929 16.7071L9.29289 15.2071C8.90237 14.8166 8.90237 14.1834 9.29289 13.7929C9.68342 13.4024 10.3166 13.4024 10.7071 13.7929L11.5 14.5858L13.7929 12.2929C14.1834 11.9024 14.8166 11.9024 15.2071 12.2929Z" fill="#F90505"/>
                                            </svg>
                                            <div style="margin-right:10px">${filename}</div>
                                        </div>
                                    </button>
                                `;
                            });
                            document.getElementById(uniqueId).innerHTML = buttonsHtml;
                        }
                    });

                    $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
                },
                error: function (xhr, status, error) {
                    console.error("Failed to load user's chat history:", error);
                }
            });
        }

            $('#chatHistoryBox').on('click', '.chat-history-entry', function () {
                console.log("Clicked on: ", this);
                const userId = $(this).data('userid');
                console.log("test " + userId);
                if (userId) {
                    loadUserChatHistory(userId);
                    $('#chatHistoryBox .chat-history-entry').each(function () {
                        $(this).find('.delete-history-button').css('visibility', 'visible');
                    });
                    $(this).find('.delete-history-button').css('visibility', 'hidden');
                } else {
                    console.error("User ID is undefined.");
                }
            });

            $('#chatHistoryBox').on('click', '.chat-history-entry button', function () {
                var userId = $(this).closest('.chat-history-entry').data('userid');
                console.log("Deleting chat history for user ID: " + userId); 

                var that = this; 

                $.ajax({
                    url: '/delete-chat-history', 
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ userId: userId }),
                    success: function (response) {
                        console.log('Chat history deleted successfully for user ID: ' + userId);

                        $(that).closest('.chat-history-entry').remove();

                        loadAllUsersChatHistory();
                    },
                    error: function (xhr, status, error) {
                        console.error("Failed to delete chat history for user ID: " + userId, error);
                    }
                });
                return false; 
            });

            function newLoadInitialDatasets() {
                document.querySelector('.active-text').style.display = 'none';
                document.getElementById('backToDatabaseList').style.display = 'block';

                $.getJSON('/get-datasets', function (datasets) {
                    $('#databaseList').empty();
                    datasets.forEach(dataset => {
                        const datasetItem = $('<a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" style="background: #DDEFE8; border: 0px">').html(`
            <div class="me-2 d-flex align-items-center">
                <input class="form-check-input me-1" type="checkbox" value="" aria-label="..." style="margin-top: 0rem">
                <span class="fas fa-folder" style="color: #0097A9; margin-left: 0.5rem"></span>
            </div>
            <span class="flex-grow-1 dataset-name" style="cursor:pointer; color: #0097A9;        font-family: 'Inter' ,sans-serif; font-weight: 600">${dataset.dataset_name}</span>

        `);
                        datasetItem.appendTo('#databaseList');
                    });
                });
            }

            $('#databaseList').on('change', '.list-group-item .form-check-input', function () {
                var datasetName = $(this).closest('.list-group-item').find('.dataset-name').text().trim();
                if ($(this).is(':checked')) {

                    if (!selectedDatasets.includes(datasetName)) {
                        selectedDatasets.push(datasetName);
                    }
                } else {

                    selectedDatasets = selectedDatasets.filter(name => name !== datasetName);
                }
                console.log(selectedDatasets); 
            });

            $('#deleteButton').click(function () {
                if ($('#databaseList').is(':visible')) {

                    selectedDatasets.forEach(function (dataset) {
                        $.ajax({
                            url: '/delete-dataset',  
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({ dataset: dataset }),  
                            success: function (response) {
                                console.log(response.message);  

                                $('#databaseList .list-group-item').each(function () {
                                    if (selectedDatasets.includes($(this).find('.dataset-name').text().trim())) {
                                        $(this).remove();  
                                    }
                                });
                                selectedFiles = [];  
                            },
                            error: function (xhr, status, error) {
                                console.error("Error deleting dataset:", error);
                                selectedFiles = [];  
                            }
                        });
                    });
                } else if ($('#fileList').is(':visible')) {

                    $('#fileList .list-group-item .form-check-input:checked').each(function () {
                        var fileItem = $(this).closest('.list-group-item');  
                        var fileName = fileItem.find('.flex-grow-1').text().trim();
                        var selectedDataset = currentSelectedDataset;

                        $.ajax({
                            url: '/delete-file',  
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({ file_name: fileName, dataset: selectedDataset }),
                            success: function (response) {
                                console.log('File deleted successfully:', response.message);
                                fileItem.remove();  
                                selectedDataset = [];
                            },
                            error: function (xhr, status, error) {
                                console.error("Error deleting file:", error);
                            }
                        });
                    });
                }

            });

            function loadFilesForDataset(datasetName) {
                document.querySelector('.active-text').style.display = 'block';
                document.getElementById('backToDatabaseList').style.display = 'block';

                $('#fileList').empty();

                $('#fileList').append('<div class="d-flex justify-content-center align-items-center" style="position: fixed; top: 35%; left: 0; width: 100%; z-index: 9999;"><div class="docu-loader"></div></div>');
                $.getJSON('/get-datasets', function (datasets) {
                    const dataset = datasets.find(d => d.dataset_name === datasetName);
                    $('#fileList').empty();

                    $('#loading-icon').remove();

                    dataset.document_list.forEach(file => {

                        const isSelected = selectedFilesMap[datasetName] && selectedFilesMap[datasetName].includes(file);
                        const fileItem = $('<a href="#" class="list-group-item list-group-item-action d-flex align-items-center" style="background: #DDEFE8; border: 0px;">').html(`
                <div class="me-2 d-flex align-items-center">
                    <input class="form-check-input me-1" type="checkbox" value="" aria-label="..." style="margin-top: 0rem">
                    <span class="fas fa-file" style="color: #0097A9; margin-left: 0.5rem"></span>
                </div>
                <span class="flex-grow-1" style="color: #0097A9;         font-family: 'Inter' ,sans-serif; font-weight: 600">${file}</span>
                <label class="switch ms-auto select-switch"> 
                    <input type="checkbox" ${isSelected ? 'checked' : ''}>
                    <span class="slider round"></span>
                </label>
            `);
                        fileItem.appendTo('#fileList');

                        fileItem.find('.select-switch input[type="checkbox"]').on('click', function () {

                            Object.keys(selectedFilesMap).forEach(key => {
                                if (key !== datasetName) {
                                    selectedFilesMap[key] = [];
                                }
                            });

                            const isChecked = $(this).is(':checked');
                            const fileName = $(this).closest('.list-group-item').find('span.flex-grow-1').text();

                            if (!selectedFilesMap[datasetName]) {
                                selectedFilesMap[datasetName] = [];
                            }

                            if (isChecked) {
                                if (!selectedFilesMap[datasetName].includes(fileName)) {
                                    selectedFilesMap[datasetName].push(fileName);
                                }
                            } else {
                                selectedFilesMap[datasetName] = selectedFilesMap[datasetName].filter(f => f !== fileName);
                            }

                            $.ajax({
                                url: '/selected-files',
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({ selectedFiles: selectedFilesMap[datasetName] }),
                                success: function (response) {
                                    console.log('Selected files sent successfully');
                                },
                                error: function (xhr, status, error) {
                                    console.log('Error: ' + error);
                                }
                            });
                        });
                    });
                });
            }

            $('#fileList').on('change', '.form-check-input', function () {
                const isChecked = $(this).is(':checked');
                const fileName = $(this).closest('.list-group-item').find('.flex-grow-1').text().trim(); 

                if (isChecked) {

                    if (!selectedFiles.includes(fileName)) {
                        selectedFiles.push(fileName);
                    }
                } else {

                    selectedFiles = selectedFiles.filter(name => name !== fileName);
                }

                console.log(selectedFiles);
            });

            $('#confirmCreateFolder').on('click', function () {
                var originalInput = $('#newFolderName').val().trim();
                var sanitizedInput = originalInput.replace(/[^a-zA-Z0-9_]/g, '_').replace(/^_+/, '');

                if (sanitizedInput) {
                    if (originalInput !== sanitizedInput) {

                    }

                    const datasetItem = $('<a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" style="background: #DDEFE8; border: 0px">')
                        .html(`
                <div class="me-2 d-flex align-items-center">
                     <input class="form-check-input me-1" type="checkbox" value="" aria-label="..." style="margin-top: 0rem">
                    <span class="fas fa-folder" style="color: #0097A9; margin-left: 0.5rem"></span>
                </div>
                <span class="flex-grow-1 dataset-name" style="cursor:pointer; color: #0097A9;         font-family: 'Inter' ,sans-serif; font-weight: 600;">${sanitizedInput}</span>
            `);
                    $('#databaseList').append(datasetItem);

                    $('#newFolderName').val('');
                    $('#createFolderModal').modal('hide');
                } else {
                    alert('Please enter a dataset name.');
                }
            });

            $('#databaseList').on('click', '.dataset-name', function () {
                const dataset = $(this).text();
                currentSelectedDataset = dataset;
                loadFilesForDataset(dataset);
                $('#databaseList').hide();
                $('#fileList').show();
                updateButtonVisibility(); 
            });

            $('#backToDatabaseList').click(function () {
                document.getElementById('backToDatabaseList').style.display = 'none';

                document.querySelector('.active-text').style.display = 'none';
                $('#fileList').hide();
                $('#databaseList').show();
                updateButtonVisibility(); 
            });

            $('#createFolderButton').on('click', function () {
                var createFolderModal = new bootstrap.Modal(document.getElementById('createFolderModal'), {
                    keyboard: false
                });
                createFolderModal.show();
            });

            function updateButtonVisibility() {
                if ($('#fileList').is(':visible')) {

                    $('#createFolderButton').hide();
                    $('#uploadFileButton').show();
                } else {

                    $('#createFolderButton').show();
                    $('#uploadFileButton').hide();
                }
            }

            function addDatasetToLoaded(dataset) {
                if (dataset && $('#loaded-dataset-selector').find('#dataset-' + dataset).length == 0) {
                    var datasetDiv = $('<div>').attr('id', 'dataset-' + dataset).addClass('loaded-dataset-item');
                    datasetDiv.html('<span class="dataset-name">' + dataset + '</span><button class="delete-dataset-btn">x</button>');
                    $('#loaded-dataset-selector').append(datasetDiv);
                }
            }

            function updateFileList(selectedDataset) {
                if (!selectedDataset) {
                    $('#fileList').empty();

                    return;
                }
                $.getJSON('../vectorstore/vsdb_log.json', function (data) {
                    var filteredFiles = data.filter(function (file) {
                        return file.dataset === selectedDataset;
                    });

                    $('#fileList').empty();

                    filteredFiles.forEach(function (file) {
                        var fileItem = $('<div>').addClass('file-item');
                        fileItem.html('<input type="checkbox"><span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');

                        $('#fileList').append(fileItem);
                    });
                    updateFileCount();
                });
            }

            updateFileList($('#dataset-selector').val());

            $('#uploadFileButton').on('click', function () {
                $('#file-upload').click();
            });

            $('#file-upload').on('change', function () {
                if (this.files.length > 0) {
                    var formData = new FormData();
                    for (var i = 0; i < this.files.length; i++) {
                        formData.append('file', this.files[i]);
                    }
                    var selectedDataset = currentSelectedDataset;
                    formData.append('selectedDataset', selectedDataset);

                    var uploadLoading = document.getElementById("uploadLoading");
                    console.log("file-upload1");

                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', '/upload', true);

                    xhr.upload.onprogress = function (e) {
                        console.log("file-upload2");

                        uploadLoading.style.visibility = "visible";
                        if (e.lengthComputable) {
                            var percentComplete = (e.loaded / e.total) * 100;
                            $('#upload-progress-bar').css('width', percentComplete + '%');

                            if (percentComplete === 100) {
                                $('#upload-status-message').text('Processing file...');
                            }
                        }
                    };

                    xhr.onload = function () {
                        if (xhr.status === 200) {
                            $('#upload-status-message').text('File uploaded successfully');
                            $('#upload-loader .loader').hide();

                            uploadLoading.style.visibility = "hidden";

                            Array.from(this.files).forEach(file => {
                                const noDotfileName = file.name.replace(/\.(txt|doc|docx|pdf)$/, (match, p1) => p1);
                                const fileName = noDotfileName.replace(/[^a-zA-Z0-9]+/g, '');
                                const newFileItem = $('<a href="#" class="list-group-item list-group-item-action d-flex align-items-center" style="background: #DDEFE8; border: 0px">').html(`
                        <div class="me-2 d-flex align-items-center">
                            <input class="form-check-input me-1" type="checkbox" value="" aria-label="..." style="margin-top: 0rem">
                            <span class="fas fa-file" style="color: #0097A9; margin-left: 0.5rem"></span>
                        </div>
                        <span class="flex-grow-1" style="color: #0097A9 ;        font-family: 'Inter' ,sans-serif; font-weight: 600">${fileName}</span>
                        <label class="switch ms-auto select-switch">
                            <input type="checkbox">
                            <span class="slider round"></span>
                        </label>
                    `);
                                newFileItem.appendTo('#fileList');
                                newFileItem.find('.select-switch input[type="checkbox"]').on('click', function () {
                                    const selectedFiles = [];
                                    $('#fileList .select-switch input[type="checkbox"]:checked').each(function () {
                                        selectedFiles.push($(this).closest('.list-group-item').find('span.flex-grow-1').text());
                                    });
                                    $.ajax({
                                        url: '/selected-files',
                                        type: 'POST',
                                        contentType: 'application/json',
                                        data: JSON.stringify({ selectedFiles: selectedFiles }),
                                        success: function (response) {
                                            console.log('Selected files sent successfully');
                                        },
                                        error: function (xhr, status, error) {
                                            console.log('Error: ' + error);
                                        }
                                    });
                                });

                            });
                        } else {

                            uploadLoading.style.visibility = "hidden";

                            $('#upload-status-message').text('Error occurred during file upload');
                            $('#upload-loader .loader').hide();
                        }
                        $('#file-upload').val('');
                    }.bind(this);

                    xhr.onerror = function () {

                        uploadLoading.style.visibility = "hidden";

                        $('#upload-status-message').text('Network error occurred during file upload');
                        $('#upload-loader .loader').hide();
                        $('#file-upload').val('');
                    };

                    xhr.send(formData);
                }
            });

            $('[data-bs-toggle="offcanvas"]').on('click', function () {
                $(this).toggleClass('collapsed');
            });

            $('#fileList').on('click', '.delete-switch', function () {
                var fileName = $(this).siblings('.flex-grow-1').text(); 
                var selectedDataset = $('#initial-dataset-selector').val(); 

                $.ajax({
                    url: '/delete-file',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ file_name: fileName, dataset: selectedDataset }),
                    success: function (response) {
                        console.log(response.message);
                    },
                    error: function (xhr, status, error) {
                        console.log("Error: " + error);
                    }
                });

                $(this).closest('.list-group-item').remove(); 
            });

            function updateDeleteButtonStatus() {
                var selectedDataset = $('#initial-dataset-selector').val();
                if (selectedDataset) {
                    $('#delete-dataset-btn').css('background-color', '#f44336').prop('disabled', false);
                } else {
                    $('#delete-dataset-btn').css('background-color', 'grey').prop('disabled', true);
                }
            }

            $('#delete-dataset-btn').show();

            $(document).on('click', '.loaded-dataset-item', function () {
                var selectedDataset = $(this).find('.dataset-name').text();
                updateFileList(selectedDataset);
                updateFileCount();
                $('#fileList').empty();

                $.ajax({
                    url: '/selected-dataset',
                    type: 'POST',
                    contentType: 'application/json',

                    data: JSON.stringify({ selectedDataset: selectedDataset }),
                    success: function (response) {
                        console.log('Selected dataset updated successfully:', response.message);
                    },
                    error: function (xhr, status, error) {
                        console.log("Error occurred while updating selected dataset:", error);
                    }
                });
            });

            $('#fileList').on('click', '.delete-btn', function () {
                var fileName = $(this).siblings('.file-name').text();
                var selectedDataset = $('#initial-dataset-selector').val(); 

                $.ajax({
                    url: '/delete-file',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ file_name: fileName, dataset: selectedDataset }), 
                    success: function (response) {
                        console.log(response.message);
                    },
                    error: function (xhr, status, error) {
                        console.log("Error: " + error);
                    }
                });

                $(this).parent('.file-item').remove();
            });

            function updateModelOptions() {
                console.log("updateModelOptions")
                var modeSelect = document.getElementById("mode");
                var modelSelect = document.getElementById("model");

                console.log(modelSelect.value)
                console.log(modelSelect.value)

                if (modeSelect.value === "online") {
                    modelSelect.options[0].style.display = "block"; 
                    modelSelect.options[1].style.display = "none"; 

                    modelSelect.value = "gpt-3.5";
                } else {
                    modelSelect.options[0].style.display = "none"; 
                    modelSelect.options[1].style.display = "block"; 

                    modelSelect.value = "mixtral";
                }
            }

            updateModelOptions();
            $('#mode').change(updateModelOptions);

            function updateFormValues() {
                var mode = $('#mode').val();
                var model = $('#model').val();
                var rag = $('#rag').val();

                $('#form_mode').val(mode);
                $('#form_model').val(model);
                $('#form_rag').val(rag);

                $('#display-mode').text(mode);
                $('#display-model').text(model);
                $('#display-rag').text(rag);
            }

            $('#mode, #model, #rag').change(updateFormValues);

            function loadInstructOptions() {
                $.ajax({
                    url: '/get-prompt-templates',
                    type: 'GET',
                    success: function (data) {
                        var promptTemplates = data.prompt_template_list;
                        var instructSelector = $('#instruct-selector');
                        instructSelector.empty(); 

                        instructSelector.append($('<option>', {
                            value: 'none',
                            text: 'No role selected'
                        }));

                        promptTemplates.forEach(function (template) {
                            instructSelector.append($('<option>', {
                                value: template,
                                text: template
                            }));
                        });
                    },
                    error: function () {
                        console.log('Error loading role options');
                    }
                });
            }

            $('#select-all-files').change(function () {
                var isChecked = $(this).is(':checked');
                $('#fileList .file-item input[type="checkbox"]').prop('checked', isChecked);

                if (isChecked) {
                    var selectedFiles = [];
                    $('#fileList .file-item input[type="checkbox"]:checked').each(function () {
                        selectedFiles.push($(this).siblings('.file-name').text());
                    });

                    $.ajax({
                        url: '/selected-files',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ selectedFiles: selectedFiles }),
                        success: function (response) {
                            console.log('Selected files sent successfully', response);
                        },
                        error: function (xhr, status, error) {
                            console.error('Error: ' + error);
                        }
                    });
                }
            });
            $('#toggle-options-btn').click(function () {
                $('.options-container').slideToggle(); 
            });
            $('#initial-dataset-selector').change(function () {
                var selectedDataset = $(this).val();
                updateFileList(selectedDataset)
                updateDeleteButtonStatus();
            });

            $('#delete-dataset-btn').click(function () {
                var selectedDataset = $('#initial-dataset-selector').val();
                if (selectedDataset) {
                    $.ajax({
                        url: '/delete-dataset',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ dataset: selectedDataset }), 
                        success: function (response) {
                            console.log('Dataset deleted successfully:', response);
                            loadInitialDatasets();
                            $('#initial-dataset-selector').val('');
                            $('#fileList').empty();
                            updateFileCount();
                        },
                        error: function (xhr, status, error) {
                            console.log("Error occurred while deleting dataset:", error);
                        }
                    });
                } else {
                    alert('Please select a dataset to delete.');
                }
            });
            $('#toggle-new-dataset-btn').click(function () {
                $('#new-dataset-container').slideToggle();
            });

            $('#create-dataset-btn').click(function () {
                var newDatasetName = $('#new-dataset-name').val().trim();
                if (newDatasetName) {
                    $('#initial-dataset-selector').append('<option value="' + newDatasetName + '">' + newDatasetName + '</option>');
                    $('#initial-dataset-selector').val(newDatasetName);
                    $('#new-dataset-name').val('');
                    $('#new-dataset-container').slideUp();
                    var selectedDataset = $(this).val();
                    updateFileList(selectedDataset);
                    updateDeleteButtonStatus();
                } else {
                    alert('Please enter a dataset name.');
                }
            });
            $('#addChatHistoryButton').click(function () {

                $.ajax({
                    url: '/create-new-chat-history',
                    type: 'POST',
                    success: function (response) {

                        console.log('New chat history added successfully:', response);

                        loadAllUsersChatHistory();

                        setTimeout(function () {
                            $('#chatHistoryBox .chat-history-entry:first-child').click(); 
                        }, 1000); 
                    },
                    error: function (xhr, status, error) {
                        console.error("Error creating new chat history:", error);
                    }
                });
            });

            $(document).on('click', '.chat-history-entry', function () {
                const userId = $(this).data('userid');
                console.log("User ID: ", userId);

                $(this).css('background-color', '#e0e0e0'); 
                setTimeout(() => {
                    $(this).css('background-color', ''); 
                }, 2000); 
            });
            $(document).on('click', '.chat-history-entry', function () {
                const userId = $(this).data('userid');

                $('#feedback').text('Loading user info...'); 

                setTimeout(() => {
                    $('#feedback').text(''); 

                }, 2000); 
            });
            $(document).on('click', '.chat-history-entry', function () {
                const userId = $(this).data('userid');
                $.ajax({
                    url: '/update-user-session',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ userId: userId }),
                    success: function (response) {
                        console.log('Send UserID Successfully', response);
                    },
                    error: function (xhr, status, error) {
                        console.error("Send UserID error", error);
                    }
                });
            });

        });

    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>