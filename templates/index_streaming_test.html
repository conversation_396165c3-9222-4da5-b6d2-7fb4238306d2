<!DOCTYPE html>
<html>
<head>
    <title>Model Streaming Output</title>
    <script src="//cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script type="text/javascript" charset="utf-8">
        var socket = io.connect('http://' + document.domain + ':' + location.port);

        socket.on('connect', function() {
            console.log('Websocket connected!');
        });

        socket.on('model_output', function(msg) {
            var outputElement = document.getElementById('model-output');
            outputElement.value += msg.output;
            outputElement.scrollTop = outputElement.scrollHeight; // 自动滚动到底部
        });

        function sendQuestion() {
            var question = document.getElementById('question').value;
            socket.emit('send_question', {question: question});
            document.getElementById('model-output').value = '';  // 清空输出区域准备显示新的输出
        }
    </script>
</head>
<body>
    <input type="text" id="question" placeholder="Enter your question">
    <button onclick="sendQuestion()">Ask</button>
    <textarea id="model-output" cols="50" rows="10" placeholder="Model output will appear here..." readonly></textarea>
</body>
</html>