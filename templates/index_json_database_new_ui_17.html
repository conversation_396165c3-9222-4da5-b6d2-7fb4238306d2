<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG-CHATBOT</title>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://kit.fontawesome.com/8e9c71f3b7.js" crossorigin="anonymous"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
    body {
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
    }
        .chat-container, .file-container {
            padding: 20px;
            border-radius: 10px;
        }
        .chat-container {
            width: 100%;
        }
        .file-container {
            width: 100%;
        }
        .file-list {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            background: #fafafa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .chat-box {
            height: 850px;
            overflow-y: auto;
            border: 1px solid #ddd;
            /*background: #fafafa;*/
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border: none;
        }

        .file-item {
            padding: 8px;
            margin: 5px 0;
            border-radius: 8px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
        }
        .user-message {
            /*text-align: left;*/
        }
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .file-name {
            flex-grow: 1;
            margin-left: 10px;
        }
        .file-form {
            display: flex;
            align-items: center;
            justify-content: flex-end; /* ???????? */

        }
        .chat-form, .file-form {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .chat-form > *, .file-form > * {
            flex: 1;
            min-width: 120px;
        }
        input[type="text"], input[type="file"], select {
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            height: 40px; /* ???? */
        }
        input[type="submit"], .delete-btn {
            background-color: #4caf50;
            color: white;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        input[type="submit"]:hover, .delete-btn:hover {
            background-color: #45a049;
        }
        input[type="text"] {
            flex-grow: 2;
        }
        input[type="submit"] {
            white-space: nowrap;
        }
        #directory {
            display: none;
        }
        .upload-btn {
            display: inline-block;
            background-color: #4caf50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
            min-width: 120px;
            /*height: 20px;*/
            line-height: 20px;
        }
        #file-upload-status {
            margin-left: 10px;
            font-size: 14px;
        }
        .copy-btn {
            display: none;
            cursor: pointer;
            margin-left: 10px;
            color: #4caf50;
        }
        .chat-message:hover .copy-btn {
            display: inline;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-radius: 50%;
            border-top: 4px solid #4caf50;
            width: 20px;
            height: 20px;
            animation: spin 2s linear infinite;
            display: inline-block;
            vertical-align: middle;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .options-container {
            margin-bottom: 20px;
        }
        .options-container select {
            margin-bottom: 10px;
            width: 20%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .loaded-dataset-item {
            background-color: #f0f0f0;
            padding: 8px;
            margin: 5px 0;
            border-radius: 8px;
            border: 1px solid #ddd;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dataset-container h2 {
            margin-top: 0;
        }
        .dataset-select-container {
            display: flex;
            width: 100%;
            margin-bottom: 10px;
        }
        #initial-dataset-selector {
            flex-grow: 1;
        }

       .load-btn {
           display: block;
            width: 100%;
            background-color: #4caf50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
        }

        .instruct-select-container select {
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .chat-form {
            display: flex;
            flex-direction: column;
            align-items: stretch;
        }

        .input-container {
            position: relative; /* Make sure the container is positioned relatively */
            display: flex;
            align-items: center;
            width: 100%;
        }

        input[type="text"] {
            width: 100%;
            box-sizing: border-box;
        }

        input[type="submit"] {
            width: auto;
            align-self: center;
        }

        .new-dataset-container input[type="text"] {
            margin-bottom: 10px;
        }

        .small-title {
            font-size: 14px;
            margin-bottom: 10px;
        }

        .dataset-name {
            font-size: 12px;
        }

        .file-name {
            font-size: 12px;
        }
        #upload-status-message {
            margin-top: 10px;
            font-size: 12px;
            display: block;
            height: 20px;
            text-align: left;
            display: flex;
            align-items: center;
            margin-right: 10px;
        }
        #upload-status-container {
            display: flex;
            align-items: center;
            min-width: 120px;
            height: 40px;
        }

        .dataset-manage {
    display: flex;
    align-items: center;
    justify-content: flex-end; /* ???????? */
        }
        #delete-dataset-btn {
            margin-left: auto; /* ????????? */
            display: inline-block;
            background-color: #f44336;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
            min-width: 120px;
            height: 40px;
            line-height: 20px;
        }
        .checkbox-container {
            display: flex;
            align-items: center;
        }
        #toggle-options-btn {
            background-color: grey;
        }

        .dataset-select-container {
            width: 100%;
            margin-bottom: 10px;
        }

        #initial-dataset-selector {
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        button:disabled {
            background-color: grey;
            cursor: not-allowed;
        }

        .chat-history-entry {
            padding: 10px;
            margin-bottom: 10px;
            /*background-color: #f0f0f0;*/
            border-radius: 8px;
        }

        .chat-history-entry {
            cursor: pointer; /* ?????????????????????? */
            transition: background-color 0.3s ease; /* ?????????????? */
        }

        .chat-history-entry:hover {
            background-color: #f0f0f0; /* ?????????? */
        }

        .container {
            display: flex;
            justify-content: space-between;
            width: calc(100% - 40px); /* Adjusted width */
            margin: 0 auto;
            transition: margin 0.3s ease; /* Smooth transition for margin adjustments */
        }
        .file-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            /*box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);*/
            transition: width 0.3s ease, padding 0.3s ease; /* Smooth transition */
        }
        .chat-history-container {
            width: 100%; /* Adjusted width */
        }
        .file-container {
            transition: width 0.3s ease, padding 0.3s ease; /* Apply transition for width and padding */
        }
        .container.collapsed-left .chat-history-container,
        .container.collapsed-right .file-container {
                    width: 0;
                    padding: 0; /* Optional, removes padding when collapsed */
        }
        .container.collapsed-right .file-container {
            width: 0;
            padding: 0; /* Ensure padding is set to 0 to fully hide the container */
            overflow: hidden; /* Prevent content from overflowing during transition */
        }
        .toggle-btn {
            position: fixed;
            text-align: center;
            /*min-width: 120px;*/
            min-width: auto;
            height: auto;
            top: 50%;
            z-index: 100;
            cursor: pointer;
            padding: 5px 10px;
            background-color: #4caf50;
            color: white;
            border: none;
            border-radius: 5px;
            transform: translateY(-50%);
        }
        .left-toggle {
            left: 0;
        }
        .right-toggle {
            right: 0;
        }
        .user-message {
            margin-left: auto;
            text-align: justify;
            max-width: 50%;
            /*display: inline-block; !* ?????? *!*/
            /*background-color: #daf8cb;*/
        }


        .human-icon, .bot-icon {
            margin-right: 10px;
            vertical-align: middle;
        }

        .chat-message i {
            margin-right: 5px;
        }

        .user-message .message-text {
            font-size: 16px;
            /*background-color: #daf8cb;*/
            padding: 5px;
            border-radius: 5px;
            display: inline-block;
        }

        .bot-message .message-text {
            font-size: 16px;
            /*background-color: #f0f0f0;*/
            padding: 5px;
            border-radius: 5px;
            display: inline-block;
        }

        pre code {
    display: block;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 5px;
    color: #333;
    overflow-x: auto;
    }
          .btn-primary {
    background-color: #5865F2; /* ?????????? */
    border-color: #5865F2; /* ???? */
    color: #fff; /* ???? */
  }
  .btn-primary:hover {
    background-color: #4752c4; /* ?????????? */
    border-color: #4752c4; /* ?????????? */
  }
  .custom-btn-height {
    height: 50px;
    padding: 0 15px; /* ??????? */
    background-color: #5865F2; /* ?????? */
    color: white; /* ???? */
    border: none; /* ???? */
    border-radius: 0 5px 5px 0; /* ???????? */
  }

  .fixed-left-center {
    position: fixed; /* ???? */
    top: 50%; /* ????????????? */
    left: 0; /* ????? */
    transform: translateY(-50%); /* ?????????50%?????? */
    z-index: 1050; /* ???????????? */
  }

  #toggle-chat-history {
    display: none; /* ????? toggle-chat-history ?? */
  }
  .btn-primary {
    background-color: transparent; /* ?????? */
    border: none; /* ???? */
    color: black; /* ???????????????? */
}

.btn-primary:hover {
    background-color: transparent; /* ??????????? */
    color: grey; /* ??????????????????? */
}

/* ???????? */
.rotate-arrow {
    transform: rotate(180deg);
    transition: transform 0.3s ease; /* ?????? */
}

.toggle-chat-history-btn {
    position: fixed; /* ?????? */
    top: 2%; /* ????????50% */
    left: 0; /* ????? */
    transform: translateY(-50%); /* ???? */
    background-color: transparent; /* ???? */
    border: none; /* ??? */
    color: black; /* ??????????? */
    cursor: pointer; /* ??????????? */
}

.toggle-chat-history-btn:hover {
    color: grey; /* ?????????? */
}

.toggle-chat-history-btn .fas {
    transition: transform 0.3s; /* ?????? */
}

.toggle-chat-history-btn.collapsed .fas {
    transform: rotate(-180deg); /* Chat History ??????? */
}

#user_input {
    flex-grow: 1; /* Ensure input takes available space */
    border-radius: 5px;
    border: 0px;
    background: #ffffff;
}
.input-group {
    position: relative;
    display: flex;
    align-items: center;
    background-color: white; /* ???????????????? */
    border: 1px solid #ccc; /* ??????????? */
    border-radius: 5px; /* ??????????? */
}

.input-group input[type="text"] {
    padding-left: 35px; /* ??????????? */
    padding-right: 35px; /* ?????????? */
    flex-grow: 1;
    border: none; /* ???????? */
    border-radius: 5px; /* ???????? */
}

.icon-btn {
    background: transparent;
    border: none;
    cursor: pointer;
}

.database-icon {
    left: 10px;
}

.send-icon {
    right: 10px;
}
.icon-btn i {
    font-size: 20px;
}
/* 开关按钮样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 22px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
    border-radius: 22px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(18px);
  -ms-transform: translateX(18px);
  transform: translateX(18px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}
.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%; /* Adjust based on your chat container's height */
    text-align: center;
    color: #666; /* Example color */
    font-size: 20px; /* Example font size */
}
.custom-icon-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 120px; /* 增加图标容器的高度 */
    animation: icon-animation 5s ease-in-out infinite;
}

.custom-icon-body {
    width: 100px;
    height: 100px; /* 增加图标本身的高度 */
    position: relative;
}

.custom-icon-circle-group {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    animation: circle-group-rotate 5s linear infinite;
}

.custom-icon-circle {
    width: 25px;
    height: 25px; /* 增加圆形的尺寸 */
    background-color: #34A853;
    border-radius: 50%;
    position: absolute;
    animation: circle-pulse 3s ease-in-out infinite;
}

.custom-icon-circle:nth-child(1) {
    top: 0;
    left: 0;
}

.custom-icon-circle:nth-child(2) {
    top: 0;
    right: 0;
}

.custom-icon-circle:nth-child(3) {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

@keyframes icon-animation {
    0% { transform: translateY(0); }
    50% { transform: translateY(20px); } /* 增加图标容器的垂直移动距离 */
    100% { transform: translateY(0); }
}

@keyframes circle-group-rotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes circle-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}
.prompt-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 15px;
    margin-bottom: 10px;
}

.prompt-box {
    border: 1px solid;
    border-radius: 8px;
    padding: 2px;
    text-align: center;
}

.prompt-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
}

.prompt-description {
    font-size: 12px;
    color: #666;
}
.send-icon {
  border: none;
  width: 36px;
  height: 36px;
  cursor: pointer;
}

#user_input {
    outline: none;
}

    </style>
</head>
<body>
    <div class="container">
        <button id="toggle-chat-history" class="toggle-btn left-toggle">
            <i class="fas fa-arrow-left"></i>
        </button>
        <div class="offcanvas offcanvas-start" tabindex="-1" id="offcanvasChatHistory" aria-labelledby="offcanvasChatHistoryLabel">
        <img src="{{ url_for('static', filename='images/deloitte-1 1.png') }}" style="width: 150px; display: block; margin-top: 10px; margin-left: 10px; margin-right: auto;">
        <button id="addChatHistoryButton" type="button" class="btn btn-primary">Add New</button>
          <div class="offcanvas-body">
            <div class="chat-history-container">
                <div id="chatHistoryBox" class="chat-box"></div>
            </div>

          </div>
            <div class="icon-container" style="display: flex; justify-content: space-between; position: absolute; bottom: 0; width: 100%; padding: 10px;">
        <button type="button" class="icon-btn" data-bs-toggle="modal" data-bs-target="#profileModal">
                        <img src="{{ url_for('static', filename='images/Profile Button.png') }}" alt="Settings">

        </button>
        <button type="button" class="icon-btn database-icon" data-bs-toggle="modal" data-bs-target="#databaseModal">
                        <img src="{{ url_for('static', filename='images/Doc Mgmt Button.png') }}" alt="Settings">

        </button>
        <button type="button" class="icon-btn settings-icon" data-bs-toggle="modal" data-bs-target="#settingsModal">
            <img src="{{ url_for('static', filename='images/Setting Button.png') }}" alt="Settings">
        </button>
    </div>
</div>

<div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="settingsModalLabel">Settings</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <select name="mode" id="mode" onchange="updateModelOptions()">
                    <option value="online">Online</option>
                    <option value="offline" selected>Offline</option>
                </select>
                <select name="model" id="model">
                    <option value="gpt-3.5">GPT-3.5</option>
                    <option value="llama" style="display:none;">LLaMA-2-13B</option>
                    <option value="mixtral" style="display:none;" selected>Mixtral-8x7B-Instruct</option>
                    <option value="instructmixtral-1" style="display:none;">instructmixtral-1</option>
                </select>
                <select name="rag" id="rag">
                    <option value="off">RAG Off</option>
                    <option value="on" selected>RAG On</option>
                </select>
                <select id="instruct-selector"></select>

                <div class="checkbox-container">
                    <input type="checkbox" id="include_history" name="include_history">
                    <label for="include_history">Include Chat History</label>
                </div>

                <div>
                    <label for="max_history_no">Max History Items:</label>
                    <select name="max_history_no" id="max_history_no">
                        <option value="3">3</option>
                        <option value="5">5</option>
                        <option value="7">7</option>
                        <option value="9">9</option>
                        <option value="11">11</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save changes</button>
            </div>
        </div>
    </div>
</div>
        <button class="toggle-chat-history-btn" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasChatHistory" aria-controls="offcanvasChatHistory">
<!--          <i class="fas fa-chevron-right"></i>-->
                <img src="{{ url_for('static', filename='images/Ribbon Button.png') }}" alt="Toggle">
        </button>
<div class="chat-container">
    <div id="chatBox" class="chat-box">
    </div>
    <form id="chatForm" class="chat-form" enctype="multipart/form-data">
    <div class="input-container">
        <div style="width: 100%; padding-left: 37px; padding-right: 37px; padding-top: 65px; padding-bottom: 65px; background: white; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div style="width: 1293px; position: relative; background: white; border-radius: 28px; border: 2px #0097A9 solid; display: flex; align-items: center;">
                <div style="margin-left: auto; padding-left: 1px;">
                    <button type="submit" class="icon-btn">
                        <img src="{{ url_for('static', filename='images/Mini Status Menu.png') }}" alt="Submit">
                    </button>
                </div>
                <textarea type="text" name="user_input" id="user_input" placeholder="" required style="resize: none;width: 1191px; color: #0097A9; font-size: 22px; font-weight: 400; overflow-wrap: break-word; flex-grow: 1; align-self: center; border: none; outline: none;"></textarea>
                <div style="margin-left: auto; padding-right: 10px;">
                    <button type="submit" class="icon-btn send-icon">
                        <img src="{{ url_for('static', filename='images/Input Button.png') }}">
                    </button>
                </div>
            </div>
        </div>
    </div>

        <input type="hidden" name="mode" id="form_mode">
        <input type="hidden" name="model" id="form_model">
        <input type="hidden" name="rag" id="form_rag">
    </form>
</div>
        <div class="modal fade" id="databaseModal" tabindex="-1" aria-labelledby="databaseModalLabel" aria-hidden="true">
          <div class="modal-dialog" style="max-width: 1400px;">
            <div class="modal-content">
              <div class="modal-header">
                <button type="button" class="btn" id="backToDatabaseList"><span class="fas fa-arrow-left"></span></button>
                <input type="text" class="form-control me-2" placeholder="Search">
                <button class="btn" id="createFolderButton"><span class="fas fa-folder-plus"></span></button>
                <button class="btn" id="deleteButton"><span class="fas fa-trash"></span></button>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body" style="height: 800px; overflow-y: auto;">
                <h2>Document Management</h2>
                <div id="databaseList" class="list-group"></div>
                <div class="list-group" style="display:none;" id="fileList"></div>
              </div>
              <div class="modal-footer" style="display:flex;">
                  <input type="file" id="file-upload" style="display: none;" multiple>
                  <button id="uploadFileButton" class="btn btn-primary rounded-circle"><span class="fas fa-plus"></span></button>
              </div>
            </div>
          </div>
        </div>
        <div class="modal fade" id="createFolderModal" tabindex="-1" aria-labelledby="createFolderModalLabel" aria-hidden="true">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="createFolderModalLabel">Create New Folder</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                <input type="text" class="form-control" id="newFolderName" placeholder="Folder Name">
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="confirmCreateFolder">Confirm</button>
              </div>
            </div>
          </div>
        </div>
    </div>


    <script>
            $(document).ready(function(){
                const textArea = document.getElementById('user_input');
                const inputContainer = document.querySelector('.input-container');
                const outerContainer = document.querySelector('.input-container > div > div');
                const submitButton = document.querySelector('.send-icon');
                function adjustTextAreaHeight() {
                    textArea.style.height = "0px";
                    textArea.style.height = Math.min(textArea.scrollHeight, 280) + "px";

                    inputContainer.style.height = "auto";
                    outerContainer.style.height = "auto";

                    var newHeight = Math.min(textArea.scrollHeight + 20, 320) + "px";
                    inputContainer.style.height = newHeight;
                    outerContainer.style.height = newHeight;
                }

                adjustTextAreaHeight();

                textArea.addEventListener('input', adjustTextAreaHeight);

                textArea.addEventListener('paste', (event) => {
                    const pastedText = event.clipboardData.getData('text/plain');
                    event.preventDefault();
                    textArea.value += pastedText;
                    adjustTextAreaHeight();
                });

                textArea.addEventListener('keydown', (event) => {
                    if (event.key === 'Enter' && !event.shiftKey) {
                        event.preventDefault();
                        submitButton.click();
                        textArea.value = '';
                        console.log('Message sent');
                    } else if (event.key === 'Enter' && event.shiftKey) {
                        event.preventDefault();
                        const cursorPosition = textArea.selectionStart;
                        textArea.value = textArea.value.substring(0, cursorPosition) + '\n' + textArea.value.substring(cursorPosition);
                        textArea.selectionStart = textArea.selectionEnd = cursorPosition + 1;
                        adjustTextAreaHeight();
                    }
                });

                adjustTextAreaHeight();

                document.addEventListener('DOMContentLoaded', function() {
                    submitButton.addEventListener('click', function() {
                        var textArea = document.getElementById('user_input');
                        textArea.value = '';
                        adjustTextAreaHeight();
                    });
                });

                $('#createFolderButton').on('click', function() {
                    var createFolderModal = new bootstrap.Modal(document.getElementById('createFolderModal'), {
                      keyboard: false
                    });
                    createFolderModal.show();
                });
                // function newLoadInitialDatasets() {
                //     $.getJSON('../vectorstore/vsdb_log.json', function (data) {``
                //         let datasets = new Set(data.map(item => item.dataset));
                //         $('#databaseList').empty();
                //         datasets.forEach(dataset => {
                //             const datasetItem = $('<a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">');
                //             datasetItem.html(`
                //                 <div class="me-2 d-flex align-items-center">
                //                     <input class="form-check-input me-1" type="checkbox" value="" aria-label="...">
                //                     <span class="fas fa-folder"></span>
                //                 </div>
                //                 <span class="flex-grow-1 dataset-name" style="cursor:pointer;">${dataset}</span>
                //                 <label class="switch ms-auto">
                //                     <input type="checkbox">
                //                     <span class="slider round"></span>
                //                 </label>
                //             `);
                //             datasetItem.appendTo('#databaseList');
                //         });
                //     });
                // }

                function newLoadInitialDatasets() {
                    $.getJSON('/get-datasets', function (datasets) {
                    $('#databaseList').empty();
                    datasets.forEach(dataset => {
                        const datasetItem = $('<a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">').html(`
                            <div class="me-2 d-flex align-items-center">
                                <input class="form-check-input me-1" type="checkbox" value="" aria-label="...">
                                <span class="fas fa-folder"></span>
                            </div>
                            <span class="flex-grow-1 dataset-name" style="cursor:pointer;">${dataset.dataset_name}</span>
                            <label class="switch ms-auto">
                                <input type="checkbox">
                                <span class="slider round"></span>
                            </label>
                        `);
                        datasetItem.appendTo('#databaseList');
                    });
                });
                }

                var currentSelectedDataset;

                $('#databaseList').on('click', '.dataset-name', function() {
                    const dataset = $(this).text();
                    currentSelectedDataset = dataset;
                    loadFilesForDataset(dataset);
                    $('#databaseList').hide();
                    $('#fileList').show();
                });
                $('#confirmCreateFolder').on('click', function() {
                var newDatasetName = $('#newFolderName').val().trim();

                if (newDatasetName) {
                    const datasetItem = $('<a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">')
                        .html(`
                            <div class="me-2 d-flex align-items-center">
                                <input class="form-check-input me-1" type="checkbox" value="" aria-label="...">
                                <span class="fas fa-folder"></span>
                            </div>
                            <span class="flex-grow-1 dataset-name" style="cursor:pointer;">${newDatasetName}</span>
                            <label class="switch ms-auto">
                                <input type="checkbox">
                                <span class="slider round"></span>
                            </label>
                        `);
                    $('#databaseList').append(datasetItem);

                    $('#newFolderName').val('');
                    $('#createFolderModal').modal('hide');
                } else {
                    alert('Please enter a dataset name.');
                }
            });
                function loadFilesForDataset(datasetName) {
                     $.getJSON('/get-datasets', function (datasets) {
                        const dataset = datasets.find(d => d.dataset_name === datasetName);
                        $('#fileList').empty();
                        dataset.document_list.forEach(file => {
                            const fileItem = $('<a href="#" class="list-group-item list-group-item-action d-flex align-items-center">').html(`
                                <div class="me-2 d-flex align-items-center">
                                    <input class="form-check-input me-1" type="checkbox" value="" aria-label="...">
                                    <span class="fas fa-file"></span>
                                </div>
                                <span class="flex-grow-1">${file}</span>
                                <label class="switch ms-auto select-switch"> <!-- Added class for targeting -->
                                    <input type="checkbox">
                                    <span class="slider round"></span>
                                </label>
                            `);
                            fileItem.appendTo('#fileList');
                            // 添加 click 事件处理程序
                            fileItem.find('.select-switch input[type="checkbox"]').on('click', function() {
                                const selectedFiles = [];
                                $('#fileList .select-switch input[type="checkbox"]:checked').each(function() {
                                    selectedFiles.push($(this).closest('.list-group-item').find('span.flex-grow-1').text());
                                });
                                $.ajax({
                                    url: '/selected-files',
                                    type: 'POST',
                                    contentType: 'application/json',
                                    data: JSON.stringify({ selectedFiles: selectedFiles }),
                                    success: function(response) {
                                        console.log('Selected files sent successfully');
                                    },
                                    error: function(xhr, status, error) {
                                        console.log('Error: ' + error);
                                    }
                                });
                            });
                        });
                    });
                }

                // Event listener for the delete switch in the file list
                $('#fileList').on('click', '.delete-switch', function() {
                    var fileName = $(this).siblings('.flex-grow-1').text(); // Modified to get the file name
                    var selectedDataset = $('#initial-dataset-selector').val(); // Assuming you have a way to get the selected dataset

                    $.ajax({
                        url: '/delete-file',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ file_name: fileName, dataset: selectedDataset }),
                        success: function(response) {
                            console.log(response.message);
                        },
                        error: function(xhr, status, error) {
                            console.log("Error: " + error);
                        }
                    });

                    $(this).closest('.list-group-item').remove(); // Modified to remove the closest list group item
                });

                $('#backToDatabaseList').click(function() {
                    $('#fileList').hide();
                    $('#databaseList').show();
                });

                newLoadInitialDatasets();

                $('[data-bs-toggle="offcanvas"]').on('click', function() {
                    $(this).toggleClass('collapsed');
                });

                $('#toggle-chat-history').click(function() {
                    $('.container').toggleClass('collapsed-left');
                    var isCollapsed = $('.container').hasClass('collapsed-left');
                    $(this).find('i').toggleClass('fa-arrow-right fa-arrow-left'); // ????
                });

                $('#toggle-file-container').click(function() {
                    $('.container').toggleClass('collapsed-right');
                    var isCollapsed = $('.container').hasClass('collapsed-right');
                    $(this).find('i').toggleClass('fa-arrow-left fa-arrow-right'); // ????
                });


                $('#toggle-database-icon-btn').click(function() {
                    $('.container').toggleClass('collapsed-right');
                });

                $(document).on('click', '.chat-history-entry', function() {
                    const userId = $(this).data('userid');
                    console.log("User ID: ", userId);
                    // ??????????
                    $(this).css('background-color', '#e0e0e0'); // ????????
                    setTimeout(() => {
                        $(this).css('background-color', ''); // ???????
                    }, 2000); // 2000 ??????????????
                });
                $(document).on('click', '.chat-history-entry', function() {
                    const userId = $(this).data('userid');
                    // ??????
                    $('#feedback').text('Loading user info...'); // ?????? ID ? feedback ???????????
                    // ????????? AJAX ??
                    setTimeout(() => {
                        $('#feedback').text(''); // ??????
                        // ??????????????
                    }, 2000); // ????????
                });
                $(document).on('click', '.chat-history-entry', function() {
                    const userId = $(this).data('userid');
                    $.ajax({
                        url: '/update-user-session',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ userId: userId }),
                        success: function(response) {
                            console.log('Send UserID Successfully', response);
                        },
                        error: function(xhr, status, error) {
                            console.error("Send UserID error", error);
                        }
                    });
                });

                function loadAllUsersChatHistory() {
                    $.ajax({
                        url: '/get-all-users-data',
                        type: 'GET',
                        success: function(data) {
                            const chatHistoryBox = $('#chatHistoryBox');
                            chatHistoryBox.empty(); // ??????
                            Object.keys(data).forEach(userId => {
                                const firstQuestion = data[userId];
                                // const entryHtml = `
                                //     <div class="chat-history-entry" data-userId="${userId}">
                                //         <div><strong>User ID:</strong> ${userId}</div>
                                //         <div><strong>First Question:</strong> ${firstQuestion}</div>
                                //     </div>
                                // `;
                                const entryHtml = `
                                    <div class="chat-history-entry" data-userId="${userId}">
                                    <div style="width: 100%; height: 100%; padding-left: 11px; padding-right: 11px; padding-top: 15px; padding-bottom: 15px; background: #DDEFE8; border-radius: 22px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: center; gap: 9px; display: inline-flex">
                                        <div style="padding-left: 1px; padding-right: 6px; justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex">
                                            <div style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 10px; display: inline-flex">
                                                <div style="color: #0097A9; font-size: 15px; font-weight: 700; word-wrap: break-word">Model:</div>
                                                <div style="width: 107px; color: #0097A9; font-size: 15px; font-weight: 700; word-wrap: break-word">${userId}</div>
                                            </div>
                                            <div style="width: 107px; text-align: right; color: #0097A9; font-size: 10px; Inter; font-weight: 700; word-wrap: break-word"> 20 Mar 2024</div>
                                        </div>
                                        <div style="width: 290px; height: 0px; border: 1px #0097A9 solid"></div>
                                        <div style="width: 284px; height: 18px; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
                                            <div style="color: #0097A9; font-size: 15px; font-weight: 700; word-wrap: break-word">Title:</div>
                                            <div style="width: 246px; color: #0097A9; font-size: 15px;font-weight: 700; word-wrap: break-word">${firstQuestion}</div>
                                        </div>
                                    </div>
                               </div>
                                `;
                                chatHistoryBox.append(entryHtml);
                            });
                        },
                        error: function(xhr, status, error) {
                            console.error("Failed to load all users' chat history:", error);
                        }
                    });
                }

                $('#chatHistoryBox').on('click', '.chat-history-entry', function() {
                    console.log("Clicked on: ", this);
                    const userId = $(this).data('userid');
                    console.log("test " + userId);
                    if (userId) {
                        console.log(userId);
                        loadUserChatHistory(userId);
                    } else {
                        console.error("User ID is undefined.");
                    }
                });

                loadAllUsersChatHistory();

                function loadUserChatHistory(userId) {
        $.ajax({
            url: '/get-user-data/' + userId,
            type: 'GET',
            success: function(data) {
                const chatBox = $('#chatBox');
                chatBox.empty();
                Object.values(data).forEach(entry => {
                    // ?? entry ?????? q_msg??????? a_msg???????
                    // const userMessageHtml = `
                    //     <div class="chat-message user-message">
                    //         <div class="icon-container"><i class="fa-solid fa-user"></i></div>
                    //         <div class="message-text">${entry.q_msg}</div>
                    //     </div>
                    // `;
                    const userMessageHtml = `
                        <div style="width: 100%; height: 100%; padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; flex-direction: column; justify-content: flex-start; align-items: flex-end; gap: 6px; display: inline-flex">
                          <div style="padding-left: 2px; padding-right: 2px; padding-top: 1px; padding-bottom: 1px; justify-content: flex-end; align-items: center; gap: 10px; display: inline-flex">
                            <div style="text-align: right; color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 20px">User
                                      <img src="{{ url_for('static', filename='images/user.png') }}"  alt="Image Description" style="vertical-align: middle;">
</div>
                          </div>
                          <div style="padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; border-radius: 18px; overflow: hidden; border: 2px #0097A9 solid; justify-content: center; align-items: center; gap: 19px; display: inline-flex">
                            <div style="width: 715px; color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 18px">${messageContent}</div>
                          </div>
                        </div>
                      `;
                    // const botMessageHtml = `
                    //     <div class="chat-message bot-message">
                    //         <div class="icon-container"><i class="fa-solid fa-robot"></i></div>
                    //         <div class="message-text">${entry.a_msg}</div>
                    //     </div>
                    // `;
                    const botMessageHtml = `
<div>
  <div style="width: 100%; height: 100%; padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; padding-bottom: 0px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex">
    <div style="transform: rotate(180deg); transform-origin: 0 0; justify-content: flex-start; align-items: flex-end; gap: 5px; display: inline-flex">
      <div style="transform: rotate(180deg); transform-origin: 0 0; text-align: right; color: #004F59; font-size: 22px; font-weight: 700; word-wrap: break-word; font-size: 20px">
                      <img src="{{ url_for('static', filename='images/robot.png') }}" alt="Image Description" style="vertical-align: middle;">
      Assistant
</div>
      <div style="width: 34px; height: 34px; position: relative; transform: rotate(180deg); transform-origin: 0 0">
        <div style="width: 34px; height: 34px; left: 0px; top: 0px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: rgba(255, 255, 255, 0.01)"></div>
        <div style="width: 21.38px; height: 17.10px; left: -6.41px; top: 12.83px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: rgba(255, 255, 255, 0.01); border: 2px #004F59 solid"></div>
        <div style="width: 2.85px; height: 2.85px; left: -10.69px; top: 17.10px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: #69746D; border: 2px #004F59 solid"></div>
        <div style="width: 2.85px; height: 2.85px; left: -20.66px; top: 17.10px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: #69746D; border: 2px #004F59 solid"></div>
        <div style="width: 8.55px; height: 2.85px; left: -12.83px; top: 22.80px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: #69746D; border: 2px #004F59 solid"></div>
        <div style="width: 28.50px; height: 19.95px; left: -2.85px; top: 4.28px; position: absolute; transform: rotate(180deg); transform-origin: 0 0">
          <div style="width: 0px; height: 5.70px; left: -14.25px; top: 2.85px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
          <div style="width: 0px; height: 5.70px; left: -0px; top: 14.25px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
          <div style="width: 0px; height: 5.70px; left: -28.50px; top: 14.25px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
          <div style="width: 2.85px; height: 2.85px; left: -12.83px; top: 0px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
        </div>
      </div>
    </div>
    <div style="padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; background: #004F59; border-radius: 18px; overflow: hidden; justify-content: center; align-items: center; gap: 19px; display: inline-flex">
      <div style="width: 715px; text-align: justify; color: white; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 18px">${entry.a_msg}</div>
    </div>
  </div>
</div>
                    `;
                    chatBox.append(userMessageHtml + botMessageHtml);
                });
                $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
                // updateChatEmptyState();
            },
            error: function(xhr, status, error) {
                console.error("Failed to load user's chat history:", error);
            }
        });
    }

                function loadInitialDatasets() {
                    $.getJSON('../vectorstore/vsdb_log.json', function (data) {
                        let datasets = new Set(data.map(item => item.dataset));

                        $('#initial-dataset-selector').empty().append('<option value="">No dataset selected</option>');

                        datasets.forEach(dataset => {
                            $('#initial-dataset-selector').append('<option value="' + dataset + '">' + dataset + '</option>');
                        });
                    });
                }

                $('#model').val('mixtral');
                function updateDeleteButtonStatus() {
                    var selectedDataset = $('#initial-dataset-selector').val();
                    if(selectedDataset) {
                        $('#delete-dataset-btn').css('background-color', '#f44336').prop('disabled', false);
                    } else {
                        $('#delete-dataset-btn').css('background-color', 'grey').prop('disabled', true);
                    }
                }

                updateDeleteButtonStatus();

                loadInitialDatasets();

                loadInstructOptions();

                updateDatasetCount();

                updateFileCount();

                $('#delete-dataset-btn').show();

                $('#load-dataset-btn').click(function() {
                    var selectedDataset = $('#initial-dataset-selector').val();
                    addDatasetToLoaded(selectedDataset);
                });

                $(document).on('click', '.delete-dataset-btn', function() {
                    $(this).closest('.loaded-dataset-item').remove();
                });

                $(document).on('click', '.loaded-dataset-item', function() {
                    var selectedDataset = $(this).text();
                    updateFileList(selectedDataset);
                });
                function updateModelOptions() {
                    var modeSelect = document.getElementById("mode");
                    var modelSelect = document.getElementById("model");

                    if (modeSelect.value === "online") {
                        modelSelect.options[0].style.display = "block"; // GPT-3.5
                        modelSelect.options[1].style.display = "none"; // LLAMA
                        modelSelect.options[2].style.display = "none"; // Mixtral-8x7B-Instruct
                        modelSelect.options[3].style.display = "none";  // instructmixtral-1
                        modelSelect.value = "gpt-3.5";
                    } else {
                        modelSelect.options[0].style.display = "none"; // GPT-3.5
                        modelSelect.options[1].style.display = "block"; // LLAMA
                        modelSelect.options[2].style.display = "block";  // Mixtral-8x7B-Instruct
                        modelSelect.options[3].style.display = "block";  // instructmixtral-1
                        modelSelect.value = "mixtral";
                    }
                }

                updateModelOptions();
                $('#mode').change(updateModelOptions);

                $('#chatForm').on('submit', function(e){
                    e.preventDefault();
                    console.log("submit")
                    var formData = new FormData(this);
                    var ragStatus = $('#rag').val();
                    var useOriginalText = $('#useOriginalText').is(':checked');
                    formData.append('useOriginalText', useOriginalText); // ??useOriginalText??
                    var userMessage = $('#user_input').val();
                    appendMessage(userMessage, 'user-message', false);
                    var loaderMessage = appendMessage('', 'bot-message', true);

                    // Include the new settings in the formData
                    var includeHistory = $('#include_history').is(':checked');
                    var maxHistoryNo = $('#max_history_no').val();

                    formData.append('include_history', includeHistory);
                    formData.append('max_history_no', maxHistoryNo);

                    // var selectedDataset = $('#initial-dataset-selector').val();
                    var selectedDataset = currentSelectedDataset;
                    console.log("selectedDataset")
                    if (ragStatus === 'on' && !selectedDataset) {
                        alert('No dataset selected. Using RAG Off mode.');
                        formData.append('rag', 'off'); // ? RAG ????? 'off'
                    } else {
                        formData.append('rag', ragStatus); // ??????? RAG ??
                    }
                    formData.append('selectedDataset', selectedDataset); // ??useOriginalText??
                    textArea.value = ''
                    adjustTextAreaHeight()
                    $.ajax({
                        url: '/chat',
                        type: 'post',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(data){
                            loaderMessage.remove();
                            appendMessage(data.response, 'bot-message', false);
                        },
                        error: function(){
                            loaderMessage.find('.message-text').html('Error loading response.');
                        }
                    });
                });

                function appendMessage(message, className, isLoader = false) {
                    var processedMessage = message.replace(/^\nAnswer: /, '');
                    processedMessage_2 = processedMessage.trim();

                    var isCodeBlock = processedMessage_2.startsWith('```') && processedMessage_2.endsWith('```');

                    var messageContent;
                    if (isLoader) {
                        messageContent = '<div class="loader"></div>';
                    } else if (isCodeBlock) {
                        var codeContent = processedMessage_2.substring(3, processedMessage_2.length - 3);
                        messageContent = '<pre><code>' + codeContent + '</code></pre>';
                    } else {
                        // ??????
                        messageContent = processedMessage_2.replace(/\n/g, '<br>');
                    }

                    var messageElement = $('<div>').addClass('chat-message ' + className);
                    console.log(messageContent)
                    if(className.includes('user-message')) {
                    //     var iconHtml = '<div><i class="fa-solid fa-user"></i><span>You: </span></div>';
                    //     var textDiv = $('<div>').addClass('message-text').html(messageContent);
                    //     messageElement.append(iconHtml).append(textDiv);
                      messageElement.html(`
                        <div style="width: 100%; height: 100%; padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; flex-direction: column; justify-content: flex-start; align-items: flex-end; gap: 6px; display: inline-flex">
                          <div style="padding-left: 2px; padding-right: 2px; padding-top: 1px; padding-bottom: 1px; justify-content: flex-end; align-items: center; gap: 10px; display: inline-flex">
                            <div style="text-align: right; color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 20px">User
                                      <img src="{{ url_for('static', filename='images/user.png') }}"  alt="Image Description" style="vertical-align: middle;">
</div>
                          </div>
                          <div style="padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; border-radius: 18px; overflow: hidden; border: 2px #0097A9 solid; justify-content: center; align-items: center; gap: 19px; display: inline-flex">
                            <div style="width: 715px; color: #0097A9; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 18px">${messageContent}</div>
                          </div>
                        </div>
                      `);
                    }
                    else {
                        // var iconHtml = '<div><i class="fa-solid fa-robot"></i><span>Bot: </span></div>';
                        // var textDiv = $('<div>').addClass('message-text').html(messageContent);
                        // messageElement.append(iconHtml).append(textDiv);
                        messageElement.html(`
                        <div>
  <div style="width: 100%; height: 100%; padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex">
    <div style="transform: rotate(180deg); transform-origin: 0 0; justify-content: flex-start; align-items: flex-end; gap: 5px; display: inline-flex">
      <div style="transform: rotate(180deg); transform-origin: 0 0; text-align: right; color: #004F59; font-size: 22px;font-weight: 700; word-wrap: break-word; font-size: 20px">
                <img src="{{ url_for('static', filename='images/robot.png') }}"  alt="Image Description" style="vertical-align: middle;">
      Assistant
      </div>
      <div style="width: 34px; height: 34px; position: relative; transform: rotate(180deg); transform-origin: 0 0">
        <div style="width: 34px; height: 34px; left: 0px; top: 0px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: rgba(255, 255, 255, 0.01)"></div>
        <div style="width: 21.38px; height: 17.10px; left: -6.41px; top: 12.83px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: rgba(255, 255, 255, 0.01); border: 2px #004F59 solid"></div>
        <div style="width: 2.85px; height: 2.85px; left: -10.69px; top: 17.10px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: #69746D; border: 2px #004F59 solid"></div>
        <div style="width: 2.85px; height: 2.85px; left: -20.66px; top: 17.10px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: #69746D; border: 2px #004F59 solid"></div>
        <div style="width: 8.55px; height: 2.85px; left: -12.83px; top: 22.80px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: #69746D; border: 2px #004F59 solid"></div>
        <div style="width: 28.50px; height: 19.95px; left: -2.85px; top: 4.28px; position: absolute; transform: rotate(180deg); transform-origin: 0 0">
          <div style="width: 0px; height: 5.70px; left: -14.25px; top: 2.85px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
          <div style="width: 0px; height: 5.70px; left: -0px; top: 14.25px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
          <div style="width: 0px; height: 5.70px; left: -28.50px; top: 14.25px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
          <div style="width: 2.85px; height: 2.85px; left: -12.83px; top: 0px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 2px #004F59 solid"></div>
        </div>
      </div>
    </div>
    <div style="padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px;; background: #004F59; border-radius: 18px; overflow: hidden; justify-content: center; align-items: center; gap: 19px; display: inline-flex">
      <div style="width: 715px; text-align: justify; color: white; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 18px">${messageContent}</div>
    </div>
  </div>
</div>`)
                    }

                    $('#chatBox').append(messageElement);
                    $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
                    // updateChatEmptyState();

                    return messageElement;
                }

                function updateFileList(selectedDataset) {
                    return new Promise((resolve, reject) => {
                        if (!selectedDataset) {
                            $('#fileList').empty();
                            updateFileCount();
                            resolve();
                            return;
                        }
                        $.getJSON('../vectorstore/vsdb_log.json', function(data) {
                            var filteredFiles = data.filter(function(file) {
                                return file.dataset === selectedDataset;
                            });

                            $('#fileList').empty();

                            filteredFiles.forEach(function(file) {
                                var fileItem = $('<div>').addClass('file-item');
                                fileItem.html('<input type="checkbox"><span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                                // fileItem.html('<span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                                $('#fileList').append(fileItem);
                            });
                            updateFileCount();
                            resolve();
                        });
                    });
                }
                function addDatasetToLoaded(dataset) {
                    if (dataset && $('#loaded-dataset-selector').find('#dataset-' + dataset).length == 0) {
                        var datasetDiv = $('<div>').attr('id', 'dataset-' + dataset).addClass('loaded-dataset-item');
                        datasetDiv.html('<span class="dataset-name">' + dataset + '</span><button class="delete-dataset-btn">x</button>');
                        $('#loaded-dataset-selector').append(datasetDiv);
                    }
                }

                $(document).on('click', '.loaded-dataset-item', function() {
                    var selectedDataset = $(this).find('.dataset-name').text();
                    updateFileList(selectedDataset);
                });

                function updateFileList(selectedDataset) {
                       if (!selectedDataset) {
                            $('#fileList').empty();
                            updateFileCount();
                            return;
                        }
                    $.getJSON('../vectorstore/vsdb_log.json', function(data) {
                        var filteredFiles = data.filter(function(file) {
                            return file.dataset === selectedDataset;
                        });

                        $('#fileList').empty();

                        filteredFiles.forEach(function(file) {
                            var fileItem = $('<div>').addClass('file-item');
                            fileItem.html('<input type="checkbox"><span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                            // fileItem.html('<span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                            $('#fileList').append(fileItem);
                        });
                        updateFileCount();
                    });
                }

                updateFileList($('#dataset-selector').val());

                $('#chatBox').on('click', '.copy-btn', function() {
                    var message = $(this).siblings('.message-text').text();
                    navigator.clipboard.writeText(message).then(() => {
                        alert('Message copied!');
                    });
                });

                $('#uploadFileButton').on('click', function() {
                    $('#file-upload').click();
                });

                $('#file-upload').on('change', function() {
                    if (this.files.length > 0) {
                        var formData = new FormData();
                        // 将选中的文件添加到formData中
                        for (var i = 0; i < this.files.length; i++) {
                            formData.append('file', this.files[i]);
                        }
                        var selectedDataset = currentSelectedDataset;
                        formData.append('selectedDataset', selectedDataset);

                        $('#upload-progress-bar').css('width', '0%');
                        $('#upload-loader').show();
                        $('#upload-status-message').show().text('Uploading file...');

                        var xhr = new XMLHttpRequest();
                        xhr.open('POST', '/upload', true);

                        xhr.upload.onprogress = function(e) {
                            if (e.lengthComputable) {
                                var percentComplete = (e.loaded / e.total) * 100;
                                $('#upload-progress-bar').css('width', percentComplete + '%');

                                if (percentComplete === 100) {
                                    $('#upload-status-message').text('Processing file...');
                                }
                            }
                        };

                        xhr.onload = function() {
                            if (xhr.status === 200) {
                                $('#upload-status-message').text('File uploaded successfully');
                                $('#upload-loader .loader').hide();

                                Array.from(this.files).forEach(file => {
                                    const fileName = file.name;
                                    const newFileItem = $('<a href="#" class="list-group-item list-group-item-action d-flex align-items-center">').html(`
                                        <div class="me-2 d-flex align-items-center">
                                            <input class="form-check-input me-1" type="checkbox" value="" aria-label="...">
                                            <span class="fas fa-file"></span>
                                        </div>
                                        <span class="flex-grow-1">${fileName}</span>
                                        <label class="switch ms-auto select-switch">
                                            <input type="checkbox">
                                            <span class="slider round"></span>
                                        </label>
                                    `);
                                    newFileItem.appendTo('#fileList');
                                });
                            } else {
                                $('#upload-status-message').text('Error occurred during file upload');
                                $('#upload-loader .loader').hide();
                            }
                        }.bind(this);

                        xhr.onerror = function() {
                            $('#upload-status-message').text('Network error occurred during file upload');
                            $('#upload-loader .loader').hide();
                        };

                        xhr.send(formData);
                    }
                });
                $('#fileList').on('click', '.delete-btn', function() {
                    var fileName = $(this).siblings('.file-name').text();
                    var selectedDataset = $('#initial-dataset-selector').val(); // ??????????

                    $.ajax({
                        url: '/delete-file',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ file_name: fileName, dataset: selectedDataset }), // ????????
                        success: function(response) {
                            console.log(response.message);
                        },
                        error: function(xhr, status, error) {
                            console.log("Error: " + error);
                        }
                    });

                    $(this).parent('.file-item').remove();
                });

                $(document).on('click', '.loaded-dataset-item', function() {
                    var selectedDataset = $(this).find('.dataset-name').text();
                    updateFileList(selectedDataset);
                    updateFileCount();
                    $('#fileList').empty();

                    $.ajax({
                        url: '/selected-dataset',
                        type: 'POST',
                        contentType: 'application/json',
                        // data: { selected_dataset: selectedDataset },
                        data: JSON.stringify({ selectedDataset: selectedDataset }),
                        success: function(response) {
                            console.log('Selected dataset updated successfully:', response.message);
                        },
                        error: function(xhr, status, error) {
                            console.log("Error occurred while updating selected dataset:", error);
                        }
                    });
                });
                // function updateFormValues() {
                //     $('#form_mode').val($('#mode').val());
                //     $('#form_model').val($('#model').val());
                //     $('#form_rag').val($('#rag').val());
                // }
                function updateFormValues() {
                    var mode = $('#mode').val();
                    var model = $('#model').val();
                    var rag = $('#rag').val();

                    // ?? form ???????
                    $('#form_mode').val(mode);
                    $('#form_model').val(model);
                    $('#form_rag').val(rag);

                    // ??????
                    $('#display-mode').text(mode);
                    $('#display-model').text(model);
                    $('#display-rag').text(rag);
                }
                updateFormValues();

                $('#mode, #model, #rag').change(updateFormValues);

                updateFormValues();

                $('#instruct-selector').change(function() {
                    var selectedOption = $(this).val();

                    $.ajax({
                        url: '/select-instruct',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ selectedInstruct: selectedOption }),
                        success: function(response) {
                            console.log('Response from server:', response);
                        },
                        error: function(xhr, status, error) {
                            console.log('An error occurred:', error);
                        }
                    });
                });

                function loadInstructOptions() {
                    $.ajax({
                        url: '/get-prompt-templates',
                        type: 'GET',
                        success: function(data) {
                            var promptTemplates = data.prompt_template_list;
                            var instructSelector = $('#instruct-selector');
                            instructSelector.empty(); // ???????

                            instructSelector.append($('<option>', {
                                value: 'none',
                                text: 'No role selected'
                            }));

                            promptTemplates.forEach(function(template) {
                                instructSelector.append($('<option>', {
                                    value: template,
                                    text: template
                                }));
                            });
                        },
                        error: function() {
                            console.log('Error loading role options');
                        }
                    });
                }

                function updateDatasetCount() {
                    var count = $('#loaded-dataset-selector').children().length;
                    $('#loaded-dataset-count').text(count);
                }

                function updateFileCount() {
                    var count = $('#fileList').children().length;
                    $('#file-count').text(count);
                }

                $('#load-dataset-btn').click(function() {
                    var selectedDataset = $('#initial-dataset-selector').val();
                    addDatasetToLoaded(selectedDataset);
                    updateDatasetCount();
                });

                $(document).on('click', '.delete-dataset-btn', function() {
                    $(this).closest('.loaded-dataset-item').remove();
                    $('#fileList').empty();
                    updateDatasetCount();
                    updateFileCount();
                });

                $('#fileList').on('click', '.delete-btn', function() {
                    $(this).parent('.file-item').remove();
                    updateFileCount();
                });
                function addDatasetToLoadedIfNotExists(dataset) {
                    if (dataset && $('#loaded-dataset-selector').find('#dataset-' + dataset).length == 0) {
                        var datasetDiv = $('<div>').attr('id', 'dataset-' + dataset).addClass('loaded-dataset-item');
                        datasetDiv.html('<span class="dataset-name">' + dataset + '</span><button class="delete-dataset-btn">x</button>');
                        $('#loaded-dataset-selector').append(datasetDiv);
                        updateDatasetCount();
                    }
                }

                $('#select-all-files').change(function() {
                    var isChecked = $(this).is(':checked');
                    $('#fileList .file-item input[type="checkbox"]').prop('checked', isChecked);

                    if (isChecked) {
                        var selectedFiles = [];
                        $('#fileList .file-item input[type="checkbox"]:checked').each(function() {
                            selectedFiles.push($(this).siblings('.file-name').text());
                        });

                        $.ajax({
                            url: '/selected-files',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({ selectedFiles: selectedFiles }),
                            success: function(response) {
                                console.log('Selected files sent successfully', response);
                            },
                            error: function(xhr, status, error) {
                                console.error('Error: ' + error);
                            }
                        });
                    }
                });
                $('#toggle-options-btn').click(function() {
                    $('.options-container').slideToggle(); // ?? slideToggle ??????
                });
                $('#initial-dataset-selector').change(function() {
                    var selectedDataset = $(this).val();
                    updateFileList(selectedDataset)
                    updateDeleteButtonStatus();
                });

                $('#delete-dataset-btn').click(function() {
                    var selectedDataset = $('#initial-dataset-selector').val();
                    if(selectedDataset) {
                        $.ajax({
                            url: '/delete-dataset',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({ dataset: selectedDataset }), // ?????????
                            success: function(response) {
                                console.log('Dataset deleted successfully:', response);
                                loadInitialDatasets();
                                $('#initial-dataset-selector').val('');
                                $('#fileList').empty();
                                updateFileCount();
                            },
                            error: function(xhr, status, error) {
                                console.log("Error occurred while deleting dataset:", error);
                            }
                        });
                    } else {
                        alert('Please select a dataset to delete.');
                    }
                });
                $('#toggle-new-dataset-btn').click(function() {
                    $('#new-dataset-container').slideToggle();
                });

                $('#create-dataset-btn').click(function() {
                    var newDatasetName = $('#new-dataset-name').val().trim();
                    if(newDatasetName) {
                        $('#initial-dataset-selector').append('<option value="' + newDatasetName + '">' + newDatasetName + '</option>');
                        $('#initial-dataset-selector').val(newDatasetName);
                        $('#new-dataset-name').val('');
                        $('#new-dataset-container').slideUp();
                        var selectedDataset = $(this).val();
                        updateFileList(selectedDataset);
                        updateDeleteButtonStatus();
                    } else {
                        alert('Please enter a dataset name.');
                    }
                });
                $('#addChatHistoryButton').click(function() {
                    // Send an AJAX request to the server to create a new chat history
                    $.ajax({
                        url: '/create-new-chat-history',
                        type: 'POST',
                        success: function(response) {
                            // Handle success response, maybe update the UI to reflect the new chat history
                            console.log('New chat history added successfully:', response);
                            // Optionally, reload the chat history to include the new one
                            loadAllUsersChatHistory();
                        },
                        error: function(xhr, status, error) {
                            console.error("Error creating new chat history:", error);
                        }
                    });
                });

            });


    </script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
