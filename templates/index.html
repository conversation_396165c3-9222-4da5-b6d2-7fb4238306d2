<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG-CHATBOT</title>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
        }
        .chat-container {
            display: flex;
            width: 100%;
            max-width: 1200px;
            margin: auto;
        }
        .chat-box-container {
            flex-grow: 2;
            padding: 20px;
        }
        .settings-container {
            flex-grow: 1;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .chat-box {
            height: calc(100vh - 160px);
            overflow-y: auto;
            border: 1px solid #ddd;
            background: #fafafa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .chat-message {
            padding: 8px;
            margin: 5px 0;
            border-radius: 8px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
        }
        .user-message {
            text-align: right;
        }
        .chat-form {
            display: flex;
            position: relative;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .chat-form > input[type="text"] {
            flex-grow: 1;
            border: none;
            padding: 10px;
            border-radius: 0;
            height: 80px;
        }
        input[type="submit"] {
            position: absolute;
            right: 0;
            top: 0;
            background: none;
            border: none;
            padding: 0 15px;
            height: 100%;
            cursor: pointer;
        }
        input[type="submit"]:hover {
            background-color: #e5e5e5;
        }
        input[type="submit"]::after {
            content: '?';
            font-size: 16px;
            color: #4caf50;
        }
        /* Hide the original text of the submit button */
        input[type="submit"].visually-hidden {
            visibility: hidden;
        }
        #directory {
            display: none;
        }
        .upload-btn {
            display: inline-block;
            background-color: #4caf50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
        }
        #file-upload-status {
            margin-left: 10px;
            font-size: 14px;
        }
        .copy-btn {
            display: none;
            cursor: pointer;
            margin-left: 10px;
            color: #4caf50;
        }

        .chat-message:hover .copy-btn {
            display: inline;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-radius: 50%;
            border-top: 4px solid #3498db;
            width: 20px;
            height: 20px;
            animation: spin 2s linear infinite;
        }
        .user-input-textarea {
            flex-grow: 1;
            border: none;
            padding: 10px;
            border-radius: 0;
            height: 80px;
            resize: none;
        }
        .user-input-textarea:focus, .chat-form > input[type="text"]:focus {
            outline: none;
        }


        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-box-container">
<!--            <h1>RAG-CHATBOT</h1>-->
            <div id="chatBox" class="chat-box">
                <!-- Chat messages here -->
            </div>
            <form id="chatForm" class="chat-form" enctype="multipart/form-data">
                <textarea name="user_input" id="user_input" placeholder="Type your message..." class="user-input-textarea"></textarea>
                <input type="submit" value="Send">
            </form>
        </div>
        <div class="settings-container">
            <!-- Settings form elements here -->
            <select name="mode" id="mode" onchange="updateModelOptions()">
                <option value="online">Online</option>
                <option value="offline">Offline</option>
            </select>
            <select name="model" id="model">
                <option value="gpt-3.5">GPT-3.5</option>
                <option value="llama" style="display:none;">LLaMA-2-13B</option>
            </select>
            <select name="rag" id="rag">
                <option value="off">RAG Off</option>
                <option value="on">RAG On</option>
            </select>
            <div id="uploadedFilesContainer">
                <h3>Uploaded Files:</h3>
                <ul id="uploadedFilesList"></ul>
            </div>
            <label for="directory" class="upload-btn">+</label>
            <input type="file" name="directory" id="directory" multiple>
            <span id="file-upload-status"></span>
            <!-- Additional settings if any -->
        </div>
    </div>
    <script>
        $(document).ready(function(){
            function updateModelOptions() {
                var modeSelect = document.getElementById("mode");
                var modelSelect = document.getElementById("model");

                if (modeSelect.value === "online") {
                    modelSelect.options[0].style.display = "block"; // GPT-3.5
                    modelSelect.options[1].style.display = "none"; // LLAMA
                    modelSelect.value = "gpt-3.5";
                } else {
                    modelSelect.options[0].style.display = "none"; // GPT-3.5
                    modelSelect.options[1].style.display = "block"; // LLAMA
                    modelSelect.value = "llama";
                }
            }

            updateModelOptions();
            $('#mode').change(updateModelOptions);

            $('#chatForm').on('submit', function(e){
                e.preventDefault();
                var formData = new FormData(this);

                var mode = $('#mode').val();
                var model = $('#model').val();
                var rag = $('#rag').val();

                formData.append('mode', mode);
                formData.append('model', model);
                formData.append('rag', rag);

                var userMessage = $('#user_input').val();
                appendMessage(userMessage, 'user-message', false);
                var loaderMessage = appendMessage('', 'bot-message', true);

                $.ajax({
                    url: '/chat',
                    type: 'post',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data){
                        loaderMessage.remove();
                        appendMessage(data.response, 'bot-message', false);
                    },
                    error: function(){
                        loaderMessage.find('.message-text').html('Error loading response.');
                    }
                });
            });

            function appendMessage(message, className, isLoader = false) {
                var messageContent = isLoader ? '<div class="loader"></div>' : message;

                var messageElement = $('<div>').addClass('chat-message ' + className);

                if (isLoader) {
                    messageElement.html('<span class="message-text">' + messageContent + '</span>');
                } else {
                    messageElement.html('<span class="message-text">' + messageContent + '</span><span class="copy-btn">Copy</span>');
                }

                $('#chatBox').append(messageElement);
                $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);

                return messageElement;
            }

            $('#chatBox').on('click', '.copy-btn', function() {
                var message = $(this).siblings('.message-text').text();
                navigator.clipboard.writeText(message).then(() => {
                    alert('Message copied!');
                });
            });

            $('#directory').on('change', function() {
                if (this.files.length > 0) {
                    $('#file-upload-status').html('📄 ' + this.files.length + ' files selected');
                } else {
                    $('#file-upload-status').html('');
                }
                updateUploadedFilesList();
            });
            function updateUploadedFilesList() {
                $.getJSON('../log/vsdb_log.json', function(data) {
                    console.log(data); // ????????????
                    var filesList = $('#uploadedFilesList');
                    filesList.empty();

                    data.forEach(function(file) {
                        filesList.append('<li>' + file.file_name + '</li>');
                    });
                }).fail(function() {
                    console.log("Error: Unable to load JSON file.");
                });
            }


            updateUploadedFilesList();
            });

    </script>
</body>
</html>


