<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Login</title>
    <style>
        body, html {
            height: 100%;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f0f0;
        }

        #authOverlay {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .frame {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .frame img {
            margin-bottom: 20px;
            width: 300px;
            height: 66px;
        }

        .input-container {
            width: 547px;
            height: 52px;
            background: white;
            border-radius: 28px;
            border: 2px solid #0097A9;
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 0 10px;
        }

        .input-container input {
            width: 100%;
            border: none;
            outline: none;
            color: #2C2C2C;
            padding: 10px;
            font-size: 18px;
        }

        .input-container input[type="password"] {
            margin-right: 10px;
        }

        .input-container button {
            border: none;
            background-color: transparent;
            cursor: pointer;
        }

        .or {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            font-size: 16px;
            color: #2C2C2C;
        }

        .sso-button {
            padding: 10px 20px;
            font-size: 16px;
            background: #0097A9;
            color: white;
            border-radius: 25px;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.3s;
        }

        .sso-button:hover {
            background-color: #007a8a;
            transform: scale(1.05);
        }

        .sso-button:active {
            background-color: #005f65;
            transform: scale(0.95);
        }

    </style>
</head>
<body>
    <div id="authOverlay">
        <div class="frame">
            <img src="{{ url_for('static', filename='images/Deloitte.png') }}" alt="Deloitte Logo">
            <div class="input-container">
                <input type="text" placeholder="Email">
            </div>
            <div class="input-container">
                <input type="password" placeholder="Password">
                <button><i class="fa-solid fa-circle-right fa-2xl" style="color: #0097A9;"></i></button>
            </div>
            <div class="or">Or</div>
            <button class="sso-button" onclick="signIn()">SSO Sign In</button>
        </div>
    </div>

    <script>
        function signIn() {
            window.location.href = "{{ url_for('login') }}";
        }
    </script>
</body>
</html>
