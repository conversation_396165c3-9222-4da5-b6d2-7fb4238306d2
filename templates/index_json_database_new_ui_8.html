<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG-CHATBOT</title>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://kit.fontawesome.com/8e9c71f3b7.js" crossorigin="anonymous"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }
        .chat-container, .file-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        }
        .chat-container {
            width: 70%;
        }
        .file-container {
            width: 30%;
            margin-left: 5%;
        }
        .file-list {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            background: #fafafa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .chat-box {
            height: 850px;
            overflow-y: auto;
            border: 1px solid #ddd;
            background: #fafafa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .chat-message, .file-item {
            padding: 8px;
            margin: 5px 0;
            border-radius: 8px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
        }
        .user-message {
            text-align: left;
        }
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .file-name {
            flex-grow: 1;
            margin-left: 10px;
        }
        .file-form {
            display: flex;
            align-items: center;
                justify-content: flex-end; /* ???????? */

        }
        .chat-form, .file-form {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .chat-form > *, .file-form > * {
            flex: 1;
            min-width: 120px;
        }
        input[type="text"], input[type="file"], select {
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            height: 40px; /* ???? */
        }
        input[type="submit"], .delete-btn {
            background-color: #4caf50;
            color: white;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        input[type="submit"]:hover, .delete-btn:hover {
            background-color: #45a049;
        }
        input[type="text"] {
            flex-grow: 2;
        }
        input[type="submit"] {
            white-space: nowrap;
        }
        #directory {
            display: none;
        }
        .upload-btn {
            display: inline-block;
            background-color: #4caf50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
            min-width: 120px;
            height: 20px;
            line-height: 20px;
        }
        #file-upload-status {
            margin-left: 10px;
            font-size: 14px;
        }
        .copy-btn {
            display: none;
            cursor: pointer;
            margin-left: 10px;
            color: #4caf50;
        }
        .chat-message:hover .copy-btn {
            display: inline;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-radius: 50%;
            border-top: 4px solid #4caf50;
            width: 20px;
            height: 20px;
            animation: spin 2s linear infinite;
            display: inline-block;
            vertical-align: middle;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .options-container {
            margin-bottom: 20px;
        }
        .options-container select {
            margin-bottom: 10px;
            width: 20%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .loaded-dataset-item {
            background-color: #f0f0f0;
            padding: 8px;
            margin: 5px 0;
            border-radius: 8px;
            border: 1px solid #ddd;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /*.delete-dataset-btn {*/
        /*    color: white;*/
        /*    background-color: #f44336;*/
        /*    border: none;*/
        /*    padding: 5px 8px;*/
        /*    border-radius: 5px;*/
        /*    cursor: pointer;*/
        /*    margin-left: 10px;*/
        /*}*/

        .dataset-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            margin-top: 20px;
        }
        .dataset-container h2 {
            margin-top: 0;
        }
        .dataset-select-container {
            display: flex;
            width: 100%;
            margin-bottom: 10px;
        }
        #initial-dataset-selector {
            flex-grow: 1;
        }
        .load-button-container {
            width: 100%;
            text-align: center;
            margin-top: 10px;
        }

       .load-btn {
           display: block;
            width: 100%;
            background-color: #4caf50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
        }
       .instruct-select-container {
            width: 100%;
            margin-top: 10px;
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .instruct-select-container select {
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .chat-form {
            display: flex;
            flex-direction: column;
            align-items: stretch;
        }

        .input-container, .submit-container {
            width: 100%;
            margin-bottom: 10px;
            height: 40px;
        }

        input[type="text"] {
            width: 100%;
            box-sizing: border-box;
        }

        input[type="submit"] {
            width: auto;
            align-self: center;
        }

        .new-dataset-container input[type="text"] {
            margin-bottom: 10px;
        }

        .new-dataset-container {
            margin-bottom: 10px;
        }
        .small-title {
            font-size: 14px;
            margin-bottom: 10px;
        }

        .dataset-name {
            font-size: 12px;
        }

        .file-name {
            font-size: 12px;
        }
        #upload-status-message {
            margin-top: 10px;
            font-size: 12px;
            display: block;
            height: 20px;
            text-align: left;
            display: flex;
            align-items: center;
            margin-right: 10px;
        }
        #upload-status-container {
            display: flex;
            align-items: center;
            min-width: 120px;
            height: 40px;
        }
        /*.loader {*/
        /*    display: none;*/
        /*}*/
        .dataset-manage {
    display: flex;
    align-items: center;
    justify-content: flex-end; /* ???????? */
        }
        #delete-dataset-btn {
            margin-left: auto; /* ????????? */
            display: inline-block;
            background-color: #f44336;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
            min-width: 120px;
            height: 40px;
            line-height: 20px;
        }
        .checkbox-container {
            display: flex;
            align-items: center;
        }
        #toggle-options-btn {
            background-color: grey;
        }

        .dataset-select-container {
            width: 100%;
            margin-bottom: 10px;
        }

        #initial-dataset-selector {
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        button:disabled {
            background-color: grey;
            cursor: not-allowed;
        }
        .chat-history-container {
            width: 25%;
            margin-right: 5%;
            padding: 20px;
            border-radius: 10px;
            background: white;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        .chat-history-entry {
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f0f0f0;
            border-radius: 8px;
        }

        .chat-history-entry {
            cursor: pointer; /* ?????????????????????? */
            transition: background-color 0.3s ease; /* ?????????????? */
        }

        .chat-history-entry:hover {
            background-color: #f0f0f0; /* ?????????? */
        }

        .container {
            display: flex;
            justify-content: space-between;
            width: calc(100% - 40px); /* Adjusted width */
            margin: 0 auto;
            transition: margin 0.3s ease; /* Smooth transition for margin adjustments */
        }
        .chat-history-container, .file-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            transition: width 0.3s ease, padding 0.3s ease; /* Smooth transition */
        }
        .chat-history-container {
            width: 20%; /* Adjusted width */
        }
        .file-container {
            transition: width 0.3s ease, padding 0.3s ease; /* Apply transition for width and padding */
        }
        .container.collapsed-left .chat-history-container,
        .container.collapsed-right .file-container {
                    width: 0;
                    padding: 0; /* Optional, removes padding when collapsed */
        }
        .container.collapsed-right .file-container {
            width: 0;
            padding: 0; /* Ensure padding is set to 0 to fully hide the container */
            overflow: hidden; /* Prevent content from overflowing during transition */
        }
        .toggle-btn {
            position: fixed;
            text-align: center;
            /*min-width: 120px;*/
            min-width: auto;
            height: auto;
            top: 50%;
            z-index: 100;
            cursor: pointer;
            padding: 5px 10px;
            background-color: #4caf50;
            color: white;
            border: none;
            border-radius: 5px;
            transform: translateY(-50%);
        }
        .left-toggle {
            left: 0;
        }
        .right-toggle {
            right: 0;
        }
        .user-message {
            margin-left: auto;
            text-align: left;
            max-width: 50%;
            /*display: inline-block; !* ?????? *!*/
            background-color: #daf8cb;
        }

        .bot-message {
            background-color: #f0f0f0;
        }

        .human-icon, .bot-icon {
            margin-right: 10px;
            vertical-align: middle;
        }

        /*.human-icon::before, .bot-icon::before {*/
        /*    display: inline-block;*/
        /*    font-family: "Font Awesome 5 Free";*/
        /*    font-weight: 900;*/
        /*    content: "\f007"; !* ??????? *!*/
        /*    margin-bottom: 5px; !* ?????????????? *!*/
        /*}*/

        /*.bot-icon::before {*/
        /*    content: "\f013"; !* ?????????????? *!*/
        /*}*/
        .chat-message i {
            margin-right: 5px;
        }

        .user-message .message-text {
            font-size: 16px;
            background-color: #daf8cb;
            padding: 5px;
            border-radius: 5px;
            display: inline-block;
        }

        .bot-message .message-text {
            font-size: 16px;
            background-color: #f0f0f0;
            padding: 5px;
            border-radius: 5px;
            display: inline-block;
        }

        .input-group {
            display: flex;
            align-items: center;
    justify-content: space-between;
        }

        .input-group .icon-btn {
            position: absolute;
            left: 0;
            border: none;
            background: transparent;
            cursor: pointer;
            padding: 10px;
            color: #4caf50;
        }

        .input-group input[type="text"] {
            padding-left: 40px; /* ?????????????? */
            flex-grow: 2;
        }

        .input-group .send-btn {
            margin-left: auto; /* ????????? */
        }
        pre code {
    display: block;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 5px;
    color: #333;
    overflow-x: auto;
}
    </style>
</head>
<body>
    <div class="container">
<button id="toggle-chat-history" class="toggle-btn left-toggle">
    <i class="fas fa-arrow-left"></i> <!-- ??????? -->
</button>
        <div class="chat-history-container">
            <h2>Chat History</h2>
            <div id="chatHistoryBox" class="chat-box"></div>
        </div>
        <div class="chat-container">
            <h2>LLM-RAG</h2>

<!--            <div class="status">mode: test</div> -->

            <div id="chatBox" class="chat-box">
            </div>
            <div id="current-settings" style="margin-bottom: 10px;">
                <strong>Mode:</strong> <span id="display-mode">Offline</span> |
                <strong>Model:</strong> <span id="display-model">Mixtral-8x7B-Instruct</span> |
                <strong>RAG:</strong> <span id="display-rag">On</span>
            </div>
            <form id="chatForm" class="chat-form" enctype="multipart/form-data">
<!--                <div class="input-container">-->
<!--                    <input type="text" name="user_input" id="user_input" placeholder="Type your message..." required>-->
<!--                </div>-->
<!--                <div class="submit-container">-->
<!--                    <input type="submit" value="Send">-->
<!--                </div>-->
                <div class="input-container">
                    <div class="input-group">
<!--                        <button id="toggle-database-icon-btn" class="icon-btn"><i class="fa fa-database"></i></button>-->
                        <input type="text" name="user_input" id="user_input" placeholder="Type your message..." required>
<!--                        <button id="toggle-database-icon-btn" class="icon-btn"><i class="fa fa-database"></i></button>-->
                        <input type="submit" value="Send" class="send-btn">
                    </div>
                </div>
                <input type="hidden" name="mode" id="form_mode">
                <input type="hidden" name="model" id="form_model">
                <input type="hidden" name="rag" id="form_rag">
            </form>
        </div>
        <div class="file-container">
            <h2>Database</h2>
            <div class="dataset-select-container">
                <select id="initial-dataset-selector"></select>
            </div>

            <div id="initialDatasetPanel">
<!--                <h2>Available Datasets</h2>-->
                <div id="new-dataset-container" style="display: none;">
                    <input type="text" id="new-dataset-name" placeholder="Enter new dataset name">
                    <button id="create-dataset-btn" class="load-btn">Create Dataset</button>
                </div>
                <button id="toggle-new-dataset-btn" class="load-btn">+</button>


<!--                <div class="dataset-select-container">
                    <select id="initial-dataset-selector">
                    </select>
                </div>

                <div class="load-button-container">
                    <button id="load-dataset-btn" class="load-btn">Load Datasets</button>
                </div>
            </div>
            <h2 class="small-title">Loaded <span id="loaded-dataset-count">0</span> Datasets </h2>
-->

<!--            <div class="dataset-container">-->
<!--                <h2>Loaded Datasets</h2>-->
<!--                <div id="loaded-dataset-selector">-->
<!--                    <select id="initial-dataset-selector" ></select>-->
<!--                    &lt;!&ndash; Loaded datasets will be listed here &ndash;&gt;-->
<!--                </div>-->

<!--            </div>-->
<!--            <h2>File Manager</h2>-->
<!--            <h2 class="small-title">-->
<!--                Filelist: <span id="file-count">0</span> files-->
<!--            </h2>-->
<!--            <h2 class="small-title">-->
<!--                <input type="checkbox" id="select-all-files"> Select All-->
<!--            </h2>-->
            <div class="file-list" id="fileList">
                <!-- Files will be listed here -->
            </div>

            <div class="file-form">
                    <!-- Status Message -->
<!--                    <p id="upload-status-message"></p>-->
<!--                    <div id="upload-loader" style="display: none;">-->
<!--                        <div class="loader"></div>-->
<!--                    </div>-->
                    <div id="upload-status-container">
                        <p id="upload-status-message" style="display: none;"></p>
                        <div id="upload-loader" style="display: none;">
                            <div class="loader"></div>
                        </div>
                    </div>
                    <!-- Progress Bar -->
<!--                    <div id="upload-progress-container" style="display:none;">-->
<!--                        <div id="upload-progress-bar" style="width: 0%; height: 20px; background-color: #4caf50;"></div>-->
<!--                    </div>-->
                </div>
                <div class="dataset-manage">
                    <label for="file-upload" class="upload-btn">Upload Files</label>
                    <input type="file" name="file-upload" id="file-upload" multiple style="display: none;">
                    <button id="delete-dataset-btn" style="background-color: #f44336; display: none;">Delete Dataset</button>
                </div>
            </div>

            <button id="toggle-options-btn" class="load-btn"  style="margin-top: 300px;">Settings</button>
            <div class="options-container"  style="display: none;">
                <select name="mode" id="mode" onchange="updateModelOptions()">
                    <option value="online">Online</option>
                    <option value="offline" selected>Offline</option>
                </select>
                <select name="model" id="model">
                    <option value="gpt-3.5">GPT-3.5</option>
                    <option value="llama" style="display:none;">LLaMA-2-13B</option>
                    <option value="mixtral" style="display:none;" selected>Mixtral-8x7B-Instruct</option>
                    <option value="instructmixtral-1" style="display:none;">instructmixtral-1</option>
                </select>
                <select name="rag" id="rag">
                    <option value="off">RAG Off</option>
                    <option value="on" selected>RAG On</option>
                </select>
                <select id="instruct-selector"></select>
<!--                <div class="instruct-select-container">-->
<!--                    <select id="instruct-selector">-->
<!--                </select>-->
<!--                </div>-->
            </div>
        </div>


<!--        <button id="toggle-file-container" class="toggle-btn right-toggle">>></button>-->
<button id="toggle-file-container" class="toggle-btn right-toggle">
    <i class="fas fa-arrow-right"></i>
</button>
    </div>

    <script>
        $(document).ready(function(){

            $('#toggle-chat-history').click(function() {
                $('.container').toggleClass('collapsed-left');
                var isCollapsed = $('.container').hasClass('collapsed-left');
                $(this).find('i').toggleClass('fa-arrow-right fa-arrow-left'); // ????
            });

            $('#toggle-file-container').click(function() {
                $('.container').toggleClass('collapsed-right');
                var isCollapsed = $('.container').hasClass('collapsed-right');
                $(this).find('i').toggleClass('fa-arrow-left fa-arrow-right'); // ????
            });


            $('#toggle-database-icon-btn').click(function() {
                $('.container').toggleClass('collapsed-right');
            });

            $(document).on('click', '.chat-history-entry', function() {
                const userId = $(this).data('userid');
                console.log("User ID: ", userId);
                // ??????????
                $(this).css('background-color', '#e0e0e0'); // ????????
                setTimeout(() => {
                    $(this).css('background-color', ''); // ???????
                }, 2000); // 2000 ??????????????
            });
            $(document).on('click', '.chat-history-entry', function() {
                const userId = $(this).data('userid');
                // ??????
                $('#feedback').text('Loading user info...'); // ?????? ID ? feedback ???????????
                // ????????? AJAX ??
                setTimeout(() => {
                    $('#feedback').text(''); // ??????
                    // ??????????????
                }, 2000); // ????????
            });


            function loadAllUsersChatHistory() {
                $.ajax({
                    url: '/get-all-users-data',
                    type: 'GET',
                    success: function(data) {
                        const chatHistoryBox = $('#chatHistoryBox');
                        chatHistoryBox.empty(); // ??????
                        Object.keys(data).forEach(userId => {
                            const firstQuestion = data[userId];
                            const entryHtml = `
                                <div class="chat-history-entry" data-userId="${userId}">
                                    <div><strong>User ID:</strong> ${userId}</div>
                                    <div><strong>First Question:</strong> ${firstQuestion}</div>
                                </div>
                            `;
                            chatHistoryBox.append(entryHtml);
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error("Failed to load all users' chat history:", error);
                    }
                });
            }

            $('#chatHistoryBox').on('click', '.chat-history-entry', function() {
                console.log("Clicked on: ", this);
                const userId = $(this).data('userid');
                console.log("test " + userId);
                if (userId) {
                    console.log(userId);
                    loadUserChatHistory(userId);
                } else {
                    console.error("User ID is undefined.");
                }
            });

            loadAllUsersChatHistory();

            function loadUserChatHistory(userId) {
                $.ajax({
                    url: '/get-user-data/' + userId,
                    type: 'GET',
                    success: function(data) {
                        const chatBox = $('#chatBox');
                        chatBox.empty();
                        Object.values(data).forEach(entry => {
                            // ?? .replace(/\n/g, '<br>') ???????
                            const formattedQMsg = entry.q_msg.replace(/\n/g, '<br>');
                            const formattedAMsg = entry.a_msg.replace(/\n/g, '<br>');

                            const messageHtml = `
                                <div class="chat-message user-message">
                                    <div>${formattedQMsg}</div>
                                </div>
                                <div class="chat-message">
                                    <div>${formattedAMsg}</div>
                                </div>
                            `;
                            chatBox.append(messageHtml);
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error("Failed to load user's chat history:", error);
                    }
                });
            }

            function loadInitialDatasets() {
                $.getJSON('../vectorstore/vsdb_log.json', function (data) {
                    let datasets = new Set(data.map(item => item.dataset));

                    $('#initial-dataset-selector').empty().append('<option value="">No dataset selected</option>');

                    datasets.forEach(dataset => {
                        $('#initial-dataset-selector').append('<option value="' + dataset + '">' + dataset + '</option>');
                    });
                });
            }
            // function loadInitialDatasets() {
            //     $.getJSON('/get-datasets', function (data) {
            //         let datasets = new Set(data.map(item => item.dataset_name)); // Use dataset_name as per your backend structure
            //
            //         $('#initial-dataset-selector').empty().append('<option value="">No dataset selected</option>');
            //
            //         datasets.forEach(dataset => {
            //             $('#initial-dataset-selector').append('<option value="' + dataset + '">' + dataset + '</option>');
            //         });
            //     });
            // }

            $('#model').val('mixtral');
            function updateDeleteButtonStatus() {
                var selectedDataset = $('#initial-dataset-selector').val();
                if(selectedDataset) {
                    $('#delete-dataset-btn').css('background-color', '#f44336').prop('disabled', false);
                } else {
                    $('#delete-dataset-btn').css('background-color', 'grey').prop('disabled', true);
                }
            }

            updateDeleteButtonStatus();

            loadInitialDatasets();

            loadInstructOptions();

            updateDatasetCount();

            updateFileCount();

            $('#delete-dataset-btn').show();

            $('#load-dataset-btn').click(function() {
                var selectedDataset = $('#initial-dataset-selector').val();
                addDatasetToLoaded(selectedDataset);
            });

            $(document).on('click', '.delete-dataset-btn', function() {
                $(this).closest('.loaded-dataset-item').remove();
            });

            $(document).on('click', '.loaded-dataset-item', function() {
                var selectedDataset = $(this).text();
                updateFileList(selectedDataset);
            });
            function updateModelOptions() {
                var modeSelect = document.getElementById("mode");
                var modelSelect = document.getElementById("model");

                if (modeSelect.value === "online") {
                    modelSelect.options[0].style.display = "block"; // GPT-3.5
                    modelSelect.options[1].style.display = "none"; // LLAMA
                    modelSelect.options[2].style.display = "none"; // Mixtral-8x7B-Instruct
                    modelSelect.options[3].style.display = "none";  // instructmixtral-1
                    modelSelect.value = "gpt-3.5";
                } else {
                    modelSelect.options[0].style.display = "none"; // GPT-3.5
                    modelSelect.options[1].style.display = "block"; // LLAMA
                    modelSelect.options[2].style.display = "block";  // Mixtral-8x7B-Instruct
                    modelSelect.options[3].style.display = "block";  // instructmixtral-1
                    modelSelect.value = "mixtral";
                }
            }

            updateModelOptions();
            $('#mode').change(updateModelOptions);

            $('#chatForm').on('submit', function(e){
                e.preventDefault();
                var formData = new FormData(this);
                var ragStatus = $('#rag').val();
                var useOriginalText = $('#useOriginalText').is(':checked');
                formData.append('useOriginalText', useOriginalText); // ??useOriginalText??
                var userMessage = $('#user_input').val();
                appendMessage(userMessage, 'user-message', false);
                var loaderMessage = appendMessage('', 'bot-message', true);

                var selectedDataset = $('#initial-dataset-selector').val();
                if (ragStatus === 'on' && !selectedDataset) {
                    alert('No dataset selected. Using RAG Off mode.');
                    formData.append('rag', 'off'); // ? RAG ????? 'off'
                } else {
                    formData.append('rag', ragStatus); // ??????? RAG ??
                }
                formData.append('selectedDataset', selectedDataset); // ??useOriginalText??

                $.ajax({
                    url: '/chat',
                    type: 'post',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data){
                        loaderMessage.remove();
                        appendMessage(data.response, 'bot-message', false);
                    },
                    error: function(){
                        loaderMessage.find('.message-text').html('Error loading response.');
                    }
                });
            });

            // function appendMessage(message, className, isLoader = false) {
            //         var processedMessage = message.replace(/^\nAnswer: /, '');
            //         processedMessage_2 = processedMessage.trim();
            //
            //
            //         var messageContent = isLoader ? '<div class="loader"></div>' : processedMessage_2.replace(/\n/g, '<br>');
            //
            //         var messageElement = $('<div>').addClass('chat-message ' + className);
            //
            //          if(className.includes('user-message')) {
            //                 var iconHtml = '<div><i class="fa-solid fa-user"></i><span>You: </span></div>';
            //                 var textDiv = $('<div>').addClass('message-text').html(messageContent);
            //                 messageElement.append(iconHtml).append(textDiv);
            //          } else {
            //                 var iconHtml = '<div><i class="fa-solid fa-robot"></i><span>Bot: </span></div>';
            //                 var textDiv = $('<div>').addClass('message-text').html(messageContent);
            //                 messageElement.append(iconHtml).append(textDiv);
            //          }
            //
            //         $('#chatBox').append(messageElement);
            //         $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
            //
            //         return messageElement;
            // }

            function appendMessage(message, className, isLoader = false) {
                var processedMessage = message.replace(/^\nAnswer: /, '');
                processedMessage_2 = processedMessage.trim();

                // ??????????
                var isCodeBlock = processedMessage_2.startsWith('```') && processedMessage_2.endsWith('```');

                // ????????
                var messageContent;
                if (isLoader) {
                    messageContent = '<div class="loader"></div>';
                } else if (isCodeBlock) {
                    // ????????
                    var codeContent = processedMessage_2.substring(3, processedMessage_2.length - 3);
                    // ??<pre>?<code>????????
                    messageContent = '<pre><code>' + codeContent + '</code></pre>';
                } else {
                    // ??????
                    messageContent = processedMessage_2.replace(/\n/g, '<br>');
                }

                var messageElement = $('<div>').addClass('chat-message ' + className);

                if(className.includes('user-message')) {
                    var iconHtml = '<div><i class="fa-solid fa-user"></i><span>You: </span></div>';
                    var textDiv = $('<div>').addClass('message-text').html(messageContent);
                    messageElement.append(iconHtml).append(textDiv);
                } else {
                    var iconHtml = '<div><i class="fa-solid fa-robot"></i><span>Bot: </span></div>';
                    var textDiv = $('<div>').addClass('message-text').html(messageContent);
                    messageElement.append(iconHtml).append(textDiv);
                }

                $('#chatBox').append(messageElement);
                $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);

                return messageElement;
            }

            function updateFileList(selectedDataset) {
                return new Promise((resolve, reject) => {
                    if (!selectedDataset) {
                        $('#fileList').empty();
                        updateFileCount();
                        resolve();
                        return;
                    }
                    $.getJSON('../vectorstore/vsdb_log.json', function(data) {
                        var filteredFiles = data.filter(function(file) {
                            return file.dataset === selectedDataset;
                        });

                        $('#fileList').empty();

                        filteredFiles.forEach(function(file) {
                            var fileItem = $('<div>').addClass('file-item');
                            // fileItem.html('<input type="checkbox"><span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                            fileItem.html('<span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                            $('#fileList').append(fileItem);
                        });
                        updateFileCount();
                        resolve();
                    });
                });
            }
            function addDatasetToLoaded(dataset) {
                if (dataset && $('#loaded-dataset-selector').find('#dataset-' + dataset).length == 0) {
                    var datasetDiv = $('<div>').attr('id', 'dataset-' + dataset).addClass('loaded-dataset-item');
                    datasetDiv.html('<span class="dataset-name">' + dataset + '</span><button class="delete-dataset-btn">x</button>');
                    $('#loaded-dataset-selector').append(datasetDiv);
                }
            }

            $(document).on('click', '.loaded-dataset-item', function() {
                var selectedDataset = $(this).find('.dataset-name').text();
                updateFileList(selectedDataset);
            });



            // Function to load files from JSON
            function loadFilesFromJson() {
                $.getJSON('../vectorstore/vsdb_log.json', function(data) {
                    var uniqueFiles = new Set();
                    data.forEach(function(file) {
                        uniqueFiles.add(file.filename);
                    });

                    uniqueFiles.forEach(function(fileName) {
                        var fileItem = $('<div>').addClass('file-item');
                        // fileItem.html('<input type="checkbox"><span class="file-name">' + fileName + '</span><button class="delete-btn">Delete</button>');
                        fileItem.html('<span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                        $('#fileList').append(fileItem);
                    });
                });
            }

            // loadFilesFromJson();

            function updateFileList(selectedDataset) {
                   if (!selectedDataset) {
                        $('#fileList').empty();
                        updateFileCount();
                        return;
                    }
                $.getJSON('../vectorstore/vsdb_log.json', function(data) {
                    var filteredFiles = data.filter(function(file) {
                        return file.dataset === selectedDataset;
                    });

                    $('#fileList').empty();

                    filteredFiles.forEach(function(file) {
                        var fileItem = $('<div>').addClass('file-item');
                        // fileItem.html('<input type="checkbox"><span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                        fileItem.html('<span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');
                        $('#fileList').append(fileItem);
                    });
                    updateFileCount();
                });
            }

            updateFileList($('#dataset-selector').val());

            $('#chatBox').on('click', '.copy-btn', function() {
                var message = $(this).siblings('.message-text').text();
                navigator.clipboard.writeText(message).then(() => {
                    alert('Message copied!');
                });
            });

            $('#file-upload').on('change', function() {
                if (this.files.length > 0) {
                    var formData = new FormData();
                    for (var i = 0; i < this.files.length; i++) {
                        formData.append('file', this.files[i]);
                    }

                    var selectedDataset = $('#initial-dataset-selector').val();
                    formData.append('selectedDataset', selectedDataset);

                    // $('#delete-dataset-btn').hide();
                    $('#upload-progress-bar').css('width', '0%');
                    $('#upload-loader').show();
                    $('#upload-status-message').show().text('Uploading file...');

                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', '/upload', true);

                    xhr.upload.onprogress = function(e) {
                        if (e.lengthComputable) {
                            var percentComplete = (e.loaded / e.total) * 100;
                            $('#upload-progress-bar').css('width', percentComplete + '%');

                            if (percentComplete === 100) {
                                $('#upload-status-message').text('Uploading file...');
                                $('#upload-loader .loader').show();
                                $('#upload-progress-bar').css('background-color', '#FFD700');
                            }
                        }
                    };

                    xhr.onload = function() {
                        if (xhr.status == 200) {
                            $('#upload-status-message').text('File uploaded successfully');
                            $('#upload-loader .loader').hide();

                            updateFileList(selectedDataset).then(() => {
                                setTimeout(function() {
                                    $('#delete-dataset-btn').show();
                                }, 3000);
                            });

                            addDatasetToLoadedIfNotExists(selectedDataset);
                        } else {
                            $('#upload-status-message').text('Error occurred during file upload');
                            $('#upload-loader .loader').hide();
                        }
                    };

                    xhr.onerror = function() {
                        $('#upload-status-message').text('Error occurred during file upload');
                        $('#upload-progress-container').hide();
                    };
                    updateFileCount();

                    xhr.send(formData);
                }
            });


            $('#fileList').on('click', '.delete-btn', function() {
                var fileName = $(this).siblings('.file-name').text();
                var selectedDataset = $('#initial-dataset-selector').val(); // ??????????

                $.ajax({
                    url: '/delete-file',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ file_name: fileName, dataset: selectedDataset }), // ????????
                    success: function(response) {
                        console.log(response.message);
                    },
                    error: function(xhr, status, error) {
                        console.log("Error: " + error);
                    }
                });

                $(this).parent('.file-item').remove();
            });

            $('#fileList').on('change', 'input[type="checkbox"]', function() {
            var selectedFiles = [];
            $('#fileList .file-item input[type="checkbox"]:checked').each(function() {
                selectedFiles.push($(this).siblings('.file-name').text());
            });

            $.ajax({
                url: '/selected-files',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ selectedFiles: selectedFiles }),
                success: function(response) {
                    console.log('Selected files sent successfully');
                },
                error: function(xhr, status, error) {
                    console.log('Error: ' + error);
                }
            });
        });

            $(document).on('click', '.loaded-dataset-item', function() {
                var selectedDataset = $(this).find('.dataset-name').text();
                updateFileList(selectedDataset);
                updateFileCount();
                $('#fileList').empty();

                $.ajax({
                    url: '/selected-dataset',
                    type: 'POST',
                    contentType: 'application/json',
                    // data: { selected_dataset: selectedDataset },
                    data: JSON.stringify({ selectedDataset: selectedDataset }),
                    success: function(response) {
                        console.log('Selected dataset updated successfully:', response.message);
                    },
                    error: function(xhr, status, error) {
                        console.log("Error occurred while updating selected dataset:", error);
                    }
                });
            });
            // function updateFormValues() {
            //     $('#form_mode').val($('#mode').val());
            //     $('#form_model').val($('#model').val());
            //     $('#form_rag').val($('#rag').val());
            // }
            function updateFormValues() {
                var mode = $('#mode').val();
                var model = $('#model').val();
                var rag = $('#rag').val();

                // ?? form ???????
                $('#form_mode').val(mode);
                $('#form_model').val(model);
                $('#form_rag').val(rag);

                // ??????
                $('#display-mode').text(mode);
                $('#display-model').text(model);
                $('#display-rag').text(rag);
            }
            updateFormValues();

            $('#mode, #model, #rag').change(updateFormValues);

            updateFormValues();

            $('#instruct-selector').change(function() {
                var selectedOption = $(this).val();

                $.ajax({
                    url: '/select-instruct',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ selectedInstruct: selectedOption }),
                    success: function(response) {
                        console.log('Response from server:', response);
                    },
                    error: function(xhr, status, error) {
                        console.log('An error occurred:', error);
                    }
                });
            });

            function loadInstructOptions() {
                $.ajax({
                    url: '/get-prompt-templates',
                    type: 'GET',
                    success: function(data) {
                        var promptTemplates = data.prompt_template_list;
                        var instructSelector = $('#instruct-selector');
                        instructSelector.empty(); // ???????

                        instructSelector.append($('<option>', {
                            value: 'none',
                            text: 'No role selected'
                        }));

                        promptTemplates.forEach(function(template) {
                            instructSelector.append($('<option>', {
                                value: template,
                                text: template
                            }));
                        });
                    },
                    error: function() {
                        console.log('Error loading role options');
                    }
                });
            }

            // $('#create-dataset-btn').click(function() {
            //     var newDatasetName = $('#new-dataset-name').val().trim();
            //     if(newDatasetName) {
            //         // Add the new dataset to the selector
            //         $('#initial-dataset-selector').append('<option value="' + newDatasetName + '">' + newDatasetName + '</option>');
            //         // Select the new dataset
            //         $('#initial-dataset-selector').val(newDatasetName);
            //     } else {
            //         alert('Please enter a dataset name.');
            //     }
            // });

            function updateDatasetCount() {
                var count = $('#loaded-dataset-selector').children().length;
                $('#loaded-dataset-count').text(count);
            }

            function updateFileCount() {
                var count = $('#fileList').children().length;
                $('#file-count').text(count);
            }

            $('#load-dataset-btn').click(function() {
                var selectedDataset = $('#initial-dataset-selector').val();
                addDatasetToLoaded(selectedDataset);
                updateDatasetCount();
            });

            $(document).on('click', '.delete-dataset-btn', function() {
                $(this).closest('.loaded-dataset-item').remove();
                $('#fileList').empty();
                updateDatasetCount();
                updateFileCount();
            });

            $('#fileList').on('click', '.delete-btn', function() {
                $(this).parent('.file-item').remove();
                updateFileCount();
            });
            function addDatasetToLoadedIfNotExists(dataset) {
                if (dataset && $('#loaded-dataset-selector').find('#dataset-' + dataset).length == 0) {
                    var datasetDiv = $('<div>').attr('id', 'dataset-' + dataset).addClass('loaded-dataset-item');
                    datasetDiv.html('<span class="dataset-name">' + dataset + '</span><button class="delete-dataset-btn">x</button>');
                    $('#loaded-dataset-selector').append(datasetDiv);
                    updateDatasetCount();
                }
            }

            $('#select-all-files').change(function() {
                var isChecked = $(this).is(':checked');
                $('#fileList .file-item input[type="checkbox"]').prop('checked', isChecked);

                if (isChecked) {
                    var selectedFiles = [];
                    $('#fileList .file-item input[type="checkbox"]:checked').each(function() {
                        selectedFiles.push($(this).siblings('.file-name').text());
                    });

                    $.ajax({
                        url: '/selected-files',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ selectedFiles: selectedFiles }),
                        success: function(response) {
                            console.log('Selected files sent successfully', response);
                        },
                        error: function(xhr, status, error) {
                            console.error('Error: ' + error);
                        }
                    });
                }
            });
            $('#toggle-options-btn').click(function() {
                $('.options-container').slideToggle(); // ?? slideToggle ??????
            });
            $('#initial-dataset-selector').change(function() {
                var selectedDataset = $(this).val();
                updateFileList(selectedDataset)
                updateDeleteButtonStatus();
            });

            $('#delete-dataset-btn').click(function() {
                var selectedDataset = $('#initial-dataset-selector').val();
                if(selectedDataset) {
                    $.ajax({
                        url: '/delete-dataset',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ dataset: selectedDataset }), // ?????????
                        success: function(response) {
                            console.log('Dataset deleted successfully:', response);
                            loadInitialDatasets();
                            $('#initial-dataset-selector').val('');
                            $('#fileList').empty();
                            updateFileCount();
                        },
                        error: function(xhr, status, error) {
                            console.log("Error occurred while deleting dataset:", error);
                        }
                    });
                } else {
                    alert('Please select a dataset to delete.');
                }
            });
            $('#toggle-new-dataset-btn').click(function() {
                $('#new-dataset-container').slideToggle();
            });

            $('#create-dataset-btn').click(function() {
                var newDatasetName = $('#new-dataset-name').val().trim();
                if(newDatasetName) {
                    $('#initial-dataset-selector').append('<option value="' + newDatasetName + '">' + newDatasetName + '</option>');
                    $('#initial-dataset-selector').val(newDatasetName);
                    $('#new-dataset-name').val('');
                    $('#new-dataset-container').slideUp();
                    var selectedDataset = $(this).val();
                    updateFileList(selectedDataset);
                    updateDeleteButtonStatus();
                } else {
                    alert('Please enter a dataset name.');
                }
            });

        });


    </script>
</body>
</html>