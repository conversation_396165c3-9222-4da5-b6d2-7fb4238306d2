#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复MinerU安装问题
解决导入失败和包装脚本错误
"""

import os
import sys
import subprocess
import tempfile

def get_venv_python(venv_path):
    """获取虚拟环境的Python路径"""
    if os.name == 'nt':  # Windows
        return os.path.join(venv_path, 'Scripts', 'python.exe')
    else:  # Linux/Mac
        return os.path.join(venv_path, 'bin', 'python')

def get_venv_pip(venv_path):
    """获取虚拟环境的pip路径"""
    if os.name == 'nt':  # Windows
        return os.path.join(venv_path, 'Scripts', 'pip.exe')
    else:  # Linux/Mac
        return os.path.join(venv_path, 'bin', 'pip')

def diagnose_mineru_installation(venv_path):
    """诊断MinerU安装问题"""
    print("🔍 诊断MinerU安装问题...")
    
    python_path = get_venv_python(venv_path)
    
    # 检查已安装的包
    check_packages_code = '''
import pkg_resources
import sys

print("📦 已安装的包:")
installed_packages = [d.project_name for d in pkg_resources.working_set]
for pkg in sorted(installed_packages):
    if any(keyword in pkg.lower() for keyword in ['magic', 'pdf', 'torch', 'transformers']):
        try:
            version = pkg_resources.get_distribution(pkg).version
            print(f"  {pkg}: {version}")
        except:
            print(f"  {pkg}: unknown version")

print("\\n🔍 尝试导入MinerU相关模块:")
modules_to_test = [
    'magic_pdf',
    'magic_pdf.api',
    'magic_pdf.api.magic_pdf_parse',
    'magic_pdf.pipe',
    'magic_pdf.pipe.UNIPipe'
]

for module in modules_to_test:
    try:
        __import__(module)
        print(f"  ✅ {module}")
    except ImportError as e:
        print(f"  ❌ {module}: {e}")
'''
    
    try:
        result = subprocess.run([python_path, '-c', check_packages_code], 
                              capture_output=True, text=True, timeout=60)
        print(result.stdout)
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
    except Exception as e:
        print(f"❌ 诊断失败: {str(e)}")

def try_alternative_installation(venv_path):
    """尝试替代安装方法"""
    print("\n🔧 尝试替代安装方法...")
    
    pip_path = get_venv_pip(venv_path)
    
    # 尝试不同的安装方法
    alternative_packages = [
        'magic-pdf',  # 不带[full]
        'magic-pdf[base]',  # 基础版本
        'git+https://github.com/opendatalab/MinerU.git',  # 从GitHub安装
    ]
    
    for package in alternative_packages:
        print(f"📦 尝试安装: {package}")
        try:
            result = subprocess.run([pip_path, 'install', '--upgrade', package], 
                                  capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                print(f"✅ {package} 安装成功")
                
                # 测试导入
                if test_import_after_install(venv_path):
                    print("✅ 导入测试成功!")
                    return True
            else:
                print(f"❌ {package} 安装失败")
                if result.stderr:
                    print(f"错误: {result.stderr[:200]}...")
                    
        except subprocess.TimeoutExpired:
            print(f"⏰ {package} 安装超时")
        except Exception as e:
            print(f"❌ {package} 安装异常: {str(e)}")
    
    return False

def test_import_after_install(venv_path):
    """安装后测试导入"""
    python_path = get_venv_python(venv_path)
    
    test_code = '''
success = False
try:
    from magic_pdf.api.magic_pdf_parse import magic_pdf_parse
    print("✅ magic_pdf.api.magic_pdf_parse 导入成功")
    success = True
except ImportError:
    try:
        from magic_pdf.pipe.UNIPipe import UNIPipe
        print("✅ magic_pdf.pipe.UNIPipe 导入成功")
        success = True
    except ImportError:
        try:
            import magic_pdf
            print("✅ magic_pdf 基础模块导入成功")
            success = True
        except ImportError:
            print("❌ 所有导入尝试都失败")

print("SUCCESS" if success else "FAILED")
'''
    
    try:
        result = subprocess.run([python_path, '-c', test_code], 
                              capture_output=True, text=True, timeout=30)
        print(result.stdout)
        return "SUCCESS" in result.stdout
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def create_simple_wrapper(venv_path):
    """创建简化的包装脚本"""
    print("\n🔧 创建简化包装脚本...")
    
    python_path = get_venv_python(venv_path)
    
    wrapper_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的MinerU虚拟环境包装器
"""

import subprocess
import sys
import json
import tempfile
import os

VENV_PYTHON = r"{python_path}"

def process_pdf_in_venv(pdf_path, output_dir=None):
    """在虚拟环境中处理PDF"""
    if output_dir is None:
        output_dir = tempfile.mkdtemp()
    
    # 创建简单的处理脚本
    script_content = """
import sys
import json
import os

def main():
    pdf_path = sys.argv[1] if len(sys.argv) > 1 else None
    output_dir = sys.argv[2] if len(sys.argv) > 2 else "/tmp"
    
    if not pdf_path:
        print("ERROR")
        print("No PDF path provided")
        return
    
    try:
        # 尝试导入MinerU
        magic_pdf_parse = None
        
        try:
            from magic_pdf.api.magic_pdf_parse import magic_pdf_parse
        except ImportError:
            try:
                from magic_pdf.pipe.UNIPipe import UNIPipe
                def magic_pdf_parse(pdf_path, output_dir, **kwargs):
                    pipe = UNIPipe(pdf_path, output_dir)
                    return pipe.pipe_parse()
            except ImportError:
                print("ERROR")
                print("无法导入MinerU")
                return
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 简单的处理调用
        result = magic_pdf_parse(pdf_path, output_dir)
        
        if result:
            print("SUCCESS")
            print(json.dumps({{"status": "success", "data": str(result)}}, ensure_ascii=False))
        else:
            print("FAILED")
            print("No result returned")
            
    except Exception as e:
        print("ERROR")
        print(str(e))

if __name__ == "__main__":
    main()
"""
    
    # 写入临时脚本文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
        f.write(script_content)
        script_path = f.name
    
    try:
        # 在虚拟环境中执行
        result = subprocess.run([VENV_PYTHON, script_path, pdf_path, output_dir], 
                              capture_output=True, text=True, timeout=300)
        
        lines = result.stdout.strip().split('\\n')
        if lines and lines[0] == "SUCCESS":
            return {{"success": True, "data": lines[1] if len(lines) > 1 else None}}, None
        elif lines and lines[0] == "FAILED":
            return None, lines[1] if len(lines) > 1 else "Unknown error"
        elif lines and lines[0] == "ERROR":
            return None, lines[1] if len(lines) > 1 else "Unknown error"
        else:
            return None, "Unexpected output format"
            
    except subprocess.TimeoutExpired:
        return None, "Processing timeout"
    except Exception as e:
        return None, str(e)
    finally:
        try:
            os.unlink(script_path)
        except:
            pass

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python mineru_simple_wrapper.py <pdf_path>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    result, error = process_pdf_in_venv(pdf_path)
    
    if result:
        print("✅ 处理成功")
        print(f"结果: {{result}}")
    else:
        print(f"❌ 处理失败: {{error}}")
'''
    
    wrapper_path = "mineru_simple_wrapper.py"
    with open(wrapper_path, 'w', encoding='utf-8') as f:
        f.write(wrapper_content)
    
    print(f"✅ 简化包装脚本已创建: {wrapper_path}")
    return wrapper_path

def main():
    """主修复流程"""
    print("🔧 MinerU安装修复脚本")
    print("=" * 50)
    
    venv_path = os.path.join(os.getcwd(), "mineru_venv")
    
    if not os.path.exists(venv_path):
        print("❌ 虚拟环境不存在，请先运行 safe_install_mineru.py")
        return False
    
    # 1. 诊断问题
    diagnose_mineru_installation(venv_path)
    
    # 2. 尝试替代安装
    print("\\n" + "=" * 30)
    if try_alternative_installation(venv_path):
        print("🎉 替代安装成功!")
    else:
        print("⚠️  替代安装也失败了")
    
    # 3. 创建简化包装脚本
    print("\\n" + "=" * 30)
    create_simple_wrapper(venv_path)
    
    # 4. 最终测试
    print("\\n🧪 最终测试...")
    if test_import_after_install(venv_path):
        print("✅ 修复成功! MinerU现在可以使用了")
        return True
    else:
        print("⚠️  MinerU仍然无法正常导入")
        print("💡 建议:")
        print("1. 删除虚拟环境: rm -rf mineru_venv")
        print("2. 尝试不同的Python版本")
        print("3. 或者不使用MinerU，v7版本会自动回退")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\\n🎉 修复完成! 现在可以运行:")
            print("python start_v7.py")
        else:
            print("\\n⚠️  修复未完全成功，但v7版本仍可正常使用")
    except KeyboardInterrupt:
        print("\\n\\n⚠️  修复被用户中断")
    except Exception as e:
        print(f"\\n\\n❌ 修复过程出现错误: {str(e)}")
