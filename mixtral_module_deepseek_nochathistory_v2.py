"""

    Module Name :           mixtral_module
    Last Modified Date :    3 Jan 2024

"""
import subprocess
from typing import Any, AsyncGenerator
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableParallel
import transformers
from datetime import datetime

# Import Self-defined Modules
import preprocessing
from langchain_openai import ChatOpenAI

# langchain.debug = True
# init mixtral pipeline
def init_mixtral_piepeline(model_para):
    pipeline = transformers.pipeline(
        model_para["task"],
        model=model_para["model"],
        device_map=model_para["device_map"],
        model_kwargs={"torch_dtype": eval(model_para["torch_dtype"])},
        max_new_tokens=model_para["max_new_tokens"],
        repetition_penalty=model_para["repetition_penalty"],
        return_full_text=eval(model_para["return_full_text"]),
        pad_token_id=2,
        eos_token_id=2
    )
    return pipeline

# 用于清理文本，确保其为有效的UTF-8编码
def sanitize_text(text):
    if text is None:
        return ""
    try:
        # 尝试编码再解码，确保是有效UTF-8
        return text.encode('utf-8', errors='replace').decode('utf-8')
    except Exception as e:
        print(f"文本清理错误: {e}")
        return ""

# 用于清理输出流中的重复标记
def clean_streaming_output(text):
    if "chunk" in text:
        # 移除所有chunk标记
        return text.replace("chunk", "")
    return text

# mixtral Module 异步版本 (改名为async_mixtral_response)
async def async_mixtral_response(
        question,
        system_prompt,
        condense_system_prompt,
        prompt_template,
        model_para,
        retriever,
        chat_history=None):
    print("... Generating AI Response")
    print(f"DEBUG: async_mixtral_response收到问题: '{question}'")

    ai_msg_content = ''

    aimodel_starttime = datetime.now()
    from langchain.callbacks.base import BaseCallbackHandler

    class StreamingCallbackHandler(BaseCallbackHandler):
        def __init__(self):
            self.partial_output = ""
            self.last_token = ""
            self.current_thinking = False
            # 用于实时传递token到外部
            self.latest_token = None
            self.token_ready = False

        def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
            try:
                # 清理token，去掉chunk标记
                if "chunk" in token:
                    token = token.replace("chunk", "")
                if not token.strip():  # 跳过空token
                    return
                
                # 检测思考模式标记
                if "<think>" in token:
                    self.current_thinking = True
                    # 去掉<think>标记
                    token = token.replace("<think>", "")
                    print("开始思考模式")
                
                if "</think>" in token:
                    self.current_thinking = False
                    # 去掉</think>标记
                    token = token.replace("</think>", "")
                    print("结束思考模式")
                
                sanitized_token = token.encode('utf-8', errors='replace').decode('utf-8')
                self.partial_output += sanitized_token
                
                # 设置最新token以便外部获取
                self.latest_token = sanitized_token
                self.token_ready = True
                
                # 避免重复打印相同token
                if sanitized_token != self.last_token:
                    # 根据当前模式打印不同颜色
                    if self.current_thinking:
                        print(f"\033[33m{sanitized_token}\033[0m", end="", flush=True)  # 黄色表示思考
                    else:
                        print(sanitized_token, end="", flush=True)
                    self.last_token = sanitized_token
            except Exception as e:
                print(f"Token处理错误: {e}")
            
    # 使用支持流式输出的实例
    streaming_callback = StreamingCallbackHandler()
    
    # 从模型参数获取 API 配置，如果没有则使用默认值
    api_key = model_para.get("api_key", "c598dbbb-4d28-4daf-8898-ee2d0aad8cfe")
    api_base = model_para.get("api_base", "https://ark.cn-beijing.volces.com/api/v3")
    model_name = model_para.get("model", "deepseek-r1-250528")
    
    print(f"DEBUG: 使用ChatOpenAI连接 - API Base: {api_base}")
    print(f"DEBUG: 使用模型: {model_name}")
    
    try:
        # 使用 ChatOpenAI 替代 Ollama
        llm = ChatOpenAI(
            model=model_name,
            openai_api_key=api_key,
            openai_api_base=api_base,
            streaming=True,
            callbacks=[streaming_callback]
        )
        print("DEBUG: 成功创建ChatOpenAI连接")
    except Exception as e:
        print(f"ERROR: 创建ChatOpenAI连接失败: {e}")
        import traceback
        print(traceback.format_exc())
        # 返回错误信息
        yield f"无法连接到语言模型服务: {str(e)}"
        return

    aimodel_endtime = datetime.now()
    load_time = aimodel_endtime - aimodel_starttime
    print(f"AI Model Loading Time = {load_time}")

    # # 测试是否能获取简单响应
    # try:
    #     print("DEBUG: 尝试获取简单响应测试...")
    #     simple_test = await llm.ainvoke("你好")
    #     print(f"DEBUG: 简单响应测试成功: {simple_test.content[:20]}...")
    # except Exception as e:
    #     print(f"ERROR: 简单响应测试失败: {e}")
    #     yield f"语言模型响应测试失败: {str(e)}"
    #     return
    
    # 定义最大块大小（字符数），控制响应分块的粒度
    MAX_CHUNK_SIZE = 1  # 每块最多100个字符
    
    # 根据是否有检索器选择不同的方法
    if not retriever:
        # Initialize chat_history as an empty list if it's None
        if chat_history is None:
            chat_history = []

        print("before_condense_q_chain: ", chat_history)
        condense_q_system_prompt = """Given a chat history and the user question \
        which might reference the chat history, formulate a standalone question \
        which can be understood without the chat history. Do NOT answer the question, \
        just reformulate it if needed and otherwise return it as is."""

        condense_q_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", condense_q_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        condense_q_chain = condense_q_prompt | llm | StrOutputParser()

        qa_system_prompt = """You are an assistant for question-answering tasks. \
        Use the following pieces of retrieved context to answer the question. \
        If you don't know the answer, just say that you don't know. \
         {context}"""

        qa_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", qa_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        def condense_question(input: dict):
            print("Input content:", input)
            if input.get("chat_history"):
                return condense_q_chain
            else:
                return input["question"]

        rag_chain = (
                RunnablePassthrough.assign(context=condense_question)
                | qa_prompt
                | llm
                | StrOutputParser()
        )
        
        # 执行RAG链以获取响应
        airespone_starttime = datetime.now()

        # 使用流式处理方法
        buffer = ""

        try:
            # 启动异步调用
            rag_chain_input = {"question": question, "chat_history": chat_history}
            print(f"DEBUG: 开始RAG链异步调用，参数: {rag_chain_input}")

            # 设置输出块计数器
            chunk_count = 0

            # 使用异步流式处理
            async for chunk in rag_chain.astream(rag_chain_input):
                if isinstance(chunk, str):
                    token = chunk
                elif hasattr(chunk, 'content'):
                    token = chunk.content
                else:
                    token = str(chunk)
                
                # 清理token，去掉chunk标记
                if "chunk" in token:
                    token = token.replace("chunk", "")
                if not token.strip():  # 跳过空token
                    continue

                # 累积到buffer
                buffer += token

                # 检查是否需要分块发送
                if len(buffer) >= MAX_CHUNK_SIZE or "</think>" in buffer or "<think>" in buffer:
                    # 清理并准备发送的块
                    chunk_to_send = buffer
                    buffer = ""
                    
                    # 处理思考模式标记
                    if "<think>" in chunk_to_send:
                        parts = chunk_to_send.split("<think>")
                        if parts[0].strip():  # 发送思考前的普通内容
                            yield parts[0]
                        chunk_to_send = parts[1]  # 继续处理思考内容
                    
                    if "</think>" in chunk_to_send:
                        parts = chunk_to_send.split("</think>")
                        if parts[0].strip():  # 发送思考内容
                            yield f"<think>{parts[0]}</think>"
                        if parts[1].strip():  # 发送思考后的普通内容
                            yield parts[1]
                    else:
                        # 如果没有特殊标记，直接发送
                        yield chunk_to_send

            # 发送剩余的buffer内容
            if buffer.strip():
                yield buffer

            print(f"DEBUG: 总共发送了{chunk_count}个响应块")

        except Exception as e:
            print(f"ERROR: 流式处理时出错: {e}")
            import traceback
            print(traceback.format_exc())
            yield f"处理响应时出错: {str(e)}"
            return
        
        airesponse_endtime = datetime.now()
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")
    
    else:
                # Initialize chat_history as an empty list if it's None
        if chat_history is None:
            chat_history = []

        print("before_condense_q_chain: ", chat_history)
        condense_q_system_prompt = """Given a chat history and the user question \
        which might reference the chat history, formulate a standalone question \
        which can be understood without the chat history. Do NOT answer the question, \
        just reformulate it if needed and otherwise return it as is."""

        condense_q_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", condense_q_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        condense_q_chain = condense_q_prompt | llm | StrOutputParser()

        qa_system_prompt = """You are an assistant for question-answering tasks. \
        Use the following pieces of retrieved context to answer the question. \
        If you don't know the answer, just say that you don't know. \
         {context}"""

        qa_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", qa_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        def condense_question(input: dict):
            print("Input content:", input)
            if input.get("chat_history"):
                return condense_q_chain
            else:
                return input["question"]

        rag_chain = (
                RunnablePassthrough.assign(context=condense_question | retriever)
                | qa_prompt
                | llm
                | StrOutputParser()
        )
        
        # 执行RAG链以获取响应
        airespone_starttime = datetime.now()
        
        # 使用流式处理方法
        buffer = ""
        
        try:
            # 启动异步调用
            rag_chain_input = {"question": question, "chat_history": chat_history}
            print(f"DEBUG: 开始RAG链异步调用，参数: {rag_chain_input}")
            
            # 设置输出块计数器
            chunk_count = 0
            
            # 使用异步流式处理
            async for chunk in rag_chain.astream(rag_chain_input):
                if isinstance(chunk, str):
                    token = chunk
                elif hasattr(chunk, 'content'):
                    token = chunk.content
                else:
                    token = str(chunk)
                
                # 清理token，去掉chunk标记
                if "chunk" in token:
                    token = token.replace("chunk", "")
                if not token.strip():  # 跳过空token
                    continue

                # 累积到buffer
                buffer += token

                # 检查是否需要分块发送
                if len(buffer) >= MAX_CHUNK_SIZE or "</think>" in buffer or "<think>" in buffer:
                    # 清理并准备发送的块
                    chunk_to_send = buffer
                    buffer = ""
                    
                    # 处理思考模式标记
                    if "<think>" in chunk_to_send:
                        parts = chunk_to_send.split("<think>")
                        if parts[0].strip():  # 发送思考前的普通内容
                            yield parts[0]
                        chunk_to_send = parts[1]  # 继续处理思考内容
                    
                    if "</think>" in chunk_to_send:
                        parts = chunk_to_send.split("</think>")
                        if parts[0].strip():  # 发送思考内容
                            yield f"<think>{parts[0]}</think>"
                        if parts[1].strip():  # 发送思考后的普通内容
                            yield parts[1]
                    else:
                        # 如果没有特殊标记，直接发送
                        yield chunk_to_send

            # 发送剩余的buffer内容
            if buffer.strip():
                yield buffer
            
            print(f"DEBUG: 总共发送了{chunk_count}个响应块")
            
        except Exception as e:
            print(f"ERROR: 流式处理时出错: {e}")
            import traceback
            print(traceback.format_exc())
            yield f"处理响应时出错: {str(e)}"
            return
        
        airesponse_endtime = datetime.now()
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")
        
    print('>>> Generated AI Response')

# 添加同步版本的函数以兼容现有代码
def mixtral_response(
        question,
        system_prompt,
        condense_system_prompt,
        prompt_template,
        model_para,
        retriever,
        chat_history=None):
    """
    同步版本的mixtral_response函数，用于保持兼容性
    """
    import asyncio
    
    # 创建一个事件循环来运行异步函数
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        print("question123", question)
        # 获取异步生成器
        async_gen = async_mixtral_response(
            question, 
            system_prompt, 
            condense_system_prompt, 
            prompt_template, 
            model_para, 
            retriever, 
            chat_history
        )
        
        # 收集所有生成的内容
        full_response = ""
        while True:
            try:
                # 获取下一个生成的内容
                chunk = loop.run_until_complete(async_gen.__anext__())
                full_response += chunk
            except StopAsyncIteration:
                break
                
        print("result:", full_response[:50] + "..." if len(full_response) > 50 else full_response)
        return full_response
    finally:
        # 确保关闭事件循环
        loop.close()
