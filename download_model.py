from sentence_transformers import SentenceTransformer
import os

# Create the directory where the model will be saved
os.makedirs("models/embeddings/text2vec-base-chinese", exist_ok=True)

print("Downloading the Chinese embedding model...")
# This will download the model and cache it locally
model = SentenceTransformer("shibing624/text2vec-base-chinese", cache_folder="models/embeddings")
model.save("models/embeddings/text2vec-base-chinese")
print("Model downloaded and saved successfully to models/embeddings/text2vec-base-chinese") 