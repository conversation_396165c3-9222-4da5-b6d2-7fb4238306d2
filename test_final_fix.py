#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复
验证MinerU集成是否正常工作
"""

def test_mineru_integration():
    """测试MinerU集成"""
    print("🧪 测试MinerU集成")
    print("=" * 40)
    
    try:
        print("🔍 [TEST] 导入mineru_integration...")
        from mineru_integration import MinerUProcessor, MINERU_AVAILABLE
        print(f"✅ [TEST] 导入成功")
        print(f"🔍 [TEST] MINERU_AVAILABLE = {MINERU_AVAILABLE}")
        
        if MINERU_AVAILABLE:
            print("🔍 [TEST] 初始化MinerU处理器...")
            processor = MinerUProcessor()
            print(f"✅ [TEST] 处理器初始化成功: {type(processor)}")
            
            # 测试处理方法（不需要真实文件）
            print("🔍 [TEST] 测试处理方法可用性...")
            if hasattr(processor, 'process_pdf'):
                print("✅ [TEST] process_pdf 方法存在")
            if hasattr(processor, 'convert_to_chunks'):
                print("✅ [TEST] convert_to_chunks 方法存在")
            
            return True
        else:
            print("❌ [TEST] MINERU_AVAILABLE = False")
            return False
            
    except Exception as e:
        print(f"❌ [TEST] 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_v7_conditions():
    """测试v7处理条件"""
    print("\n🔍 测试v7处理条件")
    print("=" * 40)
    
    try:
        # 模拟v7的条件检查
        from mineru_integration import MINERU_AVAILABLE
        from mineru_config import get_mineru_config
        
        print(f"🔍 [TEST] MINERU_AVAILABLE = {MINERU_AVAILABLE}")
        
        mineru_config = get_mineru_config()
        print(f"🔍 [TEST] mineru_config存在: {mineru_config is not None}")
        
        if mineru_config:
            enabled = mineru_config.is_enabled()
            print(f"🔍 [TEST] mineru_config.is_enabled(): {enabled}")
        
        # 模拟处理条件
        filename = "test.pdf"
        json_path = "test.json"  # 不存在
        
        should_use_mineru = (
            filename.lower().endswith(".pdf") and 
            MINERU_AVAILABLE and 
            not False and  # 模拟预计算文件不存在
            (mineru_config is None or mineru_config.is_enabled())
        )
        
        print(f"🔍 [TEST] 处理条件检查:")
        print(f"  - 是PDF文件: {filename.lower().endswith('.pdf')}")
        print(f"  - MINERU_AVAILABLE: {MINERU_AVAILABLE}")
        print(f"  - 预计算文件不存在: True")
        print(f"  - 配置启用: {mineru_config is None or mineru_config.is_enabled()}")
        print(f"🎯 [TEST] 是否使用MinerU: {should_use_mineru}")
        
        return should_use_mineru
        
    except Exception as e:
        print(f"❌ [TEST] 条件测试失败: {str(e)}")
        return False

def test_processor_fallback():
    """测试处理器回退机制"""
    print("\n🔍 测试处理器回退机制")
    print("=" * 40)
    
    try:
        from mineru_integration import MinerUProcessor, MINERU_AVAILABLE
        
        if not MINERU_AVAILABLE:
            print("❌ [TEST] MINERU_AVAILABLE = False，无法测试")
            return False
        
        processor = MinerUProcessor()
        
        # 测试处理方法的debug输出
        print("🔍 [TEST] 测试处理器的debug输出...")
        
        # 创建一个假的PDF路径来测试debug输出
        fake_pdf = "/tmp/nonexistent.pdf"
        
        # 这会失败，但我们可以看到debug输出
        result = processor.process_pdf(fake_pdf)
        print(f"🔍 [TEST] 处理结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ [TEST] 回退测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 最终修复验证测试")
    print("=" * 50)
    
    results = []
    
    # 1. 测试MinerU集成
    results.append(test_mineru_integration())
    
    # 2. 测试v7条件
    results.append(test_v7_conditions())
    
    # 3. 测试处理器回退
    results.append(test_processor_fallback())
    
    # 总结
    print(f"\n📊 测试结果总结")
    print("=" * 50)
    
    test_names = ["MinerU集成", "v7条件检查", "处理器回退"]
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count >= 2:  # 至少前两个测试通过
        print("🎉 修复成功! MinerU应该能正常工作了")
        print("💡 现在可以运行: python chatbot_newui_new_version_v7.py")
        print("📋 上传PDF文件时应该会看到:")
        print("   🔍 [DEBUG] MINERU_AVAILABLE: True")
        print("   🔍 [DEBUG] 是否使用MinerU: True")
        print("   🔄 [DEBUG] 开始使用MinerU实时处理PDF")
    else:
        print("❌ 修复可能不完整")
        print("💡 建议检查错误信息并进一步调试")
    
    return success_count >= 2

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 测试通过! 可以启动v7了")
        else:
            print("\n❌ 测试失败，需要进一步调试")
    except Exception as e:
        print(f"\n❌ 测试过程出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
