import os
import shutil
from tqdm import tqdm
import zipfile
import time

def copy_with_progress(src, dst):
    # 获取源文件大小
    total_size = os.path.getsize(src)
    
    # 创建目标目录
    os.makedirs(os.path.dirname(dst), exist_ok=True)
    
    # 使用tqdm创建进度条
    with tqdm(total=total_size, unit='B', unit_scale=True, desc=f"Copying {os.path.basename(src)}") as pbar:
        with open(src, 'rb') as fsrc:
            with open(dst, 'wb') as fdst:
                while True:
                    buf = fsrc.read(1024)
                    if not buf:
                        break
                    fdst.write(buf)
                    pbar.update(len(buf))

def zip_with_progress(src_dir, zip_path):
    # 获取所有文件
    files = []
    for root, _, filenames in os.walk(src_dir):
        for filename in filenames:
            files.append(os.path.join(root, filename))
    
    # 计算总大小
    total_size = sum(os.path.getsize(f) for f in files)
    
    # 创建zip文件
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        with tqdm(total=total_size, unit='B', unit_scale=True, desc="Zipping files") as pbar:
            for file in files:
                file_size = os.path.getsize(file)
                file_name = os.path.relpath(file, src_dir)
                
                # 对于大文件使用分块压缩
                if file_size > 100 * 1024 * 1024:  # 100MB
                    with open(file, 'rb') as f:
                        with zipf.open(file_name, 'w', force_zip64=True) as zf:
                            while True:
                                chunk = f.read(1024 * 1024)  # 1MB chunks
                                if not chunk:
                                    break
                                zf.write(chunk)
                                pbar.update(len(chunk))
                else:
                    # 小文件直接压缩
                    zipf.write(file, file_name)
                    pbar.update(file_size)
                
                # 强制刷新进度条
                pbar.refresh()
                time.sleep(0.1)  # 短暂暂停以确保进度条更新

def main():
    # 创建目标目录
    os.makedirs("bge-m3-model", exist_ok=True)
    
    # 复制文件
    src_dir = "models/embeddings/bge-m3"
    dst_dir = "bge-m3-model"
    
    print("Starting file copy process...")
    for root, _, files in os.walk(src_dir):
        for file in files:
            src_path = os.path.join(root, file)
            dst_path = os.path.join(dst_dir, os.path.relpath(src_path, src_dir))
            copy_with_progress(src_path, dst_path)
    
    # 压缩文件
    print("\nStarting compression process...")
    zip_with_progress("bge-m3-model", "bge-m3-model.zip")
    
    print("\n✅ Process completed successfully!")

if __name__ == "__main__":
    main() 