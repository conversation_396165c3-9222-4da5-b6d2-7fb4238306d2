"""

    Module Name :           mixtral_module
    Last Modified Date :    3 Jan 2024

"""
import subprocess
from typing import Any

# Import Open-source Libraries
from langchain import HuggingFacePipeline, hub
from langchain.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain.schema import StrOutputParser
from langchain_community.chat_models import ChatOpenAI
from langchain_community.llms.llamafile import Llamafile
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableParallel
from langchain_core.messages import HumanMessage
import transformers
from transformers import AutoTokenizer
import torch
from huggingface_hub import hf_hub_download
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain.llms import LlamaCpp
from langchain import PromptTemplate, LLMChain
from datetime import datetime

# Import Self-defined Modules
import preprocessing
from langchain_community.llms import Ollama
from langchain_community.chat_models import ChatOllama
from ollama import Client
# import tools.gpu_detect

# init mixtral pipeline
def init_mixtral_piepeline(model_para):
    pipeline = transformers.pipeline(
        model_para["task"],
        model=model_para["model"],
        device_map=model_para["device_map"],
        model_kwargs={"torch_dtype": eval(model_para["torch_dtype"])},
        max_new_tokens=model_para["max_new_tokens"],
        repetition_penalty=model_para["repetition_penalty"],
        return_full_text=eval(model_para["return_full_text"]),
        pad_token_id=2,
        eos_token_id=2
    )
    return pipeline


# mixtral Module
def mixtral_response(
                    question,
                    system_prompt,
                    condense_system_prompt,
                    prompt_template,
                    model_para,
                    retriever,
                    chat_history=None):
    
    print("... Generating AI Response")

    ai_msg_content = ''

    aimodel_starttime = datetime.now()
    from langchain.callbacks.base import BaseCallbackHandler

    class StreamingCallbackHandler(BaseCallbackHandler):
        def __init__(self):
            self.partial_output = ""

        def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
            self.partial_output += token
            print(token, end="", flush=True)

    llm = Ollama(base_url="http://*************:11434", model="mistral")

    # llm =Llamafile()
    aimodel_endtime = datetime.now()
    load_time = aimodel_endtime - aimodel_starttime
    print(f"AI Model Loading Time = {load_time}")

    # Without Vector Store
    if not retriever:
        # Initialize chat_history as an empty list if it's None
        if chat_history is None:
            chat_history = []

        print("before_condense_q_chain: ", chat_history)
        condense_q_system_prompt = """Given a chat history and the user question \
        which might reference the chat history, formulate a standalone question \
        which can be understood without the chat history. Do NOT answer the question, \
        just reformulate it if needed and otherwise return it as is."""

        condense_q_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", condense_q_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        condense_q_chain = condense_q_prompt | llm | StrOutputParser()

        qa_system_prompt = """You are an assistant for question-answering tasks. \
        Use the following pieces of retrieved context to answer the question. \
        If you don't know the answer, just say that you don't know. \
        Use three sentences maximum and keep the answer concise.\

         {context}"""

        qa_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", qa_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        def condense_question(input: dict):
            print("Input content:", input)
            if input.get("chat_history"):
                return condense_q_chain
            else:
                return input["question"]

        rag_chain = (
                RunnablePassthrough.assign(context=condense_question)
                | qa_prompt
                | llm
                | StrOutputParser()
        )
        # Invoke the RAG chain and get the AI message
        airespone_starttime = datetime.now()

        ai_msg_content = rag_chain.invoke({"question": question, "chat_history": chat_history})
        # print(ai_msg_content)
        airesponse_endtime = datetime.now()
        # for chunks in rag_chain.stream(question):
        #     print(chunks)
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")
        # tools.gpu_detect.get_gpu_info()

    # With Vector Store
    else:

        print("before_condense_q_chain: ", chat_history)
        condense_q_system_prompt = """Given a chat history and the user question \
        which might reference the chat history, formulate a standalone question \
        which can be understood without the chat history. Do NOT answer the question, \
        just reformulate it if needed and otherwise return it as is."""

        condense_q_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", condense_q_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        condense_q_chain = condense_q_prompt | llm | StrOutputParser()

        qa_system_prompt = """You are an assistant for question-answering tasks. \
        Use the following pieces of retrieved context to answer the question. \
        If you don't know the answer, just say that you don't know. \
        Use three sentences maximum and keep the answer concise.\

         {context}"""

        qa_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", qa_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        def condense_question(input: dict):
            print("Input content:", input)
            if input.get("chat_history"):
                return condense_q_chain
            else:
                return input["question"]

        rag_chain = (
                RunnablePassthrough.assign(context=condense_question | retriever)
                | qa_prompt
                | llm
                | StrOutputParser()
        )
        # Invoke the RAG chain and get the AI message
        airespone_starttime = datetime.now()
        # Initialize chat_history as an empty list if it's None
        if chat_history is None:
            chat_history = []

        # Now you can safely call rag_chain.invoke
        ai_msg_content = rag_chain.invoke({"question": question, "chat_history": chat_history})
        print(ai_msg_content)

        airesponse_endtime = datetime.now()
        # for chunks in rag_chain.stream(question):
        #     print(chunks)
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")
        # tools.gpu_detect.get_gpu_info()

    print('>>> Generated AI Response')

    return ai_msg_content
    # Return only the content of the AI's response