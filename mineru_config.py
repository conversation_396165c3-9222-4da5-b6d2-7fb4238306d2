#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU配置文件
用于管理MinerU的各种设置和参数
"""

import os
from typing import Dict, Any

class MinerUConfig:
    """MinerU配置管理类"""
    
    def __init__(self):
        """初始化配置"""
        self.config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 基础设置
            "enabled": True,  # 是否启用MinerU
            "fallback_on_error": True,  # 出错时是否回退到原始方法
            "save_results": True,  # 是否保存MinerU处理结果
            
            # 处理模式
            "parse_mode": "auto",  # auto, ocr, txt
            "output_formats": ["json"],  # markdown, json, txt
            
            # 语言设置
            "lang": "auto",  # auto, zh, en
            
            # 布局检测配置
            "layout_config": {
                "model": "doclayout_yolo",  # 布局检测模型
                "confidence_threshold": 0.5,  # 置信度阈值
                "enable": True
            },
            
            # 公式识别配置
            "formula_config": {
                "enable": True,
                "mfd_model": "yolo_v8_mfd",  # 公式检测模型
                "mfr_model": "unimernet_small",  # 公式识别模型
                "confidence_threshold": 0.5
            },
            
            # 表格识别配置
            "table_config": {
                "enable": True,
                "model": "rapid_table",  # 表格识别模型
                "confidence_threshold": 0.5
            },
            
            # OCR配置
            "ocr_config": {
                "enable": True,
                "model": "paddleocr",  # paddleocr, easyocr
                "lang": ["ch_sim", "en"],  # 支持的语言
                "confidence_threshold": 0.5
            },
            
            # 输出配置
            "output_config": {
                "save_images": False,  # 是否保存提取的图片
                "save_tables": True,   # 是否保存表格
                "save_formulas": True, # 是否保存公式
                "image_format": "png", # 图片格式
                "table_format": "html" # 表格格式
            },
            
            # 性能配置
            "performance_config": {
                "batch_size": 1,       # 批处理大小
                "max_workers": 4,      # 最大工作线程数
                "gpu_enabled": False,  # 是否使用GPU
                "memory_limit": "4GB"  # 内存限制
            },
            
            # 文件过滤配置
            "file_filter": {
                "max_file_size": "100MB",  # 最大文件大小
                "min_pages": 1,            # 最小页数
                "max_pages": 1000,         # 最大页数
                "supported_formats": [".pdf"]  # 支持的文件格式
            }
        }
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self.config.copy()
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """更新配置"""
        self._deep_update(self.config, new_config)
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict) -> None:
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def is_enabled(self) -> bool:
        """检查MinerU是否启用"""
        return self.config.get("enabled", False)
    
    def should_fallback_on_error(self) -> bool:
        """检查是否在出错时回退"""
        return self.config.get("fallback_on_error", True)
    
    def should_save_results(self) -> bool:
        """检查是否保存结果"""
        return self.config.get("save_results", True)
    
    def get_parse_config(self) -> Dict[str, Any]:
        """获取解析配置"""
        return {
            "parse_mode": self.config.get("parse_mode", "auto"),
            "output_format": self.config.get("output_formats", ["json"]),
            "lang": self.config.get("lang", "auto"),
            "layout_config": self.config.get("layout_config", {}),
            "formula_config": self.config.get("formula_config", {}),
            "table_config": self.config.get("table_config", {}),
            "ocr_config": self.config.get("ocr_config", {})
        }
    
    def is_file_supported(self, file_path: str) -> bool:
        """检查文件是否支持"""
        file_ext = os.path.splitext(file_path)[1].lower()
        supported_formats = self.config.get("file_filter", {}).get("supported_formats", [".pdf"])
        return file_ext in supported_formats
    
    def check_file_size(self, file_path: str) -> bool:
        """检查文件大小是否在限制范围内"""
        try:
            file_size = os.path.getsize(file_path)
            max_size_str = self.config.get("file_filter", {}).get("max_file_size", "100MB")
            
            # 解析大小限制
            if max_size_str.endswith("MB"):
                max_size = int(max_size_str[:-2]) * 1024 * 1024
            elif max_size_str.endswith("GB"):
                max_size = int(max_size_str[:-2]) * 1024 * 1024 * 1024
            else:
                max_size = int(max_size_str)
            
            return file_size <= max_size
        except Exception:
            return True  # 如果无法检查，默认允许
    
    def load_from_file(self, config_file: str) -> None:
        """从文件加载配置"""
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            self.update_config(file_config)
            print(f"✅ 从文件加载MinerU配置: {config_file}")
        except Exception as e:
            print(f"⚠️  加载配置文件失败: {str(e)}")
    
    def save_to_file(self, config_file: str) -> None:
        """保存配置到文件"""
        try:
            import json
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"✅ MinerU配置已保存到: {config_file}")
        except Exception as e:
            print(f"❌ 保存配置文件失败: {str(e)}")

# 全局配置实例
mineru_config = MinerUConfig()

# 尝试加载用户自定义配置
config_file_path = "mineru_user_config.json"
if os.path.exists(config_file_path):
    mineru_config.load_from_file(config_file_path)

def get_mineru_config() -> MinerUConfig:
    """获取MinerU配置实例"""
    return mineru_config

def create_sample_config():
    """创建示例配置文件"""
    sample_config = {
        "enabled": True,
        "parse_mode": "auto",
        "lang": "auto",
        "layout_config": {
            "model": "doclayout_yolo",
            "confidence_threshold": 0.6
        },
        "formula_config": {
            "enable": True,
            "confidence_threshold": 0.7
        },
        "table_config": {
            "enable": True,
            "confidence_threshold": 0.6
        }
    }
    
    with open("mineru_sample_config.json", 'w', encoding='utf-8') as f:
        import json
        json.dump(sample_config, f, ensure_ascii=False, indent=2)
    
    print("✅ 示例配置文件已创建: mineru_sample_config.json")

if __name__ == "__main__":
    # 创建示例配置文件
    create_sample_config()
    
    # 测试配置
    config = get_mineru_config()
    print("当前配置:")
    import json
    print(json.dumps(config.get_config(), ensure_ascii=False, indent=2))
