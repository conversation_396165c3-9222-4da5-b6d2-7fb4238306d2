"""

    Module Name :           openai_module
    Last Modified Date :    3 Jan 2024

"""
from datetime import datetime

# Import Libraries
import openai
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
# from langchain.chat_models import ChatOpenAI
from langchain_openai import ChatOpenAI

from langchain.prompts import Chat<PERSON><PERSON>pt<PERSON><PERSON>plate, MessagesPlaceholder
from langchain.schema import Str<PERSON><PERSON><PERSON><PERSON>arser
from langchain_core.runnables import RunnablePassthrough, RunnableParallel
from langchain_core.messages import HumanMessage
from langchain import PromptTemplate, LLMChain


import os

# Import Self-defined Libraries
import preprocessing

# Set openai key in env
openai.api_key = os.environ["OPENAI_API_KEY"] = "***************************************************"

def openai_response(question,
                    system_prompt,
                    condense_system_prompt,
                    prompt_template,
                    model_para,
                    retriever,
                    chat_history):
    # print("system_prompt:", system_prompt)
    print("... Generating AI Response")
    ai_msg_content = ''

    # Without Vector Store
    if not retriever:
        # Change content value from
        # system_prompt:  You are a helpful assistant for question-answering tasks with professional tone.  Give the responses in 3 sentences with the maximum of 200.  If your confidence level is below 0.9, just say that you do not know.  The input language is English and the output language is English.
        # to
        # You are a helpful assistant.
        q_prompt = [{
            "role": "system",
            "content": "You are a helpful assistant."
        }]
        q_prompt.append({"role": "user",
                         "content": question})
        responses = openai.ChatCompletion.create(model=model_para["model"], 
                                                 messages=q_prompt,
                                                 )
        ai_msg_content = responses['choices'][0]['message']['content']
        """ *** Refine >>> to be chat history """
        q_prompt.append({"role": "assistant", "content": ai_msg_content})


    # With Vector Store
    else:

        # import bs4
        # from langchain import hub
        # from langchain.text_splitter import RecursiveCharacterTextSplitter
        # from langchain_community.document_loaders import WebBaseLoader
        # from langchain_community.vectorstores import Chroma
        # from langchain_core.output_parsers import StrOutputParser
        # from langchain_core.runnables import RunnableParallel, RunnablePassthrough
        #
        # # Load, chunk and index the contents of the blog.
        # bs_strainer = bs4.SoupStrainer(class_=("post-content", "post-title", "post-header"))
        # loader = WebBaseLoader(
        #     web_paths=("https://lilianweng.github.io/posts/2023-06-23-agent/",),
        #     bs_kwargs={"parse_only": bs_strainer},
        # )
        # docs = loader.load()
        #
        # text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
        # splits = text_splitter.split_documents(docs)
        # vectorstore = Chroma.from_documents(documents=splits, embedding=OpenAIEmbeddings())
        # # Retrieve and generate using the relevant snippets of the blog.
        # retriever = vectorstore.as_retriever()
        # prompt = hub.pull("rlm/rag-prompt")
        # llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0)
        # def format_docs(docs):
        #     return "\n\n".join(doc.page_content for doc in docs)
        #
        # rag_chain_from_docs = (
        #         RunnablePassthrough.assign(context=(lambda x: format_docs(x["context"])))
        #         | prompt
        #         | llm
        #         | StrOutputParser()
        # )
        #
        # rag_chain_with_source = RunnableParallel(
        #     {"context": retriever, "question": RunnablePassthrough()}
        # ).assign(answer=rag_chain_from_docs)
        # for chunk in rag_chain_with_source.stream(question):
        #     print(chunk)


        # callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
        # aimodel_starttime = datetime.now()
        # llm = ChatOpenAI(model_name="gpt-3.5-turbo",
        #                  callback_manager=callback_manager)
        # aimodel_endtime = datetime.now()
        # print(f"AI Model Loading Time = {aimodel_endtime - aimodel_starttime}")
        #
        # condense_q_system_prompt = """Given a chat history and the user question \
        #         which might reference the chat history, formulate a standalone question \
        #         which can be understood without the chat history. Do NOT answer the question, \
        #         just reformulate it if needed and otherwise return it as is."""
        #
        # condense_q_prompt = ChatPromptTemplate.from_messages(
        #     [
        #         ("system", condense_q_system_prompt),
        #         MessagesPlaceholder(variable_name="chat_history"),
        #         ("human", "{question}"),
        #     ]
        # )
        #
        # condense_q_chain = condense_q_prompt | llm | StrOutputParser()
        #
        # qa_system_prompt = """You are an assistant for question-answering tasks. \
        #         Use the following pieces of retrieved context to answer the question. \
        #         If you don't know the answer, just say that you don't know. \
        #         Use three sentences maximum and keep the answer concise.\
        #
        #          {context}"""
        #
        # qa_prompt = ChatPromptTemplate.from_messages(
        #     [
        #         ("system", qa_system_prompt),
        #         MessagesPlaceholder(variable_name="chat_history"),
        #         ("human", "{question}"),
        #     ]
        # )
        #
        # def condense_question(input: dict):
        #     print("Input content:", input)
        #     if input.get("chat_history"):
        #         return condense_q_chain
        #     else:
        #         return input["question"]
        #
        # rag_chain = (
        #         RunnablePassthrough.assign(context=condense_question | retriever | preprocessing.format_docs)
        #         | qa_prompt
        #         | llm
        #         | StrOutputParser()
        # )
        #
        # # Invoke the RAG chain and get the AI message
        # ai_msg_content = rag_chain.invoke({"question": question, "chat_history": chat_history})
        callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
        llm = ChatOpenAI(model_name="gpt-3.5-turbo", callback_manager=callback_manager)

        from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder

        condense_q_system_prompt = """Given a chat history and the latest user question \
            which might reference the chat history, formulate a standalone question \
            which can be understood without the chat history. Do NOT answer the question, \
            just reformulate it if needed and otherwise return it as is."""
        condense_q_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", condense_q_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )
        condense_q_chain = condense_q_prompt | llm | StrOutputParser()

        qa_system_prompt = """You are an assistant for question-answering tasks. \
            Use the following pieces of retrieved context to answer the question. \
            If you don't know the answer, just say that you don't know. \
            Use three sentences maximum and keep the answer concise.\

            {context}"""

        qa_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", qa_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        def condense_question(input: dict):
            print("Input content:", input)
            if input.get("chat_history"):
                return condense_q_chain
            else:
                return input["question"]

        rag_chain = (
                RunnablePassthrough.assign(context=condense_question | retriever | preprocessing.format_docs)
                | qa_prompt
                | llm
        )
        print("preprocessing.format_docs:", preprocessing.format_docs)

        # Invoke the RAG chain and get the AI message
        ai_msg = rag_chain.invoke({"question": question, "chat_history": chat_history})

        # Extract the content from the AIMessage object
        ai_msg_content = ai_msg.content

        # Append the human message and the AI's response content to the chat history
        chat_history.extend([HumanMessage(content=question), ai_msg])

        # Return only the content of the AI's response
        return ai_msg_content

    print('>>> Generated AI Response')
    return ai_msg_content # Return only the content of the AI's response