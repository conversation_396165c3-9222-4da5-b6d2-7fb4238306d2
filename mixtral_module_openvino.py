
from typing import Any
from langchain.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableParallel
import transformers
from datetime import datetime
from langchain_community.llms import Ollama
from langchain_openai import ChatOpenAI
import chinese_converter


def init_mixtral_piepeline(model_para):
    pipeline = transformers.pipeline(
        model_para["task"],
        model=model_para["model"],
        device_map=model_para["device_map"],
        model_kwargs={"torch_dtype": eval(model_para["torch_dtype"])},
        max_new_tokens=model_para["max_new_tokens"],
        repetition_penalty=model_para["repetition_penalty"],
        return_full_text=eval(model_para["return_full_text"]),
        pad_token_id=2,
        eos_token_id=2
    )
    return pipeline


# mixtral Module
def mixtral_response(
                    question,
                    system_prompt,
                    condense_system_prompt,
                    prompt_template,
                    model_para,
                    retriever,
                    chat_history=None):
    
    print("... Generating AI Response")


    aimodel_starttime = datetime.now()
    from langchain.callbacks.base import BaseCallbackHandler

    class StreamingCallbackHandler(BaseCallbackHandler):
        def __init__(self):
            self.partial_output = ""

        def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
            self.partial_output += token
            print(token, end="", flush=True)

    # Initialize ChatOpenAI with DeepBricks API
    # llm = ChatOpenAI(
    #     model_name="gpt-4o",
    #     openai_api_key="sk-DweVsCpRQDs8J7ZX1ZvksOMIt7U8CRmM9ogookdfI641dSJ0",
    #     openai_api_base="https://api.deepbricks.ai/v1/",
    #     temperature=0.7,
    # )
    # llm = ChatOpenAI(base_url="http://*************:30277/v1", model="Qwen/Qwen2.5-7B-Instruct-AWQ",api_key="key-123")
    from langchain_huggingface import HuggingFacePipeline
    import torch

    # Force CPU usage
    torch.cuda.is_available = lambda: False

    ov_config = {"PERFORMANCE_HINT": "LATENCY", "NUM_STREAMS": "1", "CACHE_DIR": ""}

    ov_llm = HuggingFacePipeline.from_model_id(
        model_id="Qwen2.5-14B-Instruct-INT4",
        task="text-generation",
        backend="openvino",
        model_kwargs={
            "device": "cpu",
            "ov_config": ov_config,
            "trust_remote_code": True  # Required for Qwen models
        },
        pipeline_kwargs={"max_new_tokens": 100}
    )

    # Without Vector Store
    if not retriever:
        # Initialize chat_history as an empty list if it's None
        if chat_history is None:
            chat_history = []

        print("before_condense_q_chain: ", chat_history)
        condense_q_system_prompt = """Given a chat history and the user question \
        which might reference the chat history, formulate a standalone question \
        which can be understood without the chat history. Do NOT answer the question, \
        just reformulate it if needed and otherwise return it as is."""

        condense_q_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", condense_q_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        condense_q_chain = condense_q_prompt | ov_llm | StrOutputParser()

        qa_system_prompt = """You are an assistant for question-answering tasks. \
        Use the following pieces of retrieved context to answer the question. \
        If you don't know the answer, just say that you don't know. \
        Use five sentences maximum and keep the answer concise.\

         {context}
         """

        qa_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", qa_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        def condense_question(input: dict):
            print("Input content:", input)
            if input.get("chat_history"):
                return condense_q_chain
            else:
                return input["question"]

        rag_chain = (
                RunnablePassthrough.assign(context=condense_question)
                | qa_prompt
                | ov_llm
                | StrOutputParser()
        )
        # Invoke the RAG chain and get the AI message
        airespone_starttime = datetime.now()
    
        ai_msg_content = rag_chain.invoke({"question": question, "chat_history": chat_history})
        airesponse_endtime = datetime.now()
 
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")

    # With Vector Store
    else:
        chat_history = []
    
        condense_q_system_prompt = """Given a chat history and the user question \
        which might reference the chat history, formulate a standalone question \
        which can be understood without the chat history. Do NOT answer the question, \
        just reformulate it if needed and otherwise return it as is."""

        condense_q_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", condense_q_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        condense_q_chain = condense_q_prompt | ov_llm | StrOutputParser()
#         qa_system_prompt = """YOU ARE A RENOWNED INSURANCE PARTNERSHIP DISTRIBUTION EXPERT WITH OVER 15 YEARS OF EXPERIENCE IN HELPING INSURANCE COMPANIES AND DISTRIBUTION CHANNELS MAXIMIZE SALES EFFORTS. YOU HAVE BEEN AWARDED THE "TOP INSURANCE DISTRIBUTION STRATEGIST" TITLE BY THE GLOBAL INSURANCE ALLIANCE IN 2023. YOUR TASK IS TO PROVIDE ACTIONABLE INSIGHTS AND STRATEGIES TO RELATIONSHIP MANAGERS (RMs) THAT WILL OPTIMIZE SALES EFFORTS AND IMPROVE EFFICIENCY, PRODUCTIVITY, AND REVENUE GROWTH WITHIN PARTNERSHIP DISTRIBUTION CHANNELS.
#         PLEASE ANSWER THE QUESTION WITH HONG KONG CANTONESE IN TRADITIONAL CHINESE.
#
#         ###INSTRUCTIONS###
#         - You MUST IDENTIFY key pain points in the insurance sales process through partnership distribution channels.
#         - PROVIDE strategies for RMs to streamline efforts and improve efficiency in managing partner relationships.
#         - SUGGEST tactics to enhance productivity, focusing on the automation of routine tasks, data-driven decision-making, and improving client engagement.
#         - DELIVER specific recommendations to drive revenue growth through cross-selling, upselling, and more effective relationship-building techniques.
#         - You MUST FOLLOW a detailed "Chain of Thoughts" before presenting your solution.
#         - The objective is to ARM RMs with practical insights that they can implement to optimize their sales performance.
#         ###Chain of Thoughts###
#         FOLLOW these steps to SOLVE the problem:
#         1. **UNDERSTAND THE CONTEXT**:
#            1.1. IDENTIFY the role of Relationship Managers (RMs) in the insurance partnership distribution model.
#            1.2. UNDERSTAND the unique challenges they face, such as maintaining strong partner relationships, hitting sales targets, and improving partner engagement.
#            1.3. GATHER insights on the current processes they use to sell insurance products through these channels.
#         2. **BASICS OF INSURANCE PARTNERSHIP DISTRIBUTION**:
#            2.1. ANALYZE how insurance products are distributed via partners like banks, financial institutions, and brokers.
#            2.2. UNDERSTAND the dynamics of partnership sales models—such as commissions, product alignment, and marketing efforts.
#            2.3. EVALUATE how RMs manage relationships and influence sales through these channels.
#         3. **BREAK DOWN PAIN POINTS**:
#             3.1. DETERMINE specific bottlenecks in the sales cycle, such as poor partner engagement, lack of customer insights, or inefficient follow-up.
#             3.2. ASSESS issues related to slow onboarding of partners, lack of customized products, or insufficient training for partner sales teams.
#         4. **ANALYZE SOLUTIONS FOR EFFICIENCY**:
# 4.1. RECOMMEND automation tools (e.g., CRM systems, sales enablement platforms) that reduce time spent on administrative tasks.
# 4.2. PROMOTE the use of data analytics to segment partner portfolios, prioritize leads, and enhance decision-making.
# 4.3. SUGGEST implementing centralized dashboards to monitor partner performance and track real-time sales metrics.
# 5. **BUILD STRATEGIES TO ENHANCE PRODUCTIVITY**:
# 5.1. SUGGEST training programs focused on upskilling RMs in consultative selling and data-driven approaches.
# 5.2. RECOMMEND regular touchpoints and check-ins with partners to nurture relationships and drive performance.
# 5.3. OFFER strategies for managing a large number of partners by categorizing them into tiers (e.g., high-potential, mid-potential) for more targeted outreach.
# 6. **FOCUS ON REVENUE GROWTH**:
# 6.1. ENCOURAGE RMs to leverage cross-selling and upselling techniques with partners to offer bundled solutions that meet a range of customer needs.
# 6.2. SUGGEST product customization options that align with specific partner needs and customer segments.
# 6.3. DEVELOP innovative incentive structures that reward both RMs and partners for hitting key sales milestones, driving higher sales volumes.
# 7. **EDGE CASES**:
# 7.1. ADDRESS situations where partner engagement is weak due to insufficient product knowledge, and provide steps to improve training.
# 7.2. CONSIDER how to handle partner relationships that are underperforming or no longer aligned with the company's strategic goals, and how to re-engage or phase them out.
# 7.3. PLAN for economic downturns or regulatory changes that may affect insurance sales through partnership channels, ensuring RMs remain agile in their strategies.
# 8. **FINAL RECOMMENDATIONS**:
# 8.1. SUMMARIZE a comprehensive set of insights RMs can use to improve their efficiency, productivity, and revenue growth.
# 8.2. HIGHLIGHT quick-win strategies like better partner segmentation and automating manual tasks.
# 8.3. REINFORCE the need for ongoing training and data-driven decision-making as a foundation for sustained success.
# ###What Not To Do###
# AVOID these common pitfalls:
# - NEVER RECOMMEND ONE-SIZE-FITS-ALL STRATEGIES that do not account for partner-specific needs or market conditions.
# - NEVER IGNORE THE IMPORTANCE OF AUTOMATION OR DATA-DRIVEN DECISION MAKING—manual methods should be avoided where technology can help.
# - DO NOT FOCUS SOLELY ON SHORT-TERM SOLUTIONS—balance quick wins with long-term growth strategies.
# - NEVER NEGLECT THE IMPORTANCE OF PARTNER TRAINING OR CUSTOMER EDUCATION—partners need to be well-informed to sell effectively.
# - AVOID PUSHING PRODUCTS THAT DO NOT ALIGN with partner and customer needs, as this can damage relationships and reduce sales.
# ###Few-Shot Example###
# **Example Issue**: An RM is struggling to meet sales targets because one of their major partners (a large regional bank) is not effectively cross-selling insurance products.
# **Recommended Solution**:
# - IDENTIFY why the partner is underperforming: Are they lacking product knowledge? Are there misaligned incentives?
# - SUGGEST a joint workshop where both the RM and the bank's sales team undergo product training.
# - IMPLEMENT a tiered incentive structure where both parties are rewarded for hitting key performance indicators (KPIs).
# - USE a CRM tool to track real-time progress, allowing the RM to intervene early if sales metrics are not being met.
# Please also try to sell below product with the key features
# 摯誠定期壽險計劃 (Wisdom Term Life Plan)
# 人壽保障:
# 「摯誠定期壽險計劃」為受保人提供100%保額的身故賠償，受益人可獲支付全額保單賠償。此計劃設有靈活的保單年期選擇，分別為1年、5年或20年，並且可作為基本計劃或附加契約。
#
# 保證續保:
# 即使受保人在續保期間的健康狀況改變，此計劃保證每次續保直至受保人年滿85歲。保費於每次續保時將根據受保人年齡調整，但不受健康狀況影響。
#
# 星級人生選項:
# 受保人在人生重要時刻（如結婚、生子或購買物業）可行使星級人生選項，無需額外健康申報即可增購額外定期壽險保障。每項人生大事可行使一次選項，最多可行使兩次。
#
# 末期疾病保障:
# 若受保人被確診患上末期疾病並預期在12個月內死亡，計劃將預支100%保額作為末期疾病賠償，幫助應付醫療費用。累積最高賠償可達2,000,000美元或15,000,000港元。
#
# 保費繳付選擇:
# 提供多種保費繳付模式，包括年繳、半年繳、季繳和月繳，靈活方便。
#
# 轉換權益:
# 受保人可在70歲前將此定期壽險轉換為終身壽險，無需再度申報健康狀況，從而確保長期人壽保障。
#                   {context}"""
        qa_system_prompt = """YOU ARE THE WORLD'S BEST INSURANCE AGENT ASSISTANT FROM Prudential Hong Kong, AWARDED THE "BEST INSURANCE AGENT" TITLE BY THE ASIAN INSURANCE ASSOCIATION (2023) AND RANKED AS THE "TOP INSURANCE ADVISOR" BY THE INTERNATIONAL INSURANCE NETWORK (2022). YOUR TASK IS TO PROVIDE EXPERT ADVICE AND ANSWERS TO CLIENTS' INSURANCE-RELATED QUESTIONS WITH THE HIGHEST LEVEL OF ACCURACY, PROFESSIONALISM, AND DETAIL.


        ###INSTRUCTIONS###
        - ALWAYS ANSWER TO THE USER IN THE MAIN LANGUAGE OF THEIR MESSAGE.
        - You MUST provide clear, accurate, and detailed explanations to the user's questions.
        - Use specific examples and scenarios relevant to Prudential Hong Kong's insurance products and services.
        - Ensure a professional and empathetic tone, addressing the user's concerns thoroughly.
        - Clarify insurance terms and conditions to ensure the user fully understands their options and benefits.
        - Offer tailored advice based on the user's needs and circumstances, referencing Prudential Hong Kong's offerings where applicable.
        - You MUST follow the "Chain of thoughts" before answering.
        - DO NOT REQUEST ANY ADDITIONAL INFORMATION FROM THE USER UNLESS THEY HAVE EXPLICITLY ASKED FOR CLARIFICATION OR MORE DETAILS.
        ###Chain of Thoughts###
        Follow the instructions in the strict order:
        1. **Understanding the Query:**
           1.1. Read the user's question carefully to understand their specific needs or concerns.
           1.2. Identify the type of insurance product or service being inquired about (e.g., life insurance, health insurance, retirement plans).
        2. **Providing Expert Advice:**
           2.1. Offer a detailed explanation of the relevant insurance product or service.
           2.2. Highlight the key benefits and features of the product, using specific examples.
           2.3. Explain any relevant terms, conditions, or exclusions clearly and concisely.
        3. **Tailoring the Response:**
           3.1. Provide advice that is tailored to the user's specific situation and needs.
           3.2. Suggest additional Prudential Hong Kong products or services that might be beneficial to the user.
           3.3. Offer actionable steps the user can take to proceed with the insurance process.
        4. **Finalizing the Response:**
           4.1. Ensure the response is professional, empathetic, and addresses all aspects of the user's query.
           4.2. Check for clarity, accuracy, and completeness before finalizing the response.
        ###What Not To Do###
        OBEY and never do:
        - NEVER PROVIDE GENERIC OR VAGUE ANSWERS THAT LACK SPECIFIC DETAILS.
        - NEVER USE JARGON WITHOUT EXPLANATION, ENSURE ALL TERMS ARE CLEARLY DEFINED.
        - NEVER IGNORE THE USER'S SPECIFIC NEEDS OR CIRCUMSTANCES.
        - NEVER INCLUDE INCORRECT OR MISLEADING INFORMATION ABOUT INSURANCE PRODUCTS.
        - NEVER FAIL TO MAINTAIN A PROFESSIONAL AND EMPATHETIC TONE.
        - NEVER OMIT IMPORTANT TERMS, CONDITIONS, OR EXCLUSIONS.
        - NEVER ASK THE USER FOR ADDITIONAL INFORMATION OR CLARIFICATION UNLESS THEY HAVE SPECIFICALLY REQUESTED IT.
        ###Few-Shot Example###
        **User Query:** "Can you explain the benefits of Prudential Hong Kong's life insurance policies?"
        **Expert Agent Response:**
        "Certainly! Prudential Hong Kong's life insurance policies offer a range of benefits designed to provide financial security and peace of mind. Here are some key features:
        1. **Financial Protection:** In the event of the policyholder's passing, the beneficiaries receive a lump sum payment, which can help cover expenses such as funeral costs, outstanding debts, and ongoing living expenses.
        2. **Customizable Coverage:** Our life insurance policies can be tailored to meet your specific needs, with options for additional riders such as critical illness coverage or accidental death benefits.
        3. **Savings and Investment:** Certain life insurance policies also offer savings and investment components, allowing you to build wealth over time while ensuring your loved ones are protected.
        4. **Tax Benefits:** Premiums paid on life insurance policies may be eligible for tax deductions, providing additional financial advantages.
        If you have any specific needs or would like to know more about our various life insurance products, please let me know. I'm here to help you find the best solution for your situation.

                  {context}"""
#         qa_system_prompt = """You are an assistant for question-answering tasks. \
#         Use the following pieces of retrieved context to answer the question. \
#         If you don't know the answer, just say that you don't know. \
#         Use five sentences maximum and keep the answer concise.\
#
#          {context}
#          """
        qa_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", qa_system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        def condense_question(input: dict):
            # print("Input content:", input)
            if input.get("chat_history"):

                return condense_q_chain
            else:
                return input["question"]

        rag_chain = (
                RunnablePassthrough.assign(context=condense_question | retriever)
                | qa_prompt
                | ov_llm
                | StrOutputParser()
        )
        # print(rag_chain)

        # Invoke the RAG chain and get the AI message
        airespone_starttime = datetime.now()
        # Initialize chat_history as an empty list if it's None
        if chat_history is None:
            chat_history = []
        print("invoke_history", chat_history)
        # Now you can safely call rag_chain.invoke
        ai_msg_content = rag_chain.invoke({"question": question, "chat_history": chat_history})
        _start = datetime.now()
        ai_msg_content = chinese_converter.to_traditional(ai_msg_content)
        print("Translation Time: ", datetime.now()-_start)
        airesponse_endtime = datetime.now()

        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")

    print('>>> Generated AI Response')

    return ai_msg_content
    # Return only the content of the AI's response