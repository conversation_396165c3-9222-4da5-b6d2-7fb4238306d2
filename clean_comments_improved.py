import re

def remove_comments(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 删除HTML注释 <!-- -->
    content = re.sub(r'<!--[\s\S]*?-->', '', content)
    
    # 删除CSS和JS多行注释 /* */
    content = re.sub(r'/\*[\s\S]*?\*/', '', content)
    
    # 删除JS单行注释 //
    # 不删除URL中的 //
    lines = content.split('\n')
    processed_lines = []
    for line in lines:
        if '//' in line and not ('http://' in line or 'https://' in line):
            line = re.sub(r'//.*$', '', line)
        processed_lines.append(line)
    content = '\n'.join(processed_lines)
    
    # 删除连续的空行
    content = re.sub(r'\n\s*\n+', '\n\n', content)
    
    # 写入清理后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已成功删除注释并保存到 {output_file}")

if __name__ == "__main__":
    remove_comments("templates/index_json_database_new_ui_21.html", "templates/index_clean_final.html") 