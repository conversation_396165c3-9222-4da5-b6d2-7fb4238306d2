"""

    Module Name :           preprocessing_module
    Last Modified Date :    23 Jan 2024

"""

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.text_splitter import CharacterTextSplitter
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.embeddings import HuggingFaceInstructEmbeddings
from langchain.embeddings.huggingface import HuggingFaceEmbeddings
from sentence_transformers import SentenceTransformer


def chunk_document(document,
                   chunk_config):
    
    if chunk_config["method"] == "recursive_character_text_splitter":
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_config["size"], 
                                                       chunk_overlap=chunk_config["overlap"])
    elif chunk_config["method"] == "character_text_splitter":
        text_splitter = CharacterTextSplitter(chunk_size=chunk_config["size"], 
                                              chunk_overlap=chunk_config["overlap"])
    
    else:
        print('Error : Chunking Method Not Found')
    chunked_content = text_splitter.split_documents(document)

    return chunked_content

def load_embedding(embedding_config):
    method = embedding_config["method"]

    if method.upper() == "HUGGINGFACE":
        embedding = HuggingFaceInstructEmbeddings()
        # embedding = SentenceTransformer("mixedbread-ai/mxbai-embed-large-v1")



    elif method.upper() == "MINILM":
        embedding = HuggingFaceEmbeddings(model_name='sentence-transformers/all-MiniLM-L6-v2')

    elif method.upper() == "OPENAI":
        embedding = OpenAIEmbeddings()

    else:
        print(f"Caution : Unknown Embedding Method <{method}>. OpenAIEmbeddings is thus Used by Default")
        embedding = HuggingFaceInstructEmbeddings()

    return embedding

def format_docs(docs):
    return "\n\n".join(doc.page_content for doc in docs)