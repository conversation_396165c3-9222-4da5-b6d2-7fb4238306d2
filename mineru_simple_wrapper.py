#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的MinerU虚拟环境包装器
"""

import subprocess
import sys
import json
import tempfile
import os

VENV_PYTHON = r"/home/<USER>/Downloads/dtt-llm-rag/mineru_venv/bin/python"

def process_pdf_in_venv(pdf_path, output_dir=None):
    """在虚拟环境中处理PDF"""
    if output_dir is None:
        output_dir = tempfile.mkdtemp()
    
    # 创建简单的处理脚本
    script_content = """
import sys
import json
import os

def main():
    pdf_path = sys.argv[1] if len(sys.argv) > 1 else None
    output_dir = sys.argv[2] if len(sys.argv) > 2 else "/tmp"
    
    if not pdf_path:
        print("ERROR")
        print("No PDF path provided")
        return
    
    try:
        # 尝试导入MinerU
        magic_pdf_parse = None
        
        try:
            from magic_pdf.api.magic_pdf_parse import magic_pdf_parse
        except ImportError:
            try:
                from magic_pdf.pipe.UNIPipe import UNIPipe
                def magic_pdf_parse(pdf_path, output_dir, **kwargs):
                    pipe = UNIPipe(pdf_path, output_dir)
                    return pipe.pipe_parse()
            except ImportError:
                print("ERROR")
                print("无法导入MinerU")
                return
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 简单的处理调用
        result = magic_pdf_parse(pdf_path, output_dir)
        
        if result:
            print("SUCCESS")
            print(json.dumps({"status": "success", "data": str(result)}, ensure_ascii=False))
        else:
            print("FAILED")
            print("No result returned")
            
    except Exception as e:
        print("ERROR")
        print(str(e))

if __name__ == "__main__":
    main()
"""
    
    # 写入临时脚本文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
        f.write(script_content)
        script_path = f.name
    
    try:
        # 在虚拟环境中执行
        result = subprocess.run([VENV_PYTHON, script_path, pdf_path, output_dir], 
                              capture_output=True, text=True, timeout=300)
        
        lines = result.stdout.strip().split('\n')
        if lines and lines[0] == "SUCCESS":
            return {"success": True, "data": lines[1] if len(lines) > 1 else None}, None
        elif lines and lines[0] == "FAILED":
            return None, lines[1] if len(lines) > 1 else "Unknown error"
        elif lines and lines[0] == "ERROR":
            return None, lines[1] if len(lines) > 1 else "Unknown error"
        else:
            return None, "Unexpected output format"
            
    except subprocess.TimeoutExpired:
        return None, "Processing timeout"
    except Exception as e:
        return None, str(e)
    finally:
        try:
            os.unlink(script_path)
        except:
            pass

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python mineru_simple_wrapper.py <pdf_path>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    result, error = process_pdf_in_venv(pdf_path)
    
    if result:
        print("✅ 处理成功")
        print(f"结果: {result}")
    else:
        print(f"❌ 处理失败: {error}")
