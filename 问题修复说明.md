# 预计算布局文件处理问题修复说明

## 问题诊断

从您提供的错误日志可以看出，主要问题是：

1. **空数据处理**: 预计算布局文件 `amberplace.json` 被成功读取，但提取了 **0 个文本块**
2. **向量存储错误**: 系统尝试将空数据插入到 Milvus 向量数据库中，导致 `ParamError` 错误
3. **数据验证不足**: 代码没有充分验证处理后的数据是否有效

## 修复内容

### 1. 增强数据验证和错误处理

**修改文件**: `chatbot_newui_new_version_v4.py`

#### 1.1 改进 `process_precomputed_layout()` 函数

- ✅ 添加详细的调试输出
- ✅ 检查空文本块并跳过
- ✅ 验证JSON数据格式
- ✅ 更好的错误处理和提示信息

#### 1.2 增强 `upload_file()` 函数

- ✅ 添加多层数据验证
- ✅ 确保文本块数量与元数据数量匹配
- ✅ 空数据时自动回退到原始PDF处理
- ✅ 处理后再次验证数据有效性

### 2. 新增调试工具

#### 2.1 测试脚本 (`test_precomputed_layout.py`)

运行此脚本可以验证您的JSON文件格式：

```bash
python test_precomputed_layout.py
```

#### 2.2 调试API端点 (`/debug-precomputed-file`)

可以通过POST请求检查JSON文件：

```bash
curl -X POST http://localhost:8037/debug-precomputed-file \\
     -H "Content-Type: application/json" \\
     -d '{"file_path": "amberplace.json"}'
```

## 预计算JSON文件格式要求

### 格式1: 简单列表格式（推荐）

```json
[
    {
        "chunk_text": "这里是文档内容...",
        "metadata": {
            "source": "document.pdf",
            "start_page_num": 1,
            "end_page_num": 1,
            "start_line_num": 1,
            "end_line_num": 5,
            "content_type": "text",
            "bbox": [10, 20, 300, 150]
        }
    }
]
```

### 重要要求：

1. **chunk_text 不能为空**: 必须包含实际文本内容
2. **metadata 必须完整**: 所有字段都应该存在
3. **content_type**: 支持 "text", "table", "image", "heading" 等
4. **bbox 格式**: [x1, y1, x2, y2] 坐标数组

## 问题排查步骤

### 步骤1: 检查您的JSON文件

运行测试脚本检查文件格式：

```bash
python test_precomputed_layout.py
```

### 步骤2: 验证JSON内容

1. 打开 `uploads/amberplace.json`
2. 检查是否有 `chunk_text` 字段不为空的项目
3. 确认 `metadata` 字段包含所有必要信息

### 步骤3: 使用调试API

如果您的JSON文件存在问题，可以使用新的调试端点获取详细信息。

## 常见问题解决

### 问题1: JSON文件为空或格式错误

**解决方案**: 
- 确保JSON文件不为空
- 使用在线JSON验证器检查格式
- 参考提供的示例文件

### 问题2: chunk_text 全部为空

**解决方案**:
- 检查您的"miner"工具是否正确提取了文本
- 确保每个文本块都有实际内容
- 移除空的文本块

### 问题3: 元数据字段缺失

**解决方案**:
- 确保每个项目都有完整的 metadata
- 参考示例文件中的字段结构
- 使用默认值填充缺失字段

## 测试验证

修复后，您的上传流程应该：

1. ✅ 成功检测到预计算文件
2. ✅ 正确提取文本块（数量 > 0）
3. ✅ 成功插入向量数据库
4. ✅ 完成文件上传过程

## 下一步操作

1. 运行测试脚本验证修复效果
2. 检查您的 `amberplace.json` 文件内容
3. 如果仍有问题，使用调试API获取详细信息
4. 重新上传文件测试

如果问题仍然存在，请提供您的 `amberplace.json` 文件内容以便进一步诊断。 