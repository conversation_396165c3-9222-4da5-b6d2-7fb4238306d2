from flask import Flask, request, redirect, url_for, render_template, flash
import json
import os

# Assuming the existing app initialization here
app = Flask(__name__)
app.secret_key = 'your_secret_key'  # Needed for session management and flash messages


# Function to load user credentials from a file
def load_user_credentials():
    try:
        with open('user_credentials.json', 'r') as file:
            return json.load(file)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}


# Function to save user credentials to a file
def save_user_credentials(credentials):
    with open('user_credentials.json', 'w') as file:
        json.dump(credentials, file, indent=4)


@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        # Load existing credentials
        user_credentials = load_user_credentials()

        # Check if user exists and password is correct
        if username in user_credentials and user_credentials[username] == password:
            # Redirect to another page if login is successful
            return redirect(url_for('index'))  # Assuming 'index' is the function name of your main page
        else:
            flash('Invalid username or password')

    # Show the login form
    return render_template('login.html')


# Add more routes and logic as needed

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8010)
