# import re

# def extract_bracketed_text(text):
#     """
#     Extracts the text inside square brackets from the given text.
#     """
#     pattern = r'\[(.*?)\]'
#     matches = re.findall(pattern, text)
#     return matches

# # Example usage
# text = "This [\"is\"] a [sample] text with [some] [brackets]."
# bracketed_text = extract_bracketed_text(text)
# print(bracketed_text[0])
import ast
from typing import Any, List, Dict, Optional, Tuple
from kg_construction.CustomNodes import *
import requests
import re
import preprocessing
import init_interface
from langchain.document_loaders import DirectoryLoader
import time
import multiprocessing

def extract_entities(text:str,i) -> list :
    url = 'http://*************:5002/v1/chat/completions'
    url = 'http://*************:5002/v1/chat/completions'
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json'
    }
    data = {
        'messages': [
            {
            'content' : f"""
            Text:  \"\"\""{text}"\"\"\""
            You are an smart and helpful AI assistant. 
            Given the above text, extract all entities into a list of string format. 
            Output one single list of string only, without comments.
            For example: ["Pen", "Science", "Tall Building"]. Please include the "" to indicate it is a string.
            """,
            'role' : 'system'
            },
            {
            'content': "AI: [\"",
            'role': 'AI'
            }
        ],
        'max_tokens': 500,
        'temperature': 0,
    }
    
    response = requests.post(url, headers=headers, json=data)
    completion_tokens = response.json()['usage']['completion_tokens']
    prompt_tokens = response.json()['usage']['prompt_tokens']
    response = response.json()['choices'][0]['message']['content']

    # Add ending characters for incomplete messages
    if not response.endswith("\"]"):
        response += "\"]"
    #print("Response: ", response)
    
    pattern = r'\[(.*?)\]'
    matches = re.findall(pattern, response)
    if len(matches)>0:
        response = f"[{matches[0]}]"
    #print("Parsed Response: ", response)
    print("number",i,"complete")

    # Remove prohibited characters
    def transform(result):
        rx = r'[\n；\" ]'
        return re.sub(rx,"",str(result)[:15])

    try:
        result_list =  [transform(result) for result in ast.literal_eval(response)]
        return i
        return result_list, completion_tokens, prompt_tokens
    except Exception as e:
        print("Error is ",e)
        return i
        return [], 0, 0


start_time = time.time()
chunk_mode = 'chunk-1'
embedding_mode = 'embedding-1'
chunk_method, embedding_method = init_interface.load_preprocessing_config(chunk_mode, embedding_mode)
loader = DirectoryLoader("./kg_construction/data/data4", show_progress=True, use_multithreading=True)
document = loader.load()
print("loaded other type document")
if not document:
    print('Warning: Cannot Find Document in Upload Folder')
chunked_content = preprocessing.chunk_document(document, chunk_method)
text_list1 = [chunk.page_content for chunk in chunked_content]
text_list1 = text_list1[:26]



if True:
    with multiprocessing.Pool(processes=25) as pool:

        # Submit tasks to the pool and collect the results
        results = [(1,pool.apply_async(extract_entities, args=(text,i,))) for i, text in enumerate(text_list1)]
        pool.close()
        pool.join() 

        # Wait for all tasks to complete and get the results
        output = [(r[0], r[1].get()) for r in results]
else:
    output = [extract_entities(text,i) for i, text in emerate(text_list1)]


print("THE FINAL OUTPUT:", output)
print("total seconds: ", time.time() - start_time)