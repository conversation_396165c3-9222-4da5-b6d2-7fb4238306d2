"""

    Module Name :           mixtral_module_online
    Last Modified Date :    3 Jan 2024

"""
import subprocess

# Import Open-source Libraries
from langchain import HuggingFacePipeline, hub
from langchain.memory import ConversationBufferMemory
from langchain.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain.schema import StrOutputParser
from langchain_community.chat_models import ChatOpenAI
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableParallel
from langchain_core.messages import HumanMessage
import transformers
from transformers import AutoTokenizer
import torch
from huggingface_hub import hf_hub_download
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain.llms import LlamaCpp
from langchain import PromptTemplate, LLMChain
from datetime import datetime

# Import Self-defined Modules
import preprocessing
from langchain_community.llms import Ollama
from langchain_community.chat_models import ChatOllama
from ollama import Client

import os
from langchain.utilities import GoogleSearchAPIWrapper

os.environ["GOOGLE_CSE_ID"] = "33bbba4170237431c"
os.environ["GOOGLE_API_KEY"] = "AIzaSyDPrPQ-3TMOxCGtASOh32LCM4ABCLWkOys"
search = GoogleSearchAPIWrapper()


# init mixtral pipeline
def init_mixtral_piepeline(model_para):
    pipeline = transformers.pipeline(
        model_para["task"],
        model=model_para["model"],
        device_map=model_para["device_map"],
        model_kwargs={"torch_dtype": eval(model_para["torch_dtype"])},
        max_new_tokens=model_para["max_new_tokens"],
        repetition_penalty=model_para["repetition_penalty"],
        return_full_text=eval(model_para["return_full_text"]),
        pad_token_id=2,
        eos_token_id=2
    )
    return pipeline


# mixtral Module
def mixtral_response(
                    question,
                    system_prompt,
                    condense_system_prompt,
                    prompt_template,
                    model_para,
                    retriever,
                    chat_history=None):
    
    print("... Generating AI Response")

    ai_msg_content = ''

    aimodel_starttime = datetime.now()
    # llm = Ollama(model="mistral")
    llm = Ollama(base_url="http://*************:11434", model="mistral")
    # llm = Ollama(base_url="http://*************:11434", model="mixtral:8x7b-instruct-v0.1-fp16")
    # llm = Ollama(base_url="http://*************:11434", model="mistral_ball_sir")
    # llm = Ollama(base_url="http://*************:11434", model="mixtral")
    # llm = Ollama(base_url="http://*************:11434", model="mixtral:8x7b-instruct-v0.1-q6_K")
    # llm = Ollama(base_url="http://*************:11434", model="mistral:7b-instruct-v0.2-fp16")

    # llm = Ollama(model="mixtral:8x7b")
    # llm = Ollama(model="mixtral:8x7b-instruct-v0.1-fp16")
    aimodel_endtime = datetime.now()
    print(f"AI Model Loading Time = {aimodel_endtime - aimodel_starttime}")

    # llm = ChatOllama(model="mixtral:8x7b")
    # Without Vector Store
    if not retriever:
        airespone_starttime = datetime.now()

        ai_msg_content = llm.invoke(question)
        airesponse_endtime = datetime.now()
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")
  #       prompt = [
  #   "Answer the Question at the end.",
  #   "\nQuestion: {question}"
  # ]
  #
        # for local call
  #       ai_msg_content = llm.generate(prompts=prompt, num_predict=-1)
  #       print(ai_msg_content)
        # for ollama api call
        # client = Client(host='http://*************:11434')
        # ai_msg_content = client.chat(model='mistral', messages=[
        #     {
        #         'role': 'user',
        #         'content': question,
        #     },
        # ])
        # print(ai_msg_content["message"]["content"])
        # ai_msg_content = ai_msg_content["message"]["content"]
        # for triton api call
        airespone_starttime = datetime.now()

        # command = f"python3 end_to_end_grpc_client.py -u *************:38001 -p '{question}' -S -o 128"
        #
        # process = subprocess.run(command, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        #
        # output = process.stdout.decode('utf-8')
        # error = process.stderr.decode('utf-8')
        #
        # if error:
        #     print("Error:", error)
        # else:
        #     print("Output:", output)
        #     ai_msg_content = output
        #     airesponse_endtime = datetime.now()
        #     print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")

        # messages = [{"role": "user", "content": question}]
        # prompt = pipeline.tokenizer.apply_chat_template(
        #     messages,
        #     tokenize=False,
        #     add_generation_prompt=True
        # )
        #
        # outputs = pipeline(
        #     prompt,
        #     max_new_tokens=10000,
        #     do_sample=True,
        #     top_k=50,
        #     top_p=0.95)
        #
        # ai_msg_content = outputs[0]["generated_text"].replace('<s>[INST] who is Alice? [/INST]', '')


    # With Vector Store
    else:
        # aimodel_starttime = datetime.now()
        # # llm = HuggingFacePipeline(pipeline=pipeline)
        #
        # aimodel_endtime = datetime.now()
        # print(f"AI Model Loading Time = {aimodel_endtime - aimodel_starttime}")

        # condense_q_system_prompt = """Given a chat history and the user question \
        # which might reference the chat history, formulate a standalone question \
        # which can be understood without the chat history. Do NOT answer the question, \
        # just reformulate it if needed and otherwise return it as is."""
        #
        # condense_q_prompt = ChatPromptTemplate.from_messages(
        #     [
        #         ("system", condense_q_system_prompt),
        #         MessagesPlaceholder(variable_name="chat_history"),
        #         ("human", "{question}"),
        #     ]
        # )
        #
        # condense_q_chain = condense_q_prompt | llm | StrOutputParser()
        #
        # qa_system_prompt = """You are an assistant for question-answering tasks. \
        # Use the following pieces of retrieved context to answer the question. \
        # If you don't know the answer, just say that you don't know. \
        # Use three sentences maximum and keep the answer concise.\
        #
        #  {context}"""
        #
        # qa_prompt = ChatPromptTemplate.from_messages(
        #     [
        #         ("system", qa_system_prompt),
        #         MessagesPlaceholder(variable_name="chat_history"),
        #         ("human", "{question}"),
        #     ]
        # )
        #
        # def condense_question(input: dict):
        #     print("Input content:", input)
        #     if input.get("chat_history"):
        #         return condense_q_chain
        #     else:
        #         return input["question"]
        #
        # rag_chain = (
        #         RunnablePassthrough.assign(context=condense_question | retriever | preprocessing.format_docs)
        #         | qa_prompt
        #         | llm
        #         | StrOutputParser()
        # )
        # Invoke the RAG chain and get the AI message

        # Create prompt from prompt template
        prompt = PromptTemplate(
            input_variables=["context", "question"],
            template=prompt_template,
        )

        from langchain.retrievers.web_research import WebResearchRetriever

        web_research_retriever = WebResearchRetriever.from_llm(
            vectorstore=retriever,
            llm=llm,
            search=search,
        )

        # prompt = PromptTemplate.from_template(prompt_template)
        # print("prompt: ", prompt)
        # llm_chain = LLMChain(llm=llm, prompt=prompt)
        # print("llm_chain: ", llm_chain)
        rag_chain = (
                {"context": web_research_retriever, "question": RunnablePassthrough()}
                | llm
        )
        print("retriever: ", retriever)
        # print("rag_chain: ", rag_chain)
        # template = """You are a chatbot having a conversation with a human.
        #
        # {chat_history}
        # Human: {human_input}
        # Chatbot:"""
        #
        # prompt = PromptTemplate(
        #     input_variables=["chat_history", "human_input"], template=template
        # )
        # memory = ConversationBufferMemory(memory_key="chat_history")
        # # llm = OpenAI()
        # llm_chain = LLMChain(
        #     llm=llm,
        #     prompt=prompt,
        #     verbose=True,
        #     memory=memory,
        # )
        airespone_starttime = datetime.now()
        # print(rag_chain)
        # ai_msg_content = llm_chain.predict(human_input=question)
        ai_msg_content = rag_chain.invoke(question)
        # ai_msg_content = rag_chain.invoke({"question": question, "chat_history": chat_history})

        # ai_msg_content = rag_chain.invoke({"question": question, "chat_history": chat_history})
        print("ai_msg_content: ", ai_msg_content)
        airesponse_endtime = datetime.now()
        # for chunks in rag_chain.stream(question):
        #     print(chunks)
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")

        ai_msg_content = ai_msg_content["text"]
    print('>>> Generated AI Response')

    return ai_msg_content
    # Return only the content of the AI's response
