#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU API客户端
用于通过API调用MinerU服务
"""

import requests
import json
import os
import tempfile
import time
from typing import Dict, List, Tuple, Optional
import logging

class MinerUAPIClient:
    """MinerU API客户端"""
    
    def __init__(self, api_url: str = "http://localhost:8080", api_key: Optional[str] = None):
        """
        初始化API客户端
        
        Args:
            api_url: MinerU API服务地址
            api_key: API密钥（如果需要）
        """
        self.api_url = api_url.rstrip('/')
        self.api_key = api_key
        self.logger = logging.getLogger(__name__)
        
        # 设置请求头
        self.headers = {
            'Content-Type': 'application/json'
        }
        if api_key:
            self.headers['Authorization'] = f'Bearer {api_key}'
    
    def check_service_status(self) -> bool:
        """检查MinerU服务是否可用"""
        try:
            response = requests.get(f"{self.api_url}/health", timeout=10)
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"无法连接到MinerU服务: {str(e)}")
            return False
    
    def upload_and_process_pdf(self, pdf_path: str, config: Optional[Dict] = None) -> Tuple[Optional[Dict], Optional[str]]:
        """
        上传PDF并处理
        
        Args:
            pdf_path: PDF文件路径
            config: 处理配置
            
        Returns:
            Tuple[layout_data, error_message]: 布局数据和错误信息
        """
        if not os.path.exists(pdf_path):
            return None, f"文件不存在: {pdf_path}"
        
        try:
            # 准备文件上传
            with open(pdf_path, 'rb') as f:
                files = {'file': (os.path.basename(pdf_path), f, 'application/pdf')}
                
                # 准备配置数据
                data = {}
                if config:
                    data['config'] = json.dumps(config)
                
                # 发送请求
                response = requests.post(
                    f"{self.api_url}/process",
                    files=files,
                    data=data,
                    headers={'Authorization': self.headers.get('Authorization', '')},
                    timeout=300  # 5分钟超时
                )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('layout_data'), None
            else:
                error_msg = f"API请求失败: {response.status_code} - {response.text}"
                self.logger.error(error_msg)
                return None, error_msg
                
        except Exception as e:
            error_msg = f"处理PDF时出错: {str(e)}"
            self.logger.error(error_msg)
            return None, error_msg
    
    def process_pdf_async(self, pdf_path: str, config: Optional[Dict] = None) -> Optional[str]:
        """
        异步处理PDF
        
        Args:
            pdf_path: PDF文件路径
            config: 处理配置
            
        Returns:
            Optional[str]: 任务ID，如果失败返回None
        """
        if not os.path.exists(pdf_path):
            self.logger.error(f"文件不存在: {pdf_path}")
            return None
        
        try:
            with open(pdf_path, 'rb') as f:
                files = {'file': (os.path.basename(pdf_path), f, 'application/pdf')}
                
                data = {}
                if config:
                    data['config'] = json.dumps(config)
                
                response = requests.post(
                    f"{self.api_url}/process_async",
                    files=files,
                    data=data,
                    headers={'Authorization': self.headers.get('Authorization', '')},
                    timeout=60
                )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('task_id')
            else:
                self.logger.error(f"异步处理请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"异步处理PDF时出错: {str(e)}")
            return None
    
    def get_task_result(self, task_id: str) -> Tuple[Optional[Dict], Optional[str], str]:
        """
        获取异步任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            Tuple[layout_data, error_message, status]: 布局数据、错误信息和状态
        """
        try:
            response = requests.get(
                f"{self.api_url}/task/{task_id}",
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                status = result.get('status', 'unknown')
                
                if status == 'completed':
                    return result.get('layout_data'), None, status
                elif status == 'failed':
                    return None, result.get('error', '未知错误'), status
                else:
                    return None, None, status  # processing, pending等状态
            else:
                return None, f"获取任务结果失败: {response.status_code}", 'error'
                
        except Exception as e:
            return None, f"获取任务结果时出错: {str(e)}", 'error'
    
    def wait_for_task_completion(self, task_id: str, max_wait_time: int = 300, poll_interval: int = 5) -> Tuple[Optional[Dict], Optional[str]]:
        """
        等待异步任务完成
        
        Args:
            task_id: 任务ID
            max_wait_time: 最大等待时间（秒）
            poll_interval: 轮询间隔（秒）
            
        Returns:
            Tuple[layout_data, error_message]: 布局数据和错误信息
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            layout_data, error_msg, status = self.get_task_result(task_id)
            
            if status == 'completed':
                return layout_data, None
            elif status == 'failed':
                return None, error_msg
            elif status in ['processing', 'pending']:
                time.sleep(poll_interval)
                continue
            else:
                return None, f"未知状态: {status}"
        
        return None, "任务超时"

class MinerUAPIProcessor:
    """基于API的MinerU处理器"""
    
    def __init__(self, api_url: str = "http://localhost:8080", api_key: Optional[str] = None):
        """初始化API处理器"""
        self.client = MinerUAPIClient(api_url, api_key)
        self.logger = logging.getLogger(__name__)
    
    def is_available(self) -> bool:
        """检查API服务是否可用"""
        return self.client.check_service_status()
    
    def process_pdf(self, pdf_path: str, output_dir: str = None) -> Tuple[Optional[Dict], Optional[str]]:
        """
        处理PDF文件
        
        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录（API模式下可能不需要）
            
        Returns:
            Tuple[layout_data, markdown_content]: 布局数据和Markdown内容
        """
        if not self.is_available():
            self.logger.error("MinerU API服务不可用")
            return None, None
        
        # 配置处理参数
        config = {
            "parse_mode": "auto",
            "output_format": ["json"],
            "lang": "auto"
        }
        
        # 调用API处理
        layout_data, error_msg = self.client.upload_and_process_pdf(pdf_path, config)
        
        if error_msg:
            self.logger.error(f"API处理失败: {error_msg}")
            return None, None
        
        return layout_data, None  # API模式下暂不返回markdown
    
    def convert_to_chunks(self, layout_data: Dict, filename: str) -> List[Tuple[str, Dict]]:
        """
        将API返回的布局数据转换为chunks格式
        这个方法与直接集成版本相同
        """
        chunks_with_metadata = []
        
        try:
            if not layout_data or "pdf_info" not in layout_data:
                self.logger.warning("API返回的布局数据格式不正确")
                return chunks_with_metadata
            
            # 处理逻辑与直接集成版本相同
            for page_idx, page_info in enumerate(layout_data["pdf_info"]):
                page_num = page_idx + 1
                
                if "preproc_blocks" not in page_info:
                    continue
                
                for block_idx, block in enumerate(page_info["preproc_blocks"]):
                    chunk_text = self._extract_text_from_block(block)
                    
                    if not chunk_text or chunk_text.strip() == "":
                        continue
                    
                    metadata = {
                        'start_page_num': page_num,
                        'end_page_num': page_num,
                        'start_line_num': block_idx + 1,
                        'end_line_num': block_idx + 1,
                        'content_type': block.get('type', 'text'),
                        'bbox': block.get('bbox', [0, 0, 0, 0]),
                        'source': filename,
                        'block_index': block_idx,
                        'confidence': block.get('score', 1.0)
                    }
                    
                    chunks_with_metadata.append((chunk_text, metadata))
            
            self.logger.info(f"API处理完成，生成 {len(chunks_with_metadata)} 个文本块")
            return chunks_with_metadata
            
        except Exception as e:
            self.logger.error(f"转换API结果失败: {str(e)}")
            return chunks_with_metadata
    
    def _extract_text_from_block(self, block: Dict) -> str:
        """从block中提取文本内容（与直接集成版本相同）"""
        # 这里的实现与mineru_integration.py中的相同
        text_content = ""
        
        try:
            if block.get('type') == 'text':
                if 'lines' in block:
                    for line in block['lines']:
                        if 'spans' in line:
                            for span in line['spans']:
                                if span.get('type') == 'text' and 'content' in span:
                                    text_content += span['content'] + " "
            # ... 其他类型的处理逻辑
                                    
        except Exception as e:
            self.logger.warning(f"提取文本失败: {str(e)}")
            
        return text_content.strip()

# 使用示例
if __name__ == "__main__":
    # 测试API客户端
    processor = MinerUAPIProcessor("http://localhost:8080")
    
    if processor.is_available():
        print("✅ MinerU API服务可用")
        
        # 测试处理PDF
        test_pdf = "test.pdf"
        if os.path.exists(test_pdf):
            layout_data, _ = processor.process_pdf(test_pdf)
            if layout_data:
                chunks = processor.convert_to_chunks(layout_data, "test.pdf")
                print(f"✅ 成功处理PDF，生成 {len(chunks)} 个文本块")
            else:
                print("❌ PDF处理失败")
        else:
            print(f"⚠️  测试文件不存在: {test_pdf}")
    else:
        print("❌ MinerU API服务不可用")
