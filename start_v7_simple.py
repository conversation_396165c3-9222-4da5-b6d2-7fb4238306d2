#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的v7启动脚本
直接启动，跳过复杂的检查
"""

import os
import sys
import subprocess
import webbrowser
from threading import Timer

def open_browser_delayed(url, delay=3):
    """延迟打开浏览器"""
    def open_browser():
        try:
            webbrowser.open(url)
            print(f"🌐 浏览器已打开: {url}")
        except Exception as e:
            print(f"⚠️  无法自动打开浏览器: {str(e)}")
            print(f"请手动访问: {url}")
    
    Timer(delay, open_browser).start()

def main():
    """简化启动流程"""
    print("🚀 启动 chatbot_newui_new_version_v7")
    print("=" * 50)
    
    # 检查主程序文件
    if not os.path.exists('chatbot_newui_new_version_v7.py'):
        print("❌ 找不到主程序文件: chatbot_newui_new_version_v7.py")
        return False
    
    print("📋 启动信息:")
    print(f"   - Python: {sys.executable}")
    print(f"   - 工作目录: {os.getcwd()}")
    print(f"   - 主程序: chatbot_newui_new_version_v7.py")
    
    # 检查MinerU状态（简单检查）
    mineru_status = "未知"
    
    # 检查虚拟环境
    venv_path = os.path.join(os.getcwd(), "mineru_venv")
    if os.path.exists(venv_path):
        mineru_status = "虚拟环境可用"
        print("✅ 发现MinerU虚拟环境")
    else:
        # 检查系统环境
        try:
            import magic_pdf
            mineru_status = "系统环境可用"
            print("✅ 系统环境MinerU可用")
        except ImportError:
            mineru_status = "不可用（将使用原始处理）"
            print("⚠️  MinerU不可用，将使用原始PDF处理")
    
    print(f"📊 MinerU状态: {mineru_status}")
    print()
    
    # 延迟打开浏览器
    open_browser_delayed('http://localhost:5000/chat', delay=5)
    open_browser_delayed('http://localhost:5000/mineru', delay=8)
    
    print("🌐 应用将在以下地址启动:")
    print("   - 主界面: http://localhost:5000/chat")
    print("   - MinerU状态: http://localhost:5000/mineru")
    print()
    print("⏳ 正在启动应用...")
    print("=" * 50)
    
    try:
        # 直接启动应用
        cmd = [sys.executable, 'chatbot_newui_new_version_v7.py']
        process = subprocess.run(cmd)
        return process.returncode == 0
        
    except KeyboardInterrupt:
        print("\n\n⚠️  应用被用户中断")
        return False
    except Exception as e:
        print(f"\n\n❌ 启动失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 chatbot_newui_new_version_v7 简化启动器")
    print("跳过复杂检查，直接启动")
    print("=" * 50)
    
    try:
        success = main()
        if success:
            print("\n✅ 应用启动成功")
        else:
            print("\n❌ 应用启动失败")
    except KeyboardInterrupt:
        print("\n\n⚠️  启动被用户中断")
    except Exception as e:
        print(f"\n\n❌ 启动过程出现错误: {str(e)}")
        print("请检查错误信息并重试")
