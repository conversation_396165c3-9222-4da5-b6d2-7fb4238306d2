"""

    Module Name :           chatbot
    Last Modified Date :    23 Jan 2024

"""
import json
import re
import shutil
import subprocess
import base64

# from PyQt5.QtWebEngineWidgets import QWebEngineView
from flask import Flask, render_template, request, jsonify, send_from_directory, session, redirect, url_for, flash
import os
import pandas as pd

from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

import warnings

# import llama3_module
import mixtral_module
import mixtral_module_deepseek_nochathistory
import mixtral_module_openai
import mixtral_module_openvino
# import mixtral_module_retriever_to_text
from login import save_user_credentials, load_user_credentials

warnings.filterwarnings('ignore')

# Import Self-defined Libraries
import env
import preprocessing
import prompt_module
import vectorstore_module_newui
from graphstore_module_newui import graphstore_module_newui
import openai_module
import llama_module
# import mixtral_module_pipeline
import init_interface
import test_module
import webbrowser
import threading
from langchain.document_loaders import DirectoryLoader
from langchain.memory import ChatMessageHistory

from werkzeug.serving import make_server
# from PyQt5.QtWidgets import QApplication, QMainWindow
# from PyQt5.QtCore import QThread, QUrl
import sys
import datetime as dt

datetime_userid_format= "%Y%m%d-%H-%M-%S"

app = Flask(__name__)

# File Upload Configuration
os.makedirs(env.default_upload_folder, exist_ok=True)
app.config['UPLOAD_FOLDER'] = env.default_upload_folder     # save all files in the default folder
app.config['ALLOWED_EXTENSIONS'] = env.valid_file_extension # valid file formats
app.config['PROCESSED_FOLDER'] = 'processed/'


""" Temp """
prompt_template_list = init_interface.load_prompt_template()
model_dict, basemodel_list = init_interface.load_model_template()

""" Inititalization """
# Retrieval of Default Configuration
config_dir = os.path.join(env.default_dir, env.default_config_dir)
config = pd.read_json(config_dir, typ="series")

# Retriveval of Preprocessing Configuration
chunk_mode = 'chunk-1'
embedding_mode = 'embedding-1'
chunk_method, embedding_method = init_interface.load_preprocessing_config_chi(chunk_mode, embedding_mode) # For the Chinese version
# chunk_method, embedding_method = init_interface.load_preprocessing_config(chunk_mode, embedding_mode) # For the English version

# Retrieval of System Prompt Parameters
prompt_dir = os.path.join(env.default_dir, env.default_prompt_dir)
system_prompt_para = pd.read_json(prompt_dir, typ="series")

# Retrieval of Vector Store Configuration
vs_dir = os.path.join(env.vs_dir_root, "milvus" + env.vs_config_suffix)
vs_config = pd.read_json(vs_dir, typ="series")

dataset_list = vectorstore_module_newui.init_vsdb(vs_config)
print("load dataset")
print(*dataset_list, sep='\n')


# *** To convert to input parameter
vsschema_uid = 'vsschema-6'
schema_config_dir = env.vs_dir_root + vsschema_uid + env.vsschema_config_fmt
schema_config = pd.read_json(schema_config_dir, typ='series')


# Retrieval RAG Configuration
search_method = 'search-1'
search_config = pd.read_json(env.search_config_dir + \
                             search_method + \
                             env.search_config_suffix, typ="series")


# Retrieval of User Information
""" *** Refine Later >>> Build simple user authentication function """
# raw_uid = 0o02
# # uid_digit = config["uid_digit"]
# # user_id = f"%0{uid_digit}d" %raw_uid

user_id = dt.datetime.now().strftime(datetime_userid_format)

# User Authentication
# user_fullform = env.user_prefix + str(user_id)
user_fullform = env.user_prefix + user_id

user_dir = os.path.join(env.user_dir_root, user_fullform)

# Retrieval of User Profile
# user_profile = os.path.join(user_dir, user_fullform + env.user_prof_suffix)

# Retrieval of User Chat History
user_history = os.path.join(user_dir, user_fullform + env.user_chat_suffix)
print("user_history: ", user_history)
chat_history = {"dir": None,
                "df": None,
                "langchain": None}
print("chat_history: ", chat_history)

chat_history["langchain"] = ChatMessageHistory()
print("chat_history(langchain)", chat_history["langchain"])

chat_history["dir"] = user_history
if not os.path.exists(chat_history["dir"]):
    chat_history["df"] = pd.DataFrame()
    chat_history["langchain"].add_user_message("")
    chat_history["langchain"].add_ai_message("")

else:
    df = pd.read_json(chat_history["dir"], orient="index")
    # print("chat_history(dir): ", chat_history["dir"])

    chat_history["df"] = df
    # print("chat_history(df): ", chat_history["df"])

    # Convert Chat History to langchain Format
    for q_msg, a_msg in zip(df["q_msg"].to_list(),
                            df["a_msg"].to_list()):
        chat_history["langchain"].add_user_message(q_msg)
        chat_history["langchain"].add_ai_message(a_msg)
        # print("chat_history:", chat_history)
        # print("chat_history[langchain]:", chat_history["langchain"])

    # print("chat_history(langchain)", chat_history["langchain"])
    chat_history["langchain"] = chat_history["langchain"].messages
    # print("chat_history(langchain)(messages)", chat_history["langchain"])

print("Chat History at Init: " , chat_history)

# Init Collection
loaded_dataset = None
loaded_dataset_name = None
loaded_files = []


# ***** Retrival of stroed collections and documents
#  {'dataset_name': 'Dataset_Woody_Test',
#  'document_list': ['kyototxt', 'alicetxt']}]
dataset_list = vectorstore_module_newui.init_vsdb(vs_config)

# initate llm pipeline
# torch.set_default_tensor_type(torch.cuda.HalfTensor)
mixtral_config = r"model/mixtral/mixtral-1_config.json"
# pipeline_starttime = datetime.now()
# mixtral_pipeline = mixtral_module.init_mixtral_piepeline(pd.read_json(mixtral_config, typ='series'))
# mixtral_pipeline = mixtral_module_org.init_mixtral_piepeline_2()
llama_config = r"model/llama/llama+rag-1_config.json"
# llama_pipeline = llama_module.init_llama_piepeline(pd.read_json(llama_config, typ='series'))
pipeline_endtime = datetime.now()
# print(f"Pipeline Loading Time = {pipeline_endtime-pipeline_starttime}")

# print(mixtral_pipeline)
""" Chatbot """
@app.route('/chat', methods=['GET'])
def index():
    # return render_template('index.html')
    return render_template('index_json_database_new_ui_21.html')

selected_dataset = None # dataset storing files
@app.route('/selected-dataset', methods=['POST'])
def point_selected_dataset():
    global selected_dataset
    dataset = request.get_json()
    selected_dataset = dataset['selectedDataset']
    print(f'Selected Dataset: <{selected_dataset}>')
    return 'Received Dataset Selection'

selected_instrcut = 'role-default'
@app.route('/select-instruct', methods=['POST'])
def select_instruct():
    global selected_instrcut
    data = request.json

    selected_instrcut = data['selectedInstruct']
    print(f'Selected instruct option <{selected_instrcut}>')
    return jsonify({'message': 'Received instruct option: ' + selected_instrcut})

from datetime import datetime
import fitz  # PyMuPDF
from typing import List, Tuple, Dict
import numpy as np
from sklearn.cluster import DBSCAN
from pydantic import BaseModel
import requests
from flask import jsonify, request
from langchain_community.document_loaders import PyMuPDFLoader, DirectoryLoader
import tiktoken
from uuid import UUID
import os
from datetime import datetime
from flask import jsonify, request
import fitz  # PyMuPDF
from typing import List, Tuple, Dict
import numpy as np
from sklearn.cluster import DBSCAN
from langchain_community.document_loaders import PyMuPDFLoader, DirectoryLoader
from langchain.schema import Document
import preprocessing
import vectorstore_module_newui
import requests
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
import pdfplumber
from PIL import Image as PILImage
import io
import pdb
from openai import OpenAI
from img2table.document import Image
from IPython.display import display_html
# Use PaddleOCR
from img2table.ocr import PaddleOCR
ocr = PaddleOCR(lang="ch")
# Use AzureOCR
# from img2table.ocr import AzureOCR
# from dotenv import load_dotenv
# import os

# load_dotenv()
# # Get the endpoint and subscription key from .env file
# endpoint = os.getenv('AZURE_OCR_ENDPOINT')
# subscription_key = os.getenv('AZURE_OCR_SUBSCRIPTION_KEY')

# # Use the variables in your OCR setup
# ocr = AzureOCR(endpoint=endpoint, subscription_key=subscription_key)

class ImageDescriptionPromptResultResponse(BaseModel):
    entry_id: UUID
    image_name: Optional[str] = None
    prompt_result: Optional[str] = None
    number_of_input_tokens: Optional[int] = None
    number_of_output_tokens: Optional[int] = None

    class Config:
        from_attributes = True

class ImageFileBase64(BaseModel):
    image_file_name:Optional[str] =None
    image_file_bytes_base64str:Optional[str] = None
    image_file_type: Optional[str] = None
    class Config:
        from_attributes = True

class OCRResultResponse(BaseModel):
    ocr_requestid:UUID
    ocr_name: Optional[str]
    ocr_image_results: Optional[list[ImageFileBase64]]
    ocr_text_results:Optional[list[str]]
    class Config:
        from_attributes= True

client = OpenAI(api_key='ollama',base_url="http://*************:30291/v1")
def png_image_to_base64_data_uri(file_path):
    with open(file_path,"rb") as img_file:
        base64_data=base64.b64encode(img_file.read()).decode('utf-8')
        return f"data:image/png;base64,{base64_data}"

def truncate_text(text, max_length=65535):
    if isinstance(text, list):
        text = '\n'.join([' | '.join(map(str, row)) for row in text])
    if len(text) > max_length:
        return text[:max_length], True
    return text, False

def detect_images(page, file_name, folder_path="image"):
    """
    Detect and process images from a PDF page
    Added boundary checking to ensure bbox stays within page limits
    """
    OCR_model_name = "OpenGVLab/InternVL3-78B-AWQ"
    images_extraction = []
    image_descriptions = []
    number_of_tokens = 0
    number_of_input_tokens = 0
    number_of_output_tokens = 0

    # Create folder if it doesn't exist
    os.makedirs(f"{folder_path}/{file_name}", exist_ok=True)

    def clamp(value, min_val, max_val):
        """Helper function to clamp values within bounds"""
        return max(min_val, min(value, max_val))

    for i, image in enumerate(page.images):
        # Get page dimensions for boundary checking
        page_width = page.width
        page_height = page.height

        # Calculate initial coordinates
        x0 = image['x0']
        invertedy0 = page_height - image['y1']
        invertedy1 = page_height - image['y0']

        # Clamp coordinates to page boundaries
        x0 = clamp(x0, 0, page_width)
        x1 = clamp(image['x1'], 0, page_width)
        y0 = clamp(invertedy0, 0, page_height)
        y1 = clamp(invertedy1, 0, page_height)

        # Create bbox with clamped coordinates
        bbox = (x0, y0, x1, y1)

        # Ensure bbox dimensions are valid (width and height > 0)
        if bbox[2] <= bbox[0] or bbox[3] <= bbox[1]:
            print(f"Skipping invalid bbox for image {i}: {bbox}")
            continue

        try:
            cropped_image = page.crop(bbox)
            image_object = cropped_image.to_image(resolution=200)
            image_name = f"image-new-{page.page_number}-{i}"
            image_file = f"{folder_path}/{file_name}/{image_name}.png"
            image_object.save(image_file)

            data_uri = png_image_to_base64_data_uri(image_file)
            messages_checking = [
                {"role": "system",
                 "content": "you are an assistant who can perfectly check if an image contains texts."},
                {"role": "user", "content": [
                    {"type": "image_url", "image_url": {"url": data_uri}},
                    {"type": "text",
                     "text": "Check if there is any text in this image. If there is no text in the image, the output should be: nothing, else, the output should be: some texts are detected"}
                ]}
            ]

            response = client.chat.completions.create(model=OCR_model_name, messages=messages_checking)
            num_of_tokens = response.usage.completion_tokens
            print("image name:", image_name)

            if num_of_tokens <= 3:
                text = None
                print(text)
            else:
                try:
                    image = PILImage.open(image_file).convert('L')
                    print(f"Image size: {image.size}, format: {image.format}")
                    threshold_value = 190
                    grayscale_image = image.point(lambda p: 255 if p > threshold_value else 0)
                    img_byte_arr = io.BytesIO()
                    grayscale_image.save(img_byte_arr, format='PNG')
                    img = Image(src=img_byte_arr.getvalue())

                    print("Attempting to extract tables...")
                    extracted_tables = img.extract_tables(
                        ocr=ocr,
                        implicit_rows=False,
                        borderless_tables=False,
                        min_confidence=50,
                    )

                    print(f"Number of extracted tables: {len(extracted_tables)}")

                    if len(extracted_tables) == 0:
                        print("No tables found, using OCR to extract text.")
                        messages_extraction = [
                            {"role": "system", "content": "you are an assistant who perfectly describes images."},
                            {"role": "user", "content": [
                                {"type": "image_url", "image_url": {"url": data_uri}},
                                {"type": "text",
                                 "text": "Extract the text in this image. The output should be the text content only."}
                            ]}
                        ]
                        response = client.chat.completions.create(model=OCR_model_name, messages=messages_extraction)
                        text = response.choices[0].message.content
                        print(f"Extracted content length: {len(text)}")
                    else:
                        table_content_list = []
                        for i, table in enumerate(extracted_tables):
                            content = table.content
                            table_content_list.append(content)
                            print(f"Table {i + 1} content rows: {len(content)}")
                        text = table_content_list

                except ZeroDivisionError:
                    print(
                        f"ZeroDivisionError occurred while processing image. This might indicate an issue with the image or table structure.")
                    text = "Error: Unable to process image due to division by zero."
                except Exception as e:
                    print(f"An unexpected error occurred while processing image: {str(e)}")
                    text = f"Error: Unable to process image. {str(e)}"

                print(
                    f"Final extracted text type: {type(text)}, length: {len(text) if isinstance(text, (str, list)) else 'N/A'}")

            number_of_tokens = number_of_input_tokens + number_of_output_tokens
            print("text:", text)

            if text is not None:
                if isinstance(text, str) and len(text) < 5:
                    print(f"Skipping image {image_name} due to insufficient text content")
                    continue
                elif isinstance(text, list) and sum(len(row) for row in text) < 5:
                    print(f"Skipping image {image_name} due to insufficient table content")
                    continue

                truncated_text, was_truncated = truncate_text(text)
                if was_truncated:
                    print(f"Warning: Text for image {image_name} was truncated from {len(text)} to 65535 characters")

                image_descriptions.append(truncated_text)
                images_extraction.append({"type": "image", "bbox": bbox, "id": (page.page_number, i)})
            else:
                print(f"Skipping image {image_name} as no text was detected")

        except Exception as e:
            print(f"Error processing image {i}: {str(e)}")
            continue

    return images_extraction, image_descriptions, number_of_input_tokens, number_of_output_tokens, number_of_tokens

# array version
# def clean_nested_table(table):
#     cleaned_table=[]
#     for row in table:
#         cleaned_row = []
#         for element in row:
#             if element is not None and element != "":
#                 element = element.replace("\n", "")
#                 cleaned_row.append(element)
#         if cleaned_row:
#             cleaned_table.append(cleaned_row)
#     return cleaned_table

def clean_nested_table(table):
    clean_table_text = ""
    for row in table:
        for element in row:
            if element is not None and element != "":
                element = element.replace("\n", "")
                clean_table_text += element + " "
    return clean_table_text.strip()

def detect_tables(page,file_name,folder_path="table"):
    # prev_page_num=-1
    table_extraction=[]
    tables = page.find_tables()
    # if tables:
        # os.makedirs(f"{folder_path}/{file_name}", exist_ok=True)
        # prev_page_num=page.page_number
        # tables_text = [ t.extract(x_tolerance=2, y_tolerance=0) for t in tables ]
    # page_image=page.to_image(resolution=200)
    # current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    # table_name = f"table-{page.page_number}-{current_time}"

    for i, t in enumerate(tables):
        width, height = t.bbox[2] - t.bbox[0], t.bbox[3] - t.bbox[1]
        new_bbox = (
            t.bbox[0] - 0.1 * width,
            t.bbox[1] - 0.1 * height,
            t.bbox[2] + 0.1 * width,
            t.bbox[3] + 0.1 * height
        )
        tables_text = t.extract(x_tolerance=2, y_tolerance=0)
        tables_text =clean_nested_table(tables_text)
        # fill_color = (0, 0, 0, 0)
        # stroke_color = (0, 0, 255)
        # page_image.draw_rect(bbox, fill=fill_color, stroke=stroke_color, stroke_width=5)

        # print(tables_text[i])
        table_extraction.append({"text":tables_text,"bbox":new_bbox ,"id":(page.page_number,i),"type":"table"})

    # if prev_page_num!=-1:
    #     page_image.save(f"{folder_path}/{file_name}/{table_name}.png")
    return table_extraction


# def detect_images(page, file_name, folder_path="image"):
#     images = []
#     image_descriptions = []
#     os.makedirs(folder_path, exist_ok=True)
#     for img in page.get_images(full=True):
#         xref = img[0]
#         try:
#             base_image = page.parent.extract_image(xref)
#             if base_image:
#                 pix = fitz.Pixmap(page.parent, xref)
#                 current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
#                 image_name = f"{file_name}-image-{xref}_{current_time}"
#                 image_path = os.path.join(folder_path, f"{image_name}.png")
#                 pix.save(image_path)
#                 post_data = {
#                     'image name': image_name ,
#                     'image format': '.png',
#                     'use default prompt': False,
#                     'prompt': 'Give me a short description on the image'
#                 }
#                 post_image = [('image', (f'{image_name }', pix.tobytes()))]
#                 print("call api")
#                 res = requests.get('http://*************:58887/api/image-description/result', data=post_data,
#                                    files=post_image)
#                 print(res.status_code)
#                 if res.status_code >= 300:
#                     print(f"Fail to transfer image on page {page.number + 1} to text")
#                     return
#                 structured_res = ImageDescriptionPromptResultResponse(**res.json())
#                 text = structured_res.prompt_result
#                 encoding = tiktoken.get_encoding("cl100k_base")
#                 tokens = encoding.encode(text)
#                 print("number of tokens:",len(tokens))

#                 # print(structured_res.image_name)
#                 # print(text)

#                 image_descriptions.append(text)

#                 bbox = (pix.x, pix.y, pix.x + pix.width, pix.y + pix.height)
#                 images.append({"type": "image", "bbox": bbox})
#         except Exception as e:
#             print(f"Error processing image on page {page.number + 1}: {str(e)}")
#     return images, image_descriptions

# def detect_rects(
#     page,
#     filename,
#     padding1=0,
#     padding2=0,
#     padding3=0,
#     padding4=0,
#     folder_path="rec",
#     graphics=None,
# ):
#     """Detect and join rectangles of neighboring vector graphics.
#     parameter:
#         page: pdf current page
#         filename: the name of processed file
#         padding1: the percentage value of padding on the left side of rect (0-1)
#         padding2: the percentage value of padding at top (0-1)
#         padding3: the percentage value of padding on the right side of rect (0-1)
#         padding4: the percentage value of padding at bottom (0-1)
#         folder_path: where to save images of detected tables and charts
#         graphics: the vector graphics of the page
#     return: It returns a list of joined rectangles - which for instance can be used as the
#             "clip" parameter of a Pixmap.
#     """
#     delta = 3

#     def are_neighbors(r1, r2):
#         """Detect whether r1, r2 are "neighbors".

#         Neighbors are defined as:
#         The minimum distance between points of r1 and points of r2 is not
#         larger than delta.

#         This check supports empty rect-likes and thus also lines.
#         """
#         if (
#             (
#                 r2.x0 - delta <= r1.x0 <= r2.x1 + delta
#                 or r2.x0 - delta <= r1.x1 <= r2.x1 + delta
#             )
#             and (
#                 r2.y0 - delta <= r1.y0 <= r2.y1 + delta
#                 or r2.y0 - delta <= r1.y1 <= r2.y1 + delta
#             )
#             or (
#                 r1.x0 - delta <= r2.x0 <= r1.x1 + delta
#                 or r1.x0 - delta <= r2.x1 <= r1.x1 + delta
#             )
#             and (
#                 r1.y0 - delta <= r2.y0 <= r1.y1 + delta
#                 or r1.y0 - delta <= r2.y1 <= r1.y1 + delta
#             )
#         ):
#             return True
#         return False

#     # we exclude graphics not contained in reasonable page margins
#     parea = page.rect + (-36, -36, 36, 36)

#     if graphics is None:
#         graphics = page.get_drawings()
#     # exclude graphics not contained inside margins
#     paths = [
#         p
#         for p in page.get_drawings()
#         if parea.x0 <= p["rect"].x0 <= p["rect"].x1 <= parea.x1
#         and parea.y0 <= p["rect"].y0 <= p["rect"].y1 <= parea.y1
#     ]

#     # list of all vector graphic rectangles
#     prects = sorted([p["rect"] for p in paths], key=lambda r: (r.y1, r.x0))
#     new_rects = []  # the final list of the joined rectangles

#     # -------------------------------------------------------------------------
#     # The strategy is to identify and join all rects that are neighbors
#     # -------------------------------------------------------------------------
#     while prects:  # the algorithm will empty this list
#         r = prects[0]  # first rectangle
#         repeat = True
#         while repeat:
#             repeat = False
#             for i in range(len(prects) - 1, 0, -1):  # back to front
#                 if are_neighbors(prects[i], r):
#                     r |= prects[i].tl  # join in to first rect
#                     r |= prects[i].br
#                     del prects[i]  # delete this rect
#                     repeat = True

#         prects[0] = +r
#         # move first item over to result list
#         new_rects.append(prects.pop(0))
#         prects = sorted(list(set(prects)), key=lambda r: (r.y1, r.x0))

#     new_rects = sorted(list(set(new_rects)), key=lambda r: (r.y1, r.x0))
#     filter_rects = (
#         []
#     )  # add padding to the rects to make the image not lose important info
#     for r in new_rects:
#         if r.width > 20 and r.height > 20:
#             r.x0 = r.x0 - padding1 * (r.x1 - r.x0)
#             r.y0 = r.y0 - padding2 * (r.y1 - r.y0)
#             r.x1 = r.x1 + padding3 * (r.x1 - r.x0)
#             r.y1 = r.y1 + padding4 * (r.y1 - r.y0)
#             filter_rects.append(r)

#     current_time = datetime.now().strftime("%Y-%m-%d_%H-%M")
#     os.makedirs(f"{folder_path}/{filename}", exist_ok=True)  # save the found recs in one folder
#     for i, r in enumerate(filter_rects):
#         pix = page.get_pixmap(dpi=150, clip=r)
#         image_name=("graphic-%03i-%02i" % (page.number, i))
#         bbox = (pix.x, pix.y, pix.x + pix.width, pix.y + pix.height)
#         pix.save(f"{folder_path}/{filename}/{image_name}_{current_time}.png") #save image

#         post_data = {
#                     "name": image_name,
#                     "ocr image format": ".png",
#                     "language":'ch'
#             }
#         post_image = [("image", (f"{image_name}", pix.tobytes()))]
#         res = requests.get(
#                     "http://*************:58880/api/ocr/upload-image/result",
#                     data=post_data,
#                     files=post_image,
#                 )
#         print(res.status_code)
#         if res.status_code >= 300:
#             print(f"Fail to transfer image on page {page.number + 1} to text")
#             return
#         print(image_name)
#         print(bbox)
#         requested_ocr_result=OCRResultResponse(**res.json())
#         # print(requested_ocr_result.ocr_text_results)
#         text=requested_ocr_result.ocr_text_results[0]
#         # print(text)

#     return filter_rects

# def detect_tables(page):
#     tables = []
#     words = page.get_text("words")
#     lines = page.get_drawings()
#     rects = [l for l in lines if l["type"] == "rect"]
#
#     for rect in rects:
#         rect_words = [w for w in words if fitz.Rect(w[:4]).intersects(rect["rect"])]
#         if len(rect_words) > 4:  # Assume it's a table if it contains more than 4 words
#             tables.append({"type": "table", "bbox": rect["rect"]})
#
#     return tables


# def detect_charts(page):
#     charts = []
#     paths = page.get_drawings()
#     lines = [p for p in paths if p["type"] == "line"]
#
#     if len(lines) > 10:  # Assume it's a chart if there are many lines
#         # Use DBSCAN to cluster lines and identify potential charts
#         line_points = np.array([(l["rect"][0], l["rect"][1]) for l in lines])
#         clustering = DBSCAN(eps=20, min_samples=5).fit(line_points)
#
#         unique_labels = set(clustering.labels_)
#         for label in unique_labels:
#             if label != -1:  # -1 is noise
#                 cluster_points = line_points[clustering.labels_ == label]
#                 x_min, y_min = np.min(cluster_points, axis=0)
#                 x_max, y_max = np.max(cluster_points, axis=0)
#                 charts.append({"type": "chart", "bbox": (x_min, y_min, x_max, y_max)})
#
#     return charts


# def detect_flowcharts(page):
#     flowcharts = []
#     paths = page.get_drawings()
#     shapes = [p for p in paths if p["type"] in ["rect", "circle"]]
#     lines = [p for p in paths if p["type"] == "line"]
#
#     if len(shapes) > 5 and len(lines) > 5:  # Assume it's a flowchart if there are shapes and connecting lines
#         all_points = [(s["rect"][0], s["rect"][1]) for s in shapes] + [(l["rect"][0], l["rect"][1]) for l in lines]
#         points_array = np.array(all_points)
#         x_min, y_min = np.min(points_array, axis=0)
#         x_max, y_max = np.max(points_array, axis=0)
#         flowcharts.append({"type": "flowchart", "bbox": (x_min, y_min, x_max, y_max)})
#
#     return flowcharts
#
#
# def detect_confusion_matrices(page):
#     matrices = []
#     words = page.get_text("words")
#     numbers = [w for w in words if w[4].replace(".", "").isdigit()]
#
#     if len(numbers) > 4:  # Assume it's a confusion matrix if there are many numbers in a grid-like structure
#         numbers_array = np.array([(float(n[0]), float(n[1])) for n in numbers])
#         clustering = DBSCAN(eps=20, min_samples=4).fit(numbers_array)
#
#         if len(set(clustering.labels_)) > 1:  # If we have clusters, it might be a matrix
#             x_min, y_min = np.min(numbers_array, axis=0)
#             x_max, y_max = np.max(numbers_array, axis=0)
#             matrices.append({"type": "confusion_matrix", "bbox": (x_min, y_min, x_max, y_max)})
#
#     return matrices



def detect_drawings_pdf(page,images_bbox_list,table_bbox_list,words_bbox_list):
    flag=False
    if images_bbox_list or table_bbox_list:
        for i_bbox in images_bbox_list: # draw image rect
            page.draw_rect(fitz.Rect(i_bbox), color=(1,0,0), fill=None)

        for t_bbox in table_bbox_list: # draw table rect
            page.draw_rect(fitz.Rect(t_bbox), color=(0,0,1), fill=None)
        flag=True

    if words_bbox_list:
        for w_bbox in words_bbox_list: # draw text rect
            page.draw_rect(fitz.Rect(w_bbox), color=(0,1,0), fill=None)
        flag=True
    return flag

def bbox_intersects(bbox1, bbox2):
    """ Check if two bounding boxes intersect """
    x1, y1, x2, y2 = bbox1
    a1, b1, a2, b2 = bbox2
    return not (x2 < a1 or x1 > a2 or y2 < b1 or y1 > b2)

def get_element_description(element_type: str, element: Dict, image_description: str = None) -> str:
    if element_type == 'image':
        return f"{image_description}"
    elif element_type == 'table':
        return f"{element['text']}"
    # elif element_type == 'chart':
    #     return f"Chart detected at coordinates: {element['bbox']}"
    # elif element_type == 'flowchart':
    #     return f"Flowchart detected at coordinates: {element['bbox']}"
    # elif element_type == 'confusion_matrix':
    #     return f"Confusion matrix detected at coordinates: {element['bbox']}"
    else:
        return f"Unknown element type: {element_type}"


def chunk_pdf_advanced(pdf_path: str, file_dataset: str, chunk_size: int = 800, chunk_overlap: int = 20) -> List[Tuple[str, Dict]]:
    chunks = []
    doc = fitz.open(pdf_path)
    DEFAULT_BBOX = (10000, 10000, -1, -1)

    doc_pdfplumber = pdfplumber.open(pdf_path)
    element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}
    file_name = os.path.basename(pdf_path).split('.')[0]
    num_of_input_tokens = 0
    num_of_output_tokens = 0
    total_num_of_tokens = 0

    current_line_number = 1
    current_page_number = 1
    start_time = datetime.now()
    for page_index in range(len(doc)):
        page = doc[page_index]
        page_padplumber = doc_pdfplumber.pages[page_index]  # pageindex+1=page_number

        # detect images
        images, image_descriptions, input_tokens, output_tokens, total_tokens = detect_images(page_padplumber,
                                                                                              file_name)
        image_bbox_list = [element["bbox"] for element in images]
        total_num_of_tokens += total_tokens
        num_of_input_tokens += input_tokens
        num_of_output_tokens += output_tokens

        # detect tables
        tables = detect_tables(page_padplumber, file_name)
        tables_bbox_list = [element["bbox"] for element in tables]
        page_element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}

        words = page.get_text("words")
        words_dict_list = [{"bbox": word[:4], "text": word[4], "type": "word"} for word in words]
        components = sorted(images + tables + words_dict_list, key=lambda c: (c["bbox"][3], c["bbox"][0]))
        words_bbox_list = []

        current_chunk = ""
        current_metadata = {
            "start_page_num": current_page_number,
            "end_page_num": current_page_number,
            "start_line_num": current_line_number,
            "end_line_num": current_line_number,
            "contains_image": False,
            "contains_table": False,
            "contains_chart": False,
            "contains_flowchart": False,
            "contains_confusion_matrix": False,
            "elements": [],
            "bbox": DEFAULT_BBOX,
            "content_type": "text"
        }
        x_left, y_left, x_right, y_right = DEFAULT_BBOX

        last_y = words[0][3] if words else 0

        for index, c in enumerate(components):
            c_type = c["type"]
            if c_type == "word":
                ignore = False
                for image_bbox in image_bbox_list:
                    if bbox_intersects(c["bbox"], image_bbox):
                        ignore = True
                        break
                if not ignore:
                    for table_bbox in tables_bbox_list:
                        if bbox_intersects(c["bbox"], table_bbox):
                            ignore = True
                            break
                if ignore:
                    continue

                word_text = c["text"] + " "
                if abs(c["bbox"][3] - last_y) > 5:  # New line if y-coordinate differs by more than 5
                    current_line_number += 1
                    last_y = c["bbox"][3]

                current_chunk += word_text
                current_metadata["end_line_num"] = current_line_number
                x_left, y_left, x_right, y_right = min(x_left, c["bbox"][0]), min(y_left, c["bbox"][1]), max(x_right,
                                                                                                             c["bbox"][
                                                                                                                 2]), max(
                    y_right, c["bbox"][3])
                current_metadata["bbox"] = (x_left, y_left, x_right, y_right)
                if len(current_chunk) >= chunk_size:
                    current_metadata["end_page_num"] = current_page_number
                    words_bbox_list.append(current_metadata["bbox"])

                    # Check if the chunk matches the specific pattern
                    chunk_content = current_chunk.strip()
                    if re.match(
                            r'^此文件只供內部參考 \d+ 不可作複印、分發或上載於任何社交媒體平台\s*$|^「AIA」 、 「本公司」 或 「我們」 \(於百慕達註冊成立之有限公司\) 。 是指友邦保險 \(國際\) 有限公司\s*$',
                            chunk_content):
                        chunk_content = ""

                    chunks.append((chunk_content, current_metadata))
                    x_left, y_left, x_right, y_right = DEFAULT_BBOX
                    # Reset current chunk and metadata

                    current_chunk = " ".join(
                        [word for word in current_chunk.strip().rsplit(' ', chunk_overlap + 1)[-1 - chunk_overlap:-1]])
                    current_metadata = {
                        "start_page_num": current_page_number,
                        "end_page_num": current_page_number,
                        "start_line_num": current_line_number,
                        "end_line_num": current_line_number,
                        "content_type": "text",
                        "bbox": DEFAULT_BBOX,
                        "elements": []
                    }
            else:  # component belongs to the elements (images or tables)
                if current_chunk:
                    current_metadata["end_page_num"] = current_page_number
                    words_bbox_list.append(current_metadata["bbox"])

                    # Check if the chunk matches the specific pattern
                    chunk_content = current_chunk.strip()
                    if re.match(
                            r'^此文件只供內部參考 \d+ 不可作複印、分發或上載於任何社交媒體平台\s*$|^「AIA」 、 「本公司」 或 「我們」 \(於百慕達註冊成立之有限公司\) 。 是指友邦保險 \(國際\) 有限公司\s*$',
                            chunk_content):
                        chunk_content = ""

                    chunks.append((chunk_content, current_metadata))
                    x_left, y_left, x_right, y_right = DEFAULT_BBOX

                element_counters[c_type] += 1
                page_element_counters[c_type] += 1

                if c_type == 'image':
                    print("description_all", image_descriptions[page_element_counters[c_type] - 1])
                    description = image_descriptions[page_element_counters[c_type] - 1]
                    print("description", description)
                elif c_type == 'table':
                    description = '\n'.join([' | '.join(row) for row in c['text']])
                else:
                    description = None

                element_description = get_element_description(c_type, c, description)
                element_metadata = {
                    "start_page_num": current_page_number,
                    "end_page_num": current_page_number,
                    "start_line_num": current_line_number,
                    "end_line_num": current_line_number,
                    "content_type": c_type,
                    "elements": [c],
                    "bbox": (
                    min(current_metadata["bbox"][0], c["bbox"][0]), min(current_metadata["bbox"][1], c["bbox"][1]),
                    max(current_metadata["bbox"][2], c["bbox"][2]), max(current_metadata["bbox"][3], c["bbox"][3]))
                }
                chunks.append((current_chunk.strip() + element_description, element_metadata))
                x_left, y_left, x_right, y_right = DEFAULT_BBOX
                # Reset current chunk and metadata
                current_chunk = ""
                current_metadata = {
                    "start_page_num": current_page_number,
                    "end_page_num": current_page_number,
                    "start_line_num": current_line_number,
                    "end_line_num": current_line_number,
                    "content_type": "text",
                    "elements": [],
                    "bbox": DEFAULT_BBOX
                }

        if current_chunk:
            current_metadata["end_page_num"] = current_page_number
            words_bbox_list.append(current_metadata["bbox"])

            # Check if the chunk matches the specific pattern
            chunk_content = current_chunk.strip()
            if re.match(
                    r'^此文件只供內部參考 \d+ 不可作複印、分發或上載於任何社交媒體平台\s*$|^「AIA」 、 「本公司」 或 「我們」 \(於百慕達註冊成立之有限公司\) 。 是指友邦保險 \(國際\) 有限公司\s*$',
                    chunk_content):
                chunk_content = ""

            chunks.append((chunk_content, current_metadata))
            x_left, y_left, x_right, y_right = DEFAULT_BBOX

        current_page_number += 1
        current_line_number = 1

    os.makedirs(f"saved_pdf/{file_dataset}", exist_ok=True)
    output_file_name = f"{os.path.splitext(os.path.basename(pdf_path))[0]}.pdf"
    doc.save(f"saved_pdf/{file_dataset}/{output_file_name}")

    print("total number of tokens (image):", total_num_of_tokens)
    print("number of input tokens (image):", num_of_input_tokens)
    print("number of output tokens (image):", num_of_output_tokens)
    end_time = datetime.now()
    print("Time used: ", end_time - start_time)
    return chunks, element_counters
@app.route('/upload', methods=['POST'])
def upload_file():
    global vs_config
    global selected_dataset
    global schema_config
    global vs_df # log file for vector store
    global user_id

    selected_dataset = request.form.get('selectedDataset', None)

    if not selected_dataset:
        selected_dataset = env.default_dataset_name
        print(f'* No Selected Dataset Name : Default Name <{selected_dataset}> is Used')

    # Create Collection
    created = vectorstore_module_newui.create_vscollection(
        vs_config,
        selected_dataset,
        schema_config
    )
    graph_store = graphstore_module_newui(selected_dataset)

    print(f'... Uploading the File into the Selected Dataset <{selected_dataset}>')
    uploaded_files = request.files.getlist('file')
    print("uploaded_files: ", uploaded_files)

    if not uploaded_files:
        print('Error: No Uploading Files Found!')
        return jsonify({'status': 'error', 'message': 'No files found for upload'})
    
    
    from transformers import AutoTokenizer
    tokenizer = AutoTokenizer.from_pretrained("hkunlp/instructor-large")
    total_tokens = 0

    for file in uploaded_files:
        if file:
            filename = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
            file.save(filename)
            print("saved file")

            if filename.lower().endswith(".pdf"):
                chunks_with_metadata, element_counters = chunk_pdf_advanced(filename, selected_dataset)
                chunked_rawtexts = [chunk for chunk, _ in chunks_with_metadata]
                chunked_metadata = [metadata for _, metadata in chunks_with_metadata]
            else:
                loader = DirectoryLoader(app.config['UPLOAD_FOLDER'], show_progress=True, use_multithreading=True)
                document = loader.load()
                print("loaded other type document")
                if not document:
                    print('Warning: Cannot Find Document in Upload Folder')
                chunked_content = preprocessing.chunk_document(document, chunk_method)
                chunked_rawtexts = [chunk.page_content for chunk in chunked_content]
                chunked_metadata = [{}] * len(chunked_rawtexts)  # Empty metadata for non-PDF files

            # Process each chunk to remove specific headers
            processed_chunked_rawtexts = []
            for chunk in chunked_rawtexts:
                # Remove the specific headers if they are at the beginning of the chunk
                chunk = re.sub(
                    r'PRUHealth Guardian Critical Illness Plan Series 「誠保一生」危疾保系列 \(CIM3/BCIM3\)\s*', '',
                    chunk)
                chunk = re.sub(r'PRUHealth Guardian Critical Illness Plan Series\s*', '', chunk)
                chunk = re.sub(r'All words in the image: 「擎識定期壽險計劃」 4.*?\s*', '', chunk)
                chunk = re.sub(r'All words in the image.*?\s*', '', chunk)
                processed_chunked_rawtexts.append(chunk)
            for chunk in processed_chunked_rawtexts:
                tokens = tokenizer.encode(chunk)
                total_tokens += len(tokens)

            embedded_vectors = embedding_method.embed_documents(processed_chunked_rawtexts)

            # Create Partition
            partition_name = ''.join(e for e in file.filename if e.isalnum())
            created = vectorstore_module_newui.create_vspartition(
                vs_config,
                selected_dataset,
                partition_name=partition_name
            )
            # Generate id_list
            id_list = [partition_name + "_" + str(np.int64(i)) for i in range(1, len(processed_chunked_rawtexts) + 1)]
            # Process each chunk individually
            field_dicts = []
            for i, (chunk_text, metadata, node_id) in enumerate(
                    zip(processed_chunked_rawtexts, chunked_metadata, id_list)):
                chunk_lines = chunk_text.split('\n')

                # Use the metadata directly from the chunk
                start_page = metadata['start_page_num']
                end_page = metadata['end_page_num']
                start_line = metadata['start_line_num']
                end_line = metadata['end_line_num']
                bbox = str(metadata['bbox'])
                content_type = metadata['content_type']

                field_dicts.append({
                    "source": partition_name,
                    "extension": file.filename.split('.')[-1],
                    "language": "US",
                    "permission": "3",
                    "date": datetime.now().strftime("%Y-%m-%d, %H:%M:%S"),
                    "uploader": user_id,
                    "start_page_num": start_page,
                    "end_page_num": end_page,
                    "start_line_num": start_line,
                    "end_line_num": end_line,
                    "node_id": node_id,
                    "content_type":content_type,
                    "bbox":bbox,
                    # "image_bboxes": image_bboxes,
                    # "has_image": bool(image_bboxes),
                    # "has_table": any(elem[0] == 'table' for elem in chunk.metadata['element_info']),
                    # "has_chart": any(elem[0] == 'chart' for elem in chunk.metadata['element_info']),
                    # "has_flowchart": any(elem[0] == 'flowchart' for elem in chunk.metadata['element_info']),
                    # "has_confusion_matrix": any(
                    #     elem[0] == 'confusion_matrix' for elem in chunk.metadata['element_info'])
                })
            
            print(f'Total embedding input tokens: {total_tokens}')
            # Insert chunks into graph store
            graph_store.insert_document(chunked_rawtexts,field_dicts,id_list, partition_name)

            # Insert Vectors into Vector Store
            vectorstore_module_newui.insert_vectors_2(
                vs_config,
                selected_dataset,
                partition_name,
                schema_config,
                processed_chunked_rawtexts,
                embedded_vectors,
                field_dicts
            )
            print("inserted document")

            print(f'Successfully Added the Uploaded File <{file.filename}> into Vector Store')

            # Update log file for vector store
            new_file = {"dataset": selected_dataset,
                        "filename": file.filename,
                        "chunk_size": len(chunked_rawtexts),
                        "permission_level": 3,
                        "uploader": user_id,
                        "upload_time": datetime.now().strftime("%Y-%m-%d, %H:%M:%S")}

            # vs_df = vectorstore_module_newui.update_vs_df(new_file)

            # Remove File after Storing into Vector Store
            # os.remove(filename)
            # get_datasets()
            # print(f'Deleted File <{file.filename}> from Upload Folder')
            
    return jsonify({'status': 'success', 'message': 'File uploaded successfully'})

selected_files = []
@app.route('/selected-files', methods=['POST'])
def point_selected_files():
    global selected_files
    selected_files.clear()  # clear global_file_list

    data = request.get_json()
    print(data)
    selected_files = data['selectedFiles']  # new files in the global_file_list
    # selected_files = [f .split('.')[0]for f in selected_files]
    selected_files = [f for f in selected_files]

    # selected_files = data
    print('Selected Files:')
    print(*selected_files, sep='\n')

    return jsonify({'status': 'success', 'message': 'Selected files received'})

@app.route('/get-datasets', methods=['GET'])
def get_datasets():
    dataset_list = vectorstore_module_newui.init_vsdb(vs_config)
    print(dataset_list)
    return jsonify(dataset_list)

@app.route('/vectorstore/<filename>', methods=['GET'])
def log_file(filename):
    return send_from_directory('vectorstore', filename)

@app.route('/get-files', methods=['GET'])
def get_files(vsdb_log_dir=env.vsdb_log_dir):
    global vs_df
    # Load and return the list of files from file
    vs_df = pd.read_json(vsdb_log_dir)
    unique_files = vs_df['filename'].unique().tolist()
    return jsonify(unique_files)

@app.route('/delete-file', methods=['POST'])
def delete_file():
    global vs_df
    data = request.get_json()
    del_file = data['file_name']
    selected_dataset = data['dataset']
    # Add logic to delete the file from  server
    # os.remove(os.path.join(UPLOAD_FOLDER, file_to_delete))
    print(f"Deleting File: {del_file}")

    # ***** Newly Added
    del_file = ''.join(e for e in del_file if e.isalnum())

    graph_store = graphstore_module_newui(selected_dataset)
    graph_store.delete_document(del_file)
    vectorstore_module_newui.delete_entities(vs_config,
                                       del_file,
                                       selected_dataset)
    print('Deleted File Entities from Vector Store')

    # vs_df = vectorstore_module_newui.delete_vs_df(data['file_name'])
    # print('Deleted File from File Log')
    return jsonify({'status': 'success', 'message': 'File deleted successfully'})

@app.route('/delete-dataset', methods=['POST'])
def delete_dataset():
    global vs_df
    data = request.get_json()
    selected_dataset = data['dataset']
    graph_store = graphstore_module_newui()
    graph_store.delete_dataset(selected_dataset)
    
    vectorstore_module_newui.delete_collection(vs_config, selected_dataset)
    return jsonify({'message': 'Dataset deleted successfully'})


@app.route('/get-prompt-templates', methods=['GET'])
def get_prompt_templates():
    return jsonify({'prompt_template_list': prompt_template_list})

USER_DATA_DIR = 'user/'

@app.route('/get-all-users-data')
def get_all_users_data():
    all_users_data = {}
    for user_dir in os.listdir(USER_DATA_DIR):
        user_dir_path = os.path.join(USER_DATA_DIR, user_dir)
        if os.path.isdir(user_dir_path):
            user_id = user_dir
            chat_file_path = os.path.join(user_dir_path, f'{user_id}_chat.json')
            try:
                if os.path.exists(chat_file_path):
                    with open(chat_file_path, 'r', encoding='utf-8') as file:
                        user_data = json.load(file)
                        first_entry = next(iter(user_data.values()), None)
                        if first_entry:
                            first_question = first_entry.get('q_msg', 'No question available')
                            question_time = first_entry.get('q_time', 'No time available')
                            all_users_data[user_id] = {
                                'q_msg': first_question,
                                'q_time': question_time,
                            }
                        else:
                            all_users_data[user_id] = {'q_msg': 'No question available', 'q_time': 'No time available'}
            except (IOError, json.JSONDecodeError) as e:
                print(f"Error reading from {chat_file_path}: {e}")
                all_users_data[user_id] = {'q_msg': 'Error loading data', 'q_time': 'Error loading data'}
    return jsonify(all_users_data)

@app.route('/get-user-data/<user_id>')
def get_user_data(user_id):
    user_data_path = os.path.join(USER_DATA_DIR, user_id, f'{user_id}_chat.json')
    if os.path.exists(user_data_path):
        with open(user_data_path, 'r', encoding='utf-8') as file:
            user_data = json.load(file)
            print(user_data)
            return jsonify(user_data)
    else:
        return jsonify({'error': 'User data not found'}), 404

@app.route('/get-user-count', methods=['GET'])
def get_user_count():
    user_dir_path = os.path.join(USER_DATA_DIR)  # Assuming 'user' dir is at the root of your Flask app
    try:
        user_dirs = [name for name in os.listdir(user_dir_path) if os.path.isdir(os.path.join(user_dir_path, name))]
        user_count = len(user_dirs)
        print("user_count: ", user_count)
        return jsonify({'status': 'success', 'user_count': user_count})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/delete-chat-history', methods=['POST'])
def delete_chat_history():
    data = request.get_json()  # Get the JSON data sent from the frontend
    user_id = data['userId']  # Extract the userId sent from the frontend

    # Define the path to the user's folder
    user_folder_path = os.path.join('user', str(user_id))
    print("user_folder_path: ", user_folder_path)

    # Check if the folder exists and then delete it
    if os.path.exists(user_folder_path):
        try:
            shutil.rmtree(user_folder_path)  # Removes the folder and all its contents
            return jsonify({'message': 'Chat history successfully deleted'}), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500  # Internal Server Error
    else:
        return jsonify({'message': 'User folder not found'}), 404  # Not Found


@app.route('/create-new-chat-history', methods=['POST'])
def create_new_chat_history():
    global user_id
    global chat_history

    user_dir_path = os.path.join(os.getcwd(), 'user')  # Assuming 'user' directory is at the root of your Flask app

    # Ensure the 'user' directory exists
    if not os.path.exists(user_dir_path):
        os.makedirs(user_dir_path)

    # Find the next available user ID by checking existing directories
    user_dirs = [d for d in os.listdir(user_dir_path) if os.path.isdir(os.path.join(user_dir_path, d))]
    user_id = dt.datetime.now().strftime(datetime_userid_format)
    new_user_dir_name = "user-" + user_id

    # next_user_id = len(user_dirs) + 1  # Assuming user IDs are sequential and start from 1
    # new_user_dir_name = f"user-{next_user_id:03d}"  # Format ID as three digits
    new_user_dir_path = os.path.join(user_dir_path, new_user_dir_name)

    # Create the new user directory
    os.makedirs(new_user_dir_path)

    # Create a new chat history JSON file inside the new user directory
    new_chat_history_path = os.path.join(new_user_dir_path, f"{new_user_dir_name}_chat.json")
    chat_history["dir"] = new_chat_history_path
    chat_history["df"] = pd.DataFrame()
    chat_history["langchain"] = ChatMessageHistory()
    chat_history["langchain"].add_user_message("")
    chat_history["langchain"].add_ai_message("")
    chat_history["langchain"] = chat_history["langchain"].messages

    print("Chat History at Create: ", chat_history)

    with open(new_chat_history_path, 'w') as file:
        json.dump({}, file)  # Start with an empty JSON object

    return jsonify({'status': 'success', 'message': f'New chat history for {new_user_dir_name} created successfully.'})

@app.route('/process-selected-chat-history', methods=['POST'])
def process_selected_chat_history():
    data = request.get_json()  # Parse the JSON data sent in the request
    user_id = data.get('userId')
    # Now, you can use `user_id` to perform operations, such as retrieving and sending back the chat history
    # For demonstration, we'll just send back a success message
    return jsonify({'status': 'success', 'message': f'Processed chat history for user ID: {user_id}'})

@app.route('/update-user-session', methods=['POST'])
def update_user_session():
    global user_id
    global user_profile
    global chat_history

    data = request.get_json()
    user_id_str = data.get('userId')
    match = re.search(r'\d+$', user_id_str)

    # Assuming userId is a string that needs to be converted to an integer
    try:
        # raw_uid = int(match.group())
        # uid_digit = config["uid_digit"]
        # user_id = f"%0{uid_digit}d" %raw_uid

        # User Authentication
        # user_fullform = env.user_prefix + str(user_id)

        # user_fullform = env.user_prefix + user_id
        user_fullform = user_id_str
        print("user_fullform: ", user_fullform)
        user_dir = os.path.join(env.user_dir_root, user_fullform)

        # Retrieval of User Profile
        user_profile = os.path.join(user_dir, user_fullform + env.user_prof_suffix)

        # Retrieval of User Chat History
        user_history = os.path.join(user_dir, user_fullform + env.user_chat_suffix)
        chat_history = {"dir": None,
                        "df": None,
                        "langchain": None}

        chat_history["langchain"] = ChatMessageHistory()
        chat_history["dir"] = user_history
        if not os.path.exists(chat_history["dir"]):
            chat_history["df"] = pd.DataFrame()
            chat_history["langchain"].add_user_message("")
            chat_history["langchain"].add_ai_message("")

        else:
            df = pd.read_json(chat_history["dir"], orient="index")
            chat_history["df"] = df
            print("chat_history1:", chat_history)
            # Convert Chat History to langchain Format
            try:
                for q_msg, a_msg in zip(df["q_msg"].to_list(),
                                        df["a_msg"].to_list()):
                    chat_history["langchain"].add_user_message(q_msg)
                    chat_history["langchain"].add_ai_message(a_msg)
                    # print("chat_history:", chat_history)
                    # print("chat_history[langchain]:", chat_history["langchain"])
            except KeyError:
                chat_history["langchain"].add_user_message("")
                chat_history["langchain"].add_ai_message("")

        chat_history["langchain"] = chat_history["langchain"].messages

        return jsonify({'status': 'success', 'message': 'User session updated successfully.'})
    except ValueError:
        return jsonify({'status': 'error', 'message': 'Invalid userId format'}), 400


app.secret_key = 'your_secret_key'


# Existing functions for loading and saving user credentials...

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user_credentials = load_user_credentials()

        if username in user_credentials and check_password_hash(user_credentials[username], password):
            flash('Login successful.', 'success')
            return redirect(url_for('index'))  # Assuming 'index' is the route for your main page
        elif username not in user_credentials:
            flash('Username does not exist.', 'danger')
        else:
            flash('Incorrect password.', 'danger')

    return render_template('login.html')


@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user_credentials = load_user_credentials()

        if username in user_credentials:
            flash('Username already exists.', 'warning')
        else:
            user_credentials[username] = generate_password_hash(password)
            save_user_credentials(user_credentials)
            flash('Registration successful. Please login.', 'success')
            return redirect(url_for('login'))

    return render_template('register.html')



@app.route('/chat', methods=['POST'])
def chat():
    global selected_dataset
    global user_id
    global chat_history

    print("Chat History at Chat: " , chat_history)

    # Check file extension
    def allowed_file(filename):
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

    # User Input
    user_input = request.form.get('user_input', '')
    filename_list=request.form.get('filename_list','')
    print(filename_list)
    mode = request.form.get('mode', 'online')
    # model = request.form.get('model', 'gpt-3.5')
    model = request.form.get('model', 'mixtral')
    rag = request.form.get('rag', 'off')
    selected_dataset = request.form.get('selectedDataset', '')

    include_history = request.form.get('include_history', 'false') == 'true'
    max_history_no = request.form.get('max_history_no', '3')  # Get the value as a string, providing

    print(model)
    print(selected_dataset)
    # Convert max_history_no to integer, using 0 as default if conversion fails
    try:
        max_history_no = int(max_history_no)
    except ValueError:
        max_history_no = 0  # Fallback value if conversion fails

    # *** Refine Model Name on UI Later
    if model.lower() == "gpt-3.5":
        model = "openai"
    elif model.lower() == "llama":
        if rag == "on":
            model = "llama+rag-1"
        else:
            model = "llama-1"
    elif model.lower() == "mixtral":
        if rag == "on":
            model = "mixtral+rag-1"
        else:
            model = "mixtral-1"

    q_time = datetime.now()

    # Initialize bot_response to an empty string or a default value
    bot_response = ""

    # Prompt Engineering
    system_prompt = prompt_module.gen_system_prompt(system_prompt_para) # Using default values
    condense_system_prompt = prompt_module.gen_condense_system_prompt(system_prompt_para)
    prompt_df = pd.read_json(env.prompt_tempte_dir + selected_instrcut + '.json')
    prompt_template = '\n'.join(prompt_df["prompt"])

    # Model Retrieval
    model_name = model.split('-')[0] # Split Model Number
    prime_model_name = model_name.split('+')[0] # Split + for RAG
    prime_model_root = os.path.join(env.model_dir_root, prime_model_name)
    model_config_dir = os.path.join(prime_model_root, model + env.model_config_suffix)
    chunks_list = None
    # Check if Model Config File Exists
    if not os.path.exists(model_config_dir):
        err_msg = f" Input Error : The Model ID <{model}> could not be Found in {prime_model_root}"
        print(err_msg)
        bot_response = err_msg

    # Retrieve Model Config File
    else:
        print("Successfully Retrieved Model Config File")
        model_para = pd.read_json(model_config_dir, typ='series')



    # Get retriever for RAG; Return <None> if RAG is OFF
    chunk_list = None
    retriever = None
    files_pages_data = None
    if rag == "on":
        if selected_dataset:
            print(f'RAG is ON. Loaded Dataset {selected_dataset}')
            retriever, chunks_list = vectorstore_module_newui.get_retriever3(vs_config, embedding_method, selected_dataset, selected_files, search_config, user_input)
            files_pages_data={}
            for chunk in chunks_list:
                filename = chunk['source']
                filename = filename.replace('pdf', '.pdf')
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                page_number = chunk['page_num']
                bbox = chunk['bbox']
                content_type = chunk['content_type']
                
                if filename not in files_pages_data:
                    files_pages_data[filename]={}
                if page_number not in files_pages_data[filename]:
                    files_pages_data[filename][page_number]=[]
                files_pages_data[filename][page_number].append({"bbox":bbox,"content_type":content_type})
            #from util import drawings_pdf
            # drawings_pdf(chunks_list, selected_dataset)
            # retriever = vectorstore_module_newui.get_retriever2(vs_config,
            #                                              embedding_method,
            #                                              selected_dataset,
            #                                              selected_files,
            #                                              search_config)
            
            # retriever = vectorstore_module_newui.get_retriever(vs_config,
            #                                              embedding_method,
            #                                              selected_dataset,
            #                                              search_config)
        else:
            print(f'Error : RAG is ON, but Cannot Find Dataset')
            retriever = None
    else:
        print('RAG is OFF')
        retriever = None

    # # Retrieval of Files for RAG Vector Store
    # # Find directory path for RAG; Return <None> if RAG is Off
    # if rag == "on":

    #     # Check whether Files are Selected for RAG
    #     if selected_dataset:
    #         print(f'RAG is ON. Loaded Dataset {selected_dataset}')
    #         rag_dataset = selected_dataset
    #     else:
    #         print(f'Error : RAG is ON, but Cannot Find Dataset')
    #         rag_dataset = []

    # else:
    #     rag_dataset = []

    # Generate AI Response
    print('LLM Model Selected :')
    print(f'- Model UID : {model_para["uid"]}')
    print(f'- Base Model : {model_para["model"]}')
    print(f'- RAG : {retriever is not None}')

    if include_history:
        print("max_history_no: ", max_history_no)
        print("chat_history: ", chat_history["langchain"])
        print("len chat_history: ", len(chat_history["langchain"])/2)
        chat_record = chat_history["langchain"]
        if len(chat_history["langchain"]) - 2 > max_history_no:
            n_pairs = max_history_no * 2
            chat_record = chat_history["langchain"][-n_pairs:]
            print("chat_record:", chat_record)

    else:
        chat_record = None

    # if prime_model_name.lower() == "llama":
    #     bot_response = llama_module.llama_response(user_input,
    #                                 system_prompt,
    #                                 condense_system_prompt,
    #                                 prompt_template,
    #                                 model_para,
    #                                 retriever,
    #                                 chat_record)
    if prime_model_name.lower() == "llama":
        bot_response = llama_module.llama_response(user_input,
                                    system_prompt,
                                    condense_system_prompt,
                                    prompt_template,
                                    model_para,
                                    retriever,
                                    chat_record)

    elif prime_model_name.lower() == "openai":
        bot_response = openai_module.openai_response(user_input,
                                     system_prompt,
                                     condense_system_prompt,
                                     prompt_template,
                                     model_para,
                                     retriever,
                                     chat_record)

    elif prime_model_name.lower() == "mixtral":
        bot_response = mixtral_module_deepseek_nochathistory.mixtral_response(
                                     user_input,
                                     system_prompt,
                                     condense_system_prompt,
                                     prompt_template,
                                     model_para,
                                     retriever,
                                     chat_record)

    # elif prime_model_name.lower() == "instructmixtral":
    #     bot_response = instruct_mixtral_module.mixtral_response(user_input,
    #                                  system_prompt,
    #                                  condense_system_prompt,
    #                                  prompt_template,
    #                                  model_para,
    #                                  mixtral_pipeline,
    #                                  retriever,
    #                                  chat_history["langchain"])
    # Ball sir output json file
    # test_module_2.output_report(bot_response)

    a_time = datetime.now()

    # Update Chat History
    new_chat = pd.DataFrame([{
        "q_msg": user_input,
        "a_msg": bot_response,
        "q_time": q_time.strftime("%Y-%m-%d %H:%M:%S"),
        "a_time": a_time.strftime("%Y-%m-%d %H:%M:%S"),
        "llm": model,
        "similarity": 1,
        "rating": 3,
        "chunks_list": files_pages_data,
    }])

    chat_history["df"] = pd.concat([chat_history["df"], new_chat], ignore_index=True)
    chat_history["df"].to_json(chat_history["dir"], orient="index", indent=4)


    new_chat = ChatMessageHistory()
    new_chat.add_user_message(user_input)
    new_chat.add_ai_message(bot_response)
    chat_history["langchain"] += new_chat.messages

    return jsonify({'response': bot_response, 'chunk_list': files_pages_data})

#pdf file viewer  
@app.route('/processed/<filename>')
def Get_file(filename):
    return send_from_directory(app.config['PROCESSED_FOLDER'], filename)

# def generate_buttons_html(processed_files):
#     buttons_html = ""
#     for file in processed_files:
#         file_name = file['filename']
#         pdf_path = file['filepath']
#         button_html = f'''
#         <button id="reference-button" style="text-align: center; color: black; background: white; font-weight: 700; word-wrap: break-word; font-size: 18px; border-radius:10px; height:40px; margin-right:10px; margin-bottom:10px; display:block;" onclick="showPDFChunkReference('{pdf_path}')">
#             <div style="display:flex; align-items:center">
#                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right:15px">
#                     <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4C7.34315 4 6 5.34315 6 7V17C6 18.6569 7.34315 20 9 20H15C16.6569 20 18 18.6569 18 17V10H15C13.3431 10 12 8.65685 12 7V4H9ZM14 5.41421L16.5858 8H15C14.4477 8 14 7.55228 14 7V5.41421ZM4 7C4 4.23858 6.23858 2 9 2H13C13.2652 2 13.5196 2.10536 13.7071 2.29289L19.7071 8.29289C19.8946 8.48043 20 8.73478 20 9V17C20 19.7614 17.7614 22 15 22H9C6.23858 22 4 19.7614 4 17V7ZM15.2071 12.2929C15.5976 12.6834 15.5976 13.3166 15.2071 13.7071L12.2071 16.7071C11.8166 17.0976 11.1834 17.0976 10.7929 16.7071L9.29289 15.2071C8.90237 14.8166 8.90237 14.1834 9.29289 13.7929C9.68342 13.4024 10.3166 13.4024 10.7071 13.7929L11.5 14.5858L13.7929 12.2929C14.1834 11.9024 14.8166 11.9024 15.2071 12.2929Z" fill="#F90505"/>
#                 </svg>
#                 <div>{file_name}</div>
#             </div>
#         </button>
#         '''
#         buttons_html += button_html
#     return buttons_html

# @app.route('/processed_chunk', methods=['GET', 'POST'])
# def chunk_extraction():
    data = request.get_json()
    list_of_chunks = data['chunk']
    files_pages_data={}

    for chunk in list_of_chunks:
        filename = chunk['source']
        filename = filename.replace('pdf', '.pdf')
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        page_number = chunk['page_num']
        bbox = chunk['bbox']
        content_type = chunk['content_type']
        
        if filename not in files_pages_data:
            files_pages_data[filename]={}
        if page_number not in files_pages_data[filename]:
            files_pages_data[filename][page_number]=[]
        files_pages_data[filename][page_number].append({"bbox":bbox,"content_type":content_type})
        
    print("Extracted Files, Page Numbers, and Bounding Boxes:", files_pages_data)
    return jsonify({'response': "finish prcessing chunk", 'data_extracted': files_pages_data})


@app.route('/draw_file', methods=['GET', 'POST'])
def draw_bbox():
    data = request.get_json()
    pages_data = data['pages_data']
    filename = data['filename']

    new_doc = fitz.open()
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    doc = fitz.open(file_path)

    for page_number, bboxes_data in pages_data.items():
        page_number = int(page_number)
        page = doc[page_number - 1]  # Adjust for zero-based index

        # Separate bboxes by content type
        text_bboxes = []
        image_bboxes = []
        table_bboxes = []

        for bbox_data in bboxes_data:
            content_type = bbox_data['content_type']
            bbox_coords = bbox_data['bbox']
            if content_type == "text":
                text_bboxes.append(fitz.Rect(bbox_coords))
            elif content_type == "image":
                image_bboxes.append(fitz.Rect(bbox_coords))
            elif content_type == "table":
                table_bboxes.append(fitz.Rect(bbox_coords))

        # Function to check if a bbox is inside another
        def is_inside(inner, outer):
            return (inner.x0 >= outer.x0 and inner.y0 >= outer.y0 and
                    inner.x1 <= outer.x1 and inner.y1 <= outer.y1)

        # Draw bounding boxes
        for table_bbox in table_bboxes:
            page.draw_rect(table_bbox, color=(0, 0, 1), fill=None)

        for image_bbox in image_bboxes:
            page.draw_rect(image_bbox, color=(1, 0, 0), fill=None)

        for text_bbox in text_bboxes:
            # Check if the text bbox is inside any table or image bbox
            if not any(is_inside(text_bbox, table_bbox) for table_bbox in table_bboxes) and \
                    not any(is_inside(text_bbox, image_bbox) for image_bbox in image_bboxes):
                page.draw_rect(text_bbox, color=(0, 1, 0), fill=None)

        temp_doc = fitz.open()
        temp_doc.insert_pdf(doc, from_page=page_number - 1, to_page=page_number - 1)
        new_doc.insert_pdf(temp_doc)

    os.makedirs("processed", exist_ok=True)
    processed_file_path = os.path.join("processed", filename)
    new_doc.save(processed_file_path)

    return jsonify({'response': "finish drawing bboxes", 'processed_file_path': processed_file_path})

@app.route('/')
def root():
    return redirect(url_for('chat'))

# if __name__ == '__main__':
#     app.run(host='127.0.0.1', port=8008)
# if __name__ == '__main__':
#     app.run(host="localhost", port=5000)

# def open_browser():
#     webbrowser.open_new('http://127.0.0.1:8009')
#
#
# if __name__ == '__main__':
#     threading.Thread(target=open_browser).start()
#
#     app.run(host='0.0.0.0', port=8009)

# class ServerThread(QThread):
#     def __init__(self, flask_app):
#         super().__init__()
#         self.flask_app = flask_app
#         self.srv = make_server('127.0.0.1', 8008, flask_app)
#         self.ctx = flask_app.app_context()
#         self.ctx.push()
#
#     def run(self):
#         self.srv.serve_forever()
#
#     def stop(self):
#         self.srv.shutdown()

# class MainWindow(QMainWindow):
#     def __init__(self):
#         super().__init__()
#         self.setWindowTitle("D.WiseBot")
#         self.setGeometry(750, 75, 2000, 1300)
#         self.web_view = QWebEngineView()
#         self.setCentralWidget(self.web_view)
#
#         self.web_view.load(QUrl("http://127.0.0.1:8008"))
#
#     def closeEvent(self, event):
#         # vectorstore_module_newui.stop_milvue_lite()
#         os.system(r'wmic process where name="milvus.exe_org" call terminate')
#         global flaskThread
#         flaskThread.stop()
#         event.accept()
#
# def start_ollama_service():
#     # 设置模型路径环境变量并启动 ollama 服务
#     model_path = r'C:\Users\<USER>\PycharmProjects\dtt-llm-rag\ollama_models'
#     os.environ['OLLAMA_MODELS'] = model_path
#     subprocess.Popen(['ollama', 'serve'], creationflags=subprocess.CREATE_NEW_CONSOLE)
import psutil

def terminate_ollama_processes():
    for process in psutil.process_iter(['pid', 'name']):
        if process.info['name'] == 'ollama.exe':
            process.terminate()  # 终止找到的ollama进程
            process.wait()  # 等待进程真正终止
def start_ollama_service():
    # 设置模型路径环境变量
    base_path = os.path.dirname(os.path.abspath(__file__))
    model_path = os.path.join(base_path, 'ollama_models')
    os.environ['OLLAMA_MODELS'] = model_path

    # 启动 ollama serve 在新的控制台窗口
    subprocess.Popen(['ollama', 'serve'], creationflags=subprocess.CREATE_NO_WINDOW)

# def run_ollama():
#     # 假设当前脚本位于与 Ollama 目录同一个 U 盘中的某个目录
#     ollama_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Ollama', 'ollama.app.exe')
#     subprocess.call([ollama_path])

# def check_ollama_path():
#     ollama_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Ollama', 'ollama.exe')
#     print("Ollama path:", ollama_path)
#     exists = os.path.exists(ollama_path)
#     print("Does Ollama exist at the specified path?", exists)
#     return ollama_path, exists


def is_ollama_running():
    """检查 ollama.exe 是否正在运行"""
    for process in psutil.process_iter(['name']):
        if process.info['name'] == 'ollama.exe':
            return True
    return False
def try_start_ollama():
    """尝试在系统上启动 ollama.exe"""
    ollama_path = os.path.join('C:', 'Program Files', 'Ollama', 'ollama.exe')  # 根据实际安装路径调整
    try:
        subprocess.Popen([ollama_path], creationflags=subprocess.CREATE_NO_WINDOW)
        return True
    except Exception as e:
        print(f"Failed to start ollama.exe: {e}")
        return False


if __name__ == '__main__':
    if __name__ == '__main__':
        app.run(host='0.0.0.0', port=8036)
    # # if not is_ollama_running():  # 如果 ollama.exe 没有运行
    # #     if not try_start_ollama():  # 尝试启动系统上的 ollama.exe
    # #         subprocess.call(['OllamaSetup.exe'])  # 如果启动失败，则运行安装程序
    # #
    # # terminate_ollama_processes()  # 首先终止任何现有的 ollama 进程
    # # start_ollama_service()  # 重新启动 ollama 服务
    #
    # qt_app = QApplication(sys.argv)
    # window = MainWindow()
    # window.show()
    #
    # # ollama_pull_process.wait()
    #
    # flaskThread = ServerThread(app)
    # flaskThread.start()
    #
    # sys.exit(qt_app.exec_())