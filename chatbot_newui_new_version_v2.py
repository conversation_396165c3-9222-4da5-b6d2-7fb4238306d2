"""

    Module Name :           chatbot
    Last Modified Date :    23 Jan 2024

"""
import json
import re
import shutil
import subprocess
import base64

# from PyQt5.QtWebEngineWidgets import QWebEngineView
from flask import Flask, render_template, request, jsonify, send_from_directory, session, redirect, url_for, flash
import os
import pandas as pd
from typing import Any, List, Tuple, Dict, Optional

from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

import warnings

# import llama3_module
import mixtral_module
import mixtral_module_deepseek_nochathistory_v2
import mixtral_module_openai
import mixtral_module_openvino
# import mixtral_module_retriever_to_text
from login import save_user_credentials, load_user_credentials

warnings.filterwarnings('ignore')

# Import Self-defined Libraries
import env
import preprocessing
import prompt_module
import vectorstore_module_newui
from graphstore_module_newui import graphstore_module_newui
import openai_module
import llama_module
# import mixtral_module_pipeline
import init_interface
import test_module
import webbrowser
import threading
from langchain.document_loaders import DirectoryLoader
from langchain.memory import ChatMessageHistory

from werkzeug.serving import make_server
# from PyQt5.QtWidgets import QApplication, QMainWindow
# from PyQt5.QtCore import QThread, QUrl
import sys
import datetime as dt

datetime_userid_format= "%Y%m%d-%H-%M-%S"

app = Flask(__name__)

# File Upload Configuration
os.makedirs(env.default_upload_folder, exist_ok=True)
app.config['UPLOAD_FOLDER'] = env.default_upload_folder     # save all files in the default folder
app.config['ALLOWED_EXTENSIONS'] = env.valid_file_extension # valid file formats
app.config['PROCESSED_FOLDER'] = 'processed/'


""" Temp """
prompt_template_list = init_interface.load_prompt_template()
model_dict, basemodel_list = init_interface.load_model_template()

""" Inititalization """
# Retrieval of Default Configuration
config_dir = os.path.join(env.default_dir, env.default_config_dir)
config = pd.read_json(config_dir, typ="series")

# Retriveval of Preprocessing Configuration
chunk_mode = 'chunk-1'
embedding_mode = 'embedding-1'
chunk_method, embedding_method = init_interface.load_preprocessing_config_chi(chunk_mode, embedding_mode) # For the Chinese version
# chunk_method, embedding_method = init_interface.load_preprocessing_config(chunk_mode, embedding_mode) # For the English version

# Retrieval of System Prompt Parameters
prompt_dir = os.path.join(env.default_dir, env.default_prompt_dir)
system_prompt_para = pd.read_json(prompt_dir, typ="series")

# Retrieval of Vector Store Configuration
vs_dir = os.path.join(env.vs_dir_root, "milvus" + env.vs_config_suffix)
vs_config = pd.read_json(vs_dir, typ="series")

dataset_list = vectorstore_module_newui.init_vsdb(vs_config)
print("load dataset")
print(*dataset_list, sep='\n')


# *** To convert to input parameter
vsschema_uid = 'vsschema-6'
schema_config_dir = env.vs_dir_root + vsschema_uid + env.vsschema_config_fmt
schema_config = pd.read_json(schema_config_dir, typ='series')


# Retrieval RAG Configuration
search_method = 'search-1'
search_config = pd.read_json(env.search_config_dir + \
                             search_method + \
                             env.search_config_suffix, typ="series")


# Retrieval of User Information
""" *** Refine Later >>> Build simple user authentication function """
# raw_uid = 0o02
# # uid_digit = config["uid_digit"]
# # user_id = f"%0{uid_digit}d" %raw_uid

user_id = dt.datetime.now().strftime(datetime_userid_format)

# User Authentication
# user_fullform = env.user_prefix + str(user_id)
user_fullform = env.user_prefix + user_id

user_dir = os.path.join(env.user_dir_root, user_fullform)

# Retrieval of User Profile
# user_profile = os.path.join(user_dir, user_fullform + env.user_prof_suffix)

# Retrieval of User Chat History
user_history = os.path.join(user_dir, user_fullform + env.user_chat_suffix)
print("user_history: ", user_history)
chat_history = {"dir": None,
                "df": None,
                "langchain": None}
print("chat_history: ", chat_history)

chat_history["langchain"] = ChatMessageHistory()
print("chat_history(langchain)", chat_history["langchain"])

chat_history["dir"] = user_history
if not os.path.exists(chat_history["dir"]):
    chat_history["df"] = pd.DataFrame()
    chat_history["langchain"].add_user_message("")
    chat_history["langchain"].add_ai_message("")

else:
    df = pd.read_json(chat_history["dir"], orient="index")
    # print("chat_history(dir): ", chat_history["dir"])

    chat_history["df"] = df
    # print("chat_history(df): ", chat_history["df"])

    # Convert Chat History to langchain Format
    for q_msg, a_msg in zip(df["q_msg"].to_list(),
                            df["a_msg"].to_list()):
        chat_history["langchain"].add_user_message(q_msg)
        chat_history["langchain"].add_ai_message(a_msg)
        # print("chat_history:", chat_history)
        # print("chat_history[langchain]:", chat_history["langchain"])

    # print("chat_history(langchain)", chat_history["langchain"])
    chat_history["langchain"] = chat_history["langchain"].messages
    # print("chat_history(langchain)(messages)", chat_history["langchain"])

print("Chat History at Init: " , chat_history)

# Init Collection
loaded_dataset = None
loaded_dataset_name = None
loaded_files = []


# ***** Retrival of stroed collections and documents
#  {'dataset_name': 'Dataset_Woody_Test',
#  'document_list': ['kyototxt', 'alicetxt']}]
dataset_list = vectorstore_module_newui.init_vsdb(vs_config)

# initate llm pipeline
# torch.set_default_tensor_type(torch.cuda.HalfTensor)
mixtral_config = r"model/mixtral/mixtral-1_config.json"
# pipeline_starttime = datetime.now()
# mixtral_pipeline = mixtral_module.init_mixtral_piepeline(pd.read_json(mixtral_config, typ='series'))
# mixtral_pipeline = mixtral_module_org.init_mixtral_piepeline_2()
llama_config = r"model/llama/llama+rag-1_config.json"
# llama_pipeline = llama_module.init_llama_piepeline(pd.read_json(llama_config, typ='series'))
pipeline_endtime = datetime.now()
# print(f"Pipeline Loading Time = {pipeline_endtime-pipeline_starttime}")

# print(mixtral_pipeline)
""" Chatbot """
@app.route('/chat', methods=['GET'])
def index():
    # return render_template('index.html')
    return render_template('index_json_database_new_ui_23.html')

selected_dataset = None # dataset storing files
@app.route('/selected-dataset', methods=['POST'])
def point_selected_dataset():
    global selected_dataset
    
    data = request.get_json()
    print(f"DEBUG: 收到的数据集选择数据: {data}")
    
    # 检查数据格式
    if data is None:
        print("ERROR: 收到的数据为None")
        return jsonify({'status': 'error', 'message': 'No data received'}), 400
    
    if 'selectedDataset' not in data:
        print(f"ERROR: 数据中没有'selectedDataset'键，数据内容: {data}")
        return jsonify({'status': 'error', 'message': 'No selectedDataset in data'}), 400
    
    selected_dataset = data['selectedDataset']
    print(f'Selected Dataset: <{selected_dataset}>')
    print(f'DEBUG: 全局变量selected_dataset更新为: {selected_dataset}')
    
    return jsonify({'status': 'success', 'message': f'Received Dataset Selection: {selected_dataset}'})

selected_instrcut = 'role-default'
@app.route('/select-instruct', methods=['POST'])
def select_instruct():
    global selected_instrcut
    data = request.json

    selected_instrcut = data['selectedInstruct']
    print(f'Selected instruct option <{selected_instrcut}>')
    return jsonify({'message': 'Received instruct option: ' + selected_instrcut})

from uuid import UUID
import os
from datetime import datetime
from flask import jsonify, request
import fitz  # PyMuPDF
from typing import List, Tuple, Dict
import numpy as np
from sklearn.cluster import DBSCAN
from langchain_community.document_loaders import PyMuPDFLoader, DirectoryLoader
from langchain.schema import Document
import preprocessing
import vectorstore_module_newui
import requests
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
import pdfplumber
from PIL import Image as PILImage
import io
import pdb
from openai import OpenAI
from img2table.document import Image
from IPython.display import display_html
# Use PaddleOCR
from img2table.ocr import PaddleOCR
ocr = PaddleOCR(lang="ch")

class ImageDescriptionPromptResultResponse(BaseModel):
    entry_id: UUID
    image_name: Optional[str] = None
    prompt_result: Optional[str] = None
    number_of_input_tokens: Optional[int] = None
    number_of_output_tokens: Optional[int] = None

    class Config:
        from_attributes = True

class ImageFileBase64(BaseModel):
    image_file_name:Optional[str] =None
    image_file_bytes_base64str:Optional[str] = None
    image_file_type: Optional[str] = None
    class Config:
        from_attributes = True

class OCRResultResponse(BaseModel):
    ocr_requestid:UUID
    ocr_name: Optional[str]
    ocr_image_results: Optional[list[ImageFileBase64]]
    ocr_text_results:Optional[list[str]]
    class Config:
        from_attributes= True

client = OpenAI(api_key='ollama',base_url="http://*************:30291/v1")
def png_image_to_base64_data_uri(file_path):
    with open(file_path,"rb") as img_file:
        base64_data=base64.b64encode(img_file.read()).decode('utf-8')
        return f"data:image/png;base64,{base64_data}"

def truncate_text(text, max_length=65535):
    if isinstance(text, list):
        text = '\n'.join([' | '.join(map(str, row)) for row in text])
    if len(text) > max_length:
        return text[:max_length], True
    return text, False

def detect_images(page, file_name, folder_path="image"):
    """
    Detect and process images from a PDF page
    Added boundary checking to ensure bbox stays within page limits
    """
    OCR_model_name = "OpenGVLab/InternVL2_5-78B-MPO-AWQ"
    images_extraction = []
    image_descriptions = []
    number_of_tokens = 0
    number_of_input_tokens = 0
    number_of_output_tokens = 0

    # Create folder if it doesn't exist
    os.makedirs(f"{folder_path}/{file_name}", exist_ok=True)

    def clamp(value, min_val, max_val):
        """Helper function to clamp values within bounds"""
        return max(min_val, min(value, max_val))

    for i, image in enumerate(page.images):
        # Get page dimensions for boundary checking
        page_width = page.width
        page_height = page.height

        # Calculate initial coordinates
        x0 = image['x0']
        invertedy0 = page_height - image['y1']
        invertedy1 = page_height - image['y0']

        # Clamp coordinates to page boundaries
        x0 = clamp(x0, 0, page_width)
        x1 = clamp(image['x1'], 0, page_width)
        y0 = clamp(invertedy0, 0, page_height)
        y1 = clamp(invertedy1, 0, page_height)

        # Create bbox with clamped coordinates
        bbox = (x0, y0, x1, y1)

        # Ensure bbox dimensions are valid (width and height > 0)
        if bbox[2] <= bbox[0] or bbox[3] <= bbox[1]:
            print(f"Skipping invalid bbox for image {i}: {bbox}")
            continue

        try:
            cropped_image = page.crop(bbox)
            image_object = cropped_image.to_image(resolution=200)
            image_name = f"image-new-{page.page_number}-{i}"
            image_file = f"{folder_path}/{file_name}/{image_name}.png"
            image_object.save(image_file)

            data_uri = png_image_to_base64_data_uri(image_file)
            messages_checking = [
                {"role": "system",
                 "content": "you are an assistant who can perfectly check if an image contains texts."},
                {"role": "user", "content": [
                    {"type": "image_url", "image_url": {"url": data_uri}},
                    {"type": "text",
                     "text": "Check if there is any text in this image. If there is no text in the image, the output should be: nothing, else, the output should be: some texts are detected"}
                ]}
            ]

            response = client.chat.completions.create(model=OCR_model_name, messages=messages_checking)
            num_of_tokens = response.usage.completion_tokens
            print("image name:", image_name)

            if num_of_tokens <= 3:
                text = None
                print(text)
            else:
                try:
                    image = PILImage.open(image_file).convert('L')
                    print(f"Image size: {image.size}, format: {image.format}")
                    threshold_value = 190
                    grayscale_image = image.point(lambda p: 255 if p > threshold_value else 0)
                    img_byte_arr = io.BytesIO()
                    grayscale_image.save(img_byte_arr, format='PNG')
                    img = Image(src=img_byte_arr.getvalue())

                    print("Attempting to extract tables...")
                    extracted_tables = img.extract_tables(
                        ocr=ocr,
                        implicit_rows=False,
                        borderless_tables=False,
                        min_confidence=50,
                    )

                    print(f"Number of extracted tables: {len(extracted_tables)}")

                    if len(extracted_tables) == 0:
                        print("No tables found, using OCR to extract text.")
                        messages_extraction = [
                            {"role": "system", "content": "you are an assistant who perfectly describes images."},
                            {"role": "user", "content": [
                                {"type": "image_url", "image_url": {"url": data_uri}},
                                {"type": "text",
                                 "text": "Extract the text in this image. The output should be the text content only."}
                            ]}
                        ]
                        response = client.chat.completions.create(model=OCR_model_name, messages=messages_extraction)
                        text = response.choices[0].message.content
                        print(f"Extracted content length: {len(text)}")
                    else:
                        table_content_list = []
                        for i, table in enumerate(extracted_tables):
                            content = table.content
                            table_content_list.append(content)
                            print(f"Table {i + 1} content rows: {len(content)}")
                        text = table_content_list

                except ZeroDivisionError:
                    print(
                        f"ZeroDivisionError occurred while processing image. This might indicate an issue with the image or table structure.")
                    text = "Error: Unable to process image due to division by zero."
                except Exception as e:
                    print(f"An unexpected error occurred while processing image: {str(e)}")
                    text = f"Error: Unable to process image. {str(e)}"

                print(
                    f"Final extracted text type: {type(text)}, length: {len(text) if isinstance(text, (str, list)) else 'N/A'}")

            number_of_tokens = number_of_input_tokens + number_of_output_tokens
            print("text:", text)

            if text is not None:
                if isinstance(text, str) and len(text) < 5:
                    print(f"Skipping image {image_name} due to insufficient text content")
                    continue
                elif isinstance(text, list) and sum(len(row) for row in text) < 5:
                    print(f"Skipping image {image_name} due to insufficient table content")
                    continue

                truncated_text, was_truncated = truncate_text(text)
                if was_truncated:
                    print(f"Warning: Text for image {image_name} was truncated from {len(text)} to 65535 characters")

                image_descriptions.append(truncated_text)
                images_extraction.append({"type": "image", "bbox": bbox, "id": (page.page_number, i)})
            else:
                print(f"Skipping image {image_name} as no text was detected")

        except Exception as e:
            print(f"Error processing image {i}: {str(e)}")
            continue

    return images_extraction, image_descriptions, number_of_input_tokens, number_of_output_tokens, number_of_tokens

def clean_nested_table(table):
    clean_table_text = ""
    for row in table:
        for element in row:
            if element is not None and element != "":
                element = element.replace("\n", "")
                clean_table_text += element + " "
    return clean_table_text.strip()

def detect_tables(page,file_name,folder_path="table"):
    # prev_page_num=-1
    table_extraction=[]
    tables = page.find_tables()
    # if tables:
        # os.makedirs(f"{folder_path}/{file_name}", exist_ok=True)
        # prev_page_num=page.page_number
        # tables_text = [ t.extract(x_tolerance=2, y_tolerance=0) for t in tables ]
    # page_image=page.to_image(resolution=200)
    # current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    # table_name = f"table-{page.page_number}-{current_time}"

    for i, t in enumerate(tables):
        width, height = t.bbox[2] - t.bbox[0], t.bbox[3] - t.bbox[1]
        new_bbox = (
            t.bbox[0] - 0.1 * width,
            t.bbox[1] - 0.1 * height,
            t.bbox[2] + 0.1 * width,
            t.bbox[3] + 0.1 * height
        )
        tables_text = t.extract(x_tolerance=2, y_tolerance=0)
        tables_text =clean_nested_table(tables_text)
        # fill_color = (0, 0, 0, 0)
        # stroke_color = (0, 0, 255)
        # page_image.draw_rect(bbox, fill=fill_color, stroke=stroke_color, stroke_width=5)

        # print(tables_text[i])
        table_extraction.append({"text":tables_text,"bbox":new_bbox ,"id":(page.page_number,i),"type":"table"})

    # if prev_page_num!=-1:
    #     page_image.save(f"{folder_path}/{file_name}/{table_name}.png")
    return table_extraction

def detect_drawings_pdf(page,images_bbox_list,table_bbox_list,words_bbox_list):
    flag=False
    if images_bbox_list or table_bbox_list:
        for i_bbox in images_bbox_list: # draw image rect
            page.draw_rect(fitz.Rect(i_bbox), color=(1,0,0), fill=None)

        for t_bbox in table_bbox_list: # draw table rect
            page.draw_rect(fitz.Rect(t_bbox), color=(0,0,1), fill=None)
        flag=True

    if words_bbox_list:
        for w_bbox in words_bbox_list: # draw text rect
            page.draw_rect(fitz.Rect(w_bbox), color=(0,1,0), fill=None)
        flag=True
    return flag

def bbox_intersects(bbox1, bbox2):
    """ Check if two bounding boxes intersect """
    x1, y1, x2, y2 = bbox1
    a1, b1, a2, b2 = bbox2
    return not (x2 < a1 or x1 > a2 or y2 < b1 or y1 > b2)

def get_element_description(element_type: str, element: Dict, image_description: str = None) -> str:
    if element_type == 'image':
        return f"{image_description}"
    elif element_type == 'table':
        return f"{element['text']}"
    # elif element_type == 'chart':
    #     return f"Chart detected at coordinates: {element['bbox']}"
    # elif element_type == 'flowchart':
    #     return f"Flowchart detected at coordinates: {element['bbox']}"
    # elif element_type == 'confusion_matrix':
    #     return f"Confusion matrix detected at coordinates: {element['bbox']}"
    else:
        return f"Unknown element type: {element_type}"


def chunk_pdf_advanced(pdf_path: str, file_dataset: str, chunk_size: int = 800, chunk_overlap: int = 20) -> List[Tuple[str, Dict]]:
    chunks = []
    doc = fitz.open(pdf_path)
    DEFAULT_BBOX = (10000, 10000, -1, -1)

    doc_pdfplumber = pdfplumber.open(pdf_path)
    element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}
    file_name = os.path.basename(pdf_path).split('.')[0]
    num_of_input_tokens = 0
    num_of_output_tokens = 0
    total_num_of_tokens = 0

    current_line_number = 1
    current_page_number = 1
    start_time = datetime.now()
    for page_index in range(len(doc)):
        page = doc[page_index]
        page_padplumber = doc_pdfplumber.pages[page_index]  # pageindex+1=page_number

        # detect images
        images, image_descriptions, input_tokens, output_tokens, total_tokens = detect_images(page_padplumber,
                                                                                              file_name)
        image_bbox_list = [element["bbox"] for element in images]
        total_num_of_tokens += total_tokens
        num_of_input_tokens += input_tokens
        num_of_output_tokens += output_tokens

        # detect tables
        tables = detect_tables(page_padplumber, file_name)
        tables_bbox_list = [element["bbox"] for element in tables]
        page_element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}

        words = page.get_text("words")
        words_dict_list = [{"bbox": word[:4], "text": word[4], "type": "word"} for word in words]
        components = sorted(images + tables + words_dict_list, key=lambda c: (c["bbox"][3], c["bbox"][0]))
        words_bbox_list = []

        current_chunk = ""
        current_metadata = {
            "start_page_num": current_page_number,
            "end_page_num": current_page_number,
            "start_line_num": current_line_number,
            "end_line_num": current_line_number,
            "contains_image": False,
            "contains_table": False,
            "contains_chart": False,
            "contains_flowchart": False,
            "contains_confusion_matrix": False,
            "elements": [],
            "bbox": DEFAULT_BBOX,
            "content_type": "text"
        }
        x_left, y_left, x_right, y_right = DEFAULT_BBOX

        last_y = words[0][3] if words else 0

        for index, c in enumerate(components):
            c_type = c["type"]
            if c_type == "word":
                ignore = False
                for image_bbox in image_bbox_list:
                    if bbox_intersects(c["bbox"], image_bbox):
                        ignore = True
                        break
                if not ignore:
                    for table_bbox in tables_bbox_list:
                        if bbox_intersects(c["bbox"], table_bbox):
                            ignore = True
                            break
                if ignore:
                    continue

                word_text = c["text"] + " "
                if abs(c["bbox"][3] - last_y) > 5:  # New line if y-coordinate differs by more than 5
                    current_line_number += 1
                    last_y = c["bbox"][3]

                current_chunk += word_text
                current_metadata["end_line_num"] = current_line_number
                x_left, y_left, x_right, y_right = min(x_left, c["bbox"][0]), min(y_left, c["bbox"][1]), max(x_right,
                                                                                                             c["bbox"][
                                                                                                                 2]), max(
                    y_right, c["bbox"][3])
                current_metadata["bbox"] = (x_left, y_left, x_right, y_right)
                if len(current_chunk) >= chunk_size:
                    current_metadata["end_page_num"] = current_page_number
                    words_bbox_list.append(current_metadata["bbox"])

                    # Check if the chunk matches the specific pattern
                    chunk_content = current_chunk.strip()
                    if re.match(
                            r'^此文件只供內部參考 \d+ 不可作複印、分發或上載於任何社交媒體平台\s*$|^「AIA」 、 「本公司」 或 「我們」 \(於百慕達註冊成立之有限公司\) 。 是指友邦保險 \(國際\) 有限公司\s*$',
                            chunk_content):
                        chunk_content = ""

                    chunks.append((chunk_content, current_metadata))
                    x_left, y_left, x_right, y_right = DEFAULT_BBOX
                    # Reset current chunk and metadata

                    current_chunk = " ".join(
                        [word for word in current_chunk.strip().rsplit(' ', chunk_overlap + 1)[-1 - chunk_overlap:-1]])
                    current_metadata = {
                        "start_page_num": current_page_number,
                        "end_page_num": current_page_number,
                        "start_line_num": current_line_number,
                        "end_line_num": current_line_number,
                        "content_type": "text",
                        "bbox": DEFAULT_BBOX,
                        "elements": []
                    }
            else:  # component belongs to the elements (images or tables)
                if current_chunk:
                    current_metadata["end_page_num"] = current_page_number
                    words_bbox_list.append(current_metadata["bbox"])

                    # Check if the chunk matches the specific pattern
                    chunk_content = current_chunk.strip()
                    if re.match(
                            r'^此文件只供內部參考 \d+ 不可作複印、分發或上載於任何社交媒體平台\s*$|^「AIA」 、 「本公司」 或 「我們」 \(於百慕達註冊成立之有限公司\) 。 是指友邦保險 \(國際\) 有限公司\s*$',
                            chunk_content):
                        chunk_content = ""

                    chunks.append((chunk_content, current_metadata))
                    x_left, y_left, x_right, y_right = DEFAULT_BBOX

                element_counters[c_type] += 1
                page_element_counters[c_type] += 1

                if c_type == 'image':
                    print("description_all", image_descriptions[page_element_counters[c_type] - 1])
                    description = image_descriptions[page_element_counters[c_type] - 1]
                    print("description", description)
                elif c_type == 'table':
                    description = '\n'.join([' | '.join(row) for row in c['text']])
                else:
                    description = None

                element_description = get_element_description(c_type, c, description)
                element_metadata = {
                    "start_page_num": current_page_number,
                    "end_page_num": current_page_number,
                    "start_line_num": current_line_number,
                    "end_line_num": current_line_number,
                    "content_type": c_type,
                    "elements": [c],
                    "bbox": (
                    min(current_metadata["bbox"][0], c["bbox"][0]), min(current_metadata["bbox"][1], c["bbox"][1]),
                    max(current_metadata["bbox"][2], c["bbox"][2]), max(current_metadata["bbox"][3], c["bbox"][3]))
                }
                chunks.append((current_chunk.strip() + element_description, element_metadata))
                x_left, y_left, x_right, y_right = DEFAULT_BBOX
                # Reset current chunk and metadata
                current_chunk = ""
                current_metadata = {
                    "start_page_num": current_page_number,
                    "end_page_num": current_page_number,
                    "start_line_num": current_line_number,
                    "end_line_num": current_line_number,
                    "content_type": "text",
                    "elements": [],
                    "bbox": DEFAULT_BBOX
                }

        if current_chunk:
            current_metadata["end_page_num"] = current_page_number
            words_bbox_list.append(current_metadata["bbox"])

            # Check if the chunk matches the specific pattern
            chunk_content = current_chunk.strip()
            if re.match(
                    r'^此文件只供內部參考 \d+ 不可作複印、分發或上載於任何社交媒體平台\s*$|^「AIA」 、 「本公司」 或 「我們」 \(於百慕達註冊成立之有限公司\) 。 是指友邦保險 \(國際\) 有限公司\s*$',
                    chunk_content):
                chunk_content = ""

            chunks.append((chunk_content, current_metadata))
            x_left, y_left, x_right, y_right = DEFAULT_BBOX

        current_page_number += 1
        current_line_number = 1

    os.makedirs(f"saved_pdf/{file_dataset}", exist_ok=True)
    output_file_name = f"{os.path.splitext(os.path.basename(pdf_path))[0]}.pdf"
    doc.save(f"saved_pdf/{file_dataset}/{output_file_name}")

    print("total number of tokens (image):", total_num_of_tokens)
    print("number of input tokens (image):", num_of_input_tokens)
    print("number of output tokens (image):", num_of_output_tokens)
    end_time = datetime.now()
    print("Time used: ", end_time - start_time)
    return chunks, element_counters
@app.route('/upload', methods=['POST'])
def upload_file():
    global vs_config
    global selected_dataset
    global schema_config
    global vs_df # log file for vector store
    global user_id

    selected_dataset = request.form.get('selectedDataset', None)

    if not selected_dataset:
        selected_dataset = env.default_dataset_name
        print(f'* No Selected Dataset Name : Default Name <{selected_dataset}> is Used')

    # Create Collection
    created = vectorstore_module_newui.create_vscollection(
        vs_config,
        selected_dataset,
        schema_config
    )
    graph_store = graphstore_module_newui(selected_dataset)

    print(f'... Uploading the File into the Selected Dataset <{selected_dataset}>')
    uploaded_files = request.files.getlist('file')
    print("uploaded_files: ", uploaded_files)

    if not uploaded_files:
        print('Error: No Uploading Files Found!')
        return jsonify({'status': 'error', 'message': 'No files found for upload'})
    
    
    from transformers import AutoTokenizer
    tokenizer = AutoTokenizer.from_pretrained("hkunlp/instructor-large")
    total_tokens = 0

    for file in uploaded_files:
        if file:
            filename = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
            file.save(filename)
            print("saved file")

            if filename.lower().endswith(".pdf"):
                chunks_with_metadata, element_counters = chunk_pdf_advanced(filename, selected_dataset)
                chunked_rawtexts = [chunk for chunk, _ in chunks_with_metadata]
                chunked_metadata = [metadata for _, metadata in chunks_with_metadata]
            else:
                loader = DirectoryLoader(app.config['UPLOAD_FOLDER'], show_progress=True, use_multithreading=True)
                document = loader.load()
                print("loaded other type document")
                if not document:
                    print('Warning: Cannot Find Document in Upload Folder')
                chunked_content = preprocessing.chunk_document(document, chunk_method)
                chunked_rawtexts = [chunk.page_content for chunk in chunked_content]
                chunked_metadata = [{}] * len(chunked_rawtexts)  # Empty metadata for non-PDF files

            # Process each chunk to remove specific headers
            processed_chunked_rawtexts = []
            for chunk in chunked_rawtexts:
                # Remove the specific headers if they are at the beginning of the chunk
                chunk = re.sub(
                    r'PRUHealth Guardian Critical Illness Plan Series 「誠保一生」危疾保系列 \(CIM3/BCIM3\)\s*', '',
                    chunk)
                chunk = re.sub(r'PRUHealth Guardian Critical Illness Plan Series\s*', '', chunk)
                chunk = re.sub(r'All words in the image: 「擎識定期壽險計劃」 4.*?\s*', '', chunk)
                chunk = re.sub(r'All words in the image.*?\s*', '', chunk)
                processed_chunked_rawtexts.append(chunk)
            for chunk in processed_chunked_rawtexts:
                tokens = tokenizer.encode(chunk)
                total_tokens += len(tokens)

            embedded_vectors = embedding_method.embed_documents(processed_chunked_rawtexts)

            # Create Partition
            partition_name = ''.join(e for e in file.filename if e.isalnum())
            created = vectorstore_module_newui.create_vspartition(
                vs_config,
                selected_dataset,
                partition_name=partition_name
            )
            # Generate id_list
            id_list = [partition_name + "_" + str(np.int64(i)) for i in range(1, len(processed_chunked_rawtexts) + 1)]
            # Process each chunk individually
            field_dicts = []
            for i, (chunk_text, metadata, node_id) in enumerate(
                    zip(processed_chunked_rawtexts, chunked_metadata, id_list)):
                chunk_lines = chunk_text.split('\n')

                # Use the metadata directly from the chunk
                start_page = metadata['start_page_num']
                end_page = metadata['end_page_num']
                start_line = metadata['start_line_num']
                end_line = metadata['end_line_num']
                bbox = str(metadata['bbox'])
                content_type = metadata['content_type']

                field_dicts.append({
                    "source": partition_name,
                    "extension": file.filename.split('.')[-1],
                    "language": "US",
                    "permission": "3",
                    "date": datetime.now().strftime("%Y-%m-%d, %H:%M:%S"),
                    "uploader": user_id,
                    "start_page_num": start_page,
                    "end_page_num": end_page,
                    "start_line_num": start_line,
                    "end_line_num": end_line,
                    "node_id": node_id,
                    "content_type":content_type,
                    "bbox":bbox,
                    # "image_bboxes": image_bboxes,
                    # "has_image": bool(image_bboxes),
                    # "has_table": any(elem[0] == 'table' for elem in chunk.metadata['element_info']),
                    # "has_chart": any(elem[0] == 'chart' for elem in chunk.metadata['element_info']),
                    # "has_flowchart": any(elem[0] == 'flowchart' for elem in chunk.metadata['element_info']),
                    # "has_confusion_matrix": any(
                    #     elem[0] == 'confusion_matrix' for elem in chunk.metadata['element_info'])
                })
            
            print(f'Total embedding input tokens: {total_tokens}')
            # Insert chunks into graph store
            graph_store.insert_document(chunked_rawtexts,field_dicts,id_list, partition_name)

            # Insert Vectors into Vector Store
            vectorstore_module_newui.insert_vectors_2(
                vs_config,
                selected_dataset,
                partition_name,
                schema_config,
                processed_chunked_rawtexts,
                embedded_vectors,
                field_dicts
            )
            print("inserted document")

            print(f'Successfully Added the Uploaded File <{file.filename}> into Vector Store')

            # Update log file for vector store
            new_file = {"dataset": selected_dataset,
                        "filename": file.filename,
                        "chunk_size": len(chunked_rawtexts),
                        "permission_level": 3,
                        "uploader": user_id,
                        "upload_time": datetime.now().strftime("%Y-%m-%d, %H:%M:%S")}

            # vs_df = vectorstore_module_newui.update_vs_df(new_file)

            # Remove File after Storing into Vector Store
            # os.remove(filename)
            # get_datasets()
            # print(f'Deleted File <{file.filename}> from Upload Folder')
            
    return jsonify({'status': 'success', 'message': 'File uploaded successfully'})

selected_files = []
@app.route('/selected-files', methods=['POST'])
def point_selected_files():
    global selected_files
    selected_files.clear()  # clear global_file_list

    data = request.get_json()
    print(f"DEBUG: 收到的文件选择数据: {data}")
    
    # 检查数据格式
    if data is None:
        print("ERROR: 收到的数据为None")
        return jsonify({'status': 'error', 'message': 'No data received'}), 400
    
    if 'selectedFiles' not in data:
        print(f"ERROR: 数据中没有'selectedFiles'键，数据内容: {data}")
        return jsonify({'status': 'error', 'message': 'No selectedFiles in data'}), 400
    
    selected_files = data['selectedFiles']  # new files in the global_file_list
    # selected_files = [f .split('.')[0]for f in selected_files]
    selected_files = [f for f in selected_files]

    # selected_files = data
    print('Selected Files:')
    print(*selected_files, sep='\n')
    print(f"DEBUG: 更新后的全局selected_files变量: {selected_files}")

    return jsonify({'status': 'success', 'message': 'Selected files received'})

@app.route('/get-datasets', methods=['GET'])
def get_datasets():
    dataset_list = vectorstore_module_newui.init_vsdb(vs_config)
    print(dataset_list)
    return jsonify(dataset_list)

@app.route('/vectorstore/<filename>', methods=['GET'])
def log_file(filename):
    return send_from_directory('vectorstore', filename)

@app.route('/get-files', methods=['GET'])
def get_files(vsdb_log_dir=env.vsdb_log_dir):
    global vs_df
    # Load and return the list of files from file
    vs_df = pd.read_json(vsdb_log_dir)
    unique_files = vs_df['filename'].unique().tolist()
    return jsonify(unique_files)

@app.route('/delete-file', methods=['POST'])
def delete_file():
    global vs_df
    data = request.get_json()
    del_file = data['file_name']
    selected_dataset = data['dataset']
    # Add logic to delete the file from  server
    # os.remove(os.path.join(UPLOAD_FOLDER, file_to_delete))
    print(f"Deleting File: {del_file}")

    # ***** Newly Added
    del_file = ''.join(e for e in del_file if e.isalnum())

    graph_store = graphstore_module_newui(selected_dataset)
    graph_store.delete_document(del_file)
    vectorstore_module_newui.delete_entities(vs_config,
                                       del_file,
                                       selected_dataset)
    print('Deleted File Entities from Vector Store')

    # vs_df = vectorstore_module_newui.delete_vs_df(data['file_name'])
    # print('Deleted File from File Log')
    return jsonify({'status': 'success', 'message': 'File deleted successfully'})

@app.route('/delete-dataset', methods=['POST'])
def delete_dataset():
    global vs_df
    data = request.get_json()
    selected_dataset = data['dataset']
    graph_store = graphstore_module_newui()
    graph_store.delete_dataset(selected_dataset)
    
    vectorstore_module_newui.delete_collection(vs_config, selected_dataset)
    return jsonify({'message': 'Dataset deleted successfully'})


@app.route('/get-prompt-templates', methods=['GET'])
def get_prompt_templates():
    return jsonify({'prompt_template_list': prompt_template_list})

USER_DATA_DIR = 'user/'

@app.route('/get-all-users-data')
def get_all_users_data():
    all_users_data = {}
    for user_dir in os.listdir(USER_DATA_DIR):
        user_dir_path = os.path.join(USER_DATA_DIR, user_dir)
        if os.path.isdir(user_dir_path):
            user_id = user_dir
            chat_file_path = os.path.join(user_dir_path, f'{user_id}_chat.json')
            try:
                if os.path.exists(chat_file_path):
                    with open(chat_file_path, 'r', encoding='utf-8') as file:
                        user_data = json.load(file)
                        first_entry = next(iter(user_data.values()), None)
                        if first_entry:
                            first_question = first_entry.get('q_msg', 'No question available')
                            question_time = first_entry.get('q_time', 'No time available')
                            all_users_data[user_id] = {
                                'q_msg': first_question,
                                'q_time': question_time,
                            }
                        else:
                            all_users_data[user_id] = {'q_msg': 'No question available', 'q_time': 'No time available'}
            except (IOError, json.JSONDecodeError) as e:
                print(f"Error reading from {chat_file_path}: {e}")
                all_users_data[user_id] = {'q_msg': 'Error loading data', 'q_time': 'Error loading data'}
    return jsonify(all_users_data)

@app.route('/get-user-data/<user_id>')
def get_user_data(user_id):
    user_data_path = os.path.join(USER_DATA_DIR, user_id, f'{user_id}_chat.json')
    if os.path.exists(user_data_path):
        with open(user_data_path, 'r', encoding='utf-8') as file:
            user_data = json.load(file)
            print(user_data)
            return jsonify(user_data)
    else:
        return jsonify({'error': 'User data not found'}), 404

@app.route('/get-user-count', methods=['GET'])
def get_user_count():
    user_dir_path = os.path.join(USER_DATA_DIR)  # Assuming 'user' dir is at the root of your Flask app
    try:
        user_dirs = [name for name in os.listdir(user_dir_path) if os.path.isdir(os.path.join(user_dir_path, name))]
        user_count = len(user_dirs)
        print("user_count: ", user_count)
        return jsonify({'status': 'success', 'user_count': user_count})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/delete-chat-history', methods=['POST'])
def delete_chat_history():
    data = request.get_json()  # Get the JSON data sent from the frontend
    user_id = data['userId']  # Extract the userId sent from the frontend

    # Define the path to the user's folder
    user_folder_path = os.path.join('user', str(user_id))
    print("user_folder_path: ", user_folder_path)

    # Check if the folder exists and then delete it
    if os.path.exists(user_folder_path):
        try:
            shutil.rmtree(user_folder_path)  # Removes the folder and all its contents
            return jsonify({'message': 'Chat history successfully deleted'}), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500  # Internal Server Error
    else:
        return jsonify({'message': 'User folder not found'}), 404  # Not Found


@app.route('/create-new-chat-history', methods=['POST'])
def create_new_chat_history():
    global user_id
    global chat_history

    user_dir_path = os.path.join(os.getcwd(), 'user')  # Assuming 'user' directory is at the root of your Flask app

    # Ensure the 'user' directory exists
    if not os.path.exists(user_dir_path):
        os.makedirs(user_dir_path)

    # Find the next available user ID by checking existing directories
    user_dirs = [d for d in os.listdir(user_dir_path) if os.path.isdir(os.path.join(user_dir_path, d))]
    user_id = dt.datetime.now().strftime(datetime_userid_format)
    new_user_dir_name = "user-" + user_id

    # next_user_id = len(user_dirs) + 1  # Assuming user IDs are sequential and start from 1
    # new_user_dir_name = f"user-{next_user_id:03d}"  # Format ID as three digits
    new_user_dir_path = os.path.join(user_dir_path, new_user_dir_name)

    # Create the new user directory
    os.makedirs(new_user_dir_path)

    # Create a new chat history JSON file inside the new user directory
    new_chat_history_path = os.path.join(new_user_dir_path, f"{new_user_dir_name}_chat.json")
    chat_history["dir"] = new_chat_history_path
    chat_history["df"] = pd.DataFrame()
    chat_history["langchain"] = ChatMessageHistory()
    chat_history["langchain"].add_user_message("")
    chat_history["langchain"].add_ai_message("")
    chat_history["langchain"] = chat_history["langchain"].messages

    print("Chat History at Create: ", chat_history)

    with open(new_chat_history_path, 'w') as file:
        json.dump({}, file)  # Start with an empty JSON object

    return jsonify({'status': 'success', 'message': f'New chat history for {new_user_dir_name} created successfully.'})

@app.route('/process-selected-chat-history', methods=['POST'])
def process_selected_chat_history():
    data = request.get_json()  # Parse the JSON data sent in the request
    user_id = data.get('userId')
    # Now, you can use `user_id` to perform operations, such as retrieving and sending back the chat history
    # For demonstration, we'll just send back a success message
    return jsonify({'status': 'success', 'message': f'Processed chat history for user ID: {user_id}'})

@app.route('/update-user-session', methods=['POST'])
def update_user_session():
    global user_id
    global user_profile
    global chat_history

    data = request.get_json()
    user_id_str = data.get('userId')
    match = re.search(r'\d+$', user_id_str)

    # Assuming userId is a string that needs to be converted to an integer
    try:
        # raw_uid = int(match.group())
        # uid_digit = config["uid_digit"]
        # user_id = f"%0{uid_digit}d" %raw_uid

        # User Authentication
        # user_fullform = env.user_prefix + str(user_id)

        # user_fullform = env.user_prefix + user_id
        user_fullform = user_id_str
        print("user_fullform: ", user_fullform)
        user_dir = os.path.join(env.user_dir_root, user_fullform)

        # Retrieval of User Profile
        user_profile = os.path.join(user_dir, user_fullform + env.user_prof_suffix)

        # Retrieval of User Chat History
        user_history = os.path.join(user_dir, user_fullform + env.user_chat_suffix)
        chat_history = {"dir": None,
                        "df": None,
                        "langchain": None}

        chat_history["langchain"] = ChatMessageHistory()
        chat_history["dir"] = user_history
        if not os.path.exists(chat_history["dir"]):
            chat_history["df"] = pd.DataFrame()
            chat_history["langchain"].add_user_message("")
            chat_history["langchain"].add_ai_message("")

        else:
            df = pd.read_json(chat_history["dir"], orient="index")
            chat_history["df"] = df
            print("chat_history1:", chat_history)
            # Convert Chat History to langchain Format
            try:
                for q_msg, a_msg in zip(df["q_msg"].to_list(),
                                        df["a_msg"].to_list()):
                    chat_history["langchain"].add_user_message(q_msg)
                    chat_history["langchain"].add_ai_message(a_msg)
                    # print("chat_history:", chat_history)
                    # print("chat_history[langchain]:", chat_history["langchain"])
            except KeyError:
                chat_history["langchain"].add_user_message("")
                chat_history["langchain"].add_ai_message("")

        chat_history["langchain"] = chat_history["langchain"].messages

        return jsonify({'status': 'success', 'message': 'User session updated successfully.'})
    except ValueError:
        return jsonify({'status': 'error', 'message': 'Invalid userId format'}), 400


app.secret_key = 'your_secret_key'


# Existing functions for loading and saving user credentials...

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user_credentials = load_user_credentials()

        if username in user_credentials and check_password_hash(user_credentials[username], password):
            flash('Login successful.', 'success')
            return redirect(url_for('index'))  # Assuming 'index' is the route for your main page
        elif username not in user_credentials:
            flash('Username does not exist.', 'danger')
        else:
            flash('Incorrect password.', 'danger')

    return render_template('login.html')


@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user_credentials = load_user_credentials()

        if username in user_credentials:
            flash('Username already exists.', 'warning')
        else:
            user_credentials[username] = generate_password_hash(password)
            save_user_credentials(user_credentials)
            flash('Registration successful. Please login.', 'success')
            return redirect(url_for('login'))

    return render_template('register.html')



@app.route('/chat', methods=['POST'])
def chat():
    """将POST请求重定向到chat_stream端点，实现兼容性"""
    # 所有/chat的POST请求都转发到/chat_stream处理
    return redirect('/chat_stream?' + request.query_string.decode('utf-8'), code=307)  # 307保留POST方法

@app.route('/chat_stream', methods=['GET', 'POST'])
def chat_stream():
    """流式输出聊天响应，使用Server-Sent Events (SSE)"""
    global selected_dataset
    global user_id
    global chat_history
    print("Chat History at Chat: " , chat_history)
    print(f"DEBUG: chat_stream开始时的全局变量selected_dataset: {selected_dataset}")

    # 获取请求参数
    method = request.method
    if method == 'GET':
        user_input = request.args.get('user_input', '')
        model = request.args.get('model', 'mixtral')
        rag = request.args.get('rag', 'off')
        dataset = request.args.get('selectedDataset', '')
        include_history = request.args.get('include_history', 'false') == 'true'
        max_history_no = request.args.get('max_history_no', '3')
        filename_list = request.args.get('filename_list', '')
    else:  # POST方法
        user_input = request.form.get('user_input', '')
        model = request.form.get('model', 'mixtral')
        rag = request.form.get('rag', 'off')
        dataset = request.form.get('selectedDataset', '')
        include_history = request.form.get('include_history', 'false') == 'true'
        max_history_no = request.form.get('max_history_no', '3')
        filename_list = request.form.get('filename_list', '')

    # 添加调试信息
    print(f"DEBUG: chat_stream 请求参数: user_input='{user_input}', model='{model}', rag='{rag}'")
    print(f"DEBUG: dataset='{dataset}', include_history={include_history}, max_history_no={max_history_no}")

    # 如果请求中没有提供数据集，但全局变量中有，则使用全局变量的值
    if not dataset and selected_dataset:
        dataset = selected_dataset
        print(f"DEBUG: 请求中没有提供数据集，使用全局变量的值: {dataset}")
    
    # 检查是否有选定的数据集和文件，如果有则启用RAG
    print(f"DEBUG: 在chat_stream中检查 dataset={dataset}, selected_files={selected_files}")
    if dataset and len(selected_files) > 0:
        print(f"DEBUG: 检测到选定数据集: {dataset} 和文件: {selected_files}")
        rag = "on"
        print(f"DEBUG: 自动启用RAG")
    elif dataset and not selected_files:
        print(f"WARNING: 检测到数据集 {dataset} 但没有选择文件，将尝试使用数据集中的所有文件")
        # 不自动开启RAG，等到generate函数中处理
    
    # 检查空输入
    if not user_input or user_input.strip() == '':
        def generate_error():
            yield f"data: {json.dumps({'type': 'content', 'content': '请输入问题'})}\n\n"
            yield f"data: {json.dumps({'type': 'end'})}\n\n"
        return app.response_class(generate_error(), mimetype='text/event-stream')
    
    # 更新全局变量
    selected_dataset = dataset

    # 设置响应头
    def generate(user_input, model, rag, dataset, include_history, max_history_no, filename_list):
        # 声明使用全局变量
        global selected_files
        
        # 转换参数类型
        try:
            max_history_no = int(max_history_no)
        except ValueError:
            max_history_no = 0

        # 处理模型名称
        if model.lower() == "gpt-3.5":
            model = "openai"
        elif model.lower() == "llama":
            if rag == "on":
                model = "llama+rag-1"
            else:
                model = "llama-1"
        elif model.lower() == "mixtral":
            if rag == "on":
                model = "mixtral+rag-1"
            else:
                model = "mixtral-1"
        
        print(f"DEBUG: 使用模型: {model}")

        q_time = datetime.now()

        # 初始化响应
        bot_response = ""

        # 生成提示词
        system_prompt = prompt_module.gen_system_prompt(system_prompt_para)
        condense_system_prompt = prompt_module.gen_condense_system_prompt(system_prompt_para)
        prompt_df = pd.read_json(env.prompt_tempte_dir + selected_instrcut + '.json')
        prompt_template = '\n'.join(prompt_df["prompt"])

        # 获取模型配置
        model_name = model.split('-')[0]
        prime_model_name = model_name.split('+')[0]
        prime_model_root = os.path.join(env.model_dir_root, prime_model_name)
        model_config_dir = os.path.join(prime_model_root, model + env.model_config_suffix)
        print(f"DEBUG: 模型配置文件路径: {model_config_dir}")
        chunks_list = None

        # 检查模型配置是否存在
        if not os.path.exists(model_config_dir):
            err_msg = f"Input Error: The Model ID <{model}> could not be Found in {prime_model_root}"
            print(f"ERROR: {err_msg}")
            yield f"data: {json.dumps({'type': 'content', 'content': err_msg})}\n\n"
            yield f"data: {json.dumps({'type': 'end'})}\n\n"
            return

        model_para = pd.read_json(model_config_dir, typ='series')
        print(f"DEBUG: 模型参数: {model_para}")

        # 获取检索器
        retriever = None
        files_pages_data = None
        if rag == "on":
            if dataset:
                yield f"data: {json.dumps({'type': 'content', 'content': '正在从知识库检索相关信息...'})}\n\n"
                print(f"DEBUG: 使用数据集: {dataset}")
                print(f"DEBUG: 选择的文件: {selected_files}")
                
                # 确保selected_files不为空
                if not selected_files:
                    print("WARNING: 没有选择文件，将使用数据集中的所有文件")
                    # 获取数据集中的所有文件
                    unique_files = pd.read_json(env.vsdb_log_dir)
                    selected_files = unique_files['filename'].unique().tolist()
                    print(f"DEBUG: 使用数据集中的所有文件: {selected_files}")
                
                # 获取检索器和相关文档块
                retriever, chunks_list = vectorstore_module_newui.get_retriever3(
                    vs_config, embedding_method, dataset, selected_files, search_config, user_input
                )
                
                # 打印检索到的文档块用于调试
                print(f"DEBUG: 检索到 {len(chunks_list)} 个文档块")
                for i, chunk in enumerate(chunks_list[:3]):  # 只打印前3个作为示例
                    print(f"DEBUG: 块 {i+1} - 来源: {chunk['source']}, 页码: {chunk.get('page_num', 'N/A')}")
                    print(f"DEBUG: 内容: {chunk.get('page_content', '')[:100]}...")
                
                # 创建引用数据
                files_pages_data={}
                for chunk in chunks_list:
                    filename = chunk['source']
                    filename = filename.replace('pdf', '.pdf')
                    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                    page_number = chunk.get('page_num', 1)  # 默认为第1页
                    bbox = chunk.get('bbox', [0, 0, 0, 0])  # 默认边界框
                    content_type = chunk.get('content_type', 'text')  # 默认内容类型
                    
                    if filename not in files_pages_data:
                        files_pages_data[filename]={}
                    if page_number not in files_pages_data[filename]:
                        files_pages_data[filename][page_number]=[]
                    files_pages_data[filename][page_number].append({"bbox":bbox,"content_type":content_type})
                
                print(f"DEBUG: 创建的引用数据: {json.dumps(files_pages_data, indent=2)[:200]}...")
            else:
                yield f"data: {json.dumps({'type': 'content', 'content': '错误: RAG已开启，但找不到数据集'})}\n\n"
                retriever = None
                print("ERROR: RAG已开启，但找不到数据集。请选择一个数据集。")
        else:
            retriever = None
            
        # 处理文件名列表参数
        if filename_list:
            print(f"接收到文件名列表: {filename_list}")
            
        # 获取聊天历史
        chat_record = None
        if include_history:
            chat_record = chat_history["langchain"]
            if len(chat_history["langchain"]) - 2 > max_history_no:
                n_pairs = max_history_no * 2
                chat_record = chat_history["langchain"][-n_pairs:]

        # 定义回调处理函数来捕获模型思考过程和输出
        from langchain.callbacks.base import BaseCallbackHandler
        
        class StreamingCallbackHandler(BaseCallbackHandler):
            def __init__(self):
                self.thinking = ""
                self.answer = ""
                self.current_thinking = False
                
            def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
                # 清理token中的chunk标记
                if "chunk" in token:
                    token = token.replace("chunk", "")
                
                # 处理思考模式的切换
                if "<think>" in token:
                    self.current_thinking = True
                    # 发送思考开始标记
                    yield f"data: {json.dumps({'type': 'thinking', 'content': '<思考开始>'})}\n\n"
                    token = token.replace("<think>", "")
                
                if "</think>" in token:
                    self.current_thinking = False
                    # 发送思考结束标记
                    token = token.replace("</think>", "")
                    yield f"data: {json.dumps({'type': 'thinking', 'content': token + '<思考结束>'})}\n\n"
                    return
                
                # 根据当前模式发送不同类型的消息
                if self.current_thinking:
                    self.thinking += token
                    yield f"data: {json.dumps({'type': 'thinking', 'content': token})}\n\n"
                else:
                    self.answer += token
                    yield f"data: {json.dumps({'type': 'content', 'content': token})}\n\n"

        # 创建一个回调处理器
        streaming_handler = StreamingCallbackHandler()

        try:
            # 根据模型类型选择不同的处理方法
            if prime_model_name.lower() == "llama":
                print("DEBUG: 使用llama模型进行流式处理")
                # 实现llama的流式处理
                for event in llama_module.stream_llama_response(
                    user_input,
                    system_prompt,
                    condense_system_prompt,
                    prompt_template,
                    model_para,
                    retriever,
                    chat_record,
                    streaming_handler
                ):
                    yield event
                
            elif prime_model_name.lower() == "openai":
                print("DEBUG: 使用openai模型进行流式处理")
                # 实现openai的流式处理
                for event in openai_module.stream_openai_response(
                    user_input,
                    system_prompt,
                    condense_system_prompt,
                    prompt_template,
                    model_para,
                    retriever,
                    chat_record,
                    streaming_handler
                ):
                    yield event
                    
            elif prime_model_name.lower() == "mixtral":
                print("DEBUG: 使用mixtral模型进行流式处理")
                # 使用我们已经修改好的mixtral模块的异步流式处理能力
                import asyncio
                
                # 检查已有异步响应能力
                if not hasattr(mixtral_module_deepseek_nochathistory_v2, 'async_mixtral_response'):
                    print("ERROR: mixtral_module_deepseek_nochathistory_v2没有async_mixtral_response方法")
                    yield f"data: {json.dumps({'type': 'content', 'content': '系统错误：模型配置缺失'})}\n\n"
                    yield f"data: {json.dumps({'type': 'end'})}\n\n"
                    return
                
                # 创建一个异步生成器包装函数
                async def async_generate():
                    result = ""
                    in_thinking = False
                    thinking_buffer = ""
                    answer_buffer = ""
                    
                    print(f"DEBUG: 开始异步调用mixtral模型，参数: user_input='{user_input}'")

                    count = 0
                    print("DEBUG: 开始收集来自异步生成器的响应块...")
                    
                    # 保存所有块以便调试
                    all_chunks = []
                    
                    # 计算总计接收到的数据大小
                    total_data_size = 0
                    
                    async for chunk in mixtral_module_deepseek_nochathistory_v2.async_mixtral_response(
                        user_input,
                        system_prompt,
                        condense_system_prompt,
                        prompt_template,
                        model_para,
                        retriever,
                        chat_record
                    ):
                        count += 1
                        total_data_size += len(str(chunk))
                        all_chunks.append(chunk)
                        
                        print(f"DEBUG: 接收到第{count}个块: '{chunk[:50]}{'...' if len(chunk) > 50 else ''}'")
                        
                        # 累积所有响应用于最终处理
                        result += chunk
                        
                        # 检查thinking标签
                        combined_text = thinking_buffer + answer_buffer + chunk
                        
                        # 处理thinking开始
                        if "<think>" in combined_text and not in_thinking:
                            # 找到thinking开始位置
                            think_start = combined_text.find("<think>")
                            
                            # 发送thinking之前的内容作为答案
                            before_think = combined_text[:think_start]
                            if before_think.strip():
                                # 清理并发送
                                cleaned_before = before_think.replace("chunk", "").strip()
                                if cleaned_before:
                                    yield f"data: {json.dumps({'type': 'content', 'content': cleaned_before})}\n\n"
                            
                            # 更新状态
                            in_thinking = True
                            thinking_buffer = combined_text[think_start + 7:]  # 跳过<think>
                            answer_buffer = ""
                            continue
                        
                        # 处理thinking结束
                        if "</think>" in combined_text and in_thinking:
                            # 找到thinking结束位置
                            think_end = combined_text.find("</think>")
                            
                            # 发送thinking内容
                            thinking_content = combined_text[:think_end]
                            if thinking_content.strip():
                                # 清理并发送thinking内容
                                cleaned_thinking = thinking_content.replace("chunk", "").strip()
                                if cleaned_thinking:
                                    yield f"data: {json.dumps({'type': 'thinking', 'content': cleaned_thinking})}\n\n"
                            
                            # 更新状态
                            in_thinking = False
                            thinking_buffer = ""
                            answer_buffer = combined_text[think_end + 8:]  # 跳过</think>
                            continue
                        
                        # 正常处理内容
                        if in_thinking:
                            # 在thinking模式中，累积到thinking buffer
                            thinking_buffer += chunk
                            # 发送thinking内容
                            cleaned_chunk = chunk.replace("chunk", "").strip()
                            if cleaned_chunk:
                                yield f"data: {json.dumps({'type': 'thinking', 'content': cleaned_chunk})}\n\n"
                        else:
                            # 在答案模式中，累积到answer buffer
                            answer_buffer += chunk
                            # 发送答案内容
                            cleaned_chunk = chunk.replace("chunk", "").strip()
                            if cleaned_chunk:
                                yield f"data: {json.dumps({'type': 'content', 'content': cleaned_chunk})}\n\n"
                    
                    print(f"DEBUG: 总共接收到{count}个块，总大小{total_data_size}字节")
                    print(f"DEBUG: 最终结果前100字符: {result[:100]}...")
                    
                    # 处理剩余的buffer内容
                    if thinking_buffer.strip():
                        cleaned_thinking = thinking_buffer.replace("chunk", "").strip()
                        if cleaned_thinking:
                            yield f"data: {json.dumps({'type': 'thinking', 'content': cleaned_thinking})}\n\n"
                    
                    if answer_buffer.strip():
                        cleaned_answer = answer_buffer.replace("chunk", "").strip()
                        if cleaned_answer:
                            yield f"data: {json.dumps({'type': 'content', 'content': cleaned_answer})}\n\n"
                    
                    # 发送引用信息（如果有）
                    if files_pages_data:
                        yield f"data: {json.dumps({'type': 'end', 'references': files_pages_data})}\n\n"
                    else:
                        yield f"data: {json.dumps({'type': 'end'})}\n\n"
                    
                    # 保存完整响应到聊天历史
                    if result:
                        bot_response = result
                        a_time = datetime.now()
                        chat_history["langchain"].extend([HumanMessage(content=user_input), AIMessage(content=bot_response)])
                        print("Chat history saved successfully.")
                        
                        # 保存到数据库
                        try:
                            with open(env.chat_history_dir + "/" + user_id + ".json", 'w', encoding='utf-8') as json_file:
                                chat_history_records = {
                                    "user_info": {
                                        "user_name": user_id,
                                        "last_chat_time": str(datetime.now())
                                    },
                                    "chat_record": {
                                        str(len(json.load(open(env.chat_history_dir + "/" + user_id + ".json", encoding='utf-8')))["chat_record"] if os.path.exists(env.chat_history_dir + "/" + user_id + ".json") else 0): {
                                            "q_msg": user_input,
                                            "a_msg": bot_response,
                                            "q_time": str(q_time),
                                            "a_time": str(a_time),
                                            "chunks_list": files_pages_data if files_pages_data else {}
                                        }
                                    }
                                }
                                
                                # 如果用户文件存在，则合并记录
                                if os.path.exists(env.chat_history_dir + "/" + user_id + ".json"):
                                    with open(env.chat_history_dir + "/" + user_id + ".json", 'r', encoding='utf-8') as existing_file:
                                        existing_data = json.load(existing_file)
                                        existing_data["user_info"]["last_chat_time"] = str(datetime.now())
                                        existing_data["chat_record"].update(chat_history_records["chat_record"])
                                        chat_history_records = existing_data
                                
                                json.dump(chat_history_records, json_file, ensure_ascii=False, indent=4)
                                print(f"DEBUG: 聊天历史已保存到 {env.chat_history_dir}/{user_id}.json")
                        except Exception as e:
                            print(f"ERROR: 保存聊天历史失败: {e}")
                
                # 运行异步生成器
                import asyncio
                
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # 运行异步生成器并逐个yield结果
                    async_gen = async_generate()
                    while True:
                        try:
                            event = loop.run_until_complete(async_gen.__anext__())
                            yield event
                        except StopAsyncIteration:
                            break
                finally:
                    loop.close()
                
            else:
                print(f"ERROR: 不支持的模型类型: {prime_model_name}")
                yield f"data: {json.dumps({'type': 'content', 'content': f'错误：不支持的模型类型 {prime_model_name}'})}\n\n"
                yield f"data: {json.dumps({'type': 'end'})}\n\n"
                return
                
        except Exception as e:
            print(f"ERROR: 生成响应时出错: {e}")
            import traceback
            traceback.print_exc()
            yield f"data: {json.dumps({'type': 'content', 'content': f'系统错误: {str(e)}'})}\n\n"
            yield f"data: {json.dumps({'type': 'end'})}\n\n"

    # 设置响应头
    return app.response_class(generate(user_input, model, rag, dataset, include_history, max_history_no, filename_list), mimetype='text/event-stream')

#pdf file viewer  
@app.route('/processed/<filename>')
def Get_file(filename):
    return send_from_directory(app.config['PROCESSED_FOLDER'], filename)
    data = request.get_json()
    list_of_chunks = data['chunk']
    files_pages_data={}

    for chunk in list_of_chunks:
        filename = chunk['source']
        filename = filename.replace('pdf', '.pdf')
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        page_number = chunk['page_num']
        bbox = chunk['bbox']
        content_type = chunk['content_type']
        
        if filename not in files_pages_data:
            files_pages_data[filename]={}
        if page_number not in files_pages_data[filename]:
            files_pages_data[filename][page_number]=[]
        files_pages_data[filename][page_number].append({"bbox":bbox,"content_type":content_type})
        
    print("Extracted Files, Page Numbers, and Bounding Boxes:", files_pages_data)
    return jsonify({'response': "finish prcessing chunk", 'data_extracted': files_pages_data})


@app.route('/draw_file', methods=['GET', 'POST'])
def draw_bbox():
    data = request.get_json()
    pages_data = data['pages_data']
    filename = data['filename']

    new_doc = fitz.open()
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    doc = fitz.open(file_path)

    for page_number, bboxes_data in pages_data.items():
        page_number = int(page_number)
        page = doc[page_number - 1]  # Adjust for zero-based index

        # Separate bboxes by content type
        text_bboxes = []
        image_bboxes = []
        table_bboxes = []

        for bbox_data in bboxes_data:
            content_type = bbox_data['content_type']
            bbox_coords = bbox_data['bbox']
            if content_type == "text":
                text_bboxes.append(fitz.Rect(bbox_coords))
            elif content_type == "image":
                image_bboxes.append(fitz.Rect(bbox_coords))
            elif content_type == "table":
                table_bboxes.append(fitz.Rect(bbox_coords))

        # Function to check if a bbox is inside another
        def is_inside(inner, outer):
            return (inner.x0 >= outer.x0 and inner.y0 >= outer.y0 and
                    inner.x1 <= outer.x1 and inner.y1 <= outer.y1)

        # Draw bounding boxes
        for table_bbox in table_bboxes:
            page.draw_rect(table_bbox, color=(0, 0, 1), fill=None)

        for image_bbox in image_bboxes:
            page.draw_rect(image_bbox, color=(1, 0, 0), fill=None)

        for text_bbox in text_bboxes:
            # Check if the text bbox is inside any table or image bbox
            if not any(is_inside(text_bbox, table_bbox) for table_bbox in table_bboxes) and \
                    not any(is_inside(text_bbox, image_bbox) for image_bbox in image_bboxes):
                page.draw_rect(text_bbox, color=(0, 1, 0), fill=None)

        temp_doc = fitz.open()
        temp_doc.insert_pdf(doc, from_page=page_number - 1, to_page=page_number - 1)
        new_doc.insert_pdf(temp_doc)

    os.makedirs("processed", exist_ok=True)
    processed_file_path = os.path.join("processed", filename)
    new_doc.save(processed_file_path)

    return jsonify({'response': "finish drawing bboxes", 'processed_file_path': processed_file_path})

@app.route('/')
def root():
    return redirect(url_for('chat'))
import psutil

def terminate_ollama_processes():
    for process in psutil.process_iter(['pid', 'name']):
        if process.info['name'] == 'ollama.exe':
            process.terminate()  # 终止找到的ollama进程
            process.wait()  # 等待进程真正终止
def start_ollama_service():
    # 设置模型路径环境变量
    base_path = os.path.dirname(os.path.abspath(__file__))
    model_path = os.path.join(base_path, 'ollama_models')
    os.environ['OLLAMA_MODELS'] = model_path

    # 启动 ollama serve 在新的控制台窗口
    subprocess.Popen(['ollama', 'serve'], creationflags=subprocess.CREATE_NO_WINDOW)


def is_ollama_running():
    """检查 ollama.exe 是否正在运行"""
    for process in psutil.process_iter(['name']):
        if process.info['name'] == 'ollama.exe':
            return True
    return False
def try_start_ollama():
    """尝试在系统上启动 ollama.exe"""
    ollama_path = os.path.join('C:', 'Program Files', 'Ollama', 'ollama.exe')  # 根据实际安装路径调整
    try:
        subprocess.Popen([ollama_path], creationflags=subprocess.CREATE_NO_WINDOW)
        return True
    except Exception as e:
        print(f"Failed to start ollama.exe: {e}")
        return False

# 定义全局可用的安全存储函数
def sanitize_text(text):
    """清理文本确保是有效的UTF-8编码"""
    if text is None:
        return ""
    try:
        # 尝试编码再解码，确保是有效UTF-8
        return text.encode('utf-8', errors='replace').decode('utf-8')
    except Exception as e:
        print(f"文本清理错误: {e}")
        return ""

def sanitize_dataframe(df):
    """清理DataFrame中的所有字符串列"""
    for column in df.columns:
        if df[column].dtype == 'object':  # 对于字符串列
            df[column] = df[column].apply(
                lambda x: sanitize_text(x) if isinstance(x, str) else x
            )
    return df

def save_chat_history_safely(chat_df, file_path, max_retries=3):
    """安全地保存聊天历史，尝试多种方法"""
    for i in range(max_retries):
        try:
            # 根据尝试次数使用不同的保存策略
            if i == 0:
                # 第一次尝试：标准方式
                chat_df.to_json(file_path, orient="index", indent=4)
                return True
            elif i == 1:
                # 第二次尝试：使用force_ascii=True
                chat_df.to_json(file_path, orient="index", indent=4, force_ascii=True)
                return True
            else:
                # 最后尝试：转为字典后使用Python的json模块
                import json
                chat_dict = chat_df.to_dict(orient="index")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(chat_dict, f, ensure_ascii=True, indent=4)
                return True
        except Exception as e:
            print(f"保存聊天历史尝试 {i+1} 失败: {e}")
            
            # 如果是最后一次尝试，更积极地清理数据
            if i == max_retries - 2:  # 最后一次尝试前
                try:
                    # 深度清理DataFrame中的所有数据
                    chat_df = chat_df.copy()  # 创建副本避免修改原始数据
                    for column in chat_df.columns:
                        if chat_df[column].dtype == 'object':
                            chat_df[column] = chat_df[column].apply(
                                lambda x: sanitize_text(x) if isinstance(x, str) else 
                                         (str(x).encode('utf-8', errors='replace').decode('utf-8') 
                                          if x is not None else None)
                            )
                    
                    # 对于chunks_list类型的特殊处理
                    if 'chunks_list' in chat_df.columns:
                        chat_df['chunks_list'] = chat_df['chunks_list'].apply(
                            lambda x: {} if x is not None else None
                        )
                except Exception as clean_err:
                    print(f"清理数据失败: {clean_err}")
    
    # 如果所有尝试都失败，尝试最简单的存储方法
    try:
        # 创建一个只包含必要信息的新DataFrame
        simple_df = pd.DataFrame([{
            "q_msg": "聊天记录保存失败",
            "a_msg": "系统遇到了编码问题，无法保存完整的聊天记录。",
            "q_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "a_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }])
        simple_df.to_json(file_path, orient="index", indent=4, force_ascii=True)
        return False
    except Exception as final_e:
        print(f"最终保存尝试失败: {final_e}")
        return False

if __name__ == '__main__':
    if __name__ == '__main__':
        app.run(host='0.0.0.0', port=8042)