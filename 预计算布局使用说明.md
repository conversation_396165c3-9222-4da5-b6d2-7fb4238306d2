# 预计算布局文件使用说明

## 概述

系统现在支持使用预计算的布局文件来加速文档处理。当您上传PDF文档时，系统会自动检查是否存在对应的JSON或Markdown预处理文件，如果存在，将优先使用这些文件，跳过耗时的PDF解析和OCR过程。

## 文件命名规则

对于上传的文件 `document.pdf`，系统会按以下优先级查找预处理文件：

1. **JSON文件**: `document.json` (最高优先级)
2. **Markdown文件**: `document.md` (备选方案)
3. **原始处理**: 如果以上文件都不存在，使用原始的PDF处理流程

## JSON文件格式

### 格式1: 简单列表格式（推荐）

```json
[
    {
        "chunk_text": "文本块内容",
        "metadata": {
            "source": "document.pdf",
            "start_page_num": 1,
            "end_page_num": 1,
            "start_line_num": 1,
            "end_line_num": 5,
            "content_type": "text",
            "bbox": [x1, y1, x2, y2]
        }
    }
]
```

### 格式2: 页面结构格式

```json
{
    "pages": [
        {
            "page_number": 1,
            "chunks": [
                {
                    "text": "文本内容",
                    "type": "text",
                    "start_line": 1,
                    "end_line": 5,
                    "bbox": [x1, y1, x2, y2]
                }
            ]
        }
    ]
}
```

### 格式3: 文档级别格式

```json
{
    "chunks": [
        {
            "text": "文本内容",
            "page_number": 1,
            "type": "text",
            "start_line": 1,
            "end_line": 5,
            "bbox": [x1, y1, x2, y2]
        }
    ]
}
```

## 支持的内容类型

- `text`: 普通文本
- `heading`: 标题
- `table`: 表格
- `image`: 图像描述
- `chart`: 图表
- `flowchart`: 流程图
- `confusion_matrix`: 混淆矩阵

## 边界框格式

边界框使用 `[x1, y1, x2, y2]` 格式，表示矩形的左上角和右下角坐标。

## Markdown文件格式

如果只有Markdown文件，系统会自动解析：

- 以 `#` 开头的行被识别为标题
- 包含 `|` 符号且数量大于2的段落被识别为表格
- 以 `![` 开头的行被识别为图像

## 使用步骤

1. 使用您的"miner"工具处理PDF文档，生成JSON和/或Markdown文件
2. 确保预处理文件与原PDF文件同名（仅扩展名不同）
3. 将PDF文件和预处理文件放在同一目录下
4. 上传PDF文件，系统会自动检测并使用预处理文件

## 优势

- **高效**: 跳过复杂的PDF解析和OCR处理
- **准确**: 使用专业工具预处理的布局信息
- **兼容**: 完全向后兼容，无预处理文件时自动回退到原始处理方式
- **灵活**: 支持多种JSON格式和Markdown备选方案

## API端点

- `GET /get-precomputed-format-example`: 获取JSON格式示例

## 示例文件

参考 `precomputed_layout_example.json` 和 `precomputed_layout_example.md` 文件了解具体格式。

## 故障排除

如果预处理文件格式不正确，系统会自动回退到原始处理方式并在控制台输出错误信息。请检查：

1. JSON文件格式是否正确
2. 必要的字段是否存在
3. 文件编码是否为UTF-8 