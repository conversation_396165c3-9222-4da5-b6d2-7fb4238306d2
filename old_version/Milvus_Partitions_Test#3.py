import os
# import openai
import pymilvus
#from pymilvus import connections, Collection, DataType, FieldSchema, CollectionSchema, utility, MilvusException

# Replace with your OpenAI API key
# openai.api_key = "***************************************************"

# from langchain.embeddings import HuggingFaceInstructEmbeddings
from sentence_transformers import SentenceTransformer
model = SentenceTransformer('paraphrase-MiniLM-L6-v2')

# Connect to Milvus
client = pymilvus.Milvus()
MILVUS_HOST = "localhost"
MILVUS_PORT = "19530"
milvus_connection = pymilvus.connections.connect("default", host=MILVUS_HOST, port=MILVUS_PORT)


# Get default directory path
default_path = os.path.expanduser("rag_database/")

# Read text file from default directory
try:
    with open(os.path.join(default_path, "Alice.txt"), "r") as file:
        text_data = file.read()
except FileNotFoundError:
    print("Error: File not found in the default directory.")
    exit()

'''
# Split text into chunks
chunk_size = 384  # Adjust as needed, considering model overhead
chunks = [text_data[i : i + chunk_size] for i in range(0, len(text_data),  chunk_size)]

# Generate embeddings for each chunk
all_embeddings = []
for chunk in chunks:
    chunk_embedding = model.encode(chunk)
    all_embeddings.append(chunk_embedding)

print(all_embeddings)
dimensions = [embedding.shape[0] for embedding in all_embeddings]
print(dimensions)


for chunk in chunks:
    try:
        response = openai.Embedding.create(
            input=[chunk], model="text-embedding-ada-002"
        )
        chunk_embeddings = response["data"][0]["embedding"]
        all_embeddings.extend(chunk_embeddings)
    except openai.error.OpenAIError as e:
        print(f"Error generating embeddings for chunk: {e}")
        exit()
'''

# Generate text embeddings using OpenAI
'''        
try:
    response = openai.Embedding.create(
        input=[text_data], model="text-embedding-ada-002"
    ) 
    embeddings = response["data"][0]["embedding"]
except openai.error.OpenAIError as e:
    print(f"Error generating embeddings: {e}")
    exit()
'''

id_field = pymilvus.FieldSchema(name="id", dtype=pymilvus.DataType.INT64, is_primary=True, auto_id=True)
title_field = pymilvus.FieldSchema(name='text', dtype=pymilvus.DataType.VARCHAR, max_length=200)
embedding_field = pymilvus.FieldSchema(name="embedding", dtype=pymilvus.DataType.FLOAT_VECTOR, dim=384)  # Adjust dimension if needed

print(type(id_field))
print(type(embedding_field))

schema = pymilvus.CollectionSchema (
    fields=[
        id_field,
        title_field,
        embedding_field],
    auto_id=True, enable_dynamic_field=False, description='text embeddings'
)


#        id_field = pymilvus.FieldSchema(name="id", dtype=pymilvus.DataType.INT64, is_primary=True, auto_id=True),
#        pymilvus.FieldSchema(name="embedding", dtype=pymilvus.DataType.FLOAT_VECTOR)  # Adjust dimension if needed

#collection = pymilvus.Collection(collection_name, schema=schema)



# Create collection
try:
    collection = pymilvus.Collection(name="Book", schema=schema, using='default')
except pymilvus.exceptions.ParamError as e:
    print(f"Error creating collection: {e}")
    exit()

# Create partition
try:
    partition = collection.create_partition(partition_name="P3")
except pymilvus.exceptions.ParamError as e:
    print(f"Error creating partition: {e}")
    exit()

embeds = [list(embed) for embed in model.encode(texts)]

#print(all_embeddings)
    # Insert embeddings into the partition
try:
    collection.insert([texts, embeds])
    collection.create_index(field_name="embeddings", \
                            index_params={"metric_type": "IP", "index_type": "IVF_FLAT", "params": {"nlist": 16384}})
    # for embedding in all_embeddings:
    #     collection.insert([["abc", embedding]])
#        collection.insert([["embed"], [embedding]])   #, partition_name=partition)
    # collection.insert({"id": 1, "embedding": embedding_data}, partition)
    # collection.insert(embedding_data, partition)
    # collection.insert(partition, {"id": 1, "embedding": [{"embedding": embedding} for embedding in all_embeddings]})
    # partition.insert([{embedding.key: embedding.value} for embedding in all_embeddings])
    #    partition.insert([{"embedding": embedding} for embedding in embeddings])
    print("Embeddings successfully inserted into Milvus collection .")
except pymilvus.exceptions.ParamError as e:
    print(f"Error inserting data: {e}")

collection = Collection('book')
collection.load()
documents = collection.search(data=query_encode, anns_field="embeddings", param={"metric":"IP","offset":0},
output_fields=["text"], limit=1)
