
List All Containers (including stopped ones):
docker ps -a

Stop a Running Container:
docker stop CONTAINER_ID

Start a Stopped Container:
docker start CONTAINER_ID

Remove a Container:
docker rm CONTAINER_ID

Use the -f option to force removal if the container is running
docker rm -f CONTAINER_ID


Listen all ports running:
ss -tulpn

Check IP config:
ifconfig

Removing Existing Container:
docker rm -f <container>

List running containers:
docker ps

Start Milvus:
sudo docker-compose up -d

Check Milvus status:
sudo docker-compose ps

End Milvus
Start Docker:
sudo docker-compose down

Restart docker:
sudo systemctl restart docker.socket docker.service

Running Milvus Attu from Docker:
sudo docker run -p 8000:3000 -e MILVUS_URL={0.0.0.0}:19530 zilliz/attu:v2.3.1

Connect to Attu GUI through Web browser:
http://**********:3000

Milvus IP Address (For Attu Login):
**********:19530


