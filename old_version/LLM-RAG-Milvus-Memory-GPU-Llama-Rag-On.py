import gc

from flask import Flask, render_template, request, jsonify, send_from_directory
from langchain.vectorstores.faiss import FAISS
from transformers import AutoTokenizer
from werkzeug.utils import secure_filename
from langchain.document_loaders import DirectoryLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import SentenceTransformerEmbeddings
from langchain.vectorstores import Chroma
from langchain.chat_models import ChatOpenAI
from langchain.chains.question_answering import load_qa_chain
import openai
import os
from datetime import datetime
from langchain.llms import LlamaCpp
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler

from langchain import PromptTemplate, LLMChain
from langchain.embeddings import HuggingFaceInstructEmbeddings

from huggingface_hub import hf_hub_download

from langchain.chains import RetrievalQA

from langchain.chains.qa_with_sources import load_qa_with_sources_chain
from langchain.llms import OpenAI

from langchain.text_splitter import CharacterTextSplitter

from langchain import OpenAI, VectorDBQA
from langchain.embeddings.openai import OpenAIEmbeddings

from langchain.vectorstores import Milvus
from langchain.document_loaders import TextLoader
from langchain.docstore.document import Document

# Milvus Database
from pymilvus import (
    connections,
    utility,
    FieldSchema,
    CollectionSchema,
    DataType,
    Collection
)

from langchain.schema import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.messages import AIMessage, HumanMessage

from transformers import AutoTokenizer
from langchain import HuggingFacePipeline
import transformers
import torch

import pandas as pd
from pymilvus import Collection

app = Flask(__name__)
# Set openai key in env
openai.api_key = os.environ["OPENAI_API_KEY"] = "***************************************************"

# File upload configuration
# Save all uploaded files in the "uploads/"
app.config['UPLOAD_FOLDER'] = 'uploads/'
# Allow these fomat
app.config['ALLOWED_EXTENSIONS'] = set(['txt', 'json', 'csv', 'pdf', 'docx'])

# Milvus Database Collection name
COLLECTION_NAME = "Book"

# Remove Old Milvus collection
MILVUS_HOST = "localhost"
MILVUS_PORT = "19530"
# milvus_connection = connections.connect("default", host=MILVUS_HOST, port=MILVUS_PORT)

stored_docs = []

# Check file extension
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def load_vs_df(vs_log_dir='log/vsdb_log.json'):
    # Load vs_df
    if os.path.exists(vs_log_dir):
        vs_df = pd.read_json(vs_log_dir, orient='records')

    # Cretae new vs_df
    else:
        vs_df = pd.DataFrame()

    return vs_df

def update_vs_df(new_file, selected_dataset):
    vs_log_dir = 'log/vsdb_log.json'
    vs_df = load_vs_df(vs_log_dir)

    # Update vs_df log json
    if len(vs_df) == 0:
        offset = 0
    else:
        offset = vs_df.iloc[-1]["offset"] + vs_df.iloc[-1]["chunk_size"]

    new_doc_dict = {"user": "test",  # *** to refine as parameter later
                    "dataset": selected_dataset,  # *** to refine as parameter later
                    "file_name": new_file["name"],
                    "chunk_size": new_file["chunk_size"],
                    "offset": offset,
                    "permission_level": 0}
    new_df = pd.DataFrame([new_doc_dict])
    vs_df = pd.concat([vs_df, new_df], ignore_index=True)
    vs_df.to_json(vs_log_dir, orient='records')
    return vs_df

def reset_offset_vs_df(vs_df, vs_log_dir='log/vsdb_log.json'):
    # Update vs_df log json
    vs_df = vs_df.reset_index(drop=True)
    for index, row in vs_df.iterrows():
        print(row["file_name"])
        if index == 0:
            offset = 0
        else:
            offset = vs_df.iloc[index-1]["offset"] + vs_df.iloc[index-1]["chunk_size"]
        vs_df.at[index, "offset"] = offset
    vs_df.to_json(vs_log_dir, orient='records')
    return vs_df

def create_collection(new_doc, embedding, collection_name):

    milvusDb = Milvus.from_documents(
        new_doc,
        embedding=embedding,
        collection_name=collection_name,
        connection_args={"host": MILVUS_HOST, "port": MILVUS_PORT}
    )
    # select_entities("unity3d.pdf")

    # delete_entities("unity3d.pdf")

    # Print query

    return milvusDb

def query_collection(selected_dataset):

    # def get_doc(stored_docs):
    #     file_name = [d["filename"] for d in stored_docs]
    #     file_len  = [d["chunk_size"] for d in stored_docs]
    #
    #     offest = 0
    #     offset += file_len[i]
    #
    #     for i, doc in enumerate(file_name):
    #         if i == 0:
    #             lower_boundary = 0
    #             upper_boundary = doc["chunk_size"]
    #             offset = upper_boundary + 1
    #         else:
    #             lower_boundary = offset
    #             upper_boundary = offset + doc["chunk_size"]
    #             offset = upper_boundary + 1
    #
    #     return lower_boundary, upper_boundary


    from pymilvus import connections
    connections.connect(host=MILVUS_HOST, port=MILVUS_PORT)

    tmpCollection = Collection(selected_dataset)
    tmpCollection.load()

    print("Query collection")
    vs_df = load_vs_df()
    for index, doc in vs_df.iterrows():
        res = tmpCollection.query(
            expr="pk >=0",
            # offset=0,
            # limit=10,
            offset=doc["offset"],
            limit=doc["chunk_size"],
            output_fields=["description"],
        )
        # print(f"Query for File <{doc['file_name']}>: \n {res}")

def build_query_expression(filenamelist):
    if not filenamelist:
        return ""

    like_clauses = [f"description LIKE '{filename}'" for filename in filenamelist]

    expr = " && ".join(like_clauses)
    return expr

def select_entities(filenamelist,collection_name):
    expr = build_query_expression(filenamelist)
    connections.connect(host=MILVUS_HOST, port=MILVUS_PORT)
    collection = Collection(collection_name)
    collection.load()
    res = collection.query(
        expr = expr,
        # offset=0,
        # limit=10,
        output_fields=["vector"]
    )
    print(f"Select for File <{res}>:")
    return res

def delete_entities(description, selected_dataset):
    expr = f"description LIKE '{description}'"
    connections.connect(host=MILVUS_HOST, port=MILVUS_PORT)
    collection = Collection(selected_dataset)
    print(f"Before delete entities = {collection.num_entities}")
    collection.delete(expr)
    print(f"After delete entities = {collection.num_entities}")
    print("Delete entities")

    query_collection(selected_dataset)


def load_collection(embeddings, collection_name):
    milvusDb = Milvus(
        embeddings,
        collection_name=collection_name,
        connection_args={"host": MILVUS_HOST, "port": MILVUS_PORT},
    )



    from pymilvus import connections
    connections.connect(host=MILVUS_HOST, port=MILVUS_PORT)


    # query_collection()
    # select_entities("unity3d.pdf")
    # delete_entities("unity3d.pdf")

    return milvusDb

'''
def create_collection2():
    book_id = FieldSchema(
        name="book_id",
        dtype=DataType.INT64,
        is_primary=True,
    )
    book_intro = FieldSchema(
        name="book_embedding",
        dtype=DataType.FLOAT_VECTOR,
        dim=2

    )

    collection = Collection(COLLECTION_NAME + "2")
    collection.create_collection()
'''

model = "meta-llama/Llama-2-13b-chat-hf"

tokenizer = AutoTokenizer.from_pretrained(model)

pipeline = transformers.pipeline(
    "text-generation",
    model=model,
    tokenizer=tokenizer,
    torch_dtype=torch.bfloat16,
    trust_remote_code=True,
    device_map="auto",
    # max_length=1000,
    eos_token_id=tokenizer.eos_token_id,
    repetition_penalty=1.1
)
chat_history=[]

def chunk_document(file_names_string, document):
    # Split single document into chunks
    text_splitter = CharacterTextSplitter(chunk_size=800, chunk_overlap=20)
    split_docs = text_splitter.split_documents(document)

    new_docs = []
    # split_docs = chunks
    for doc in split_docs:
        met = doc.metadata
        met['title'] = "T"
        met['description'] = file_names_string # alice.text +..
        met['language'] = 'us'
        new_docs.append(Document(page_content=doc.page_content, metadata=met))

    return new_docs

def llama_response(directory, query, hasNewCollection, valid_files):

    # Load vs_df
    vs_df = load_vs_df()

    # print(collection)
    # print(valid_files)
    file_names = [file.filename for file in valid_files]
    print(file_names)
    file_names_string = ', '.join(file_names)

    # callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
    #
    # model_name_or_path = "TheBloke/Llama-2-13B-chat-GGUF"
    # model_basename = "llama-2-13b-chat.Q5_K_M.gguf"
    # model_path = hf_hub_download(repo_id=model_name_or_path, filename=model_basename)
    #
    # llm = LlamaCpp(
    #     model_path=model_path,
    #     n_ctx=6000,
    #     n_gpu_layers=512,
    #     n_batch=30,
    #     callback_manager=callback_manager,
    #     max_tokens=4095,
    #     # max_tokens=256,
    #     n_parts=1,
    # )

    # llm = HuggingFacePipeline(pipeline=pipeline, model_kwargs={'temperature': 0})
    llm = HuggingFacePipeline(pipeline=pipeline)
    # load files from directory
    # loader = DirectoryLoader(directory)
    # documents = loader.load()
    #
    # text_splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=20)
    # docs = text_splitter.split_documents(documents)

    # embedding engine
    docsearch = ""

    # Create and Add new collection
    if hasNewCollection == True:

        # Split document into chunks
        loader = DirectoryLoader(directory)
        document = loader.load()
        # print(document)

        # Split single document into chunks
        new_docs = chunk_document(file_names_string, document)

        # embedding engine
        hf_embedding = HuggingFaceInstructEmbeddings()

        # Create/Add Milvus DB
        docsearch = create_collection(new_docs, hf_embedding)

        new_file = {"name": file_names_string,
                    "chunk_size": len(new_docs)}
        vs_df = update_vs_df(new_file)
        print('All Stored Docs :')
        print(vs_df["file_name"])



    # Load existing collection
    else:
        # embedding engine
        hf_embedding = HuggingFaceInstructEmbeddings()
        docsearch = load_collection(hf_embedding)

    # # save embeddings in local directory
    # db.save_local("uploads")
    #
    # # load from local
    # db = FAISS.load_local("uploads/", embeddings=hf_embedding)

    # use_original_text = request.form.get('useOriginalText') == 'on'
    # print(use_original_text)
    # if use_original_text:
    #     query = query + ", Just give me the original text you found."
    # else:
    #     query = query

    # docsearch = db.similarity_search(query)

    # chain = load_qa_chain(llm, chain_type="stuff", verbose=False)
    #
    # answer = chain.run(input_documents=search, question=query)
    # return answer
    retriever = docsearch.as_retriever(search_type="similarity", search_kwargs={"k": 6})

    # qa = RetrievalQA.from_chain_type(llm=OpenAI(), chain_type="stuff", retriever=docsearch.as_retriever(),
    #                                  return_source_documents=True)

    #   qa = VectorDBQA.from_chain_type(llm=llm, chain_type="stuff", vectorstore=docsearch, return_source_documents=True)

    def format_docs(docs):
        return "\n\n".join(doc.page_content for doc in docs)

    from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder

    condense_q_system_prompt = """Given a chat history and the user question \
     which might reference the chat history, formulate a standalone question \
     which can be understood without the chat history. Do NOT answer the question, \
     just reformulate it if needed and otherwise return it as is."""

    condense_q_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", condense_q_system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{question}"),
        ]
    )
    condense_q_chain = condense_q_prompt | llm | StrOutputParser()

    # condense_q_chain.invoke(
    #     {
    #         "chat_history": [
    #             HumanMessage(content="What does LLM stand for?"),
    #             AIMessage(content="Large language model"),
    #         ],
    #         "question": "What is meant by large",
    #     }
    # )
    #
    # condense_q_chain.invoke(
    #     {
    #         "chat_history": [
    #             HumanMessage(content="What does LLM stand for?"),
    #             AIMessage(content="Large language model"),
    #         ],
    #         "question": "How do transformers work",
    #     }
    # )

    qa_system_prompt = """You are an assistant for question-answering tasks. \
     Use the following pieces of retrieved context to answer the question. \
     If you don't know the answer, just say that you don't know. \
     Use three sentences maximum and keep the answer concise.\

     {context}"""
    qa_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", qa_system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{question}"),
        ]
    )

    def condense_question(input: dict):
        print("Input content:", input)
        if input.get("chat_history"):
            return condense_q_chain
        else:
            return input["question"]

    rag_chain = (
            RunnablePassthrough.assign(context=condense_question | retriever | format_docs)
            | qa_prompt
            | llm
    )
    question = query
    # Invoke the RAG chain and get the AI message
    ai_msg = rag_chain.invoke({"question": question, "chat_history": chat_history})
    # print('ai_msg: ', ai_msg.content)
    # Extract the content from the AIMessage object
    # ai_msg_content = ai_msg.content

    # Append the human message and the AI's response content to the chat history
    chat_history.extend([HumanMessage(content=question), AIMessage(content=ai_msg)])

    # Return only the content of the AI's response
    return ai_msg

# chat_history = []
# def rag_mode(directory, query, hasNewCollection):
#     # Remove Old Milvus collection
#     MILVUS_HOST = "localhost"
#     MILVUS_PORT = "19530"
#     milvus_connection = connections.connect("default", host=MILVUS_HOST, port=MILVUS_PORT)
#
#     docsearch = ""
#
#     # Creating new Vector DB
#     if hasNewCollection == True:
#         loader = DirectoryLoader(directory)
#         documents = loader.load()
#         for filename in documents:
#             print("filename: " + os.path.join(loader.path, filename))
#
#         text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=20)
#         split_docs = text_splitter.split_documents(documents)
#
#         new_doc = []
#         for doc in split_docs:
#             met = doc.metadata
#             met['title'] = "L"
#             met['description'] = "L"
#             met['language'] = 'us'
#             new_doc.append(Document(page_content=doc.page_content, metadata=met))
#         #    	continue
#
#         callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
#         embeddings = OpenAIEmbeddings()
#
#         # Create New Milvus collection & Store new documents into the collection
#         docsearch = create_collection(new_doc, embeddings)
#
#     else:
#         embeddings = OpenAIEmbeddings()
#         docsearch = load_collection(embeddings)
#         # loader = DirectoryLoader(directory)
#         #
#         # documents = loader.load()
#         #
#         # text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=20)
#         # split_docs = text_splitter.split_documents(documents)
#         #
#         # new_doc = []
#         # for doc in split_docs:
#         #     met = doc.metadata
#         #     met['title'] = "L"
#         #     met['description'] = "L"
#         #     met['language'] = 'us'
#         #     new_doc.append(Document(page_content=doc.page_content, metadata=met))
#         # #    	continue
#         #
#         # # Loading Vector DB
#         #
#         # collection = Collection(COLLECTION_NAME)  # Get an existing collection.
#         #
#         # callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
#         # embeddings = OpenAIEmbeddings()
#         #
#         # docsearch = load_collection(new_doc, embeddings)
#
#     # Create New Milvus collection & Store new documents into the collection
#     #    docsearch = Milvus.from_documents(
#     #	new_doc,
#     #	embedding=embeddings,
#     #	collection_name=COLLECTION_NAME,
#     #	connection_args={"host": MILVUS_HOST, "port": MILVUS_PORT}
#     #    )
#
#     # Create an Index for the new Milvus collection
#     #    index_param = {
#     #        'index_type': "IVF_FLAT",
#     #        'params': {'nlist': 16384},  # Adjust 'nlist' based on your dataset size and characteristics
#     #        'metric_type': "L2"  # Choose the appropriate metric type for your vectors
#     #    }
#
#     #    collection = Collection(COLLECTION_NAME)
#     #    collection.create_index(
#     #    	field_name="text",
#     #  	index_params=index_param)
#
#     #    print(f"Index created for collection: {collection_name}")
#
#     #    utility.index_building_progress(COLLECTION_NAME)
#
#     #    docsearch = Chroma.from_documents(split_docs, embeddings)
#     llm = ChatOpenAI(model_name="gpt-3.5-turbo", callback_manager=callback_manager)
#
#     retriever = docsearch.as_retriever(search_type="similarity", search_kwargs={"k": 6})
#
#     # qa = RetrievalQA.from_chain_type(llm=OpenAI(), chain_type="stuff", retriever=docsearch.as_retriever(),
#     #                                  return_source_documents=True)
#
#     #   qa = VectorDBQA.from_chain_type(llm=llm, chain_type="stuff", vectorstore=docsearch, return_source_documents=True)
#
#     def format_docs(docs):
#         return "\n\n".join(doc.page_content for doc in docs)
#
#     from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
#
#     condense_q_system_prompt = """Given a chat history and the latest user question \
#     which might reference the chat history, formulate a standalone question \
#     which can be understood without the chat history. Do NOT answer the question, \
#     just reformulate it if needed and otherwise return it as is."""
#     condense_q_prompt = ChatPromptTemplate.from_messages(
#         [
#             ("system", condense_q_system_prompt),
#             MessagesPlaceholder(variable_name="chat_history"),
#             ("human", "{question}"),
#         ]
#     )
#     condense_q_chain = condense_q_prompt | llm | StrOutputParser()
#
#     condense_q_chain.invoke(
#         {
#             "chat_history": [
#                 HumanMessage(content="What does LLM stand for?"),
#                 AIMessage(content="Large language model"),
#             ],
#             "question": "What is meant by large",
#         }
#     )
#
#     condense_q_chain.invoke(
#         {
#             "chat_history": [
#                 HumanMessage(content="What does LLM stand for?"),
#                 AIMessage(content="Large language model"),
#             ],
#             "question": "How do transformers work",
#         }
#     )
#
#     qa_system_prompt = """You are an assistant for question-answering tasks. \
#     Use the following pieces of retrieved context to answer the question. \
#     If you don't know the answer, just say that you don't know. \
#     Use three sentences maximum and keep the answer concise.\
#
#     {context}"""
#     qa_prompt = ChatPromptTemplate.from_messages(
#         [
#             ("system", qa_system_prompt),
#             MessagesPlaceholder(variable_name="chat_history"),
#             ("human", "{question}"),
#         ]
#     )
#
#     def condense_question(input: dict):
#         if input.get("chat_history"):
#             return condense_q_chain
#         else:
#             return input["question"]
#
#     rag_chain = (
#             RunnablePassthrough.assign(context=condense_question | retriever | format_docs)
#             | qa_prompt
#             | llm
#     )
#
#     question = query
#     # Invoke the RAG chain and get the AI message
#     ai_msg = rag_chain.invoke({"question": question, "chat_history": chat_history})
#
#     # Extract the content from the AIMessage object
#     ai_msg_content = ai_msg.content
#
#     # Append the human message and the AI's response content to the chat history
#     chat_history.extend([HumanMessage(content=question), ai_msg])
#
#     # Return only the content of the AI's response
#     return ai_msg_content

chat_history = []
def new_gpt_response(query, selected_dataset):
    print("New GPT response:", selected_dataset)
    callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
    llm = ChatOpenAI(model_name="gpt-3.5-turbo", callback_manager=callback_manager)

    hf_embedding = HuggingFaceInstructEmbeddings()
    docsearch = load_collection(hf_embedding, selected_dataset)
    retriever = docsearch.as_retriever(search_type="similarity", search_kwargs={"k": 6})

    # qa = RetrievalQA.from_chain_type(llm=OpenAI(), chain_type="stuff", retriever=docsearch.as_retriever(),
    #                                  return_source_documents=True)

    #   qa = VectorDBQA.from_chain_type(llm=llm, chain_type="stuff", vectorstore=docsearch, return_source_documents=True)

    def format_docs(docs):
        return "\n\n".join(doc.page_content for doc in docs)

    from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder

    condense_q_system_prompt = """Given a chat history and the latest user question \
    which might reference the chat history, formulate a standalone question \
    which can be understood without the chat history. Do NOT answer the question, \
    just reformulate it if needed and otherwise return it as is."""
    condense_q_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", condense_q_system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{question}"),
        ]
    )
    condense_q_chain = condense_q_prompt | llm | StrOutputParser()

    condense_q_chain.invoke(
        {
            "chat_history": [
                HumanMessage(content="What does LLM stand for?"),
                AIMessage(content="Large language model"),
            ],
            "question": "What is meant by large",
        }
    )

    condense_q_chain.invoke(
        {
            "chat_history": [
                HumanMessage(content="What does LLM stand for?"),
                AIMessage(content="Large language model"),
            ],
            "question": "How do transformers work",
        }
    )

    qa_system_prompt = """You are an assistant for question-answering tasks. \
    Use the following pieces of retrieved context to answer the question. \
    If you don't know the answer, just say that you don't know. \
    Use three sentences maximum and keep the answer concise.\

    {context}"""
    qa_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", qa_system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{question}"),
        ]
    )

    def condense_question(input: dict):
        if input.get("chat_history"):
            return condense_q_chain
        else:
            return input["question"]

    rag_chain = (
            RunnablePassthrough.assign(context=condense_question | retriever | format_docs)
            | qa_prompt
            | llm
    )

    question = query
    # Invoke the RAG chain and get the AI message
    ai_msg = rag_chain.invoke({"question": question, "chat_history": chat_history})

    # Extract the content from the AIMessage object
    ai_msg_content = ai_msg.content

    # Append the human message and the AI's response content to the chat history
    chat_history.extend([HumanMessage(content=question), ai_msg])

    # Return only the content of the AI's response
    return ai_msg_content



# def new_llama_response(query, query_filename_list, selected_dataset):
def new_llama_response(query, selected_dataset):
    # print("new_llama_response", query_filename_list)
    print("new_llama_response", selected_dataset)
    # Load vs_df
    vs_df = load_vs_df()

    # print(collection)
    # print(valid_files)
    # file_names = [file.filename for file in valid_files]
    # print(file_names)
    # file_names_string = ', '.join(file_names)

    # callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
    #
    # model_name_or_path = "TheBloke/Llama-2-13B-chat-GGUF"
    # model_basename = "llama-2-13b-chat.Q5_K_M.gguf"
    # model_path = hf_hub_download(repo_id=model_name_or_path, filename=model_basename)
    #
    # llm = LlamaCpp(
    #     model_path=model_path,
    #     n_ctx=6000,
    #     n_gpu_layers=512,
    #     n_batch=30,
    #     callback_manager=callback_manager,
    #     max_tokens=4095,
    #     # max_tokens=256,
    #     n_parts=1,
    # )

    # llm = HuggingFacePipeline(pipeline=pipeline, model_kwargs={'temperature': 0})
    llm = HuggingFacePipeline(pipeline=pipeline)
    # load files from directory
    # loader = DirectoryLoader(directory)
    # documents = loader.load()
    #
    # text_splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=20)
    # docs = text_splitter.split_documents(documents)

    # embedding engine
    docsearch = ""

    # Create and Add new collection
    # if hasNewCollection == True:
    #
    #     # Split document into chunks
    #     loader = DirectoryLoader(directory)
    #     document = loader.load()
    #     # print(document)
    #
    #     # Split single document into chunks
    #     new_docs = chunk_document(file_names_string, document)
    #
    #     # embedding engine
    #     hf_embedding = HuggingFaceInstructEmbeddings()
    #
    #     # Create/Add Milvus DB
    #     docsearch = create_collection(new_docs, hf_embedding)
    #
    #     new_file = {"name": file_names_string,
    #                 "chunk_size": len(new_docs)}
    #     vs_df = update_vs_df(new_file)
    #     print('All Stored Docs :')
    #     print(vs_df["file_name"])
    #
    #
    #
    # # Load existing collection
    # else:
    #     # embedding engine
    hf_embedding = HuggingFaceInstructEmbeddings()
    docsearch = load_collection(hf_embedding, selected_dataset)
    print('docsearch', docsearch)
    # docsearch = select_entities(query_filename_list)
    # # save embeddings in local directory
    # db.save_local("uploads")
    #
    # # load from local
    # db = FAISS.load_local("uploads/", embeddings=hf_embedding)

    # use_original_text = request.form.get('useOriginalText') == 'on'
    # print(use_original_text)
    # if use_original_text:
    #     query = query + ", Just give me the original text you found."
    # else:
    #     query = query

    # docsearch = db.similarity_search(query)

    # chain = load_qa_chain(llm, chain_type="stuff", verbose=False)
    #
    # answer = chain.run(input_documents=search, question=query)
    # return answer
    retriever = docsearch.as_retriever(search_type="similarity", search_kwargs={"k": 6})
    print('retriever', retriever)

    # qa = RetrievalQA.from_chain_type(llm=OpenAI(), chain_type="stuff", retriever=docsearch.as_retriever(),
    #                                  return_source_documents=True)

    #   qa = VectorDBQA.from_chain_type(llm=llm, chain_type="stuff", vectorstore=docsearch, return_source_documents=True)

    def format_docs(docs):
        return "\n\n".join(doc.page_content for doc in docs)

    from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder

    condense_q_system_prompt = """Given a chat history and the user question \
     which might reference the chat history, formulate a standalone question \
     which can be understood without the chat history. Do NOT answer the question, \
     just reformulate it if needed and otherwise return it as is."""

    condense_q_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", condense_q_system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{question}"),
        ]
    )
    condense_q_chain = condense_q_prompt | llm | StrOutputParser()

    # condense_q_chain.invoke(
    #     {
    #         "chat_history": [
    #             HumanMessage(content="What does LLM stand for?"),
    #             AIMessage(content="Large language model"),
    #         ],
    #         "question": "What is meant by large",
    #     }
    # )
    #
    # condense_q_chain.invoke(
    #     {
    #         "chat_history": [
    #             HumanMessage(content="What does LLM stand for?"),
    #             AIMessage(content="Large language model"),
    #         ],
    #         "question": "How do transformers work",
    #     }
    # )

    qa_system_prompt = """You are an assistant for question-answering tasks. \
     Use the following pieces of retrieved context to answer the question. \
     If you don't know the answer, just say that you don't know. \
     Use three sentences maximum and keep the answer concise.\

     {context}"""
    qa_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", qa_system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{question}"),
        ]
    )

    def condense_question(input: dict):
        print("Input content:", input)
        if input.get("chat_history"):
            return condense_q_chain
        else:
            return input["question"]

    rag_chain = (
            RunnablePassthrough.assign(context=condense_question | retriever | format_docs)
            | qa_prompt
            | llm
    )
    question = query
    # Invoke the RAG chain and get the AI message
    ai_msg = rag_chain.invoke({"question": question, "chat_history": chat_history})
    # print('ai_msg: ', ai_msg.content)
    # Extract the content from the AIMessage object
    # ai_msg_content = ai_msg.content

    # Append the human message and the AI's response content to the chat history
    chat_history.extend([HumanMessage(content=question), AIMessage(content=ai_msg)])

    # Return only the content of the AI's response
    return ai_msg

chat_history = []
def rag_mode(directory, query, hasNewCollection):
    # Remove Old Milvus collection
    MILVUS_HOST = "localhost"
    MILVUS_PORT = "19530"
    milvus_connection = connections.connect("default", host=MILVUS_HOST, port=MILVUS_PORT)

    docsearch = ""

    # Creating new Vector DB
    if hasNewCollection == True:
        loader = DirectoryLoader(directory)
        documents = loader.load()
        for filename in documents:
            print("filename: " + os.path.join(loader.path, filename))

        text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=20)
        split_docs = text_splitter.split_documents(documents)

        new_doc = []
        for doc in split_docs:
            met = doc.metadata
            met['title'] = "L"
            met['description'] = "L"
            met['language'] = 'us'
            new_doc.append(Document(page_content=doc.page_content, metadata=met))
        #    	continue

        callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
        embeddings = OpenAIEmbeddings()

        # Create New Milvus collection & Store new documents into the collection
        docsearch = create_collection(new_doc, embeddings)

    else:
        embeddings = OpenAIEmbeddings()
        docsearch = load_collection(embeddings)
        # loader = DirectoryLoader(directory)
        #
        # documents = loader.load()
        #
        # text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=20)
        # split_docs = text_splitter.split_documents(documents)
        #
        # new_doc = []
        # for doc in split_docs:
        #     met = doc.metadata
        #     met['title'] = "L"
        #     met['description'] = "L"
        #     met['language'] = 'us'
        #     new_doc.append(Document(page_content=doc.page_content, metadata=met))
        # #    	continue
        #
        # # Loading Vector DB
        #
        # collection = Collection(COLLECTION_NAME)  # Get an existing collection.
        #
        # callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
        # embeddings = OpenAIEmbeddings()
        #
        # docsearch = load_collection(new_doc, embeddings)

    # Create New Milvus collection & Store new documents into the collection
    #    docsearch = Milvus.from_documents(
    #	new_doc,
    #	embedding=embeddings,
    #	collection_name=COLLECTION_NAME,
    #	connection_args={"host": MILVUS_HOST, "port": MILVUS_PORT}
    #    )

    # Create an Index for the new Milvus collection
    #    index_param = {
    #        'index_type': "IVF_FLAT",
    #        'params': {'nlist': 16384},  # Adjust 'nlist' based on your dataset size and characteristics
    #        'metric_type': "L2"  # Choose the appropriate metric type for your vectors
    #    }

    #    collection = Collection(COLLECTION_NAME)
    #    collection.create_index(
    #    	field_name="text",
    #  	index_params=index_param)

    #    print(f"Index created for collection: {collection_name}")

    #    utility.index_building_progress(COLLECTION_NAME)

    #    docsearch = Chroma.from_documents(split_docs, embeddings)
    llm = ChatOpenAI(model_name="gpt-3.5-turbo", callback_manager=callback_manager)

    retriever = docsearch.as_retriever(search_type="similarity", search_kwargs={"k": 6})

    # qa = RetrievalQA.from_chain_type(llm=OpenAI(), chain_type="stuff", retriever=docsearch.as_retriever(),
    #                                  return_source_documents=True)

    #   qa = VectorDBQA.from_chain_type(llm=llm, chain_type="stuff", vectorstore=docsearch, return_source_documents=True)

    def format_docs(docs):
        return "\n\n".join(doc.page_content for doc in docs)

    from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder

    condense_q_system_prompt = """Given a chat history and the latest user question \
    which might reference the chat history, formulate a standalone question \
    which can be understood without the chat history. Do NOT answer the question, \
    just reformulate it if needed and otherwise return it as is."""
    condense_q_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", condense_q_system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{question}"),
        ]
    )
    condense_q_chain = condense_q_prompt | llm | StrOutputParser()

    condense_q_chain.invoke(
        {
            "chat_history": [
                HumanMessage(content="What does LLM stand for?"),
                AIMessage(content="Large language model"),
            ],
            "question": "What is meant by large",
        }
    )

    condense_q_chain.invoke(
        {
            "chat_history": [
                HumanMessage(content="What does LLM stand for?"),
                AIMessage(content="Large language model"),
            ],
            "question": "How do transformers work",
        }
    )

    qa_system_prompt = """You are an assistant for question-answering tasks. \
    Use the following pieces of retrieved context to answer the question. \
    If you don't know the answer, just say that you don't know. \
    Use three sentences maximum and keep the answer concise.\

    {context}"""
    qa_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", qa_system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{question}"),
        ]
    )

    def condense_question(input: dict):
        if input.get("chat_history"):
            return condense_q_chain
        else:
            return input["question"]

    rag_chain = (
            RunnablePassthrough.assign(context=condense_question | retriever | format_docs)
            | qa_prompt
            | llm
    )

    question = query
    # Invoke the RAG chain and get the AI message
    ai_msg = rag_chain.invoke({"question": question, "chat_history": chat_history})

    # Extract the content from the AIMessage object
    ai_msg_content = ai_msg.content

    # Append the human message and the AI's response content to the chat history
    chat_history.extend([HumanMessage(content=question), ai_msg])

    # Return only the content of the AI's response
    return ai_msg_content


# Chatbot function
def gpt3chatbot(message):
    messages = [{"role": "system", "content": "You are a helpful assistant."}]
    messages.append({"role": "user", "content": message})
    response = openai.ChatCompletion.create(model="gpt-3.5-turbo", messages=messages)
    chat_message = response['choices'][0]['message']['content']
    messages.append({"role": "assistant", "content": chat_message})
    return chat_message


def llamachatbot(message):
    model_name_or_path = "TheBloke/Llama-2-13B-chat-GGUF"
    model_basename = "llama-2-13b-chat.Q5_K_M.gguf"
    model_path = hf_hub_download(repo_id=model_name_or_path, filename=model_basename)
    callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])

    # load the large language model file
    # LLM = Llama(model_path="llama-2-7b.ggmlv3.q2_K.bin")
    # LLM = Llama(model_path=model_path)
    LLM = LlamaCpp(
        model_path=model_path,
        n_ctx=6000,
        n_gpu_layers=512,
        n_batch=30,
        callback_manager=callback_manager,
        max_tokens=4095,
        n_parts=1,
    )
    # create a text prompt
    prompt = message
    print("Starting to generate llama2-rag-off response...")
    # generate a response (takes several seconds)
    output = LLM(prompt)
    print("Response generated.")
    return output

@app.route('/get-files', methods=['GET'])
def get_files():
    # Load and return the list of files from 'files.json'
    with open('files.json', 'r') as file:
        files = json.load(file)
    unique_files = {f['file_name'] for f in files}  # Extract unique filenames
    return jsonify(list(unique_files))

@app.route('/delete-file', methods=['POST'])
def delete_file():
    file_to_delete = request.form.get('file_name')
    # Add logic to delete the file from  server
    # os.remove(os.path.join(UPLOAD_FOLDER, file_to_delete))
    print(f"File to be deleted: {file_to_delete}")
    delete_entities(file_to_delete, selected_dataset)

    vs_df = load_vs_df()
    new_vs_df = vs_df.loc[vs_df['file_name'] != file_to_delete]
    vs_df = reset_offset_vs_df(new_vs_df)
    print("Deleted File >>> The remaining files in json are:")
    print(vs_df["file_name"])

    return jsonify({'status': 'success', 'message': 'File deleted successfully'})

selected_dataset = None

@app.route('/selected-dataset', methods=['POST'])
def handle_selected_dataset():
    global selected_dataset
    data = request.get_json()
    selected_dataset = data['selectedDataset']
    print('selected_dataset: ', selected_dataset)
    return 'Dataset selection received'

@app.route('/upload', methods=['POST'])
def upload_file():
    selected_dataset = 'dataset1'
    print('upload in the selected_dataset: ', selected_dataset)
    print("Uploading file...")
    if 'file' not in request.files:
        return redirect(request.url)
    file = request.files['file']
    if file.filename == '':
        return redirect(request.url)

    if file:
        filename = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
        file.save(filename)

        # Split document into chunks
        loader = DirectoryLoader(app.config['UPLOAD_FOLDER'])
        document = loader.load()

        if document:
            # print(document)
            pass
        else:
            print("Cannot find document")

        # Split single document into chunks
        new_docs = chunk_document(file.filename, document)

        # embedding engine
        hf_embedding = HuggingFaceInstructEmbeddings()

        # Create/Add Milvus DB
        docsearch = create_collection(new_docs, hf_embedding, selected_dataset)

        new_file = {"user": "test", # *** to refine as parameter later
                    "dataset": "User-A_Dataset1", # *** to refine as parameter later
                    "name": file.filename,
                    "chunk_size": len(new_docs),
                    "permission_level": 0}
        vs_df = update_vs_df(new_file, selected_dataset)

        os.remove(filename)
        # print('Uploaded New Docs >>> All Stored Docs :')
        # print(vs_df["file_name"])
        #
        # Add logic to update  'files.json'
        return jsonify({'status': 'success', 'message': 'File uploaded successfully'})

global_file_list = []
@app.route('/selected-files', methods=['POST'])
def handle_selected_files():
    global global_file_list
    global_file_list.clear()  # clear global_file_list

    data = request.get_json()
    global_file_list = data['selectedFiles']  # new files in the global_file_list

    print(global_file_list)

    return jsonify({'status': 'success', 'message': 'Selected files received'})

import json
@app.route('/get-datasets', methods=['GET'])
def get_datasets():
    with open('log/vsdb_log.json', 'r') as file:
        data = json.load(file)
    print("get_datasets", data)
    return jsonify(data)
@app.route('/log/<filename>')
def log_file(filename):
    return send_from_directory('log', filename)

@app.route('/', methods=['GET'])
def index():
    return render_template('index_json_database_new_ui_3.html')


# @app.route('/chat', methods=['POST'])
# def chat():
#     user_input = request.form.get('user_input', '')
#     mode = request.form.get('mode', 'online')
#     model = request.form.get('model', 'gpt-3.5')
#     rag = request.form.get('rag', 'off')
#     use_entire_uploads = 'useEntireUploads' in request.form
#     bot_response = ""  # Initialize bot_response to an empty string or a default value
#
#     if mode == 'offline' and model == 'llama' and rag == 'on':
#         files = request.files.getlist('directory')
#         if not files:
#             if (utility.has_collection(COLLECTION_NAME)):
#                 print("Test1")
#                 bot_response = llama_response("", user_input, True)
#                 return jsonify({'response': bot_response})
#             else:
#                 return jsonify({'response': "No files uploaded."})
#
#         if use_entire_uploads:
#             directory_path = app.config['UPLOAD_FOLDER']
#             print("Test2")
#             bot_response = llama_response(directory_path, user_input, True)
#             return jsonify({'response': bot_response})
#         else:
#             valid_files = [file for file in files if file and allowed_file(file.filename)]
#             invalid_files = [file.filename for file in files if file and not allowed_file(file.filename)]
#
#             if invalid_files:
#                 return jsonify({'response': f"Invalid file type."})
#
#             if valid_files:
#                 # Create a new directory named with timestamp
#                 current_time = datetime.now().strftime("%Y%m%d%H%M%S")
#                 directory_name = os.path.splitext(files[0].filename)[0] + "_" + current_time
#                 directory_path = os.path.join(app.config['UPLOAD_FOLDER'], secure_filename(directory_name))
#                 if not os.path.exists(directory_path):
#                     os.makedirs(directory_path)
#
#                 # Save uploaded files to new directory
#                 for file in valid_files:
#                     filename = secure_filename(file.filename)
#                     file.save(os.path.join(directory_path, filename))
#
#                 # collection.load(COLLECTION_NAME)
#                 # print(collection.name)
#                 print("Test3")
#                 bot_response = llama_response(directory_path, user_input, True, valid_files)
#                 return jsonify({'response': bot_response})
#
#     elif mode == 'online' and model == 'gpt-3.5' and rag == 'on':
#         files = request.files.getlist('directory')
#         if not files:
#             return jsonify({'response': "No files uploaded."})
#
#         if use_entire_uploads:
#             directory_path = app.config['UPLOAD_FOLDER']
#             bot_response = rag_mode(directory_path, user_input, True)
#             return jsonify({'response': bot_response})
#         else:
#             valid_files = [file for file in files if file and allowed_file(file.filename)]
#             invalid_files = [file.filename for file in files if file and not allowed_file(file.filename)]
#
#             if invalid_files:
#                 return jsonify({'response': f"Invalid file type."})
#
#             if valid_files:
#                 # Create a new directory named with timestamp
#                 current_time = datetime.now().strftime("%Y%m%d%H%M%S")
#                 directory_name = os.path.splitext(files[0].filename)[0] + "_" + current_time
#                 directory_path = os.path.join(app.config['UPLOAD_FOLDER'], secure_filename(directory_name))
#                 if not os.path.exists(directory_path):
#                     os.makedirs(directory_path)
#
#                 # Save uploaded files to new directory
#                 for file in valid_files:
#                     filename = secure_filename(file.filename)
#                     file.save(os.path.join(directory_path, filename))
#                 bot_response = rag_mode(directory_path, user_input, True)
#                 return jsonify({'response': bot_response})
#
#             else:
#
#                 if (utility.has_collection(COLLECTION_NAME)):
#                     bot_response = rag_mode("", user_input, False)
#                 else:
#                     return jsonify({'response': "No valid files uploaded."})
#
#     elif mode == 'online' and model == 'gpt-3.5' and rag == 'off':
#         bot_response = gpt3chatbot(user_input)
#         return jsonify({'response': bot_response})
#
#     elif mode == 'offline' and model == 'llama' and rag == 'off':
#         bot_response = llamachatbot(user_input)
#
#     return jsonify({'response': bot_response})

@app.route('/chat', methods=['POST'])
def chat():
    global global_file_list
    user_input = request.form.get('user_input', '')
    mode = request.form.get('mode', 'online')
    model = request.form.get('model', 'gpt-3.5')
    rag = request.form.get('rag', 'off')
    bot_response = ""  # Initialize bot_response to an empty string or a default value
    # print('user_input', user_input)
    # print('mode', mode)
    # print('model', model)
    # print('rag', rag)

    if mode == 'offline' and model == 'llama' and rag == 'on':
        # bot_response = new_llama_response(user_input, global_file_list, selected_dataset)
        bot_response = new_llama_response(user_input, selected_dataset)
        return jsonify({'response': bot_response})
    elif mode == 'online' and model == 'gpt-3.5' and rag == 'on':
        # bot_response = rag_mode(directory_path, user_input, True)
        bot_response = new_gpt_response(user_input, selected_dataset)
        return jsonify({'response': bot_response})
    elif mode == 'online' and model == 'gpt-3.5' and rag == 'off':
        bot_response = gpt3chatbot(user_input)
        return jsonify({'response': bot_response})
    elif mode == 'offline' and model == 'llama' and rag == 'off':
        bot_response = llamachatbot(user_input)

    return jsonify({'response': bot_response})


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8008)
