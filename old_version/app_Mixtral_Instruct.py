from flask import Flask, render_template, request, jsonify
from langchain.vectorstores.faiss import FAISS
from werkzeug.utils import secure_filename
from langchain.document_loaders import DirectoryLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import SentenceTransformerEmbeddings
from langchain.vectorstores import Chroma
from langchain.chat_models import ChatOpenAI
from langchain.chains.question_answering import load_qa_chain
import openai
import os
from datetime import datetime
from langchain.llms import LlamaCpp
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler

from langchain import PromptTemplate, LLMChain
from langchain.embeddings import HuggingFaceInstructEmbeddings

from huggingface_hub import hf_hub_download

from langchain.chains import RetrievalQA

from langchain.text_splitter import CharacterTextSplitter
from langchain import OpenAI,VectorDBQA
from langchain.embeddings.openai import OpenAIEmbeddings

import transformers
import torch
from transformers import BitsAndBytesConfig
from langchain import HuggingFacePipeline

from langchain.embeddings.huggingface import HuggingFaceEmbeddings
from llama_index import ServiceContext
from llama_index.embeddings import LangchainEmbedding
from llama_index import VectorStoreIndex, SimpleDirectoryReader, ServiceContext
from llama_index.llms import HuggingFaceLLM

from langchain_core.runnables import RunnablePassthrough
from transformers import (
  AutoTokenizer,
  AutoModelForCausalLM,
  BitsAndBytesConfig,
  pipeline,
)

app = Flask(__name__)
# Set openai key in env
openai.api_key = os.environ["OPENAI_API_KEY"] = "***************************************************"



# File upload configuration
# Save all uploaded files in the "uploads/"
app.config['UPLOAD_FOLDER'] = 'uploads/'
# Allow these fomat
app.config['ALLOWED_EXTENSIONS'] = set(['txt', 'json', 'csv', 'pdf', 'docx'])

# Check file extension
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

# def llama_response(directory, query):
#     template = """Question: {question}
#
#     Answer:"""
#
#     prompt = PromptTemplate(template=template, input_variables=["question"])
#     callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
#
#     model_name_or_path = "TheBloke/Llama-2-13B-chat-GGUF"
#     model_basename = "llama-2-13b-chat.Q5_K_M.gguf"
#     model_path = hf_hub_download(repo_id=model_name_or_path, filename=model_basename)
#
#     llm = LlamaCpp(
#         model_path=model_path,
#         n_ctx=6000,
#         n_gpu_layers=512,
#         n_batch=30,
#         callback_manager=callback_manager,
#         # max_tokens=4095,
#         max_tokens=256,
#         n_parts=1,
#     )
#     llm_chain = LLMChain(prompt=prompt, llm=llm)
#
#     # load files from directory
#     loader = DirectoryLoader(directory)
#     documents = loader.load()
#     text_splitter = RecursiveCharacterTextSplitter(chunk_size=2000, chunk_overlap=20)
#     docs = text_splitter.split_documents(documents)
#
#     # embedding engine
#     hf_embedding = HuggingFaceInstructEmbeddings()
#
#     db = FAISS.from_documents(docs, hf_embedding)
#
#     # save embeddings in local directory
#     db.save_local("uploads")
#
#     # load from local
#     db = FAISS.load_local("uploads/", embeddings=hf_embedding)
#
#     # query = "why french government using AI?"
#
#     search = db.similarity_search(query)
#
#     template = '''Context: {context}
#
#     Based on Context provide me answer for following question
#     Question: {question}
#
#     Tell me the information about the fact. The answer should be from context only
#     do not use general knowledge to answer the query'''
#
#     prompt = PromptTemplate(input_variables=["context", "question"], template=template)
#     final_prompt = prompt.format(question=query, context=search)
#     print("Starting to generate llama2-rag-on response...")
#
#     answer = llm_chain.run(final_prompt)
#     # answer = llm_chain.run(input_documents=search, question=query)
#     print("Response generated.")
#
#     return answer

def llama_response(directory, query):
    callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])

    model_name_or_path = "TheBloke/Llama-2-13B-chat-GGUF"
    model_basename = "llama-2-13b-chat.Q5_K_M.gguf"
    model_path = hf_hub_download(repo_id=model_name_or_path, filename=model_basename)

    llm = LlamaCpp(
        model_path=model_path,
        n_ctx=6000,
        n_gpu_layers=512,
        n_batch=30,
        callback_manager=callback_manager,
        max_tokens=4095,
        # max_tokens=256,
        n_parts=1,
    )

    # load files from directory
    loader = DirectoryLoader(directory)
    documents = loader.load()
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=20)
    docs = text_splitter.split_documents(documents)

    # embedding engine
    hf_embedding = HuggingFaceInstructEmbeddings()

    db = FAISS.from_documents(docs, hf_embedding)

    # save embeddings in local directory
    db.save_local("uploads")

    # load from local
    db = FAISS.load_local("uploads/", embeddings=hf_embedding)

    use_original_text = request.form.get('useOriginalText') == 'on'
    print(use_original_text)
    if use_original_text:
        query = query + ", Just give me the original text you found."
    else:
        query = query

    search = db.similarity_search(query)

    chain = load_qa_chain(llm, chain_type="stuff", verbose=False)

    answer = chain.run(input_documents=search, question=query)
    return answer

# def rag_mode(directory, query):
#     # Load files from directory
#     loader = DirectoryLoader(directory)
#     documents = loader.load()
#     text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=20)
#     docs = text_splitter.split_documents(documents)
#     embeddings = SentenceTransformerEmbeddings(model_name="all-MiniLM-L6-v2")
#     db = Chroma.from_documents(docs, embeddings)
#     callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])
#
#     llm = ChatOpenAI(model_name="gpt-3.5-turbo", callback_manager=callback_manager)
#     chain = load_qa_chain(llm, chain_type="stuff", verbose=False)
#
#     use_original_text = request.form.get('useOriginalText') == 'on'
#     print(use_original_text)
#     if use_original_text:
#         query = query + ", Just give me the original text you found."
#     else:
#         query = query
#
#     matching_docs = db.similarity_search(query)
#     answer = chain.run(input_documents=matching_docs, question=query)
#     return answer

def rag_mode(directory, query):
    loader = DirectoryLoader(directory)

    documents = loader.load()

    text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=20)

    split_docs = text_splitter.split_documents(documents)
    callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])

    embeddings = OpenAIEmbeddings()

    docsearch = Chroma.from_documents(split_docs, embeddings)
    llm = ChatOpenAI(model_name="gpt-3.5-turbo", callback_manager=callback_manager)

    qa = VectorDBQA.from_chain_type(llm=llm, chain_type="stuff", vectorstore=docsearch,
                                    return_source_documents=True)
    use_original_text = request.form.get('useOriginalText') == 'on'
    print(use_original_text)
    if use_original_text:
        query = query + ", Just give me the original text you found."
    else:
        query = query
    result = qa({"query": query})
    return result['result']

# Chatbot function
def gpt3chatbot(message):
    messages = [{"role": "system", "content": "You are a helpful assistant."}]
    messages.append({"role": "user", "content": message})
    response = openai.ChatCompletion.create(model="gpt-3.5-turbo", messages=messages)
    chat_message = response['choices'][0]['message']['content']
    messages.append({"role": "assistant", "content": chat_message})
    return chat_message
def llamachatbot(message):
    model_name_or_path = "TheBloke/Llama-2-13B-chat-GGUF"
    model_basename = "llama-2-13b-chat.Q5_K_M.gguf"
    model_path = hf_hub_download(repo_id=model_name_or_path, filename=model_basename)
    callback_manager = CallbackManager([StreamingStdOutCallbackHandler()])

    # load the large language model file
    # LLM = Llama(model_path="llama-2-7b.ggmlv3.q2_K.bin")
    # LLM = Llama(model_path=model_path)
    LLM = LlamaCpp(
        model_path=model_path,
        n_ctx=6000,
        n_gpu_layers=512,
        n_batch=30,
        callback_manager=callback_manager,
        max_tokens=4095,
        n_parts=1,
    )
    # create a text prompt
    prompt = message+". Just give me one answer less than 200 words, do not ask other questions."
    print("Starting to generate llama2-rag-off response...")
    # generate a response (takes several seconds)
    output = LLM(prompt)
    print("Response generated.")
    return output

# The code below is belong to the mixtralRagChatBot
model_id = "mistralai/Mixtral-8x7B-Instruct-v0.1"
# model_id = "mistralai/Mixtral-8x7B-v0.1"

bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16
)
pipeline = transformers.pipeline(
    "text-generation",
    device_map="auto",
    model=model_id,
    model_kwargs={"torch_dtype": torch.float16},
    # max_length=1000,
    max_new_tokens=1000,
    temperature=0.2,
    repetition_penalty=1.1,
    return_full_text=True,
    # eos_token_id=tokenizer.eos_token_id,
    # repetition_penalty=1.1
    # model_kwargs={"torch_dtype": torch.float16, "load_in_8bit": True, "quantization_config": bnb_config},
)

# The code below is belong to the mixtralRagChatBot3
# model_id = "mistralai/Mixtral-8x7B-Instruct-v0.1"
#
# bnb_config = BitsAndBytesConfig(
#     load_in_4bit=True,
#     bnb_4bit_compute_dtype=torch.float16
# )
#
# model = AutoModelForCausalLM.from_pretrained(
#     model_id,
#     quantization_config=bnb_config,
# )
#
# tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True)
# tokenizer.pad_token = tokenizer.eos_token
# tokenizer.padding_side = "right"
#
# text_generation_pipeline = transformers.pipeline(
#     model=model,
#     tokenizer=tokenizer,
#     task="text-generation",
#     temperature=0.2,
#     repetition_penalty=1.1,
#     return_full_text=True,
#     max_new_tokens=300,
# )

def mixtralChatBot(message):
    messages = [{"role": "user", "content": message}]
    prompt = pipeline.tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)

    outputs = pipeline(prompt, max_new_tokens=256, do_sample=True, temperature=0.7, top_k=50, top_p=0.95)
    clean_text = outputs[0]["generated_text"].replace('<s>[INST] who is Alice? [/INST]', '')

    print(outputs[0]["generated_text"])
    return clean_text

def mixtralRagChatBot(directory, query):
    loader = DirectoryLoader(directory)

    documents = loader.load()

    text_splitter = CharacterTextSplitter(chunk_size=800, chunk_overlap=0)

    split_docs = text_splitter.split_documents(documents)

    embeddings = OpenAIEmbeddings()

    docsearch = Chroma.from_documents(split_docs, embeddings)
    llm = HuggingFacePipeline(pipeline=pipeline)

    qa = VectorDBQA.from_chain_type(llm=llm, chain_type="stuff", vectorstore=docsearch,
                                    return_source_documents=True)
    use_original_text = request.form.get('useOriginalText') == 'on'
    print(use_original_text)
    if use_original_text:
        query = query + ", Just give me the original text you found."
    else:
        query = query
    result = qa({"query": query})
    return result['result']

def mixtralRagChatBot2(directory, query):
    # loader = DirectoryLoader(directory)
    #
    # documents = loader.load()
    documents = SimpleDirectoryReader(directory).load_data()

    # text_splitter = CharacterTextSplitter(chunk_size=800, chunk_overlap=20)
    system_prompt = "You are a Q&A assistant. Your goal is to answer questions as accurately as possible based on the instructions and context provided."
    query_wrapper_prompt = "<|USER|>{query_str}<|ASSISTANT|>"

    # split_docs = text_splitter.split_documents(documents)
    llm = HuggingFaceLLM(
        context_window=4096,
        max_new_tokens=256,
        generate_kwargs={"temperature": 0.1, "do_sample": True},
        system_prompt=system_prompt,
        query_wrapper_prompt=query_wrapper_prompt,
        tokenizer_name="mistralai/Mistral-7B-Instruct-v0.1",
        model_name="mistralai/Mistral-7B-Instruct-v0.1",
        device_map="auto",
        tokenizer_kwargs={"max_length": 4096},
        model_kwargs={"torch_dtype": torch.float16}

    )
    embed_model = LangchainEmbedding(
        HuggingFaceEmbeddings(model_name="sentence-transformers/all-mpnet-base-v2")
    )
    service_context = ServiceContext.from_defaults(
        chunk_size=1024,
        llm=llm,
        embed_model=embed_model
    )

    # docsearch = Chroma.from_documents(split_docs, embeddings)
    index = VectorStoreIndex.from_documents(documents, service_context=service_context)
    query_engine = index.as_query_engine()
    response = query_engine.query(query)
    print(response)
    bot_response = {
        'data': response.data
    }
    return bot_response

    # qa = VectorDBQA.from_chain_type(llm=llm, chain_type="stuff", vectorstore=docsearch,
    #                                 return_source_documents=True)
    # use_original_text = request.form.get('useOriginalText') == 'on'
    # print(use_original_text)
    # if use_original_text:
    #     query = query + ", Just give me the original text you found."
    # else:
    #     query = query
    # result = qa({"query": query})
    # return result['result']

def mixtralRagChatBot3(directory, query):
    loader = DirectoryLoader(directory)

    documents = loader.load()

    text_splitter = CharacterTextSplitter(chunk_size=100, chunk_overlap=0)

    split_docs = text_splitter.split_documents(documents)

    # embeddings = OpenAIEmbeddings()

    # Load chunked documents into the FAISS index
    db = FAISS.from_documents(split_docs,
                              HuggingFaceEmbeddings(model_name='sentence-transformers/all-mpnet-base-v2'))

    # Connect query to FAISS index using a retriever
    retriever = db.as_retriever(
        search_type="similarity",
        search_kwargs={'k': 20}
    )

    prompt_template = """
    ### [INST]
    Instruction: Answer the question based on your
    cricket knowledge. Here is context to help:

    {context}

    ### QUESTION:
    {question}

    [/INST]
    """

    mixtral_llm = HuggingFacePipeline(pipeline=text_generation_pipeline)

    # Create prompt from prompt template
    prompt = PromptTemplate(
        input_variables=["context", "question"],
        template=prompt_template,
    )

    # Create llm chain
    llm_chain = LLMChain(llm=mixtral_llm, prompt=prompt)

    use_original_text = request.form.get('useOriginalText') == 'on'
    print(use_original_text)
    if use_original_text:
        query = query + ", Just give me the original text you found."
    else:
        query = query

    rag_chain = (
            {"context": retriever, "question": RunnablePassthrough()}
            | llm_chain
    )

    result = rag_chain.invoke(query)
    print(rag_chain)
    return result

@app.route('/', methods=['GET'])
def index():
    return render_template('old_index_Mixtral.html')

@app.route('/chat', methods=['POST'])
def chat():
    user_input = request.form['user_input']
    mode = request.form['mode']
    model = request.form['model']
    rag = request.form['rag']
    use_entire_uploads = 'useEntireUploads' in request.form
    bot_response = ""  # Initialize bot_response to an empty string or a default value

    if mode == 'offline' and model == 'llama' and rag == 'on':
        files = request.files.getlist('directory')
        if not files:
            return jsonify({'response': "No files uploaded."})

        if use_entire_uploads:
            directory_path = app.config['UPLOAD_FOLDER']
            bot_response = llama_response(directory_path, user_input)
        else:
            valid_files = [file for file in files if file and allowed_file(file.filename)]
            invalid_files = [file.filename for file in files if file and not allowed_file(file.filename)]

            if invalid_files:
                return jsonify({'response': f"Invalid file type."})

            if valid_files:
                # Create a new directory named with timestamp
                current_time = datetime.now().strftime("%Y%m%d%H%M%S")
                directory_name = os.path.splitext(files[0].filename)[0] + "_" + current_time
                directory_path = os.path.join(app.config['UPLOAD_FOLDER'], secure_filename(directory_name))
                if not os.path.exists(directory_path):
                    os.makedirs(directory_path)

                # Save uploaded files to new directory
                for file in valid_files:
                    filename = secure_filename(file.filename)
                    file.save(os.path.join(directory_path, filename))

                bot_response = llama_response(directory_path, user_input)

    elif mode == 'online' and model == 'gpt-3.5' and rag == 'on':
        files = request.files.getlist('directory')
        if not files:
            return jsonify({'response': "No files uploaded."})

        if use_entire_uploads:
            directory_path = app.config['UPLOAD_FOLDER']
            bot_response = rag_mode(directory_path, user_input)
        else:
            valid_files = [file for file in files if file and allowed_file(file.filename)]
            invalid_files = [file.filename for file in files if file and not allowed_file(file.filename)]

            if invalid_files:
                return jsonify({'response': f"Invalid file type."})

            if valid_files:
                # Create a new directory named with timestamp
                current_time = datetime.now().strftime("%Y%m%d%H%M%S")
                directory_name = os.path.splitext(files[0].filename)[0] + "_" + current_time
                directory_path = os.path.join(app.config['UPLOAD_FOLDER'], secure_filename(directory_name))
                if not os.path.exists(directory_path):
                    os.makedirs(directory_path)

                # Save uploaded files to new directory
                for file in valid_files:
                    filename = secure_filename(file.filename)
                    file.save(os.path.join(directory_path, filename))
                bot_response = rag_mode(directory_path, user_input)

            else:
                return jsonify({'response': "No valid files uploaded."})
    elif mode == 'offline' and model == 'mixtral' and rag == 'on':
        files = request.files.getlist('directory')
        if not files:
            return jsonify({'response': "No files uploaded."})

        if use_entire_uploads:
            directory_path = app.config['UPLOAD_FOLDER']
            bot_response = mixtralRagChatBot(directory_path, user_input)
        else:
            valid_files = [file for file in files if file and allowed_file(file.filename)]
            invalid_files = [file.filename for file in files if file and not allowed_file(file.filename)]

            if invalid_files:
                return jsonify({'response': f"Invalid file type."})

            if valid_files:
                # Create a new directory named with timestamp
                current_time = datetime.now().strftime("%Y%m%d%H%M%S")
                directory_name = os.path.splitext(files[0].filename)[0] + "_" + current_time
                directory_path = os.path.join(app.config['UPLOAD_FOLDER'], secure_filename(directory_name))
                if not os.path.exists(directory_path):
                    os.makedirs(directory_path)

                # Save uploaded files to new directory
                for file in valid_files:
                    filename = secure_filename(file.filename)
                    file.save(os.path.join(directory_path, filename))
                bot_response = mixtralRagChatBot(directory_path, user_input)
            else:
                return jsonify({'response': "No valid files uploaded."})
    elif mode == 'online' and model == 'gpt-3.5' and rag == 'off':
        bot_response = gpt3chatbot(user_input)
    elif mode == 'offline' and model == 'llama' and rag == 'off':
        bot_response = llamachatbot(user_input)
    elif mode == 'offline' and model == 'mixtral' and rag == 'off':
        bot_response = mixtralChatBot(user_input)


    return jsonify({'response': bot_response})


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8009)
