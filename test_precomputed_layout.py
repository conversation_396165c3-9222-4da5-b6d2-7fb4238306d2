#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预计算布局处理功能测试脚本
用于验证修复后的代码是否能正确处理JSON文件
"""

import json
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_json_file(json_path):
    """测试JSON文件是否能被正确处理"""
    print(f"测试文件: {json_path}")
    print("=" * 50)
    
    if not os.path.exists(json_path):
        print(f"❌ 文件不存在: {json_path}")
        return False
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ JSON文件读取成功")
        print(f"📊 数据类型: {type(data).__name__}")
        
        if isinstance(data, list):
            print(f"📝 格式: 列表格式 (Format 1)")
            print(f"📈 项目数量: {len(data)}")
            
            valid_count = 0
            for i, item in enumerate(data):
                if isinstance(item, dict) and 'chunk_text' in item and 'metadata' in item:
                    chunk_text = item['chunk_text']
                    if chunk_text and chunk_text.strip():
                        valid_count += 1
                        print(f"✅ 项目 {i+1}: 有效 (文本长度: {len(chunk_text)})")
                    else:
                        print(f"❌ 项目 {i+1}: 文本为空")
                else:
                    print(f"❌ 项目 {i+1}: 缺少必要字段")
            
            print(f"📊 有效项目: {valid_count}/{len(data)}")
            return valid_count > 0
            
        elif isinstance(data, dict):
            if 'pages' in data:
                print(f"📝 格式: 页面结构格式 (Format 2)")
                print(f"📈 页面数量: {len(data['pages'])}")
            elif 'chunks' in data:
                print(f"📝 格式: 文档块格式 (Format 3)")
                print(f"📈 块数量: {len(data['chunks'])}")
            else:
                print(f"❌ 未知字典格式，可用键: {list(data.keys())}")
                return False
            return True
        else:
            print(f"❌ 不支持的数据格式: {type(data).__name__}")
            return False
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理错误: {e}")
        return False

def create_test_json_files():
    """创建测试用的JSON文件"""
    
    # 测试文件1: 正确的格式
    test_data_1 = [
        {
            "chunk_text": "这是一个测试文档的标题部分。",
            "metadata": {
                "source": "test_document.pdf",
                "start_page_num": 1,
                "end_page_num": 1,
                "start_line_num": 1,
                "end_line_num": 1,
                "content_type": "heading",
                "bbox": [10, 10, 200, 50]
            }
        },
        {
            "chunk_text": "这是正文内容，包含了详细的信息。",
            "metadata": {
                "source": "test_document.pdf",
                "start_page_num": 1,
                "end_page_num": 1,
                "start_line_num": 2,
                "end_line_num": 3,
                "content_type": "text",
                "bbox": [10, 60, 200, 120]
            }
        }
    ]
    
    # 测试文件2: 空数据
    test_data_2 = []
    
    # 测试文件3: 包含空文本的数据
    test_data_3 = [
        {
            "chunk_text": "",
            "metadata": {
                "source": "test_document.pdf",
                "start_page_num": 1,
                "end_page_num": 1,
                "start_line_num": 1,
                "end_line_num": 1,
                "content_type": "text",
                "bbox": [10, 10, 200, 50]
            }
        },
        {
            "chunk_text": "这是有效的文本内容。",
            "metadata": {
                "source": "test_document.pdf",
                "start_page_num": 1,
                "end_page_num": 1,
                "start_line_num": 2,
                "end_line_num": 2,
                "content_type": "text",
                "bbox": [10, 60, 200, 100]
            }
        }
    ]
    
    test_files = [
        ("test_valid.json", test_data_1),
        ("test_empty.json", test_data_2),
        ("test_mixed.json", test_data_3)
    ]
    
    uploads_dir = "uploads"
    os.makedirs(uploads_dir, exist_ok=True)
    
    for filename, data in test_files:
        filepath = os.path.join(uploads_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"✅ 创建测试文件: {filepath}")
    
    return [os.path.join(uploads_dir, filename) for filename, _ in test_files]

if __name__ == "__main__":
    print("🧪 预计算布局处理功能测试")
    print("=" * 60)
    
    # 创建测试文件
    print("📁 创建测试文件...")
    test_files = create_test_json_files()
    print()
    
    # 测试每个文件
    for test_file in test_files:
        test_json_file(test_file)
        print()
    
    # 测试用户的实际文件（如果存在）
    user_file = "uploads/amberplace.json"
    if os.path.exists(user_file):
        print("🔍 测试用户文件...")
        test_json_file(user_file)
    else:
        print(f"ℹ️  用户文件不存在: {user_file}")
    
    print("🏁 测试完成") 