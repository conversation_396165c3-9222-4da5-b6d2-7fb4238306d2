# parsing/tests/test_autolev.py uses the .al files in this directory as inputs and checks
# the equivalence of the parser generated codes and the respective .py files.

# By default, this directory contains tests for all rules of the parser.

# Additional tests consisting of full physics examples shall be made available soon in
# the form of another repository. One shall be able to copy the contents of that repo
# to this folder and use those tests after uncommenting the respective code in
# parsing/tests/test_autolev.py.
