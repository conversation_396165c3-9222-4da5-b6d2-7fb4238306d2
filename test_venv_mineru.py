#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试虚拟环境中的MinerU
"""

import os
import sys
import subprocess

def test_venv_mineru():
    """测试虚拟环境中的MinerU"""
    venv_path = os.path.join(os.getcwd(), "mineru_venv")
    
    if not os.path.exists(venv_path):
        print("❌ 虚拟环境不存在")
        return False
    
    # 获取Python路径
    if os.name == 'nt':  # Windows
        python_path = os.path.join(venv_path, 'Scripts', 'python.exe')
    else:  # Linux/Mac
        python_path = os.path.join(venv_path, 'bin', 'python')
    
    if not os.path.exists(python_path):
        print("❌ 虚拟环境Python不存在")
        return False
    
    print(f"🔍 测试虚拟环境: {venv_path}")
    print(f"🐍 Python路径: {python_path}")
    
    # 详细测试代码
    test_code = '''
import sys
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")

print("\\n📦 检查已安装的包:")
try:
    import pkg_resources
    installed = [d.project_name for d in pkg_resources.working_set]
    relevant = [p for p in installed if any(k in p.lower() for k in ['magic', 'pdf', 'torch'])]
    for pkg in sorted(relevant):
        try:
            version = pkg_resources.get_distribution(pkg).version
            print(f"  {pkg}: {version}")
        except:
            print(f"  {pkg}: 已安装")
except Exception as e:
    print(f"  检查包失败: {e}")

print("\\n🔍 测试MinerU导入:")
success = False

# 测试1: 标准API
try:
    from magic_pdf.api.magic_pdf_parse import magic_pdf_parse
    print("  ✅ magic_pdf.api.magic_pdf_parse - 成功")
    success = True
except ImportError as e:
    print(f"  ❌ magic_pdf.api.magic_pdf_parse - {e}")

# 测试2: UNIPipe
if not success:
    try:
        from magic_pdf.pipe.UNIPipe import UNIPipe
        print("  ✅ magic_pdf.pipe.UNIPipe - 成功")
        success = True
    except ImportError as e:
        print(f"  ❌ magic_pdf.pipe.UNIPipe - {e}")

# 测试3: 基础模块
if not success:
    try:
        import magic_pdf
        print("  ✅ magic_pdf 基础模块 - 成功")
        print(f"    模块路径: {magic_pdf.__file__}")
        success = True
    except ImportError as e:
        print(f"  ❌ magic_pdf 基础模块 - {e}")

# 测试4: 检查模块结构
if not success:
    try:
        import magic_pdf
        print("  📁 magic_pdf 模块内容:")
        for attr in dir(magic_pdf):
            if not attr.startswith('_'):
                print(f"    {attr}")
    except:
        pass

print(f"\\n🎯 最终结果: {'成功' if success else '失败'}")
print("SUCCESS" if success else "FAILED")
'''
    
    try:
        print("🧪 开始测试...")
        result = subprocess.run([python_path, '-c', test_code], 
                              capture_output=True, text=True, timeout=60)
        
        print("📋 测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️  错误信息:")
            print(result.stderr)
        
        # 检查结果
        if "SUCCESS" in result.stdout:
            print("🎉 虚拟环境MinerU测试成功!")
            return True
        else:
            print("❌ 虚拟环境MinerU测试失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 虚拟环境MinerU测试")
    print("=" * 40)
    
    if test_venv_mineru():
        print("\n✅ 测试通过! 可以使用v7版本了")
        print("运行: python start_v7.py")
    else:
        print("\n⚠️  测试未通过，但不用担心!")
        print("v7版本会自动回退到原始PDF处理方式")
        print("你仍然可以正常使用所有功能")
        print("\n💡 修复建议:")
        print("1. 运行修复脚本: python fix_mineru_installation.py")
        print("2. 或者删除虚拟环境重新安装: rm -rf mineru_venv")
        print("3. 或者直接使用v7版本: python start_v7.py")
