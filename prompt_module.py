"""

    Module Name :           prompt_module
    Last Modified Date :    3 Jan 2024

"""

def gen_system_prompt(prompt_para={"role": "assistant",
                                   "task": "question-answering",
                                   "tone": "professional",
                                   "retrieved_content": "",
                                   "max_response_sentences": 3,
                                   "max_response_words": 200,
                                   "confidence_threshold": 0.9,
                                   "input_language": "English",
                                   "output_language": "English",
                                   "constraints": ""}):
    
    # Define role and task
    role_prompt = f' You are a helpful {prompt_para["role"]} for {prompt_para["task"]} tasks with {prompt_para["tone"]} tone. '
    
    # Define whether the response considers retrieved content from RAG
    if prompt_para["retrieved_content"]:
        retr_prompt = f' Consider the following pieces of retrieved content for responding. '
    else:
        retr_prompt = ''

    # Define the maximum number of sentences and words in the response
    leng_prompt = f' Give the responses in {prompt_para["max_response_sentences"]} sentences with the maximum of {prompt_para["max_response_words"]}. '
    
    # Define the threshold for the confidence level for the response
    conf_prompt = f' If your confidence level is below {prompt_para["confidence_threshold"]}, just say that you do not know. ' # may change into similarity
    
    # Define the input language and output language
    lang_prompt = f' The input language is {prompt_para["input_language"]} and the output language is {prompt_para["output_language"]}. '
    
    # Define the constraints that should be considered in the response
    if prompt_para["constraints"]:
        cons_prompt = f' Consider the constraints of {prompt_para["constraints"]} during responding. '
    else:
        cons_prompt = ''
    
    # 添加不要引用文件编号的指示
    no_file_ref_prompt = ' When using information from multiple documents, integrate the information naturally without referring to "first file", "second file", or any specific document numbering. Present a coherent answer that synthesizes information from all available sources. '

    # Merge all prompts into a single prompt
    system_prompt = role_prompt + \
                     retr_prompt + \
                     leng_prompt + \
                     conf_prompt + \
                     lang_prompt + \
                     cons_prompt + \
                     no_file_ref_prompt
        
    return system_prompt

def gen_condense_system_prompt(prompt_para={"max_history_length": "all"}):
    condense_system_prompt = f""" Given a chat history and the latest user question. \
    Consider the latest {prompt_para['max_history_length']} chat history, \
    formulate a standalone question, \
    which might reference the chat history and can be understood without the chat history. \
    Do NOT answer the question, \
    just reformulate it if needed and otherwise return it as is. """

    return condense_system_prompt

def gen_instruct_system_prompt(template):
    pass

def gen_multifile_system_prompt(system_prompt_para, selected_files, multifile_strategy='priority'):
    """
    生成适用于多文件场景的系统提示词
    
    Args:
        system_prompt_para: 系统提示词参数
        selected_files: 选中的文件列表
        multifile_strategy: 多文件策略 ('priority', 'separated', 'original')
    
    Returns:
        str: 优化后的系统提示词
    """
    base_prompt = gen_system_prompt(system_prompt_para)
    
    if len(selected_files) <= 1:
        return base_prompt
    
    file_list_str = "、".join(selected_files)
    
    if multifile_strategy == 'priority':
        multifile_instruction = f"""

多文件检索说明：
- 当前查询涉及多个文件：{file_list_str}
- 系统已自动分析各文件的相关性，优先展示最相关文件的内容
- 如果检测到某个文件特别相关，会主要使用该文件的信息
- 请在回答时优先使用相关性最高的文件内容，必要时再参考其他文件

回答指导：
1. 优先使用最相关文件的信息回答问题
2. 如需引用其他文件，请明确说明来源
3. 避免混淆不同文件的信息
4. 如果问题主要与某个特定文件相关，请集中使用该文件的内容"""

    elif multifile_strategy == 'separated':
        multifile_instruction = f"""

多文件检索说明：
- 当前查询涉及多个文件：{file_list_str}
- 检索结果中已标注每段内容的来源文件
- 请注意区分不同文件的信息，避免混淆

回答指导：
1. 明确标注信息来源，如"根据文件A..."、"在文件B中提到..."
2. 如果多个文件都有相关信息，请分别说明
3. 优先使用最相关的文件信息
4. 避免将不同文件的信息混为一谈"""

    else:  # original
        multifile_instruction = f"""

多文件检索说明：
- 当前查询涉及多个文件：{file_list_str}
- 请根据内容相关性选择最合适的信息进行回答
- 如需引用多个文件，请明确标注来源"""

    return base_prompt + multifile_instruction